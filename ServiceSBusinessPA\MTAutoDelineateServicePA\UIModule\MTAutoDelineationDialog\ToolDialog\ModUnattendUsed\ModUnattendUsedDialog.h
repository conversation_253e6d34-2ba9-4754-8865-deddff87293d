﻿// *********************************************************************************
// <remarks>
// FileName    : ModUnattendUsedDialog
// Author      : zlw
// CreateTime  : 2023-10-30
// Description : 当模板被无人值守使用时，显示提示框
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_ModUnattendUsedDialog.h"


class ModUnattendUsedDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    ModUnattendUsedDialog(const QString& tipStr, const bool isChecked, QWidget* parent = nullptr);
    ~ModUnattendUsedDialog();

    bool getIsChecked();

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::ModUnattendUsedDialogClass ui;
};
