<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModelInfoSetting</class>
 <widget class="QWidget" name="ModelInfoSetting">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>467</width>
    <height>146</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#label_redStart{
color: rgba(213, 65, 65, 1);
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="horizontalSpacing">
       <number>16</number>
      </property>
      <property name="verticalSpacing">
       <number>12</number>
      </property>
      <item row="3" column="1">
       <widget class="MtTextEdit" name="mtTextEdit_desc">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>62</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>62</height>
         </size>
        </property>
        <property name="scrollType">
         <enum>MtScrollBar::scrollbar1</enum>
        </property>
        <property name="maxLength">
         <number>64</number>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtTextEdit::textedit2</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="MtLineEdit" name="mtLineEdit_time">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>30</height>
         </size>
        </property>
        <property name="readOnly">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLineEdit::lineedit1</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="MtLineEdit" name="mtLineEdit_name">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>30</height>
         </size>
        </property>
        <property name="maxLength">
         <number>64</number>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLineEdit::lineedit1</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="MtLabel" name="mtLabel_2">
        <property name="text">
         <string>导入时间</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="MtLabel" name="mtLabel_3">
        <property name="text">
         <string>模型简介</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <layout class="QHBoxLayout" name="horizontalLayout">
        <property name="spacing">
         <number>4</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_redStart">
          <property name="text">
           <string>*</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtLabel" name="mtLabel">
          <property name="text">
           <string>模型名称</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtTextEdit</class>
   <extends>QTextEdit</extends>
   <header>MtTextEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
