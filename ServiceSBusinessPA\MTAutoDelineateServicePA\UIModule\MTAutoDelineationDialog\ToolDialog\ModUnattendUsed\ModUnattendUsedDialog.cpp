﻿#include "ModUnattendUsedDialog.h"
#include "MtToolButton.h"


ModUnattendUsedDialog::ModUnattendUsedDialog(const QString& tipStr, const bool isChecked, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout_2);         //设置布局
    this->setDialogWidthAndContentHeight(466, 100); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("提示"));                     //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    ui.mtLabel->setText(tipStr);
    ui.mtCheckBox->setChecked(isChecked);
}

ModUnattendUsedDialog::~ModUnattendUsedDialog()
{
}

bool ModUnattendUsedDialog::getIsChecked()
{
    return ui.mtCheckBox->isChecked();
}

/// <summary>
/// 关闭按钮
/// </summary>
void ModUnattendUsedDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void ModUnattendUsedDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void ModUnattendUsedDialog::onBtnRight1Clicked()
{
    this->accept();
}
