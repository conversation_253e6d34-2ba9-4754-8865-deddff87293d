﻿#include "AccuComponentUi/Header/UnitUIComponent\QCustMtLabel.h"
#include "ui_QCustMtLabel.h"
#include "AccuComponentUi/Header/QMTUIDefine.h"
#include <QMouseEvent>
#include <QPainter>
#include "CMtCoreDefine.h"
#include "MtComboBox.h"



/*********************单元组件的Param*********************************/
QCustMtLabelParam::QCustMtLabelParam()
{
    _cellWidgetType = DELEAGATE_QCustMtLabel;
}

QCustMtLabelParam::~QCustMtLabelParam()
{
}
QWidget* QCustMtLabelParam::CreateUIModule(QWidget* parent)
{
    QCustMtLabel* labelDot = new QCustMtLabel(parent);
    labelDot->SetupCellWidget(*this);
    return labelDot;
}
/*****************************************************************/


/********************单元组件****************************/
QCustMtLabel::QCustMtLabel(QWidget* parent, bool bEnabaleDrawSquare/* = false*/)
    : QWidget(parent)
{
    ui = new Ui::QCustMtLabel;
    ui->setupUi(this);
    this->GetLabel()->setMtType(MtLabel::myLabel1);
    this->GetLabel()->setElideMode(Qt::ElideRight);
}

QCustMtLabel::~QCustMtLabel()
{
    MT_DELETE(ui);
}

void QCustMtLabel::SetupCellWidget(QCustMtLabelParam& cellWidgetParam)
{
    if (cellWidgetParam._mtType > 0)
    {
        this->GetLabel()->setMtType((MtLabel::MtType)cellWidgetParam._mtType);
    }

    if (false == cellWidgetParam._enableDot)
    {
        this->GetLabel()->setWordWrap(cellWidgetParam._bWordWrap);
    }

    if (cellWidgetParam._showPix)
    {
        this->GetLabel()->setPixmap(QPixmap(cellWidgetParam._pixPath));
    }
    else
    {
        this->GetLabel()->setText(cellWidgetParam._text);
    }
}

bool QCustMtLabel::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->GetLabel()->setText(text);
        return true;
    }

    return false;
}

QString QCustMtLabel::GetCurText()
{
    return this->GetLabel()->text();
}

//void QCustMtLabel::SetEnableEdit(bool bEdit)
//{
//    this->GetLabel()->setEnabled(bEdit);
//}

MtLabel* QCustMtLabel::GetLabel()
{
    return ui->mtLabel;
}

QString QCustMtLabel::GetFullString()
{
    return GetLabel()->text();
}

void QCustMtLabel::SetEnableDot(bool bEnable)
{
    if (bEnable)
    {
        this->GetLabel()->setElideMode(Qt::ElideRight);
    }
    else
    {
        this->GetLabel()->setElideMode(Qt::ElideNone);
    }
}
