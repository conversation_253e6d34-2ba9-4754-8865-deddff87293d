<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ProgressDialog</class>
 <widget class="QWidget" name="ProgressDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>466</width>
    <height>198</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>466</width>
    <height>198</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>466</width>
    <height>198</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>ProgressDialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#frame{
	background-color: rgb(56, 67, 85);
}

#dialogContainer {
	border: 10px;
}

QWidget{
	background:transparent; 
}

QFrame{
	border-radius: 4px;
	background-color: rgb(56, 67, 85);
}

#widget_line
{
	max-height: 1px;
	background-color: rgba(219,226,241,0.1);
}

/****************************************
 *  Progress Bar
 ****************************************/
#progressBar {
	font-size:9px;
	/*border-image: url(:/AccuUIComponentImage/images/background_progress.png) 1 1 1 1;*/
	background-color: rgb(56,67,85);	
	border: 1px;
	color: #fff;
	text-align: right;
	border-style: outset; 
	border-width: 1px;  
	border-color: rgba(188,186,186,0.34);
	border-radius:3px;
	min-height:6px;
	min-width:352px;
	max-height:6px;
	max-width:352px;
}

#progressBar::chunk {
	border-image: url(:/AccuUIComponentImage/images/bar_progress.png) 2 2 2 2 ;
	border: 2px;
	border-radius:3px;
}

#label_bar{
	max-width:30px;
	max-height: 16px;
}


/****************************************
 *  Buttons
 ****************************************/
#cancelPushButton
{
	min-width:80px;
	min-height:30px;
	max-width:80px;
	max-height:30px;
	border-radius: 4px;
	background-color: rgb(56,67,85);
	color: rgba(188,186,186,1);
	border-style: outset; 
	border-width: 1px;  
	border-color: rgba(188,186,186,0.34);
}

#cancelPushButton:hover{ 
	background-color: rgba(216,216,216,0.1);
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="dialogContainer" native="true">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>466</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>500</width>
          <height>16777215</height>
         </size>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="verticalSpacer">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Maximum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>65</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="progressLabel">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>Importing patient images...</string>
           </property>
           <property name="alignment">
            <set>Qt::AlignCenter</set>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
           <property name="indent">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Maximum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>18</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Maximum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QProgressBar" name="progressBar">
             <property name="value">
              <number>24</number>
             </property>
             <property name="textVisible">
              <bool>false</bool>
             </property>
             <property name="invertedAppearance">
              <bool>false</bool>
             </property>
             <property name="format">
              <string>%p%    </string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Maximum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>9</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QLabel" name="label_bar">
             <property name="minimumSize">
              <size>
               <width>30</width>
               <height>16</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>30</width>
               <height>16</height>
              </size>
             </property>
             <property name="text">
              <string>10%</string>
             </property>
             <property name="alignment">
              <set>Qt::AlignCenter</set>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Maximum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_3">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Maximum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>34</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="Line" name="widget_line">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>1</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>1</height>
            </size>
           </property>
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="verticalSpacer_5">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>13</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>344</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QPushButton" name="cancelPushButton">
             <property name="minimumSize">
              <size>
               <width>82</width>
               <height>32</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>82</width>
               <height>32</height>
              </size>
             </property>
             <property name="text">
              <string>取消</string>
             </property>
             <property name="class" stdset="0">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="verticalSpacer_6">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Minimum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>30</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_4">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Minimum</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>14</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
