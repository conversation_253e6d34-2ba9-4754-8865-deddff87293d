﻿#include "ImportEclipseTemplateDialog.h"
#include "MtToolButton.h"
#include "MtMessageBox.h"
#include <QFileDialog>
#include <shlobj.h>
#include <Tlhelp32.h>


ImportEclipseTemplateDialog::ImportEclipseTemplateDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);             //设置布局
    this->setDialogWidthAndContentHeight(1002, 526);    //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("导入Eclipse模板"));              //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    //getWidgetButton()->hide();                        //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //
    m_mtProgressDialog.setLabelText(tr("文件检索中..."));
    m_mtProgressDialog.setValue(0);
    m_mtProgressDialog.setCancelButtonVisible(false);
    m_mtProgressDialog.setHidden(true);
    //信号槽
    qRegisterMetaType<QVector<ST_EclipseTemplate>>("QVector<ST_EclipseTemplate>");
    connect(ui.mtPushButton, &QPushButton::clicked, this, &ImportEclipseTemplateDialog::onBtnFile); //选择文件夹
    connect(ui.mtCheckBox_all, &QCheckBox::stateChanged, this, &ImportEclipseTemplateDialog::onMtCheckBox_all);  //全选
    connect(&m_parseXmlThread, &GParseXmlThread::SigPersent, this, &ImportEclipseTemplateDialog::slotPersent);   //进度
    connect(&m_parseXmlThread, &GParseXmlThread::SigDone, this, &ImportEclipseTemplateDialog::slotDone);         //结束
    connect(ui.table_widget, &QMTAbstractTableView::sigCellWidgetStateChange, this, &ImportEclipseTemplateDialog::slotCellWidgetStateChange); //某个单元格状态改变了
}

ImportEclipseTemplateDialog::~ImportEclipseTemplateDialog()
{
}

/// <summary>
/// 设置检索目录完整路径
/// 测试提出要记忆住路径,但是不显示在edit上，路径选择按钮点击时打开之前记忆的路径
/// </summary>
/// <param name="dirPath">[IN]待检索目录完整路径</param>
/// <param name="isSub">[IN]是否检索子目录</param>
void ImportEclipseTemplateDialog::setSearchDirPath(const QString& dirPath, const bool isSub)
{
    m_searchDir = dirPath;
    ui.mtCheckBox_sub->setChecked(isSub);
}

void ImportEclipseTemplateDialog::getNewSearchDirPath(QString& dirPath, bool& isSub)
{
    dirPath = ui.mtLineEdit->text();
    isSub = ui.mtCheckBox_sub->isChecked();
}

/// <summary>
/// 获取最新的Eclipse-ROI信息
/// </summary>
/// <returns>key-roiCode value-ST_EclipseRoi</returns>
QMap<QString, ST_EclipseRoi> ImportEclipseTemplateDialog::getNewEclipseRoiInfo()
{
    return ui.table_widget->getNewEclipseRoiInfo();
}

QMap<QString, ST_EclipseTemplate> ImportEclipseTemplateDialog::getNewEclipseTemplate()
{
    return ui.table_widget->getNewEclipseTemplate();
}

bool ImportEclipseTemplateDialog::isNeedSyncInfo()
{
    return ui.mtCheckBox_syncInfo->isChecked();
}

/// <summary>
/// 关闭按钮
/// </summary>
void ImportEclipseTemplateDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void ImportEclipseTemplateDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void ImportEclipseTemplateDialog::onBtnRight1Clicked()
{
    if (ui.table_widget->getNewEclipseRoiInfo().isEmpty())
    {
        MtMessageBox::NoIcon::information_Title(this, tr("请选择需要导入的Eclipse勾画模板"));
        return;
    }

    this->accept();
}

bool ImportEclipseTemplateDialog::IsDotNetApp()
{
    bool bRet = false;
    HANDLE hModuleSnap = INVALID_HANDLE_VALUE;
    MODULEENTRY32 me32 = { 0 };
    // 创建模块快照
    hModuleSnap = CreateToolhelp32Snapshot(TH32CS_SNAPMODULE | TH32CS_SNAPMODULE32, GetCurrentProcessId());

    if (hModuleSnap == INVALID_HANDLE_VALUE)
        return bRet;

    // 遍历所有模块
    me32.dwSize = sizeof(MODULEENTRY32);

    if (Module32First(hModuleSnap, &me32))
    {
        do
        {
            if (strstr(me32.szExePath, "clr.dll") != nullptr ||
                strstr(me32.szExePath, "mscorlib.dll") != nullptr ||
                strstr(me32.szExePath, "System.dll") != nullptr)
            {
                bRet = true;
                break;
            }
        }
        while (Module32Next(hModuleSnap, &me32));
    }

    CloseHandle(hModuleSnap);
    return bRet;
}

int ImportEclipseTemplateDialog::BrowseCallbackProc(HWND hwnd, UINT uMsg, LPARAM lParam, LPARAM lpData)
{
    if (uMsg == BFFM_INITIALIZED)
    {
        if (CMtLanguageUtil::type != chinese)
        {
            SetWindowText(hwnd, "Browse folders");
        }

        // 设置初始打开的目录
        SendMessage(hwnd, BFFM_SETSELECTION, TRUE, (LPARAM)lpData);
    }

    return 0;
}
/// <summary>
/// 选择文件夹
/// </summary>
void ImportEclipseTemplateDialog::onBtnFile()
{
    QString dirPath;

    if (IsDotNetApp())
    {
        BROWSEINFOW bi = { 0 };
        LPITEMIDLIST pidl;
        wchar_t selectedPath[MAX_PATH] = { 0 };
        char initDir[MAX_PATH] = { 0 };
        WideCharToMultiByte(CP_ACP, 0, m_searchDir.replace("/", "\\").toStdWString().c_str(), -1, initDir, MAX_PATH, NULL, NULL);
        // 初始化BROWSEINFO结构
        bi.hwndOwner = NULL;
        bi.pszDisplayName = selectedPath;
        //bi.lpszTitle = "请选择一个目录";
        bi.ulFlags = BIF_RETURNONLYFSDIRS | BIF_DONTGOBELOWDOMAIN /*| BIF_NEWDIALOGSTYLE*/;
        bi.lpfn = BrowseCallbackProc;
        bi.lParam = (LPARAM) initDir;
        // 显示浏览文件夹对话框
        pidl = SHBrowseForFolderW(&bi);

        if (pidl != 0)
        {
            // 获取用户选择的目录路径
            SHGetPathFromIDListW(pidl, selectedPath);
            // 释放内存
            CoTaskMemFree(pidl);
            char szSelDir[MAX_PATH] = { 0 };
            WideCharToMultiByte(CP_UTF8, 0, selectedPath, -1, szSelDir, MAX_PATH, NULL, NULL);
            dirPath = szSelDir;
        }
    }
    else
    {
        QFileDialog* fd = new QFileDialog(this);
        fd->resize(240, 320);
        fd->setViewMode(QFileDialog::List);

        if (m_searchDir.isEmpty() == false && QFileInfo::exists(m_searchDir) == true)
            fd->setDirectory(m_searchDir);

        dirPath = fd->getExistingDirectory();
        //QString filePath = fd->getOpenFileName(nullptr, tr("选择文件"), "", "All Files (*);;Text Files (*.xml)");
        fd->close();
    }

    if (!dirPath.isEmpty())
    {
        ui.mtLineEdit->setText(dirPath);
        m_mtProgressDialog.setValue(0);
        m_mtProgressDialog.setHidden(false);
        m_parseXmlThread.startParse("eclipse", dirPath, ui.mtCheckBox_sub->isChecked());
    }
}

/// <summary>
/// 全选
/// </summary>
void ImportEclipseTemplateDialog::onMtCheckBox_all(int state)
{
    bool ret = ui.mtCheckBox_all->isChecked();
    ui.table_widget->checkAll(ret);
}

/// <summary>
/// 进度
/// </summary>
void ImportEclipseTemplateDialog::slotPersent(const int persent)
{
    m_mtProgressDialog.setValue(persent);
}

/// <summary>
/// 结束
/// </summary>
void ImportEclipseTemplateDialog::slotDone(const QVector<ST_EclipseTemplate> eclipseTemplateVec)
{
    m_allRecordSum = m_selectSum = 0;
    m_mtProgressDialog.setHidden(true);
    m_mtProgressDialog.setValue(0);

    if (eclipseTemplateVec.isEmpty())
    {
        MtMessageBox::NoIcon::information_Title(this, tr("未检索到相关数据"));
        return;
    }

    ui.table_widget->addEclipseTemplate(eclipseTemplateVec);

    if (ui.mtCheckBox_all->isChecked())
    {
        ui.table_widget->checkAll(true);
    }
}

/// <summary>
/// 某个单元格状态改变了(目前只有checkbox,可支持更多)
/// </summary>
/// <param name="cellItemIndex">[IN]一级列表行信息结构体</param>
/// <param name="state">[IN]Qt::CheckState</param>
void ImportEclipseTemplateDialog::slotCellWidgetStateChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, int state)
{
    disconnect(ui.mtCheckBox_all, &QCheckBox::stateChanged, this, &ImportEclipseTemplateDialog::onMtCheckBox_all);
    int checkNum = 0;
    int rowNum = ui.table_widget->GetRowCount();

    for (int i = 0; i < rowNum; i++)
    {
        int checkState = ui.table_widget->GetCheckBoxState(i, 0);

        if (checkState == Qt::Checked)
            checkNum = checkNum + 1;
    }

    if (checkNum == 0)
        ui.mtCheckBox_all->setCheckState(Qt::Unchecked);
    else if (checkNum == rowNum)
        ui.mtCheckBox_all->setCheckState(Qt::Checked);
    else
        ui.mtCheckBox_all->setCheckState(Qt::PartiallyChecked);

    connect(ui.mtCheckBox_all, &QCheckBox::stateChanged, this, &ImportEclipseTemplateDialog::onMtCheckBox_all);
}

