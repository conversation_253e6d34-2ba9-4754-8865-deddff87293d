﻿#include "SwitchLabelReminderDialog.h"
#include "CMtLanguageUtil.h"

SwitchLabelReminderDialog::SwitchLabelReminderDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::SwitchLabelReminderWidget;
    ui->setupUi(this);
    //
    this->setMainLayout(ui->verticalLayout);
    this->setTitle(tr("提示"));
    this->setFixedSize(466, 130);
    //this->setMargin(24, 18, 24, 20);
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    this->setAllowDrag(true);
    initCheckBox();
}

SwitchLabelReminderDialog::~SwitchLabelReminderDialog()
{
    if (ui != nullptr)
    {
        delete ui;
    }
}

void SwitchLabelReminderDialog::setMainText(const QString& str)
{
    ui->main_text->setText(str);
}

bool SwitchLabelReminderDialog::isNameChecked()
{
    return ui->mtCheckBox_name->isChecked();
}

bool SwitchLabelReminderDialog::isChineseNameChecked()
{
    return ui->mtCheckBox_ROIChName->isChecked();
}

bool SwitchLabelReminderDialog::isTypeChecked()
{
    return ui->mtCheckBox_type->isChecked();
}

bool SwitchLabelReminderDialog::isColorChecked()
{
    return ui->mtCheckBox_color->isChecked();
}

void SwitchLabelReminderDialog::initCheckBox()
{
    ui->mtCheckBox_name->setChecked(false);
    ui->mtCheckBox_ROIChName->setChecked(false);
    ui->mtCheckBox_type->setChecked(false);
    ui->mtCheckBox_color->setChecked(false);

    if (CMtLanguageUtil::type != chinese)
    {
        ui->mtCheckBox_ROIChName->hide();
    }
}

void SwitchLabelReminderDialog::onBtnRight1Clicked()
{
    this->accept();
}

void SwitchLabelReminderDialog::onBtnRight2Clicked()
{
    this->reject();
}

void SwitchLabelReminderDialog::onBtnCloseClicked()
{
    this->reject();
}

