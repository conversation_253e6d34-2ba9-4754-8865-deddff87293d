﻿#include "GroupItemListWidget.h"
#include "MtListWidget.h"
#include "GroupHTitle.h"
#include "GroupItem.h"
#include <QAbstractItemView>


GroupItemListWidget::GroupItemListWidget(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    ui.mtListWidget->setStyleSheet("#mtListWidget{background-color:transparent;}#mtListWidget::item{background-color:transparent;}");
    ui.mtListWidget->setVerticalScrollMode(QAbstractItemView::ScrollPerPixel);
    ui.mtListWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
}

GroupItemListWidget::~GroupItemListWidget()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void GroupItemListWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 设置唯一标识
/// </summary>
/// <param name="uniqueKey">[IN]唯一标识</param>
void GroupItemListWidget::setUniqueKey(const int uniqueKey)
{
    m_uniqueKey = uniqueKey;
}

/// <summary>
/// 获取唯一标识
/// </summary>
int GroupItemListWidget::getUniqueKey()
{
    return m_uniqueKey;
}

/// <summary>
/// 添加标题
/// </summary>
/// <param name="pageTypeEnum">[IN]item类型</param>
/// <param name="data">[IN]数据</param>
/// <param name="topMargin">[IN]上边距</param>
/// <param name="bottomMargin">[IN]下边距</param>
void GroupItemListWidget::addTitle(const GroupHTitle::EM_PageType pageTypeEnum, const GroupHTitleData& data, int topMargin, int bottomMargin)
{
    if (ui.mtListWidget->count() <= 0)
    {
        topMargin = 0;
    }

    GroupHTitle* item = new GroupHTitle();
    item->setImagePathHash(m_imagePathHash);
    int sizeNum = item->init(pageTypeEnum, data, topMargin, bottomMargin);
    connect(item, &GroupHTitle::sigGroupHTitleCheckToGroupItemListWidget, this, &GroupItemListWidget::slotChildHTitleCheckToGroupItemListWidget);   //标题勾选
    connect(item, &GroupHTitle::sigGroupHTitleExpandToGroupItemListWidget, this, &GroupItemListWidget::slotChildHTitleExpandToGroupItemListWidget); //标题展开/收起
    //
    QListWidgetItem* listItem = new QListWidgetItem;
    ui.mtListWidget->addItem(listItem);
    ui.mtListWidget->setItemWidget(listItem, item);
    listItem->setSizeHint(QSize(0, sizeNum));
    m_groupHTitleMap.insert(data.groupId, item);
    m_groupItemWidgetMap[data.groupId] = listItem;

    //记录非编辑页下的MtListWidget每一行对象
    if (pageTypeEnum == GroupItem::Page_LabelLabel)
    {
        m_labelLabel_listWidgetTitleMap[data.groupId].push_back(listItem);
    }
    else if (pageTypeEnum == GroupItem::Page_CheckLabel)
    {
        m_labelCheck_listWidgetTitleMap[data.groupId].push_back(listItem);
    }
}

/// <summary>
/// 添加item
/// </summary>
/// <param name="pageTypeEnum">[IN]item类型</param>
/// <param name="dataList">[IN]数据集合</param>
///  <param name="itemWidthNum">[IN]item宽度</param>
/// <param name="maxColumn">[IN]最大列数</param>
/// <param name="horizontalSpace">[IN]水平间距</param>
void GroupItemListWidget::addItems(const GroupItem::EM_PageType pageTypeEnum, const QList<GroupItemData>& dataList, int itemWidthNum, int maxColumn, int horizontalSpace)
{
    if (dataList.isEmpty())
        return;

    GroupItem* item = new GroupItem();
    item->setImagePathHash(m_imagePathHash);
    item->setItemWidth(itemWidthNum);
    int sizeNum = item->init(pageTypeEnum, dataList);
    connect(this, &GroupItemListWidget::sigCheckAllToChildGroupItem_MtLabelCheck, item, &GroupItem::slotCheckAllItem_MtCheckBoxLabel); //勾选分组下的全部MtLabelCheck，发送到GroupItem，UI变化
    connect(item, &GroupItem::sigGroupItemCheckToGroupItemListWidget, this, [=](const int groupId, const int id, const bool checked) //某个MtLabelCheck发生勾选
    {
        //设置标题复选框状态
        if (m_groupHTitleMap.contains(groupId) && m_itemStateMap.contains(groupId))
        {
            //修改内存中的勾选状态
            QMap<int, bool>& itemMap = m_itemStateMap[groupId];
            itemMap[id] = checked;
            //获取勾选数量
            int isCheckNum = 0;

            for (QMap<int, bool>::iterator it = itemMap.begin(); it != itemMap.end(); it++)
            {
                if (it.value() == true)
                    isCheckNum++;
            }

            if (isCheckNum == 0)
                m_groupHTitleMap[groupId]->setCheckState(Qt::Unchecked);
            else if (isCheckNum < itemMap.size())
                m_groupHTitleMap[groupId]->setCheckState(Qt::PartiallyChecked);
            else
                m_groupHTitleMap[groupId]->setCheckState(Qt::Checked);
        }

        //发送到AutoSketchTemplateWidget
        emit this->sigOneItemCheckFromGroupItemListWidget(groupId, { id }, checked);
    });
    //
    QListWidgetItem* listItem = new QListWidgetItem;
    ui.mtListWidget->addItem(listItem);
    ui.mtListWidget->setItemWidget(listItem, item);
    listItem->setSizeHint(QSize(itemWidthNum * 3 + 30, sizeNum));

    //记录非编辑页下的MtListWidget每一行对象
    if (pageTypeEnum == GroupItem::Page_LabelLabel)
    {
        m_labelLabel_listWidgetItemMap[dataList[0].groupId].push_back(listItem);
    }
    else if (pageTypeEnum == GroupItem::Page_CheckLabel)
    {
        m_labelCheck_listWidgetItemMap[dataList[0].groupId].push_back(listItem);
    }

    //记录每个item的勾选状态
    for (int i = 0; i < dataList.size(); i++)
    {
        m_itemStateMap[dataList[i].groupId].insert(dataList[i].valId, dataList[i].isCheck);
    }
}

/// <summary>
/// 清空
/// </summary>
void GroupItemListWidget::clearAllItem()
{
    ui.mtListWidget->clear();
    m_groupHTitleMap.clear();
    m_itemStateMap.clear();
    m_labelLabel_listWidgetTitleMap.clear();
    m_labelCheck_listWidgetTitleMap.clear();
    m_labelLabel_listWidgetItemMap.clear();
    m_labelCheck_listWidgetItemMap.clear();
    m_uniqueKey = -1;
}

/// <summary>
/// 刷新所有标题勾选状态
/// </summary>
void GroupItemListWidget::flushAllTitleCheckStatus()
{
    for (QMap<int, GroupHTitle*>::iterator it = m_groupHTitleMap.begin(); it != m_groupHTitleMap.end(); it++)
    {
        int groupId = it.key();

        if (m_itemStateMap.contains(groupId))
        {
            //修改内存中的勾选状态
            QMap<int, bool> itemMap = m_itemStateMap[groupId];
            //获取勾选数量
            int isCheckNum = 0;

            for (QMap<int, bool>::iterator it_2 = itemMap.begin(); it_2 != itemMap.end(); it_2++)
            {
                if (it_2.value() == true)
                    isCheckNum++;
            }

            if (isCheckNum == 0)
                m_groupHTitleMap[groupId]->setCheckState(Qt::Unchecked);
            else if (isCheckNum < itemMap.size())
                m_groupHTitleMap[groupId]->setCheckState(Qt::PartiallyChecked);
            else
                m_groupHTitleMap[groupId]->setCheckState(Qt::Checked);
        }
    }
}

/// <summary>
/// 滚动到最上方
/// </summary>
/// <param name="isTop">[IN]true最上方 false最下方</param>
void GroupItemListWidget::scrollTop(const bool isTop)
{
    if (isTop == true)
        ui.mtListWidget->scrollToTop();
    else
        ui.mtListWidget->scrollToBottom();
}

void GroupItemListWidget::scrollToGroup(int groupId)
{
    if (m_groupItemWidgetMap.contains(groupId))
    {
        ui.mtListWidget->scrollToItem(m_groupItemWidgetMap[groupId]);
    }
}

/// <summary>
/// 展开全部
/// </summary>
/// <param name="expand">true全部</param>
void GroupItemListWidget::expandTreeAll(const GroupHTitle::EM_PageType pageTypeEnum, const bool expand)
{
    if (pageTypeEnum == GroupHTitle::Page_Label)
    {
        for (QMap<int, QList<QListWidgetItem*>>::iterator it = m_labelLabel_listWidgetTitleMap.begin(); it != m_labelLabel_listWidgetTitleMap.end(); it++)
        {
            QList<QListWidgetItem*>& itemList = it.value();

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                {
                    GroupHTitle* groupHTitle = qobject_cast<GroupHTitle*>(ui.mtListWidget->itemWidget(itemList[i]));

                    if (groupHTitle == nullptr)
                        continue;

                    if (groupHTitle->getExpandButtonState() != expand)
                        groupHTitle->setExpandButtonState(expand);
                }
            }
        }
    }
    else if (pageTypeEnum == GroupHTitle::Page_Check)
    {
        for (QMap<int, QList<QListWidgetItem*>>::iterator it = m_labelCheck_listWidgetTitleMap.begin(); it != m_labelCheck_listWidgetTitleMap.end(); it++)
        {
            QList<QListWidgetItem*>& itemList = it.value();

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                {
                    GroupHTitle* groupHTitle = qobject_cast<GroupHTitle*>(ui.mtListWidget->itemWidget(itemList[i]));

                    if (groupHTitle == nullptr)
                        continue;

                    if (groupHTitle->getExpandButtonState() != expand)
                        groupHTitle->setExpandButtonState(expand);
                }
            }
        }
    }
}

/// <summary>
/// 勾选全部
/// </summary>
/// <param name="expand">true全部</param>
void GroupItemListWidget::checkTreeAll(const GroupHTitle::EM_PageType pageTypeEnum, const bool isCheck)
{
    if (pageTypeEnum == GroupHTitle::Page_Label)
    {
        for (QMap<int, QList<QListWidgetItem*>>::iterator it = m_labelLabel_listWidgetTitleMap.begin(); it != m_labelLabel_listWidgetTitleMap.end(); it++)
        {
            QList<QListWidgetItem*>& itemList = it.value();

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                {
                    GroupHTitle* groupHTitle = qobject_cast<GroupHTitle*>(ui.mtListWidget->itemWidget(itemList[i]));

                    if (groupHTitle == nullptr)
                        continue;

                    groupHTitle->setCheckState(isCheck);
                }
            }
        }
    }
    else if (pageTypeEnum == GroupHTitle::Page_Check)
    {
        for (QMap<int, QList<QListWidgetItem*>>::iterator it = m_labelCheck_listWidgetTitleMap.begin(); it != m_labelCheck_listWidgetTitleMap.end(); it++)
        {
            QList<QListWidgetItem*>& itemList = it.value();

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                {
                    GroupHTitle* groupHTitle = qobject_cast<GroupHTitle*>(ui.mtListWidget->itemWidget(itemList[i]));

                    if (groupHTitle == nullptr)
                        continue;

                    groupHTitle->setCheckState(isCheck);
                }
            }
        }
    }
}

/// <summary>
/// 获取勾选的item集合
/// </summary>
/// <returns>key-groupId value-itemId</returns>
QMap<int, QSet<int>> GroupItemListWidget::getCheckedItemMap()
{
    QMap<int, QSet<int>> m_itemCheckMap;

    for (QMap<int, QMap<int, bool>>::iterator it = m_itemStateMap.begin(); it != m_itemStateMap.end(); it++)
    {
        int groupId = it.key();
        QMap<int, bool> itemMap = it.value();

        for (QMap<int, bool>::iterator it_item = itemMap.begin(); it_item != itemMap.end(); it_item++)
        {
            if (it_item.value() == true)
            {
                m_itemCheckMap[groupId].insert(it_item.key());
            }
        }
    }

    return m_itemCheckMap;
}

/// <summary>
/// 标题展开/收起，接收子控件的信号
/// </summary>
void GroupItemListWidget::slotChildHTitleExpandToGroupItemListWidget(const GroupHTitle::EM_PageType pageTypeEnum, const int groupId, const bool expanded)
{
    //如果是非编辑状态，则没有roi搜索功能，此时可以直接管控标题下的MtLabelLabel控件，无需再发送到AutoSketchTemplateWidget
    if (pageTypeEnum == GroupHTitle::Page_Label)
    {
        if (m_labelLabel_listWidgetItemMap.contains(groupId) == true)
        {
            QList<QListWidgetItem*>& itemList = m_labelLabel_listWidgetItemMap[groupId];

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                    itemList[i]->setHidden(!expanded);
            }
        }
    }
    else if (pageTypeEnum == GroupHTitle::Page_Check)
    {
        if (m_labelCheck_listWidgetItemMap.contains(groupId) == true)
        {
            QList<QListWidgetItem*>& itemList = m_labelCheck_listWidgetItemMap[groupId];

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                    itemList[i]->setHidden(!expanded);
            }
        }

        //增加对所有Title此时展开状态的判断
        int expandNum = 0;

        for (QMap<int/*groupId*/, QList<QListWidgetItem*>>::iterator it = m_labelCheck_listWidgetTitleMap.begin(); it != m_labelCheck_listWidgetTitleMap.end(); it++)
        {
            QList<QListWidgetItem*>& itemList = it.value();

            for (int i = 0; i < itemList.size(); i++)
            {
                if (itemList[i] != nullptr)
                {
                    GroupHTitle* groupHTitle = qobject_cast<GroupHTitle*>(ui.mtListWidget->itemWidget(itemList[i]));

                    if (groupHTitle == nullptr)
                        continue;

                    if (groupHTitle->getExpandButtonState() == true)
                        expandNum++;
                }
            }
        }

        if (expandNum == m_labelCheck_listWidgetTitleMap.size() || expandNum == 0)
            emit this->sigAllItemExpandFromGroupItemListWidget(GroupHTitle::Page_Check, expandNum == m_labelCheck_listWidgetTitleMap.size());
    }
}

/// <summary>
/// 标题复选框是否勾选
/// </summary>
void GroupItemListWidget::slotChildHTitleCheckToGroupItemListWidget(const int groupId, const bool checked)
{
    //printf("@@@@@@@@ slotChildHTitleCheckToGroupItemListWidget\n");
    emit this->sigCheckAllToChildGroupItem_MtLabelCheck(groupId, checked);
}

