<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>EmptyRoiWidgetClass</class>
 <widget class="QWidget" name="EmptyRoiWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>739</width>
    <height>461</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>EmptyRoiWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="EmptyRoiTable" name="widget_table" native="true"/>
     </item>
     <item>
      <widget class="MtFrameEx" name="mtFrameEx_line">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>1</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>1</height>
        </size>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::frameEx3</enum>
       </property>
       <property name="mtType" stdset="0">
        <string notr="true">frameEx3</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="MtFrameEx" name="mtFrameEx">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>33</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>33</height>
        </size>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::frameEx3_30</enum>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <property name="spacing">
         <number>16</number>
        </property>
        <property name="leftMargin">
         <number>16</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtToolButton" name="mtToolButton_add">
          <property name="minimumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="text">
           <string>...</string>
          </property>
          <property name="iconSize">
           <size>
            <width>22</width>
            <height>22</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtToolButton::toolbutton2</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtToolButton" name="mtToolButton_del">
          <property name="minimumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="text">
           <string>...</string>
          </property>
          <property name="iconSize">
           <size>
            <width>22</width>
            <height>22</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtToolButton::toolbutton2</enum>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>448</width>
            <height>12</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>EmptyRoiTable</class>
   <extends>QWidget</extends>
   <header>emptyroitable.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
