﻿#include "AutoDelineationDataOpt.h"
#include <QCoreApplication>
#include <QJsonDocument>
#include <QSettings>
#include <QFile>
#include "AccuComponentUi\Header\Language.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "DBDataUtils.h"
#include "FileOperationUtils.h"

AutoDelineationDataOpt::AutoDelineationDataOpt(QObject* parent)
    : QObject(parent)
{
}

AutoDelineationDataOpt::~AutoDelineationDataOpt()
{
}

/// <summary>
/// QString转QJsonObject
/// </summary>
QJsonObject AutoDelineationDataOpt::qStringToqJsonObject(const QString& jsonString)
{
    QJsonDocument jsonDocument = QJsonDocument::fromJson(jsonString.toUtf8());

    if (jsonDocument.isNull())
    {
        return QJsonObject();
    }

    return jsonDocument.object();
}

/// <summary>
/// QString转QJsonArray
/// </summary>
QJsonArray AutoDelineationDataOpt::qStringToqJsonArray(const QString& jsonString)
{
    QJsonDocument jsonDocument = QJsonDocument::fromJson(jsonString.toUtf8());

    if (jsonDocument.isNull())
    {
        return QJsonArray();
    }

    return jsonDocument.array();
}

/// <summary>
///QJsonObject转QString
/// </summary>
QString AutoDelineationDataOpt::qJsonObjectToqString(const QJsonObject& jsonObject)
{
    return QString(QJsonDocument(jsonObject).toJson(QJsonDocument::Compact));
}

/// <summary>
///QVariantList转QStringList
/// </summary>
QStringList AutoDelineationDataOpt::qVariantListToQStringList(const QVariantList& variantList)
{
    QStringList strList;
    strList.reserve(variantList.size() + 1);

    for (int i = 0; i < variantList.size(); i++)
    {
        strList.push_back(variantList[i].toString());
    }

    return strList;
}

/// <summary>
/// 格式化时间
/// </summary>
QString AutoDelineationDataOpt::getDateTimeStr(QString dateStr, QString timeStr)
{
    bool isOK = false;
    QString dataAndTimeStr;
    QDate date;
    QTime time;
    dateStr = dateStr.remove(QRegExp("\\s")); //去除所有空格
    timeStr = timeStr.remove(QRegExp("\\s")); //去除所有空格

    if (dateStr.size() == 14)
    {
        QDateTime dateTime = QDateTime::fromString(dateStr, "yyyyMMddhhmmss");
        dateStr = dateTime.toString("yyyyMMdd");
    }

    date = QDate::fromString(dateStr, "yyyyMMdd");
    time = QTime::fromString(timeStr, "hhmmss");
    //QDateTime dateTime = QDateTime(date, time);

    if (dateStr.size() > 0)
    {
        dateStr = date.toString("yyyy-MM-dd");
    }

    //时间需要转换处理，可能有小数点，分号，hhmmss格式
    if (timeStr.size() > 0)
    {
        if (timeStr.contains(":", Qt::CaseInsensitive))
        {
        }
        else if (timeStr.contains(".", Qt::CaseInsensitive))
        {
            int index = timeStr.indexOf(".");
            timeStr = timeStr.mid(0, index);
            time = QTime::fromString(timeStr, "hhmmss");
            timeStr = time.toString("hh:mm:ss");
        }
        else
        {
            timeStr = time.toString("hh:mm:ss");
        }
    }

    if (0 == dateStr.size() && 0 == timeStr.size())
    {
        dataAndTimeStr = "NONE";
    }
    else
    {
        if (dateStr.isEmpty())
        {
            dataAndTimeStr = timeStr;
        }
        else
        {
            dataAndTimeStr = dateStr + " " + timeStr;
        }
    }

    return dataAndTimeStr;
}

/// <summary>
/// 解析JSON文件到QJsonObject
/// </summary>
/// <param name="filePath">[IN]json文件完整路径</param>
/// <param name="errMsg">[OUT]错误信息</param>
/// <returns>QJsonObject</returns>
QJsonObject AutoDelineationDataOpt::getQJsonObjectOfFile(const QString& filePath, QString& errMsg)
{
    //读取dcm_info.json
    QFile file(filePath);

    if (!file.open(QIODevice::Text | QFile::ReadOnly))
    {
        errMsg = "open file error";
        return QJsonObject();
    }

    QByteArray byteArray = file.readAll();
    file.close();
    //
    QJsonParseError jsonError;
    QJsonDocument jsonDocument = QJsonDocument::fromJson(byteArray, &jsonError);

    if (jsonDocument.isNull())
    {
        errMsg = jsonError.errorString();
        return QJsonObject();
    }

    QJsonObject jsonObject = jsonDocument.object();

    if (jsonObject.isEmpty())
    {
        errMsg = "jsonObject empty";
        return QJsonObject();
    }

    return jsonObject;
}


/**************************************************读取接口**************************************************/
/// <summary>
/// 获取不用的器官名集合
/// </summary>
/// <returns>不用的器官名集合</returns>
QSet<QString> AutoDelineationDataOpt::getNotUseOrganOfOrganInfoJson()
{
    QString errMsg;
    QJsonObject jsonObject = getQJsonObjectOfFile(QCoreApplication::applicationDirPath() + "/Config/OrganInfo.json", errMsg);
    QVariantList SketchOrganList = jsonObject.value("hideOrganList").toArray().toVariantList();
    QSet<QString> notUseOrganNameSet;

    for (int i = 0; i < SketchOrganList.size(); i++)
    {
        notUseOrganNameSet.insert(SketchOrganList[i].toString());
    }

    return notUseOrganNameSet;
}

/// <summary>
/// 获取所有分组信息
/// </summary>
/// <returns>key-ogangroupinfo表id value-ST_OrganGroupInfo</returns>
QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> AutoDelineationDataOpt::getAllGroupInfo()
{
    QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> infoMap;
    QMap<int/*groupId*/, int/*organId*/> secGroupOrganIdMap;    //亚组对应关联器官
    std::vector<DBOrganGroupInfo> organGroupInfoVec = DBDataUtils::FindAllOrganGroupInfo();
    std::vector<DBOrganGroupInfo_Extra> groupInfoExtraVec = DBDataUtils::FindAllOrganGroupInfo_Extra();

    ///解析组信息--获取主结构分组信息
    for (const DBOrganGroupInfo_Extra& itemExtra : groupInfoExtraVec)
    {
        QJsonDocument jsonDocument = QJsonDocument::fromJson(QString::fromStdString(itemExtra.GetJsonInfo()).toUtf8());

        if (jsonDocument.isObject())
        {
            secGroupOrganIdMap[itemExtra.GetOrganGroupInfoId()] = jsonDocument.object().value("ref_organinfoconfigId").toString().toInt();
        }
    }

    for (int i = 0; i < organGroupInfoVec.size(); i++)
    {
        int gid = organGroupInfoVec[i].GetId();
        n_mtautodelineationdialog::ST_OrganGroupInfo stOrganGroupInfo;
        stOrganGroupInfo.id = organGroupInfoVec[i].GetId();
        stOrganGroupInfo.name = organGroupInfoVec[i].GetName().c_str();
        stOrganGroupInfo.type = organGroupInfoVec[i].GetType();
        stOrganGroupInfo.refOrganId = secGroupOrganIdMap.contains(gid) ? secGroupOrganIdMap[gid] : -1;
        infoMap.insert(stOrganGroupInfo.id, stOrganGroupInfo);
    }

    return infoMap;
}

/// <summary>
/// 获取所有分组信息并排序
/// </summary>
/// <returns>所有分组信息并排序</returns>
QList<n_mtautodelineationdialog::ST_OrganGroupInfo> AutoDelineationDataOpt::getAllGroupInfoAndSort()
{
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo>  groupInfoList;
    //获取分组信息
    QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoMap = getAllGroupInfo();
    //获取排序规则
    std::vector<DBFeaturesConfig> configVec = DBDataUtils::FindFeaturesConfigByKey("organ_group_order");

    if (configVec.empty() == false)
    {
        QVariantList variantList = qStringToqJsonArray(configVec[0].GetFeatureValue().c_str()).toVariantList();

        for (int i = 0; i < variantList.size(); i++)
        {
            int id = variantList[i].toString().toInt();

            if (groupInfoMap.contains(id) == true)
                groupInfoList.push_back(groupInfoMap[id]);
        }
    }

    if (groupInfoList.size() != groupInfoMap.size())
    {
        groupInfoList.clear();

        for (QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::iterator it = groupInfoMap.begin(); it != groupInfoMap.end(); it++)
        {
            groupInfoList.push_back(it.value());
        }
    }

    return groupInfoList;
}

void AutoDelineationDataOpt::getAllGroupInfoAndSort(QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList)
{
    QMap<int/*groupId*/, int/*organId*/> secGroupOrganIdMap;    //亚组对应关联器官
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> tempGroupList;
    std::vector<DBOrganGroupInfo_Extra> groupInfoExtraVec = DBDataUtils::FindAllOrganGroupInfo_Extra();
    std::vector<DBOrganGroupInfo> organGroupVec = DBDataUtils::FindAllOrganGroupInfo();

    //--更新分组显示名
    for (int i = 0; i < organGroupVec.size(); ++i)
    {
        const QMap<QString, QString>& nameMap = CommonUtil::getRoiGroupNameMap();
        QString curName = organGroupVec[i].GetName().c_str();

        if (nameMap.contains(curName))
        {
            organGroupVec[i].SetName(nameMap[curName].toStdString());
        }
    }

    ///解析组信息--获取主结构分组信息
    for (const DBOrganGroupInfo_Extra& itemExtra : groupInfoExtraVec)
    {
        QJsonDocument jsonDocument = QJsonDocument::fromJson(QString::fromStdString(itemExtra.GetJsonInfo()).toUtf8());

        if (jsonDocument.isObject())
        {
            secGroupOrganIdMap[itemExtra.GetOrganGroupInfoId()] = jsonDocument.object().value("ref_organinfoconfigId").toString().toInt();
        }
    }

    for (const DBOrganGroupInfo& groupInfoItem : organGroupVec)
    {
        int gid = groupInfoItem.GetId();
        n_mtautodelineationdialog::ST_OrganGroupInfo g = { gid, groupInfoItem.GetType(), secGroupOrganIdMap.contains(gid) ? secGroupOrganIdMap[gid] : -1, groupInfoItem.GetName().c_str() };
        tempGroupList.append(g);
    }

    std::vector<DBFeaturesConfig> organGroupOrderVec = DBDataUtils::FindFeaturesConfigByKey("organ_group_order");
    //排序器官分组
    QJsonArray orderArr;

    if (organGroupOrderVec.size() != 0)
    {
        QJsonDocument jsonDocument = QJsonDocument::fromJson(QString::fromStdString(organGroupOrderVec[0].GetFeatureValue()).toUtf8());

        if (!jsonDocument.isNull())
        {
            orderArr = jsonDocument.array();
        }
    }

    if (orderArr.size() == 0)
    {
        allGroupList = tempGroupList;
    }
    else
    {
        for (int i = 0; i < orderArr.size(); ++i)
        {
            int gID = orderArr[i].toString().toInt();

            for (int r = 0; r < tempGroupList.size(); ++r)
            {
                if (gID == tempGroupList[r].id)
                {
                    allGroupList.append(tempGroupList[r]);
                    tempGroupList.removeAt(r);
                    break;
                }
            }
        }

        if (tempGroupList.size() > 0)
        {
            allGroupList.append(tempGroupList);
        }
    }
}
/// <summary>
/// 获取所有Organ信息,默认模型同名去重
/// </summary>
/// <param name="isEnbale">[IN]是否只收集使能的器官</param>
/// <param name="outOrganList">[OUT]器官集合</param>
/// <param name="outOrganMap">[OUT]器官集合 key-organinfoconfig表id</param>
/// <param name="bDefaultRepeat">[IN]默认模型器官是否保留同名</param>
void AutoDelineationDataOpt::getAllOrganUnique(const bool isEnbale, QList<n_mtautodelineationdialog::ST_Organ>& outOrganList, QMap<int, n_mtautodelineationdialog::ST_Organ>& outOrganMap, bool bDefaultRepeat/* = false*/)
{
    outOrganList.reserve(100);
    //获取不用的器官名称
    QSet<QString> notUseOrganNameSet = getNotUseOrganOfOrganInfoJson();
    QSet<QString> defOrganNameSet;
    //获取分组信息及全部器官
    QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupInfoMap = getAllGroupInfo();
    std::vector<DBOrganInfoConfig> organInfoConfigVec = DBDataUtils::FindAllOrganInfoConfigRecords();

    for (int i = 0; i < organInfoConfigVec.size(); i++)
    {
        n_mtautodelineationdialog::ST_Organ stOrgan;
        const DBOrganInfoConfig& info = organInfoConfigVec[i];

        if (isEnbale == true && info.GetEnable() == 0)
            continue;

        stOrgan.bodypart = info.GetBodypart().c_str();
        stOrgan.customColor = info.GetCustomColor().c_str();
        stOrgan.customOrganName = info.GetCustomOrganName().c_str();
        stOrgan.defaultColor = info.GetDefaultColor().c_str();
        stOrgan.defaultOrganName = info.GetDefaultOrganName().c_str();
        stOrgan.enable = info.GetEnable();
        stOrgan.id = info.GetId();
        stOrgan.isVisiable = info.GetIsVisiable();
        stOrgan.modelId = info.GetModelid();

        if (Language::type == Chinese)
        {
            stOrgan.organChineseName = info.GetOrganChineseName().c_str();
        }

        stOrgan.organEnglishName = info.GetOrganEnglishName().c_str();
        stOrgan.roiDesc = info.GetRoiDesc().c_str();
        stOrgan.roiLabel = info.GetRoiLabel().c_str();
        stOrgan.roiParam = info.GetRoiParam().c_str();
        stOrgan.roiType = info.GetRoiType().c_str();
        QStringList groupIdList = QString::fromStdString(info.GetOrgangroupinfoIdList()).split(';', QString::SplitBehavior::SkipEmptyParts);

        if (stOrgan.modelId == 0)
        {
            //剔除不使用的器官
            if (notUseOrganNameSet.contains(stOrgan.defaultOrganName) == true)
                continue;

            //默认模型同名器官去重
            if (!bDefaultRepeat && defOrganNameSet.contains(stOrgan.defaultOrganName) == true)
                continue;

            defOrganNameSet.insert(stOrgan.defaultOrganName);
        }

        for (int m = 0; m < groupIdList.size(); m++)
        {
            int groupId = groupIdList[m].toInt();

            if (organGroupInfoMap.contains(groupId) == true)
            {
                stOrgan.organGroupInfoMap.insert(groupId, organGroupInfoMap[groupId]);
            }
        }

        outOrganList.push_back(stOrgan);
        outOrganMap.insert(stOrgan.id, stOrgan);
    }
}

/// <summary>
/// 获取指定modelId的Organ信息
/// </summary>
/// <param name="isEnbale">[IN]是否只收集使能的器官</param>
/// <param name="modelId">[IN]模型id</param>
/// <returns>指定modelId的Organ信息</returns>
QList<n_mtautodelineationdialog::ST_Organ> AutoDelineationDataOpt::getAllOrganByModelId(const bool isEnbale, const int modelId)
{
    QSet<QString> defOrganNameSet;
    QList<n_mtautodelineationdialog::ST_Organ> organList;
    //获取不用的器官名称
    QSet<QString> notUseOrganNameSet = getNotUseOrganOfOrganInfoJson();
    //获取分组信息及全部器官
    QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupInfoMap = getAllGroupInfo();
    std::vector<DBOrganInfoConfig> organInfoConfigVec = DBDataUtils::FindOrganInfoByModelId(modelId);
    organList.reserve(organInfoConfigVec.size() + 1);

    for (size_t i = 0; i < organInfoConfigVec.size(); i++)
    {
        n_mtautodelineationdialog::ST_Organ stOrgan;
        DBOrganInfoConfig info = organInfoConfigVec[i];

        if (isEnbale == true && info.GetEnable() == 0)
            continue;

        stOrgan.id = info.GetId();
        stOrgan.modelId = info.GetModelid();
        stOrgan.defaultOrganName = info.GetDefaultOrganName().c_str();

        if (stOrgan.modelId == 0)
        {
            //剔除不使用的器官
            if (notUseOrganNameSet.contains(stOrgan.defaultOrganName) == true)
                continue;

            //默认模型同名器官去重
            if (defOrganNameSet.contains(stOrgan.defaultOrganName) == true)
                continue;

            defOrganNameSet.insert(stOrgan.defaultOrganName);
        }

        if (Language::type == Chinese)
            stOrgan.organChineseName = info.GetOrganChineseName().c_str();

        stOrgan.customOrganName = info.GetCustomOrganName().c_str();
        stOrgan.defaultColor = info.GetCustomColor().c_str();
        stOrgan.customColor = info.GetCustomColor().c_str();
        stOrgan.roiType = info.GetRoiType().c_str();
        stOrgan.roiLabel = info.GetRoiLabel().c_str();
        stOrgan.roiParam = info.GetRoiParam().c_str();
        stOrgan.roiDesc = info.GetRoiDesc().c_str();
        QStringList groupIdList = QString::fromStdString(info.GetOrgangroupinfoIdList()).split(";", QString::SplitBehavior::SkipEmptyParts);

        for (int m = 0; m < groupIdList.size(); m++)
        {
            int groupId = groupIdList[m].toInt();

            if (organGroupInfoMap.contains(groupId) == true)
            {
                stOrgan.organGroupInfoMap.insert(groupId, organGroupInfoMap[groupId]);
            }
        }

        organList.push_back(stOrgan);
    }

    return organList;
}

/// <summary>
/// 获取所有AL-Organ信息
/// </summary>
/// <param name="isEnbale">[IN]是否只收集使能的器官</param>
/// <returns>key-modelId</returns>
QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> AutoDelineationDataOpt::getALOrganByModelId(const bool isEnbale)
{
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> alOrganMap;
    QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupInfoMap = getAllGroupInfo();
    std::vector<DBOrganInfoConfig> organInfoConfigVec = DBDataUtils::FindAllOrganInfoConfigRecords();

    for (size_t i = 0; i < organInfoConfigVec.size(); i++)
    {
        if (organInfoConfigVec[i].GetModelid() == 0 || organInfoConfigVec[i].GetModelid() == -1)
            continue;

        n_mtautodelineationdialog::ST_Organ stOrgan;
        DBOrganInfoConfig info = organInfoConfigVec[i];

        if (isEnbale == true && info.GetEnable() == 0)
            continue;

        stOrgan.id = info.GetId();
        stOrgan.modelId = info.GetModelid();
        stOrgan.defaultOrganName = info.GetDefaultOrganName().c_str();

        if (Language::type == Chinese)
            stOrgan.organChineseName = info.GetOrganChineseName().c_str();

        stOrgan.customOrganName = info.GetCustomOrganName().c_str();
        stOrgan.defaultColor = info.GetCustomColor().c_str();
        stOrgan.customColor = info.GetCustomColor().c_str();
        stOrgan.roiType = info.GetRoiType().c_str();
        stOrgan.roiLabel = info.GetRoiLabel().c_str();
        stOrgan.roiParam = info.GetRoiParam().c_str();
        stOrgan.roiDesc = info.GetRoiDesc().c_str();
        QStringList groupIdList = QString::fromStdString(info.GetOrgangroupinfoIdList()).split(";", QString::SplitBehavior::SkipEmptyParts);

        for (int m = 0; m < groupIdList.size(); m++)
        {
            int groupId = groupIdList[m].toInt();

            if (organGroupInfoMap.contains(groupId) == true)
            {
                stOrgan.organGroupInfoMap.insert(groupId, organGroupInfoMap[groupId]);
            }
        }

        alOrganMap[stOrgan.modelId].push_back(stOrgan);
    }

    return alOrganMap;
}

/// <summary>
/// 获取器官默认设置信息
/// </summary>
/// <param name="stOrganDefaultList">器官默认设置信息</param>
void AutoDelineationDataOpt::GetOrganDefaultConfigInfo(const QString& organDefaultConfigInfoPath, QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
{
    //判断文件是否存在
    if (!QFileInfo().exists(organDefaultConfigInfoPath))
    {
        MtMessageBox::information(nullptr, tr("未找到默认设置文件信息"));
        return;
    }
    //解析配置文件
    //QString cfgPath = qApp->applicationDirPath() + "/Config/OrganInfo.json";
    QJsonObject jsonObject;

    if (!FileOperationUtils::GetJsonFileObject(organDefaultConfigInfoPath, jsonObject))
        return;

    if (!jsonObject.contains("SketchOrganList"))
        return;

    QJsonArray organArr = jsonObject["SketchOrganList"].toArray();

    for (int i = 0; i < organArr.size(); ++i)
    {
        QJsonObject organItemInfoObj = organArr[i].toObject();
        n_mtautodelineationdialog::ST_Organ organInfoItem;
        organInfoItem.defaultColor = organItemInfoObj["defaultColor"].toString();
        organInfoItem.defaultOrganName = organItemInfoObj["defaultOrganName"].toString();
        organInfoItem.customOrganName = organItemInfoObj["customOrganName"].toString();
        organInfoItem.organChineseName = organItemInfoObj["defaultChineseName"].toString();
        organInfoItem.roiDesc = organItemInfoObj["defaultRoiDesc"].toString();
        organInfoItem.roiLabel = organItemInfoObj["defaultRoiLabel"].toString();
        organInfoItem.roiType = organItemInfoObj["defaultRoiType"].toString();
        stOrganDefaultList.append(organInfoItem);
    }
}

/// <summary>
/// 获取默认模型
/// </summary>
/// <returns>默认模型(就一个)</returns>
QList<n_mtautodelineationdialog::ST_SketchModel> AutoDelineationDataOpt::getDefSketchModel(const bool isEnbale)
{
    QList<n_mtautodelineationdialog::ST_Organ> organList = getAllOrganByModelId(isEnbale, 0);

    if (organList.isEmpty())
        return QList<n_mtautodelineationdialog::ST_SketchModel>();

    QList<n_mtautodelineationdialog::ST_SketchModel> modelList;
    n_mtautodelineationdialog::ST_SketchModel stSketchModel;
    stSketchModel.id = 0;
    stSketchModel.modelName = "Default Model";
    stSketchModel.modality = "CT";
    stSketchModel.modelDesc.clear();
    stSketchModel.importTime.clear();
    stSketchModel.organList = organList;
    stSketchModel.modelType = 1;
    modelList.push_back(stSketchModel);
    return modelList;
}

/// <summary>
/// 获取空勾画模型
/// </summary>
/// <param name="isEnbale">[IN]是否只收集使能的器官</param>
/// <returns>空勾画模型(就一个)</returns>
QList<n_mtautodelineationdialog::ST_SketchModel> AutoDelineationDataOpt::getEmptySketchModel(const bool isEnbale)
{
    QList<n_mtautodelineationdialog::ST_Organ> organList = getAllOrganByModelId(isEnbale, -1);

    if (organList.isEmpty())
    {
        return QList<n_mtautodelineationdialog::ST_SketchModel>();
    }

    QList<n_mtautodelineationdialog::ST_SketchModel> modelList;
    n_mtautodelineationdialog::ST_SketchModel stSketchModel;
    stSketchModel.id = -1;
    stSketchModel.modelName = "empty_sketch";
    stSketchModel.modality.clear();
    stSketchModel.modelDesc.clear();
    stSketchModel.importTime.clear();
    stSketchModel.organList = organList;
    stSketchModel.modelType = 1;
    modelList.push_back(stSketchModel);
    return modelList;
}

/// <summary>
/// 获取AL模型
/// </summary>
/// <param name="isEnbale">[IN]是否只收集使能的器官</param>
/// <returns>AL模型</returns>
QList<n_mtautodelineationdialog::ST_SketchModel> AutoDelineationDataOpt::getALSketchModel(const bool isEnbale)
{
    QList<n_mtautodelineationdialog::ST_SketchModel> modelList;
    //获取所有AL器官
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> alOrganMap = getALOrganByModelId(isEnbale);
    //获取所有模型
    std::vector<DBAiModelInfo> aiModelInfoVec = DBDataUtils::FindAllAiModelInfo();
    modelList.reserve(aiModelInfoVec.size() + 1);

    for (size_t i = 0; i < aiModelInfoVec.size(); i++)
    {
        const DBAiModelInfo& info = aiModelInfoVec[i];
        n_mtautodelineationdialog::ST_SketchModel stSketchModel;
        stSketchModel.id = info.GetId();
        stSketchModel.modelName = info.GetModelName().c_str();
        stSketchModel.modality = QString::fromStdString(info.GetModality()).toUpper();
        stSketchModel.modelDesc = info.GetModelDesc().c_str();
        stSketchModel.importTime = info.GetInsertTime().toString("yyyy-MM-dd hh:mm:ss");
        stSketchModel.modelType = info.GetModelType();

        //获取所有organ
        if (alOrganMap.contains(stSketchModel.id) == false)
            continue;

        stSketchModel.organList = alOrganMap[stSketchModel.id];
        modelList.push_back(stSketchModel);
    }

    return modelList;
}

/// <summary>
/// 获取AL模型
/// </summary>
/// <param name="isEnbale">[IN]是否只收集使能的器官</param>
/// <returns>AL模型</returns>
QMap<int, n_mtautodelineationdialog::ST_SketchModel> AutoDelineationDataOpt::getALSketchModelMap(const bool isEnbale)
{
    QMap<int, n_mtautodelineationdialog::ST_SketchModel> modelInfoMap;
    //获取所有AL器官
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> alOrganMap = getALOrganByModelId(isEnbale);
    //获取所有模型
    std::vector<DBAiModelInfo> aiModelInfoVec = DBDataUtils::FindAllAiModelInfo();

    for (size_t i = 0; i < aiModelInfoVec.size(); i++)
    {
        const DBAiModelInfo& info = aiModelInfoVec[i];
        n_mtautodelineationdialog::ST_SketchModel stSketchModel;
        stSketchModel.id = info.GetId();
        stSketchModel.modelName = info.GetModelName().c_str();
        stSketchModel.modality = QString::fromStdString(info.GetModality()).toUpper();
        stSketchModel.modelDesc = info.GetModelDesc().c_str();
        stSketchModel.importTime = info.GetInsertTime().toString("yyyy-MM-dd hh:mm:ss");
        stSketchModel.modelType = info.GetModelType();

        //获取所有organ
        if (alOrganMap.contains(stSketchModel.id) == false)
            continue;

        stSketchModel.organList = alOrganMap[stSketchModel.id];
        modelInfoMap.insert(stSketchModel.id, stSketchModel);
    }

    return modelInfoMap;
}

/// <summary>
/// 获取所有模板,剔除没归组的器官(没有排序)
/// </summary>
/// <returns>所有模板,剔除没归组的器官(没有排序)</returns>
QList<n_mtautodelineationdialog::ST_SketchModelCollection> AutoDelineationDataOpt::getAllSketchCollection()
{
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> collectionList;
    collectionList.reserve(30);
    //获取所有organ
    QList<n_mtautodelineationdialog::ST_Organ> organList;
    QMap<int, n_mtautodelineationdialog::ST_Organ> organMap;
    getAllOrganUnique(true, organList, organMap);
    //获取所有模板
    std::vector<DBSketchModelCollection> modelCollectionVec = DBDataUtils::FindAllModelCollection();

    for (size_t i = 0; i < modelCollectionVec.size(); i++)
    {
        DBSketchModelCollection info = modelCollectionVec[i];
        n_mtautodelineationdialog::ST_SketchModelCollection stCollection;
        stCollection.id = info.GetId();
        stCollection.isUnattended = info.GetIsUnattended() == 1 ? true : false;
        stCollection.templateName = info.GetTitle().c_str();
        stCollection.remark = info.GetRemark().c_str();
        QJsonObject jsonObj = qStringToqJsonObject(info.GetModelData().c_str());

        if (jsonObj.empty() == false)
        {
            stCollection.modality = jsonObj.value("modality").toString().toUpper();
            QJsonArray arr = jsonObj.value("organ_arr").toArray();

            for (int m = 0; m < arr.size(); m++)
            {
                QJsonObject obj = arr[m].toObject();

                if (obj.empty() == false)
                {
                    //id
                    QString id = obj.value("id").toString();

                    //如果不存在该器官id，则不收集
                    if (id.isEmpty() == true && organMap.contains(id.toInt()) == false)
                        continue;

                    //该器官如果没有被分组，则不收集
                    if (organMap[id.toInt()].organGroupInfoMap.isEmpty() == true)
                        continue;

                    //show_groupId_arr
                    QVariantList show_groupId_arr = obj.value("show_groupId_arr").toArray().toVariantList();

                    for (int n = 0; n < show_groupId_arr.size(); n++)
                    {
                        QString groupId = show_groupId_arr[n].toString();

                        if (groupId.isEmpty() == true)
                            continue;

                        stCollection.groupIdSet.insert(groupId.toInt());
                        stCollection.showGroupIdMap[id.toInt()].insert(groupId.toInt());
                    }

                    //sub_in_main_group_arr
                    QVariantList sub_in_main_group_arr = obj.value("sub_in_main_group_arr").toArray().toVariantList();

                    for (int n = 0; n < sub_in_main_group_arr.size(); n++)
                    {
                        QString groupId = sub_in_main_group_arr[n].toString();

                        if (groupId.isEmpty() == true)
                            continue;

                        stCollection.subInMainGroupIdMap[id.toInt()].insert(groupId.toInt());
                    }
                }
            }
        }

        collectionList.push_back(stCollection);
    }

    return collectionList;
}

QList<n_mtautodelineationdialog::ST_SketchModelCollection> AutoDelineationDataOpt::getAllSketchCollectionOriginal()
{
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> modelCollectionList;
    std::vector<DBSketchModelCollection> modelCollectionVec = DBDataUtils::FindAllModelCollection();

    for (const DBSketchModelCollection& collectionItem : modelCollectionVec)
    {
        QJsonDocument jsonDocument = QJsonDocument::fromJson(QString::fromStdString(collectionItem.GetModelData()).toUtf8());

        if (!jsonDocument.isNull())
        {
            QJsonObject jsonObj = jsonDocument.object();
            QJsonArray organInfoArr = jsonObj.value("organ_arr").toArray();
            n_mtautodelineationdialog::ST_SketchModelCollection modelCollectionInfoItem;
            modelCollectionInfoItem.id = collectionItem.GetId();
            modelCollectionInfoItem.templateName = collectionItem.GetTitle().c_str();
            modelCollectionInfoItem.remark = collectionItem.GetRemark().c_str();
            modelCollectionInfoItem.isUnattended = collectionItem.GetIsUnattended();
            modelCollectionInfoItem.modality = jsonObj.value("modality").toString();

            for (int i = 0; i < organInfoArr.size(); ++i)
            {
                QJsonObject organObj = organInfoArr[i].toObject();
                QJsonArray show_groupId_arr = organObj["show_groupId_arr"].toArray();
                QVariantList sub_in_main_group_arr = organObj.value("sub_in_main_group_arr").toArray().toVariantList();
                int organID = organObj["id"].toString().toInt();

                //show_groupId_arr
                for (int j = 0; j < show_groupId_arr.size(); ++j)
                {
                    int gID = show_groupId_arr[j].toString().toInt();
                    modelCollectionInfoItem.showGroupIdMap[organID].insert(gID);
                    modelCollectionInfoItem.groupIdSet.insert(gID);
                }

                //sub_in_main_group_arr
                for (int n = 0; n < sub_in_main_group_arr.size(); n++)
                {
                    QString groupId = sub_in_main_group_arr[n].toString();

                    if (groupId.isEmpty() == true)
                        continue;

                    modelCollectionInfoItem.subInMainGroupIdMap[organID].insert(groupId.toInt());
                }
            }

            modelCollectionList.append(modelCollectionInfoItem);
        }
    }

    return modelCollectionList;
}

/// <summary>
/// 获取排序后的所有模板,剔除没归组的器官
/// </summary>
/// <returns>排序后的所有模板,剔除没归组的器官</returns>
QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> AutoDelineationDataOpt::getAllSketchCollectionSort()
{
    //获取所有模板
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> collectionList = getAllSketchCollection();
    QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection> collectionMap;

    for (int i = 0; i < collectionList.size(); i++)
    {
        collectionMap.insert(collectionList[i].id, collectionList[i]);
    }

    if (collectionMap.isEmpty())
    {
        return QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>();
    }

    //CT
    QList<int> ctModelIdList;
    ctModelIdList.reserve(30);
    std::vector<DBFeaturesConfig> configVec = DBDataUtils::FindFeaturesConfigByKey("sketch_model_order_ct");

    if (configVec.empty() == false)
    {
        QVariantList variantList = qStringToqJsonArray(QString::fromStdString(configVec[0].GetFeatureValue())).toVariantList();

        for (int i = 0; i < variantList.size(); i++)
        {
            int id = variantList[i].toString().toInt();
            ctModelIdList.push_back(id);
        }
    }

    //MR
    QList<int> mrModelIdList;
    mrModelIdList.reserve(30);
    configVec = DBDataUtils::FindFeaturesConfigByKey("sketch_model_order_mr");

    if (configVec.empty() == false)
    {
        QVariantList variantList = qStringToqJsonArray(QString::fromStdString(configVec[0].GetFeatureValue())).toVariantList();

        for (int i = 0; i < variantList.size(); i++)
        {
            int id = variantList[i].toString().toInt();
            mrModelIdList.push_back(id);
        }
    }

    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> sketchCollectionMap;
    sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].clear();

    for (int i = 0; i < ctModelIdList.size(); i++)
    {
        int id = ctModelIdList[i];

        if (collectionMap.contains(id))
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].push_back(collectionMap[id]);
            collectionMap.remove(id);
        }
    }

    for (int i = 0; i < mrModelIdList.size(); i++)
    {
        int id = mrModelIdList[i];

        if (collectionMap.contains(id))
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].push_back(collectionMap[id]);
            collectionMap.remove(id);
        }
    }

    //收集剩下的模板
    for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it = collectionMap.begin(); it != collectionMap.end(); it++)
    {
        if (it.value().modality.toUpper() == "CT")
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].push_back(it.value());
        }
        else if (it.value().modality.toUpper() == "MR")
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].push_back(it.value());
        }
    }

    return sketchCollectionMap;
}

/// <summary>
/// 获取排序后的所有无人值守模板,剔除没归组的器官
/// </summary>
/// <returns>排序后的所有无人值守模板,剔除没归组的器官</returns>
QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> AutoDelineationDataOpt::getAllUnattendSketchCollectionSort()
{
    //获取所有模板
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> collectionList = getAllSketchCollection();
    QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection> collectionMap;

    for (int i = 0; i < collectionList.size(); i++)
    {
        if (collectionList[i].isUnattended == true)
            collectionMap.insert(collectionList[i].id, collectionList[i]);
    }

    if (collectionMap.isEmpty())
    {
        return QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>();
    }

    //CT
    QList<int> ctModelIdList;
    ctModelIdList.reserve(30);
    std::vector<DBFeaturesConfig> configVec = DBDataUtils::FindFeaturesConfigByKey("sketch_model_order_ct");

    if (configVec.empty() == false)
    {
        QVariantList variantList = qStringToqJsonArray(QString::fromStdString(configVec[0].GetFeatureValue())).toVariantList();

        for (int i = 0; i < variantList.size(); i++)
        {
            int id = variantList[i].toString().toInt();
            ctModelIdList.push_back(id);
        }
    }

    //MR
    QList<int> mrModelIdList;
    mrModelIdList.reserve(30);
    configVec = DBDataUtils::FindFeaturesConfigByKey("sketch_model_order_mr");

    if (configVec.empty() == false)
    {
        QVariantList variantList = qStringToqJsonArray(QString::fromStdString(configVec[0].GetFeatureValue())).toVariantList();

        for (int i = 0; i < variantList.size(); i++)
        {
            int id = variantList[i].toString().toInt();
            mrModelIdList.push_back(id);
        }
    }

    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> sketchCollectionMap;
    sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].clear();

    for (int i = 0; i < ctModelIdList.size(); i++)
    {
        int id = ctModelIdList[i];

        if (collectionMap.contains(id))
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].push_back(collectionMap[id]);
            collectionMap.remove(id);
        }
    }

    for (int i = 0; i < mrModelIdList.size(); i++)
    {
        int id = mrModelIdList[i];

        if (collectionMap.contains(id))
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].push_back(collectionMap[id]);
            collectionMap.remove(id);
        }
    }

    //收集剩下的模板
    for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it = collectionMap.begin(); it != collectionMap.end(); it++)
    {
        if (it.value().modality.toUpper() == "CT")
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].push_back(it.value());
        }
        else if (it.value().modality.toUpper() == "MR")
        {
            sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].push_back(it.value());
        }
    }

    return sketchCollectionMap;
}

//获取所有无人值守模板id-名称集合
QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>> AutoDelineationDataOpt::getAllUnattendSketchCollectionName()
{
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>> templateNameMap;
    //获取所有模板
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> collectionList = getAllSketchCollection();

    for (int i = 0; i < collectionList.size(); i++)
    {
        if (collectionList[i].isUnattended == true)
        {
            if (collectionList[i].modality.toUpper() == "CT")
            {
                templateNameMap[n_mtautodelineationdialog::OptDcmType_CT].insert(collectionList[i].id, collectionList[i].templateName);
            }
            else if (collectionList[i].modality.toUpper() == "MR")
            {
                templateNameMap[n_mtautodelineationdialog::OptDcmType_MR].insert(collectionList[i].id, collectionList[i].templateName);
            }
        }
    }

    return templateNameMap;
}

/// <summary>
/// 获取所有本地服务器名(SCP/共享文件夹/Ftp站点)
/// </summary>
/// <returns>key-1:共享文件夹 2:FTP服务器 4:SCP服务器</returns>
QMap<int, QStringList> AutoDelineationDataOpt::getAllLocalServerName()
{
    //1:共享文件夹 2:FTP服务器 4:SCP服务器
    QMap<int, QStringList> allLocalServerNameMap;
    //aeserverinfo表
    std::vector<DBAEServerInfo> defLocalScpInfoVec = DBDataUtils::FindAeServerInfoWithAeType(1);
    std::vector<DBAEServerInfo> extraLocalScpInfoVec = DBDataUtils::FindAeServerInfoWithAeType(2);
    defLocalScpInfoVec.insert(defLocalScpInfoVec.end(), extraLocalScpInfoVec.begin(), extraLocalScpInfoVec.end());

    for (size_t i = 0; i < defLocalScpInfoVec.size(); i++)
    {
        allLocalServerNameMap[4].push_back(defLocalScpInfoVec[i].GetServerName().c_str());
    }

    //syncaddressinfo表
    std::vector<DBSyncAddressInfo> syncAddressInfoVec = DBDataUtils::FindAllSyncAddressInfo();

    for (size_t i = 0; i < syncAddressInfoVec.size(); i++)
    {
        DBSyncAddressInfo info = syncAddressInfoVec[i];

        if (info.GetAddressType() == 1 || info.GetAddressType() == 2) //共享文件夹/ftp服务器
        {
            allLocalServerNameMap[info.GetAddressType()].push_back(info.GetName().c_str());
        }
    }

    return allLocalServerNameMap;
}

/// <summary>
/// 获取所有远程Scp服务器
/// </summary>
/// <returns>所有远程Scp服务器</returns>
QList<n_mtautodelineationdialog::ST_AddrSimple> AutoDelineationDataOpt::getAllRemoteScpAddrList()
{
    QMap<QString/*export_serverName*/, n_mtautodelineationdialog::ST_AddrSimple> exportServerFormatMap;
    //featuresconfig表
    //获取所有远程服务器信息
    std::vector<DBAEServerInfo> exportServerVec = DBDataUtils::FindAeServerInfoWithAeType(0);

    //保存服务器-导出配置集合
    for (int i = 0; i < exportServerVec.size(); i++)
    {
        DBAEServerInfo aeServerInfo = exportServerVec[i];
        n_mtautodelineationdialog::ST_AddrSimple stAddr;
        stAddr.addrType = 4;
        stAddr.exportFormat = "0";
        stAddr.stScpInfo.serverName = aeServerInfo.GetServerName().c_str();
        exportServerFormatMap.insert(stAddr.stScpInfo.serverName, stAddr);
    }

    //查询数据库featuresconfig表export_server_config字段
    auto featureVec = DBDataUtils::FindFeaturesConfigByKey("export_server_config");

    if (!featureVec.empty())
    {
        QJsonObject jsonObject = qStringToqJsonObject(featureVec[0].GetFeatureValue().c_str());
        QJsonArray jsonArray = jsonObject.value("export_server_arr").toArray();

        for (int i = 0; i < jsonArray.size(); i++)
        {
            QString export_serverName = jsonArray[i].toObject().value("export_serverName").toString();
            QString export_format_guid = jsonArray[i].toObject().value("export_format_guid").toString();

            if (exportServerFormatMap.contains(export_serverName))
                exportServerFormatMap[export_serverName].exportFormat = (export_format_guid.isEmpty() ? "0" : export_format_guid);
        }
    }

    //按照数据库id排序
    QList<n_mtautodelineationdialog::ST_AddrSimple> addrList;
    addrList.reserve(5);

    for (int i = 0; i < exportServerVec.size(); i++)
    {
        QString serverName = exportServerVec[i].GetServerName().c_str();

        if (exportServerFormatMap.contains(serverName))
            addrList.push_back(exportServerFormatMap[serverName]);
    }

    return addrList;
}

/// <summary>
/// 获取软件记忆的自动勾画导出地址信息(用于提前选中)
/// </summary>
/// <param name="outCheckExport">[OUT]是否打勾</param>
/// <returns>软件记忆的自动勾画导出地址信息</returns>
n_mtautodelineationdialog::ST_AddrSimple AutoDelineationDataOpt::getSoftMemoryAutoSketchExportAddr(bool& outCheckExport)
{
    n_mtautodelineationdialog::ST_AddrSimple stAddrSimple;
#if fixLater
    //获取导出到服务器格式配置
    QMap<QString/*export_serverName*/, QString/*export_format_guid*/> exportScpMap = DBDataUtils::getExportServerConfigInfoOfTableFeaturesConfig();
    //获取导出到共享文件夹格式配置
    QString autoCreateShareDir, autoFormatGuid;
    DBDataUtils::GetExportDirConfigInfoOfTableFeaturesConfig(QString(), QString(), autoCreateShareDir, autoFormatGuid);
    //
    //获取自动导出地址exportType=1
    std::vector<ExportSeriesInfo> exportInfoVec = DBDataUtils::getExportSeriesInfo("manteia-0000-0000-0000", 1); //0:手动导出 1:自动导出

    if (exportInfoVec.empty() == false)
    {
        //enable: 10不导出到远程 11不导出到共享 20导出到远程 21导出到共享(个位数代表1共享0远程 十位数代表2导出1不导出)
        //判断是否自动导出打勾
        if (exportInfoVec[0].Enable() / 10 == 1) //不导出
            outCheckExport = false;
        else //导出
            outCheckExport = true;

        //判断导出地址是远程还是共享
        for (size_t i = 0; i < exportInfoVec.size(); i++)
        {
            if (exportInfoVec[i].Enable() % 10 == 0 && exportInfoVec[i].NetType() == 0) //远程服务器
            {
                stAddrSimple.addrType = 4;
                stAddrSimple.exportFormat = "0";
                stAddrSimple.exportRange = exportInfoVec[i].ExportRange();
                QString remoteScpName = exportInfoVec[i].DestAddress();
                stAddrSimple.stScpInfo.serverName = remoteScpName;

                if (exportScpMap.contains(remoteScpName) == true)
                {
                    stAddrSimple.exportFormat = exportScpMap[remoteScpName];
                }
            }
            else if (exportInfoVec[i].Enable() % 10 == 1 && exportInfoVec[i].NetType() == 1)//共享文件夹
            {
                stAddrSimple.addrType = 1;
                stAddrSimple.exportRange = exportInfoVec[i].ExportRange();
                stAddrSimple.stDirInfo.dirPath = exportInfoVec[i].DestAddress();
                stAddrSimple.stDirInfo.mkSubType = autoCreateShareDir.toInt();
                stAddrSimple.exportFormat = autoFormatGuid;
            }
        }
    }
#endif
    return stAddrSimple;
}

/// <summary>
/// 获取待合并的rt信息
/// </summary>
/// <param name="ctSeriesUID">[IN]图像的seriesUID</param>
/// <param name="outMergeRtSopInsUIDList">[OUT]所有相关rtSopInsUID</param>
/// <param name="outMergeRtValMap">[OUT]key-sopInsUID value-显示文本</param>
void AutoDelineationDataOpt::getMergeRtInfo(const QString& ctSeriesUID, QStringList& outMergeRtSopInsUIDList, QMap<QString, QString>& outMergeRtValMap)
{
    if (ctSeriesUID.isEmpty() == true)
        return;

    QMap<QString, QList<RtInfo2>> rtInfoMap = DBDataUtils::FindAllRtInfo_ReferencedSeriesUID({ ctSeriesUID });

    if (rtInfoMap.contains(ctSeriesUID) == true)
    {
        QList<RtInfo2> infoList = rtInfoMap[ctSeriesUID];

        for (int i = 0; i < infoList.size(); i++)
        {
            outMergeRtSopInsUIDList.push_back(infoList[i].getSopInsUID().c_str());
            outMergeRtValMap.insert(infoList[i].getSopInsUID().c_str(), QString("Struct %1").arg(getDateTimeStr(infoList[i].getStructureSetDate().c_str(), infoList[i].getStructureSetTime().c_str())));
        }
    }
}

/// <summary>
/// 获取所有远程Scp服务器
/// </summary>
/// <returns>所有远程Scp服务器 key-serverName</returns>
QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> AutoDelineationDataOpt::getAllRemoteScpAddrMap()
{
    QMap<QString/*serverName*/, n_mtautodelineationdialog::ST_AddrSimple> addrMap;
    QList<n_mtautodelineationdialog::ST_AddrSimple> addrList = getAllRemoteScpAddrList();

    for (int i = 0; i < addrList.size(); i++)
    {
        addrMap.insert(addrList[i].stScpInfo.serverName, addrList[i]);
    }

    return addrMap;
}

/// <summary>
/// 获取所有无人值守信息
/// </summary>
/// <param name="outUnattendedConfigMap">[OUT]无人值守信息</param>
/// <param name="outUnattendedEnable">[OUT]无人值守开关</param>
void AutoDelineationDataOpt::getUnattendedConfig(QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>& outUnattendedConfigMap, bool& outUnattendedEnable)
{
    outUnattendedEnable = true;
    //
    //获取所有远程服务器集合
    QMap<QString/*serverName*/, n_mtautodelineationdialog::ST_AddrSimple> tempScpMap = getAllRemoteScpAddrMap();
    //将所有远程服务器名转大写，以防万一
    QMap<QString/*serverName*/, n_mtautodelineationdialog::ST_AddrSimple> remoteScpUpperMap;

    for (QMap<QString/*serverName*/, n_mtautodelineationdialog::ST_AddrSimple>::iterator it = tempScpMap.begin(); it != tempScpMap.end(); it++)
    {
        remoteScpUpperMap.insert(it.key().toUpper(), it.value());
    }

    //规则信息汇总
    //
    //检索unattendedfeature表获取所有无人值守配置
    std::vector<DBUnattendedFeature> unattendedFeatureVec = DBDataUtils::FindAllUnattendedFeature();

    for (size_t i = 0; i < unattendedFeatureVec.size(); i++)
    {
        DBUnattendedFeature unattendedFeature = unattendedFeatureVec[i];
        QString localCreateTime = unattendedFeature.GetCustomId().c_str();
        bool isExistAI, isExistDcm;//存在AI部位识别和DICOM字段匹配
        isExistAI = isExistDcm = false;
        n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig;
        stUnattendedConfig.isEnable = (unattendedFeature.GetEnable() == 1 ? true : false);
        //
        //填充影像接收规则：ST_LocalServerRule
        //服务器类型curServerType(1:共享文件夹 2:FTP服务器 3:加速器log(预留*无用) 4:SCP服务器)
        QJsonObject jsonObject = qStringToqJsonObject(unattendedFeature.GetJsonValue().c_str());

        if (jsonObject.isEmpty())
            continue;

        stUnattendedConfig.stLocalServerRule.curServerType = unattendedFeature.GetServerType();
        stUnattendedConfig.stLocalServerRule.curServerName = jsonObject.value("server_name").toString();
        stUnattendedConfig.stLocalServerRule.imagefilterEnable = false;
        //filter_info
        QJsonObject filter_info = jsonObject.value("filter_info").toObject();

        if (filter_info.isEmpty() == false)
        {
            stUnattendedConfig.stLocalServerRule.imagefilterEnable = filter_info.value("is_enable").toString() == "1" ? true : false;
            stUnattendedConfig.stLocalServerRule.numfilterEnable = filter_info.value("imageNum_enable").toString() == "1" ? true : false;
            stUnattendedConfig.stLocalServerRule.numNotSketch = filter_info.value("image_num").toString().toInt();
            QJsonArray info_arr = filter_info.value("info_arr").toArray();

            for (int m = 0; m < info_arr.size(); m++)
            {
                QJsonObject obj = info_arr[m].toObject();
                QString tag = obj.value("tag").toString();
                QStringList valList = qVariantListToQStringList(obj.value("val_arr").toArray().toVariantList());

                if (tag.isEmpty() || valList.isEmpty())
                    continue;

                stUnattendedConfig.stLocalServerRule.imagefilterMap.insert(tag, valList);
            }
        }

        //填充勾画规则：ST_SketchRule
        QStringList sketchCustomIdList = qVariantListToQStringList(jsonObject.value("sketch_arr").toArray().toVariantList());

        if (sketchCustomIdList.isEmpty() == true)
            continue;

        std::vector<DBUnattendedRule> unattendedRuleVec = DBDataUtils::FindUnattendedRuleByCustomID(sketchCustomIdList);

        for (size_t m = 0; m < unattendedRuleVec.size(); m++)
        {
            DBUnattendedRule unattendedRule = unattendedRuleVec[m];
            QString createTime = unattendedRule.GetCustomId().c_str();
            QJsonObject obj = qStringToqJsonObject(unattendedRule.GetJsonValue().c_str());
            n_mtautodelineationdialog::ST_SketchIdentify stSketchIdentify;
            //
            //填充导出规则export_arr
            QStringList exportCustomIdList = qVariantListToQStringList(obj.value("export_arr").toArray().toVariantList());

            if (exportCustomIdList.isEmpty() == false)
            {
                std::vector<DBUnattendedRule> unattendedRuleVec = DBDataUtils::FindUnattendedRuleByCustomID(exportCustomIdList);

                for (size_t m = 0; m < unattendedRuleVec.size(); m++)
                {
                    DBUnattendedRule unattendedRule = unattendedRuleVec[m];
                    QString createTime = unattendedRule.GetCustomId().c_str();
                    QJsonObject exportObj = qStringToqJsonObject(unattendedRule.GetJsonValue().c_str());

                    if (exportObj.isEmpty())
                        continue;

                    //规则类型(1:AI部位识别勾画规则 2:DICOM字段匹配勾画规则 3:导出到远程服务器规则 4:导出到共享文件夹规则)
                    if (unattendedRule.GetRuleType() == 3) //导出到远程服务器
                    {
                        n_mtautodelineationdialog::ST_AddrSimple stAddr;
                        stAddr.addrType = 4; //导出地址类型-4: SCP服务器
                        stAddr.exportRange = exportObj.value("export_range").toString().toInt();
                        stAddr.stScpInfo.serverName = exportObj.value("export_serverName").toString();
                        stAddr.exportFormat = remoteScpUpperMap.contains(stAddr.stScpInfo.serverName.toUpper()) ? remoteScpUpperMap[stAddr.stScpInfo.serverName.toUpper()].exportFormat : "0";
                        stSketchIdentify.addrInfoMap.insert(createTime, stAddr);
                    }
                    else if (unattendedRule.GetRuleType() == 4)//导出到共享文件夹
                    {
                        n_mtautodelineationdialog::ST_AddrSimple stAddr;
                        stAddr.addrType = 1; //导出地址类型-1:共享文件夹
                        stAddr.stDirInfo.dirPath = exportObj.value("export_dir").toString();
                        stAddr.stDirInfo.mkSubType = exportObj.value("create_sharedir").toString() == "0" ? 0 : 1;
                        stAddr.exportRange = exportObj.value("export_range").toString().toInt();
                        stAddr.exportFormat = exportObj.value("export_format_guid").toString();
                        stSketchIdentify.addrInfoMap.insert(createTime, stAddr);
                    }
                }
            }

            //影像识别规则类型(1:AI部位识别勾画规则 2:DICOM字段匹配勾画规则 3:导出到远程服务器规则 4:导出到共享文件夹规则)
            if (unattendedRule.GetRuleType() == 1) //1:AI部位识别勾画规则
            {
                stSketchIdentify.recognitionType = 1;
                stSketchIdentify.sketchCollectionId = obj.value("modelcollectionId").toString().toInt();
                stSketchIdentify.modality = obj.value("modality").toString();
                stSketchIdentify.aiBodypart = obj.value("ai_bodypart").toString(); //111111 head chest chest_female abdomen prostate pelvis
                stUnattendedConfig.stSketchRule.sketchIdentifyMap.insert(createTime, stSketchIdentify);
            }
            else if (unattendedRule.GetRuleType() == 2) //2:DICOM字段匹配勾画规则
            {
                stSketchIdentify.recognitionType = 2;
                stSketchIdentify.sketchCollectionId = obj.value("modelcollectionId").toString().toInt();
                stSketchIdentify.modality = obj.value("modality").toString();
                stSketchIdentify.dcmTagSex = obj.value("sex").toString().toUpper();//M/F
                QJsonArray dcm_arr = obj.value("dcm_arr").toArray();

                for (int j = 0; j < dcm_arr.size(); j++)
                {
                    QJsonObject dcmObj = dcm_arr[j].toObject();
                    QString dcm_tag = dcmObj.value("dcm_tag").toString();
                    QVariantList dcm_val_arr = dcmObj.value("dcm_val_arr").toArray().toVariantList();

                    if (dcm_tag.isEmpty() == false && dcm_arr.isEmpty() == false)
                    {
                        stSketchIdentify.dcmTagMap[dcm_tag] = qVariantListToQStringList(dcm_val_arr);
                        stUnattendedConfig.stSketchRule.sketchIdentifyMap.insert(createTime, stSketchIdentify);
                    }
                }
            }
        }

        outUnattendedConfigMap.insert(localCreateTime, stUnattendedConfig);
    }
}

/// <summary>
/// 获取已经被无人值守使用的勾画模板id
/// </summary>
/// <returns>已经被无人值守使用的勾画模板id</returns>
QSet<int> AutoDelineationDataOpt::getUsedTemplateIdOfUnattended()
{
    QSet<int> usedTemplateIdSet;
    //检索unattendedfeature表获取所有无人值守配置
    std::vector<DBUnattendedFeature> unattendedFeatureVec = DBDataUtils::FindAllUnattendedFeature();

    for (size_t i = 0; i < unattendedFeatureVec.size(); i++)
    {
        DBUnattendedFeature unattendedFeature = unattendedFeatureVec[i];
        QString localCreateTime = unattendedFeature.GetCustomId().c_str();
        QJsonObject jsonObject = qStringToqJsonObject(unattendedFeature.GetJsonValue().c_str());
        //
        //sketch_arr
        QStringList sketchCustomIdList = qVariantListToQStringList(jsonObject.value("sketch_arr").toArray().toVariantList());

        if (sketchCustomIdList.isEmpty() == false)
        {
            std::vector<DBUnattendedRule> unattendedRuleVec = DBDataUtils::FindUnattendedRuleByCustomID(sketchCustomIdList);

            for (size_t m = 0; m < unattendedRuleVec.size(); m++)
            {
                DBUnattendedRule unattendedRule = unattendedRuleVec[m];
                QString createTime = unattendedRule.GetCustomId().c_str();
                QJsonObject obj = qStringToqJsonObject(unattendedRule.GetJsonValue().c_str());
                int modelcollectionId = obj.value("modelcollectionId").toString().toInt();
                usedTemplateIdSet.insert(modelcollectionId);
            }
        }
    }

    return usedTemplateIdSet;
}

/// <summary>
/// 获取所有的标签信息.
/// </summary>
/// <param name="allRoiLabelInfoList">[OUT}返回所有的标签信息.</param>
/// <param name="roiLibraryCfgVec">[IN}若该参数不为空，则直接从该参数中解析数据.</param>
void AutoDelineationDataOpt::getAllRoiLabelInfoList(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& allRoiLabelInfoList, std::vector<DBRoiLibraryConfig>& roiLibraryCfgVec)
{
    if (roiLibraryCfgVec.size() == 0)
    {
        roiLibraryCfgVec = DBDataUtils::FindAllROILibraryRecords();
    }

    QMap<QString, n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoMap;//按标签名称进行排序，所以添加临时缓存

    for (int i = 0; i < roiLibraryCfgVec.size(); ++i)
    {
        DBRoiLibraryConfig& cfgItem = roiLibraryCfgVec[i];
        n_mtautodelineationdialog::ST_RoiLabelInfo info;
        info.manteiaRoiLabel = cfgItem.GetRoiLabel().c_str();
        info.isbuiltIn = cfgItem.GetIsDefaultRoi() == 1;
        info.roiName = cfgItem.GetRoiName().c_str();
        info.roiChName = cfgItem.GetRoiChineseName().c_str();
        info.roiAlias = cfgItem.GetAliasList().c_str();
        info.roiColor = cfgItem.GetRoiColor().c_str();
        info.roiType = cfgItem.GetRoiType().c_str();
        QMap<QString, QString> codeInfoMap;
        QJsonParseError error;
        QJsonDocument jsonDoc = QJsonDocument::fromJson(QString::fromStdString(cfgItem.GetStructureCodeInfo()).toUtf8(), &error);

        if (!jsonDoc.isNull() && jsonDoc.isObject())
        {
            QJsonObject jsonObject = jsonDoc.object();
            QJsonObject eclipseObj = jsonObject.value("eclipse").toObject();

            if (!eclipseObj.isEmpty())
            {
                codeInfoMap["code"] = eclipseObj.value("code").toString();
                codeInfoMap["codeMean"] = eclipseObj.value("codeMean").toString();
                codeInfoMap["codeScheme"] = eclipseObj.value("codeScheme").toString();
                codeInfoMap["codeSchemeVer"] = eclipseObj.value("codeSchemeVer").toString();
                codeInfoMap["groupVer"] = eclipseObj.value("groupVer").toString();
                codeInfoMap["identifier"] = eclipseObj.value("identifier").toString();
                codeInfoMap["label"] = eclipseObj.value("label").toString();
                codeInfoMap["mapRes"] = eclipseObj.value("mapRes").toString();
                info.roiCodeMap.insert(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse, codeInfoMap);
            }
        }

        stRoiLabelInfoMap[info.manteiaRoiLabel.toLower()] = info;
    }

    for (const auto& item : stRoiLabelInfoMap)
    {
        allRoiLabelInfoList.push_back(item);
    }
}

/// <summary>
/// 获取当模板被无人值守使用时，是否显示提示框
/// </summary>
bool AutoDelineationDataOpt::getIsShowTipOfModUnattendUsed()
{
    QSettings settings(CommonUtil::GetClientConfigPath(), QSettings::IniFormat);
    QString val = settings.value("isShowTipOfModUnattendUsed", "1").toString();
    return (val == "1" ? true : false);
}

/// <summary>
/// 获取无人值守默认导出地址
/// </summary>
n_mtautodelineationdialog::ST_AddrSimple AutoDelineationDataOpt::getDefaultExportAddrUnattended()
{
    n_mtautodelineationdialog::ST_AddrSimple stDefautAddr;
    //获取所有远程导出地址
    QList<n_mtautodelineationdialog::ST_AddrSimple> allRemoteScp = AutoDelineationDataOpt::getAllRemoteScpAddrList();
    //
    std::vector<DBFeaturesConfig> configVec = DBDataUtils::FindFeaturesConfigByKey("unattend_default_exportAddr");

    if (configVec.empty() == false)
    {
        QJsonObject jsonObj = qStringToqJsonObject(configVec[0].GetFeatureValue().c_str());
        QString addrType = jsonObj.value("addr_type").toString();
        QString export_range = jsonObj.value("export_range").toString();

        if (addrType == "1") //共享文件夹
        {
            QString export_dir = jsonObj.value("export_dir").toString();
            QString create_sharedir = jsonObj.value("create_sharedir").toString();
            QString export_format_guid = jsonObj.value("export_format_guid").toString();
            stDefautAddr.addrType = 1;
            stDefautAddr.exportRange = export_range.toInt();
            stDefautAddr.exportFormat = export_format_guid;
            stDefautAddr.stDirInfo.dirPath = export_dir;
            stDefautAddr.stDirInfo.mkSubType = create_sharedir.toInt();
        }
        else if (addrType == "4") //Scp远程服务器
        {
            QString export_serverName = jsonObj.value("export_serverName").toString();

            for (int i = 0; i < allRemoteScp.size(); i++)
            {
                if (export_serverName == allRemoteScp[i].stScpInfo.serverName)
                {
                    stDefautAddr = allRemoteScp[i];
                    stDefautAddr.addrType = 4;
                    stDefautAddr.exportRange = export_range.toInt();
                    return stDefautAddr;
                }
            }
        }
    };

    return n_mtautodelineationdialog::ST_AddrSimple();
}

/// <summary>
/// 获取unattendedfeature表获取无人值守开关是否打开
/// </summary>
/// <returns>true打开</returns>
bool AutoDelineationDataOpt::getUnattendedEnable()
{
    int startNum = 0;
    std::vector<DBUnattendedFeature> unattendedFeatureVec = DBDataUtils::FindAllUnattendedFeature();

    for (size_t i = 0; i < unattendedFeatureVec.size(); i++)
    {
        DBUnattendedFeature unattendedFeature = unattendedFeatureVec[i];

        //只提取使能的无人值守规则
        if (unattendedFeature.GetEnable() == 1)
        {
            startNum++;
        }
    }

    return (startNum > 0 ? true : false);
}

/// <summary>
/// 更新自动导出到共享文件夹配置
/// </summary>
/// <param name="stAddrSimple">[IN]导出地址信息</param>
/// <returns>成功true</returns>
bool AutoDelineationDataOpt::updateExportDirConfigInfo(const n_mtautodelineationdialog::ST_AddrSimple& stAddrSimple)
{
    if (stAddrSimple.addrType == 1)
    {
        //获取导出到共享文件夹格式配置
        QString manualCreateShareDir, manualFormatGuid, autoCreateShareDir, autoFormatGuid;
        DBDataUtils::GetExportDirConfigInfoOfTableFeaturesConfig(manualCreateShareDir, manualFormatGuid, autoCreateShareDir, autoFormatGuid);
        DBDataUtils::UpdateExportDirConfigInfoOfTableFeaturesConfig(manualCreateShareDir, manualFormatGuid, QString::number(stAddrSimple.stDirInfo.mkSubType), stAddrSimple.exportFormat);
    }

    return true;
}

/// <summary>
/// 更新勾画模板id排序
/// </summary>
/// <param name="templateIdMap">[IN]勾画模板id</param>
/// <returns>成功true</returns>
bool AutoDelineationDataOpt::updateSketchTemplateIdSort(const QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>>& templateIdMap)
{
    //CT
    if (templateIdMap.contains(n_mtautodelineationdialog::OptDcmType_CT))
    {
        QList<int> templateIdList = templateIdMap[n_mtautodelineationdialog::OptDcmType_CT];
        QJsonArray jsonArray;

        for (int i = templateIdList.size() - 1; i >= 0; i--)
        {
            jsonArray.append(QString::number(templateIdList[i]));
        }

        QJsonDocument document(jsonArray);
        DBFeaturesConfig featuresConfig;
        featuresConfig.SetFeatureKey("sketch_model_order_ct");
        featuresConfig.SetFeatureValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
        featuresConfig.SetEnable(1);
        auto featureVec = DBDataUtils::FindFeaturesConfigByKey("sketch_model_order_ct");

        if (featureVec.empty() == true)
            return DBDataUtils::AddFeatureConfigInfo(featuresConfig);
        else
            return DBDataUtils::UpdateFeatureConfigInfo(featuresConfig);
    }

    //MR
    if (templateIdMap.contains(n_mtautodelineationdialog::OptDcmType_MR))
    {
        QList<int> templateIdList = templateIdMap[n_mtautodelineationdialog::OptDcmType_MR];
        QJsonArray jsonArray;

        for (int i = templateIdList.size() - 1; i >= 0; i--)
        {
            jsonArray.append(QString::number(templateIdList[i]));
        }

        QJsonDocument document(jsonArray);
        DBFeaturesConfig featuresConfig;
        featuresConfig.SetFeatureKey("sketch_model_order_mr");
        featuresConfig.SetFeatureValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
        featuresConfig.SetEnable(1);
        auto featureVec = DBDataUtils::FindFeaturesConfigByKey("sketch_model_order_mr");

        if (featureVec.empty() == true)
            return DBDataUtils::AddFeatureConfigInfo(featuresConfig);
        else
            return DBDataUtils::UpdateFeatureConfigInfo(featuresConfig);
    }

    return true;
}

/// <summary>
/// 更新模板信息
/// </summary>
/// <param name="optTypeEnum">[IN]操作类型</param>
/// <param name="stSketchCollection">[IN]模板信息</param>
/// <param name="newTemplateId">[IN]如果是新增则返回数据库modelcollection表id</param>
/// <returns>成功true</returns>
bool AutoDelineationDataOpt::updateSketchCollection(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const n_mtautodelineationdialog::ST_SketchModelCollection stSketchCollection, int& newTemplateId)
{
    if (optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Add || optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Mod)
    {
        QJsonArray organ_arr;

        for (QMap<int, QSet<int>>::const_iterator it = stSketchCollection.showGroupIdMap.begin(); it != stSketchCollection.showGroupIdMap.end(); it++)
        {
            int organId = it.key();
            QJsonArray show_groupId_arr, sub_in_main_group_arr;
            QSet<int> showIdSet = it.value();

            for (QSet<int>::iterator it_2 = showIdSet.begin(); it_2 != showIdSet.end(); it_2++)
            {
                show_groupId_arr.append(QString::number(*it_2));
            }

            //sub_in_main_group_arr
            if (stSketchCollection.subInMainGroupIdMap.contains(organId) == true)
            {
                QSet<int> mainGroupIdSet = stSketchCollection.subInMainGroupIdMap[organId];

                for (QSet<int>::iterator it_3 = mainGroupIdSet.begin(); it_3 != mainGroupIdSet.end(); it_3++)
                {
                    sub_in_main_group_arr.append(QString::number(*it_3));
                }
            }

            QJsonObject obj;
            obj.insert("id", QString::number(organId));
            obj.insert("show_groupId_arr", show_groupId_arr);
            obj.insert("sub_in_main_group_arr", sub_in_main_group_arr);
            organ_arr.append(obj);
        }

        QJsonObject modelDataObj;
        modelDataObj.insert("modality", stSketchCollection.modality.toUpper());
        modelDataObj.insert("organ_arr", organ_arr);
        //插入数据库
        DBSketchModelCollection dbInfo;
        dbInfo.SetEnable(1);
        dbInfo.SetTitle(stSketchCollection.templateName.toStdString());
        dbInfo.SetModelData(qJsonObjectToqString(modelDataObj).toStdString());
        dbInfo.SetRemark("");
        dbInfo.SetInsertTime(QDateTime::currentDateTime());
        dbInfo.SetIsUnattended(stSketchCollection.isUnattended == true ? 1 : 0);

        if (optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Add)
        {
            //插入数据库
            if (DBDataUtils::AddModelCollection(dbInfo) == false)
            {
                return false;
            }

            //获取模板id
            std::vector<DBSketchModelCollection> collVec = DBDataUtils::FindAllModelCollectionByName(stSketchCollection.templateName);

            if (collVec.empty() == true)
            {
                return false;
            }

            newTemplateId = collVec[0].GetId();
        }
        else
        {
            std::vector<DBSketchModelCollection> collVec = DBDataUtils::FindAllModelCollectionById(stSketchCollection.id);

            if (collVec.empty() == true)
            {
                DBDataUtils::AddModelCollection(dbInfo);
            }
            else
            {
                dbInfo.SetId(collVec[0].GetId());
                newTemplateId = collVec[0].GetId();
                DBDataUtils::UpdateModelCollection(dbInfo);
            }
        }
    }
    else if (optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Del)
    {
        DBSketchModelCollection dbInfo;
        dbInfo.SetId(stSketchCollection.id);
        DBDataUtils::DeleteModelCollection(dbInfo);
    }

    return true;
}

/// <summary>
/// 更新数据库DBSketchModelCollection表是否是无人值守字段isUnattended
/// </summary>
/// <param name="templateId">[IN]数据库modelcollection表id</param>
/// <param name="isUnattended">[IN]是否</param>
/// <returns>成功true</returns>
bool AutoDelineationDataOpt::updateSketchCollectionUnattended(const int templateId, const bool isUnattended)
{
    std::vector<DBSketchModelCollection> templateModelVec = DBDataUtils::FindAllModelCollectionById(templateId);

    if (templateModelVec.empty())
    {
        return false;
    }

    templateModelVec[0].SetIsUnattended(isUnattended ? 1 : 0);
    DBDataUtils::UpdateModelCollection(templateModelVec[0]);
    return true;
}


/// <summary>
/// 更新无人值守信息
/// </summary>
/// <param name="optTypeEnum">[IN]操作类型</param>
/// <param name="customId">[IN]unattendedfeature表customId</param>
/// <param name="stUnattendedConfig">[IN]无人值守信息</param>
/// <returns>成功true</returns>
bool AutoDelineationDataOpt::updateUnattemdedInfoToDB(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const QString customId, const n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig)
{
    struct timespec tmv1, tmv2;
    timespec_get(&tmv1, 1);
    //
    //检索unattendedfeature表获取对应无人值守下的规则记录
    QStringList exportCustomIdList, sketchCustomIdList;

    if (customId.isEmpty() == false)
    {
        std::vector<DBUnattendedFeature> unattendedFeatureVec = DBDataUtils::FindUnattendedFeatureByCustomID({ customId });

        for (size_t i = 0; i < unattendedFeatureVec.size(); i++)
        {
            DBUnattendedFeature unattendedFeature = unattendedFeatureVec[i]; //其实就一条记录
            QJsonObject jsonObject = qStringToqJsonObject(unattendedFeature.GetJsonValue().c_str());
            //sketch_arr
            sketchCustomIdList = qVariantListToQStringList(jsonObject.value("sketch_arr").toArray().toVariantList());
            //zlw 20240526 导出地址信息customId改为在勾画规则下
            std::vector<DBUnattendedRule> sketchRuleVec = DBDataUtils::FindUnattendedRuleByCustomID(sketchCustomIdList);

            //export_arr
            for (size_t m = 0; m < sketchRuleVec.size(); m++)
            {
                QJsonObject sketchJsonObj = qStringToqJsonObject(sketchRuleVec[m].GetJsonValue().c_str());
                exportCustomIdList.append(qVariantListToQStringList(sketchJsonObj.value("export_arr").toArray().toVariantList()));
            }
        }
    }

    //删除
    if (optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Del)
    {
        //删除unattendedfeature表
        QStringList customIdList;
        customIdList.reserve(20);
        customIdList.push_back(customId);
        DBDataUtils::DeleteUnattendedFeature(customIdList);
        //删除unattendedrule表
        DBDataUtils::DeleteUnattendedRule(exportCustomIdList);
        DBDataUtils::DeleteUnattendedRule(sketchCustomIdList);
        return true;
    }

    //新增或修改
    //***************************************** unattendedfeature表
    //先提取原数据库中unattendedrule的规则记录，先删除掉
    DBDataUtils::DeleteUnattendedRule(exportCustomIdList);
    DBDataUtils::DeleteUnattendedRule(sketchCustomIdList);
    //filter_info
    QJsonObject filter_info;
    filter_info.insert("imageNum_enable", stUnattendedConfig.stLocalServerRule.numfilterEnable == true ? "1" : "0");
    filter_info.insert("image_num", stUnattendedConfig.stLocalServerRule.numNotSketch < 1 ? "11" : QString::number(stUnattendedConfig.stLocalServerRule.numNotSketch));

    if (stUnattendedConfig.stLocalServerRule.imagefilterMap.contains("0008103E") &&
        stUnattendedConfig.stLocalServerRule.imagefilterMap["0008103E"].isEmpty() == false) //序列描述
    {
        QJsonArray val_arr;
        QStringList valList = stUnattendedConfig.stLocalServerRule.imagefilterMap["0008103E"];

        for (int i = 0; i < valList.size(); i++)
            val_arr.append(valList[i]);

        QJsonArray info_arr;
        info_arr.append(QJsonObject({ { "tag", "0008103E" }, { "val_arr", val_arr } }));
        filter_info.insert("is_enable", stUnattendedConfig.stLocalServerRule.imagefilterEnable == true ? "1" : "0");
        filter_info.insert("info_arr", info_arr);
    }

    //sketch_arr
    QJsonArray sketch_arr;

    for (QMap<QString/**createTime*/, n_mtautodelineationdialog::ST_SketchIdentify>::const_iterator it = stUnattendedConfig.stSketchRule.sketchIdentifyMap.begin();
         it != stUnattendedConfig.stSketchRule.sketchIdentifyMap.end(); it++)
    {
        sketch_arr.push_back(it.key());
    }

    QJsonObject jsonObject;
    jsonObject.insert("server_name", stUnattendedConfig.stLocalServerRule.curServerName);
    jsonObject.insert("filter_info", filter_info);
    jsonObject.insert("sketch_arr", sketch_arr);

    if (optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Add || optTypeEnum == n_mtautodelineationdialog::EM_OptType::OptType_Mod) //新增
    {
        std::vector<DBUnattendedFeature> unattendedFeatureVec = DBDataUtils::FindUnattendedFeatureByCustomID({ customId });

        if (unattendedFeatureVec.empty()) //新增
        {
            DBUnattendedFeature unattendedFeature;
            unattendedFeature.SetCustomId(customId.toStdString());
            unattendedFeature.SetServerType((EM_TableLocalServerType)stUnattendedConfig.stLocalServerRule.curServerType);
            unattendedFeature.SetEnable(0);
            QJsonDocument document(jsonObject);
            unattendedFeature.SetJsonValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
            DBDataUtils::AddUnattendedFeature(unattendedFeature);
        }
        else //修改
        {
            DBUnattendedFeature unattendedFeature = unattendedFeatureVec[0];
            unattendedFeature.SetServerType((EM_TableLocalServerType)stUnattendedConfig.stLocalServerRule.curServerType);
            unattendedFeature.SetEnable(0);
            QJsonDocument document(jsonObject);
            unattendedFeature.SetJsonValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
            DBDataUtils::UpdateUnattendedFeatureByCustomId(unattendedFeature);
        }
    }

    //***************************************************** unattendedrule表
    //导出规则
    for (QMap<QString/**createTime*/, n_mtautodelineationdialog::ST_SketchIdentify>::const_iterator it = stUnattendedConfig.stSketchRule.sketchIdentifyMap.begin();
         it != stUnattendedConfig.stSketchRule.sketchIdentifyMap.end(); it++)
    {
        n_mtautodelineationdialog::ST_SketchIdentify stSketchIdentify = it.value();
        QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> addrInfoMap = stSketchIdentify.addrInfoMap;

        for (QMap<QString/**createTime*/, n_mtautodelineationdialog::ST_AddrSimple>::iterator it_addr = addrInfoMap.begin(); it_addr != addrInfoMap.end(); it_addr++)
        {
            QString customId = it_addr.key();
            n_mtautodelineationdialog::ST_AddrSimple stAddr = it_addr.value();
            QJsonObject obj;

            if (it_addr.value().addrType == 1)     //共享文件夹
            {
                obj = QJsonObject(
                    {
                        {"export_range", QString::number(stAddr.exportRange)},
                        {"export_dir", stAddr.stDirInfo.dirPath},
                        {"create_sharedir", stAddr.stDirInfo.mkSubType == 0 ? "0" : "1"},
                        {"export_format_guid", stAddr.exportFormat}
                    });
            }
            else if (it_addr.value().addrType == 4) //SCP服务器
            {
                obj = QJsonObject(
                    {
                        {"export_range", QString::number(stAddr.exportRange)},
                        {"export_serverName", stAddr.stScpInfo.serverName}
                    });
            }
            else
                continue;

            //规则类型RuleType(1:AI部位识别勾画规则 2:DICOM字段匹配勾画规则 3:导出到远程服务器规则 4:导出到共享文件夹规则)
            std::vector<DBUnattendedRule> unattendedRuleVec = DBDataUtils::FindUnattendedRuleByCustomID({ customId });

            if (unattendedRuleVec.empty()) //新增
            {
                DBUnattendedRule unattendedRule;
                EM_TableUnattendedRuleType serverType;
                if (1 == it_addr.value().addrType)
                {
                    serverType = (EM_TableUnattendedRuleType)4;
                }
                else
                {
                    serverType = (EM_TableUnattendedRuleType)3;
                }
                unattendedRule.SetCustomId(customId.toStdString());
                unattendedRule.SetRuleType(serverType);
                unattendedRule.SetEnable(1);
                QJsonDocument document(obj);
                unattendedRule.SetJsonValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
                DBDataUtils::AddUnattendedRule(unattendedRule);
            }
            else //修改
            {
                DBUnattendedRule unattendedRule = unattendedRuleVec[0];
                EM_TableUnattendedRuleType serverType;
                if (1 == it_addr.value().addrType)
                {
                    serverType = (EM_TableUnattendedRuleType)4;
                }
                else
                {
                    serverType = (EM_TableUnattendedRuleType)3;
                }
                unattendedRule.SetRuleType(serverType);
                unattendedRule.SetEnable(1);
                QJsonDocument document(obj);
                unattendedRule.SetJsonValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
                DBDataUtils::UpdateUnattendedRuleByCustomId(unattendedRule);
            }
        }
    }

    //勾画规则
    for (QMap<QString/**createTime*/, n_mtautodelineationdialog::ST_SketchIdentify>::const_iterator it = stUnattendedConfig.stSketchRule.sketchIdentifyMap.begin();
         it != stUnattendedConfig.stSketchRule.sketchIdentifyMap.end(); it++)
    {
        QJsonObject obj;
        QString customId = it.key();
        n_mtautodelineationdialog::ST_SketchIdentify stSketchIdentify = it.value();
        //export_arr
        QJsonArray export_arr;
        QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> addrInfoMap = stSketchIdentify.addrInfoMap;

        for (QMap<QString/**createTime*/, n_mtautodelineationdialog::ST_AddrSimple>::const_iterator it_addr = addrInfoMap.begin(); it_addr != addrInfoMap.end(); it_addr++)
        {
            export_arr.append(it_addr.key());
        }

        if (stSketchIdentify.recognitionType == 1) //AI部位识别
        {
            obj.insert("modality", stSketchIdentify.modality.toUpper());
            obj.insert("modelcollectionId", QString::number(stSketchIdentify.sketchCollectionId));
            obj.insert("ai_bodypart", stSketchIdentify.aiBodypart);
            obj.insert("export_arr", export_arr);
        }
        else if (stSketchIdentify.recognitionType == 2 && stSketchIdentify.dcmTagMap.isEmpty() == false && stSketchIdentify.dcmTagMap.begin().value().isEmpty() == false)//DICOM字段识别
        {
            QJsonArray dcm_arr, dcm_val_arr;
            QStringList valList = stSketchIdentify.dcmTagMap.begin().value();

            for (int i = 0; i < valList.size(); i++)
            {
                dcm_val_arr.push_back(valList[i]);
            }

            dcm_arr.append(QJsonObject({ {"dcm_tag", stSketchIdentify.dcmTagMap.begin().key()}, {"dcm_val_arr", dcm_val_arr} }));
            obj.insert("modality", stSketchIdentify.modality.toUpper());
            obj.insert("modelcollectionId", QString::number(stSketchIdentify.sketchCollectionId));
            obj.insert("sex", stSketchIdentify.dcmTagSex.toUpper());
            obj.insert("dcm_arr", dcm_arr);
            obj.insert("export_arr", export_arr);
        }
        else
            continue;

        //规则类型RuleType(1:AI部位识别勾画规则 2:DICOM字段匹配勾画规则 3:导出到远程服务器规则 4:导出到共享文件夹规则)
        std::vector<DBUnattendedRule> unattendedRuleVec = DBDataUtils::FindUnattendedRuleByCustomID({ customId });

        if (unattendedRuleVec.empty()) //新增
        {
            DBUnattendedRule unattendedRule;
            unattendedRule.SetCustomId(customId.toStdString());
            unattendedRule.SetRuleType(stSketchIdentify.recognitionType == 1 ? (EM_TableUnattendedRuleType)1 : (EM_TableUnattendedRuleType)2);
            unattendedRule.SetEnable(1);
            QJsonDocument document(obj);
            unattendedRule.SetJsonValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
            DBDataUtils::AddUnattendedRule(unattendedRule);
        }
        else //修改
        {
            DBUnattendedRule unattendedRule = unattendedRuleVec[0];
            unattendedRule.SetRuleType(stSketchIdentify.recognitionType == 1 ? (EM_TableUnattendedRuleType)1 : (EM_TableUnattendedRuleType)2);
            unattendedRule.SetEnable(1);
            QJsonDocument document(obj);
            unattendedRule.SetJsonValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());
            DBDataUtils::UpdateUnattendedRuleByCustomId(unattendedRule);
        }
    }

    timespec_get(&tmv2, 1);
    printf("updateUnattemdedInfoToDB time: %lld\n", tmv2.tv_sec - tmv1.tv_sec);
    return true;
}

/// <summary>
/// 更新数据库DBFeaturesConfig表unattended_rule_config字段开关
/// </summary>
/// <param name="unattendedEnable">[IN]true使能</param>
void AutoDelineationDataOpt::updateEnableOfUnattendedDB(const bool unattendedEnable)
{
    std::vector<DBFeaturesConfig> featuresConfigVec = DBDataUtils::FindFeaturesConfigByKey("unattended_rule_config");

    if (featuresConfigVec.empty() == false)
    {
        //重新写入数据库
        DBFeaturesConfig newFeaturesConfig = featuresConfigVec[0];
        newFeaturesConfig.SetEnable(unattendedEnable == true ? 1 : 0); //总开关状态
        newFeaturesConfig.SetFeatureValue("{}");
        DBDataUtils::UpdateFeatureConfigInfo(newFeaturesConfig);
    }
    else
    {
        DBFeaturesConfig newFeaturesConfig;
        newFeaturesConfig.SetEnable(unattendedEnable == true ? 1 : 0); //总开关状态
        newFeaturesConfig.SetFeatureValue("{}");
        newFeaturesConfig.SetFeatureKey("unattended_rule_config");
        DBDataUtils::AddFeatureConfigInfo(newFeaturesConfig);
    }

    //Todo: 将无人值守开关状态同步到其他客户端
}

/// <summary>
/// 更新数据库unattendedfeature表enable字段
/// </summary>
/// <param name="customId">[IN]unattendedfeature表customId</param>
/// <param name="ruleEnable">[IN]true使能</param>
void AutoDelineationDataOpt::updateEnableOfUnattendedfeatureDB(const QString customId, const bool ruleEnable)
{
    std::vector<DBUnattendedFeature> unattendedFeatureVec = DBDataUtils::FindUnattendedFeatureByCustomID({ customId });

    if (unattendedFeatureVec.empty() == false)
    {
        unattendedFeatureVec[0].SetEnable(ruleEnable == true ? 1 : 0);
        DBDataUtils::UpdateUnattendedFeature(unattendedFeatureVec[0]);
    }

    //Todo: 将无人值守开关状态同步到其他客户端
}

/// <summary>
/// 更新数据库unattendedfeature表unattend_default_exportAddr字段
/// </summary>
void AutoDelineationDataOpt::updateUnattendDefaultExportAddrDB(const n_mtautodelineationdialog::ST_AddrSimple& stAddr)
{
    QJsonObject jsonObj;
    jsonObj.insert("addr_type", QString::number(stAddr.addrType));
    jsonObj.insert("export_range", QString::number(stAddr.exportRange));

    if (stAddr.addrType == 1) //共享文件夹
    {
        jsonObj.insert("export_format_guid", stAddr.exportFormat);
        jsonObj.insert("export_dir", stAddr.stDirInfo.dirPath);
        jsonObj.insert("create_sharedir", QString::number(stAddr.stDirInfo.mkSubType));
    }
    else if (stAddr.addrType == 4) //远程Scp服务器
    {
        jsonObj.insert("export_serverName", stAddr.stScpInfo.serverName);
    }

    std::vector<DBFeaturesConfig> featuresConfigVec = DBDataUtils::FindFeaturesConfigByKey("unattend_default_exportAddr");

    if (featuresConfigVec.empty() == false)
    {
        //重新写入数据库
        DBFeaturesConfig newFeaturesConfig = featuresConfigVec[0];
        newFeaturesConfig.SetEnable(1);
        newFeaturesConfig.SetFeatureValue(qJsonObjectToqString(jsonObj).toStdString());
        DBDataUtils::UpdateFeatureConfigInfo(newFeaturesConfig);
    }
    else
    {
        DBFeaturesConfig newFeaturesConfig;
        newFeaturesConfig.SetEnable(1);
        newFeaturesConfig.SetFeatureValue(qJsonObjectToqString(jsonObj).toStdString());
        newFeaturesConfig.SetFeatureKey("unattend_default_exportAddr");
        DBDataUtils::AddFeatureConfigInfo(newFeaturesConfig);
    }
}

void AutoDelineationDataOpt::updateOrganInfo2DB(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList)
{
    for (int i = 0; i < organInfoList.size(); ++i)
    {
        DBOrganInfoConfig obj;
        QStringList groupStrList;
        const auto& organInfo = organInfoList[i];

        if (organInfo.optTypeEnum != n_mtautodelineationdialog::OptType_Add && organInfo.optTypeEnum != n_mtautodelineationdialog::OptType_Mod)
        {
            continue;
        }

        QList<int> gIdList = organInfo.organGroupInfoMap.keys();

        for each(int id in gIdList)
        {
            groupStrList << QString::number(id);
        }

        obj.SetBodypart(organInfo.bodypart.toStdString());
        obj.SetCustomColor(organInfo.customColor.toStdString());
        obj.SetCustomOrganName(organInfo.customOrganName.toStdString());
        obj.SetDefaultColor(organInfo.defaultColor.toStdString());
        obj.SetDefaultOrganName(organInfo.defaultOrganName.toStdString());
        obj.SetId(organInfo.id);
        obj.SetModelid(organInfo.modelId);
        obj.SetOrganChineseName(organInfo.organChineseName.toStdString());
        obj.SetOrganEnglishName(organInfo.organEnglishName.toStdString());
        obj.SetOrgangroupinfoIdList(groupStrList.join(";").toStdString());
        obj.SetRoiDesc(organInfo.roiDesc.toStdString());
        obj.SetRoiLabel(organInfo.roiLabel.toStdString());
        obj.SetRoiParam(organInfo.roiParam.toStdString());
        obj.SetRoiType(organInfo.roiType.toStdString());

        if (organInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Mod)
        {
            DBDataUtils::UpdateOrganInfoConfigByDefaultOrganNameAndModelId(obj);
        }
        else
        {
            DBDataUtils::AddOrganInfoConfig(obj);
        }
    }
}

void AutoDelineationDataOpt::updateRoiLabelInfo2DB(const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& roiLabelInfoList)
{
    std::vector<DBRoiLibraryConfig>  roiLibraryCfgVec = DBDataUtils::FindAllROILibraryRecords();

    for (int r = 0; r < roiLabelInfoList.size(); ++r)
    {
        const auto& roiLabelInfo = roiLabelInfoList[r];

        if (roiLabelInfo.optTypeEnum != n_mtautodelineationdialog::OptType_Mod && roiLabelInfo.optTypeEnum != n_mtautodelineationdialog::OptType_Add)
        {
            continue;
        }

        DBRoiLibraryConfig cfgItem;

        for (int i = 0; i < roiLibraryCfgVec.size(); ++i)
        {
            if (roiLibraryCfgVec[i].GetRoiLabel() == roiLabelInfo.manteiaRoiLabel.toStdString())
            {
                cfgItem = roiLibraryCfgVec[i];
                break;
            }
        }

        cfgItem.SetRoiLabel(roiLabelInfo.manteiaRoiLabel.toStdString());
        cfgItem.SetIsDefaultRoi(roiLabelInfo.isbuiltIn ? 1 : 0);
        cfgItem.SetRoiColor(roiLabelInfo.roiColor.toStdString());
        cfgItem.SetRoiName(roiLabelInfo.roiName.toStdString());
        cfgItem.SetAliasList(roiLabelInfo.roiAlias.toStdString());
        cfgItem.SetRoiType(roiLabelInfo.roiType.toStdString());

        if (roiLabelInfo.roiCodeMap.contains(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse))
        {
            QJsonObject jsonObject;
            QJsonObject eclipseObj;
            const QMap<QString, QString>& codeInfoMap = roiLabelInfo.roiCodeMap.value(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse);
            eclipseObj.insert("code", codeInfoMap.value("code"));
            eclipseObj.insert("codeMean", codeInfoMap.value("codeMean"));
            eclipseObj.insert("codeScheme", codeInfoMap.value("codeScheme"));
            eclipseObj.insert("codeSchemeVer", codeInfoMap.value("codeSchemeVer"));
            eclipseObj.insert("groupVer", codeInfoMap.value("groupVer"));
            eclipseObj.insert("identifier", codeInfoMap.value("identifier"));
            eclipseObj.insert("label", codeInfoMap.value("label"));
            eclipseObj.insert("mapRes", codeInfoMap.value("mapRes"));
            jsonObject.insert("eclipse", eclipseObj);
            QJsonDocument jsonDocument(jsonObject);
            QString jsonString = jsonDocument.toJson(QJsonDocument::Compact);
            cfgItem.SetStructureCodeInfo(jsonString.toStdString());
        }

        if (roiLabelInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Add)
        {
            cfgItem.SetIsDefaultRoi(0);//新增的设置为非默认标签
            DBDataUtils::AddROILibraryConfig(cfgItem);
        }
        else
        {
            DBDataUtils::UpdateROILibraryConfig(roiLabelInfo.manteiaRoiLabel, cfgItem);
        }
    }
}

/// <summary>
/// 设置当模板被无人值守使用时，是否显示提示框
/// </summary>
void AutoDelineationDataOpt::setIsShowTipOfModUnattendUsed(const bool isShow)
{
    QSettings settings(CommonUtil::GetClientConfigPath(), QSettings::IniFormat);
    settings.setValue("isShowTipOfModUnattendUsed", isShow == true ? "1" : "0");;
}


/**************************************************JSON转换**************************************************/
/// <summary>
/// 自动勾画信息转QJsonObject
/// </summary>
/// <param name="seriesUID">[IN]图像seriesUID</param>
/// <param name="sketchCollection">[IN]待勾画的模板信息</param>
/// <param name="checkedMergeRt">[IN]是否选择了合并已有rtStruct选项</param>
/// <param name="mergeRtSopInsUID">[IN]合并已有rtStruct-sopInsUID</param>
/// <param name="checkedExport">[IN]是否选择了勾画完导出选项</param>
/// <param name="stAddrSimple">[IN]导出地址简易信息</param>
ST_SketchBusinessInfo AutoDelineationDataOpt::ToStructOfAutoSketch(const QString& seriesUID
                                                                   , const n_mtautodelineationdialog::ST_SketchModelCollection& sketchCollection
                                                                   , const bool checkedMergeRt
                                                                   , const QString mergeRtSopInsUID, const bool checkedExport
                                                                   , const n_mtautodelineationdialog::ST_AddrSimple& stAddrSimple)
{
    ST_SketchBusinessInfo param;
    param.seriesUID = seriesUID.toStdString();
    //param.sketch_type = "1"; //现在是按模板走,故都传1
    //param.posture = "1"; //默认仰卧位
    param.auto_export = "0";
    // 设置业务类型-----新增
    //param.bussinesstype = "patientsketch";

    //导出信息
    if (checkedExport == true)
    {
        param.auto_export = "1";
        param.export_info.export_type = "1";
        param.export_info.export_range = QString::number(stAddrSimple.exportRange).toStdString();

        if (stAddrSimple.addrType == 1) //共享文件夹
        {
            std::vector<ST_SketchBusiness_ExportDir> export_dir_arr;
            ST_SketchBusiness_ExportDir exportDirItem;
            exportDirItem.create_sharedir = QString::number(stAddrSimple.stDirInfo.mkSubType).toStdString();
            exportDirItem.export_dir = stAddrSimple.stDirInfo.dirPath.toStdString();
            exportDirItem.export_format_guid = stAddrSimple.exportFormat.toStdString();
            export_dir_arr.push_back(exportDirItem);
            //
            param.export_info.export_dir_arr = export_dir_arr;
        }
        else if (stAddrSimple.addrType == 4) //远程SCP服务器
        {
            std::vector<ST_SketchBusiness_ExportServer> export_server_arr;
            std::vector<DBAEServerInfo> aeServerInfoVec = DBDataUtils::GetAeServerInfoByServerName(0, stAddrSimple.stScpInfo.serverName);

            if (aeServerInfoVec.empty() == false)
            {
                ST_SketchBusiness_ExportServer exportServerItem;
                exportServerItem.export_aeTitle = aeServerInfoVec[0].GetAeTitle();
                exportServerItem.export_format_guid = stAddrSimple.exportFormat.toStdString();
                exportServerItem.export_ip = aeServerInfoVec[0].GetIp();
                exportServerItem.export_port = QString::number(aeServerInfoVec[0].GetScpPort()).toStdString();
                exportServerItem.export_serverName = stAddrSimple.stScpInfo.serverName.toStdString();
                export_server_arr.push_back(exportServerItem);
                param.export_info.export_server_arr = export_server_arr;
            }
        }
    }

    //筛选需要勾画的器官
    QMap<int, DBOrganInfoConfig> dbOrganMap = DBDataUtils::GetOrganInfoConfigMap();
    QMap<int/*modelId*/, QList<DBOrganInfoConfig>> needOrganMap;

    for (QMap<int/*organId*/, QSet<int>>::const_iterator it = sketchCollection.showGroupIdMap.begin(); it != sketchCollection.showGroupIdMap.end(); it++)
    {
        int organId = it.key();
        QSet<int> collection_groupIdSet = it.value();

        if (dbOrganMap.contains(organId) == true)
        {
            DBOrganInfoConfig info = dbOrganMap[organId];
            int modelId = info.GetModelid();
            //
            //20240422 zlw加一层防护，防止分组和实际器官对不上(比如roi库更新了分组，但是模板里面因为某种原因没跟新该器官分组)，导致的勾画器官数量对不上的问题
            //判断是否是子集
            bool isNormal = false;
            QStringList groupIdList = QString::fromStdString(info.GetOrgangroupinfoIdList()).split(";", QString::SplitBehavior::SkipEmptyParts);

            for (int m = 0; m < groupIdList.size(); m++)
            {
                int groupId = groupIdList[m].toInt();

                if (collection_groupIdSet.contains(groupId) == true)
                {
                    isNormal = true;
                    break;
                }
            }

            if (isNormal == false)
                continue;

            needOrganMap[modelId].push_back(info);
        }
    }

    //accucontour_sketch
    std::vector<ST_SketchBusiness_AutoSketch> accucontour_sketch;

    if (needOrganMap.contains(0) == true)
    {
        ST_SketchBusiness_AutoSketch autoSketchItem;
        std::vector<ST_SketchBusiness_SketchOrgan> sketch_organ;
        const QList<DBOrganInfoConfig>& infoVec = needOrganMap[0];

        for (int i = 0; i < infoVec.size(); i++)
        {
            const DBOrganInfoConfig& info = infoVec[i];
            ST_SketchBusiness_SketchOrgan organItem;
            ST_SketchBusiness_OrganCustomInfo custom_info;

            if (info.GetDefaultOrganName() != info.GetCustomOrganName() || info.GetDefaultColor() != info.GetCustomColor() || info.GetRoiType() != "ORGAN")
            {
                custom_info.customOrganName = info.GetCustomOrganName();
                custom_info.customColor = info.GetCustomColor();
                custom_info.roiType = info.GetRoiType();
            }

            organItem.custom_info = custom_info;
            organItem.defaultOrganName = info.GetDefaultOrganName();
            sketch_organ.push_back(organItem);
        }

        autoSketchItem.hide_organ = "";
        autoSketchItem.sketch_part = "";
        autoSketchItem.sketch_organ = sketch_organ;
        accucontour_sketch.push_back(autoSketchItem);
    }

    param.accucontour_sketch = accucontour_sketch;
    //
    //add_empty_organ
    std::vector<ST_SketchBusiness_AddEmptyOrgan> add_empty_organ;

    if (needOrganMap.contains(-1) == true)
    {
        QJsonArray sketch_organ;
        QList<DBOrganInfoConfig> infoVec = needOrganMap[-1];

        for (int i = 0; i < infoVec.size(); i++)
        {
            ST_SketchBusiness_AddEmptyOrgan emptyOrganItem;
            DBOrganInfoConfig info = infoVec[i];
            ST_SketchBusiness_OrganCustomInfo custom_info;

            if (info.GetDefaultOrganName() != info.GetCustomOrganName() || info.GetDefaultColor() != info.GetCustomColor() || info.GetRoiType() != "ORGAN")
            {
                custom_info.customOrganName = info.GetCustomOrganName();
                custom_info.customColor = info.GetCustomColor();
                custom_info.roiType = info.GetRoiType();
            }

            emptyOrganItem.defaultColor = info.GetDefaultColor();
            emptyOrganItem.defaultOrganName = info.GetDefaultOrganName();
            emptyOrganItem.roiType = info.GetRoiType();
            emptyOrganItem.custom_info = custom_info;
            add_empty_organ.push_back(emptyOrganItem);
        }
    }

    param.add_empty_organ = add_empty_organ;
    //
    //train_sketch
    std::vector<ST_SketchBusiness_TrainSketch> train_sketch;
    QMap<int/*modelId*/, DBAiModelInfo> aiModelInfoMap = DBDataUtils::GetAiModelInfoMap();

    for (QMap<int/*modelId*/, DBAiModelInfo>::iterator it = aiModelInfoMap.begin(); it != aiModelInfoMap.end(); it++)
    {
        int modelId = it.key();
        ST_SketchBusiness_TrainSketch trainOrganItem;

        if (needOrganMap.contains(modelId) == true)
        {
            std::vector<ST_SketchBusiness_SketchOrgan> sketch_organ;
            QList<DBOrganInfoConfig> infoVec = needOrganMap[modelId];

            for (int i = 0; i < infoVec.size(); i++)
            {
                DBOrganInfoConfig info = infoVec[i];
                ST_SketchBusiness_OrganCustomInfo custom_info;
                ST_SketchBusiness_SketchOrgan sketchOrganItem;

                if (info.GetDefaultOrganName() != info.GetCustomOrganName() || info.GetDefaultColor() != info.GetCustomColor() || info.GetRoiType() != "ORGAN")
                {
                    custom_info.customOrganName = info.GetCustomOrganName();
                    custom_info.customColor = info.GetCustomColor();
                    custom_info.roiType = info.GetRoiType();
                }

                sketchOrganItem.defaultOrganName = info.GetDefaultOrganName();
                sketchOrganItem.custom_info = custom_info;
                sketch_organ.push_back(sketchOrganItem);
            }

            QString trainModelOrganList;
            QJsonObject jsonInfoObj = CommonUtil::qStringToqJsonObject(it.value().GetInfoJson().c_str());

            if (!jsonInfoObj.isEmpty() && jsonInfoObj.contains("organs"));
            {
                QStringList strListTemp;
                QJsonArray organArr = jsonInfoObj.value("organs").toArray();

                for (int i = 0; i < organArr.size(); ++i)
                {
                    strListTemp.append(organArr[i].toString());
                }

                trainModelOrganList = strListTemp.join(";");
            }

            trainOrganItem.model_name = it.value().GetModelName();
            trainOrganItem.model_organ = trainModelOrganList.toStdString();
            trainOrganItem.sketch_organ = sketch_organ;
            train_sketch.push_back(trainOrganItem);
        }
    }

    param.train_sketch = train_sketch;

    //merge_rt_sopInsUID_arr
    if (checkedMergeRt == true)
    {
        std::vector<std::string> merge_rt_sopInsUID_arr;
        merge_rt_sopInsUID_arr.push_back(mergeRtSopInsUID.toStdString());
        param.merge_rt_sopInsUID_arr = merge_rt_sopInsUID_arr;
    }

    return param;
}

bool AutoDelineationDataOpt::GetSketchOrganInfo(const n_mtautodelineationdialog::ST_SketchModelCollection& sketchCollection,
                                                std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec,
                                                std::vector<ST_REQ_AutoSketchSuper_Organ>& trainOrganVec,
                                                std::vector<ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec)
{
    //筛选需要勾画的器官
    QMap<int, DBOrganInfoConfig> dbOrganMap = DBDataUtils::GetOrganInfoConfigMap();
    QMap<int/*modelId*/, QList<DBOrganInfoConfig>> needOrganMap;

    for (QMap<int/*organId*/, QSet<int>>::const_iterator it = sketchCollection.showGroupIdMap.begin(); it != sketchCollection.showGroupIdMap.end(); it++)
    {
        int organId = it.key();
        QSet<int> collection_groupIdSet = it.value();

        if (dbOrganMap.contains(organId) == true)
        {
            DBOrganInfoConfig info = dbOrganMap[organId];
            int modelId = info.GetModelid();
            //
            //20240422 zlw加一层防护，防止分组和实际器官对不上(比如roi库更新了分组，但是模板里面因为某种原因没跟新该器官分组)，导致的勾画器官数量对不上的问题
            //判断是否是子集
            bool isNormal = false;
            QStringList groupIdList = QString::fromStdString(info.GetOrgangroupinfoIdList()).split(";", QString::SplitBehavior::SkipEmptyParts);

            for (int m = 0; m < groupIdList.size(); m++)
            {
                int groupId = groupIdList[m].toInt();

                if (collection_groupIdSet.contains(groupId) == true)
                {
                    isNormal = true;
                    break;
                }
            }

            if (isNormal == false)
                continue;

            needOrganMap[modelId].push_back(info);
        }
    }

    //accucontour_sketch
    if (needOrganMap.contains(0) == true)
    {
        QJsonArray sketch_organ;
        QList<DBOrganInfoConfig> infoVec = needOrganMap[0];

        for (int i = 0; i < infoVec.size(); i++)
        {
            DBOrganInfoConfig& info = infoVec[i];
            ST_REQ_AutoSketchSuper_Organ sketchOrgan;
            sketchOrgan.customOrganName = info.GetCustomOrganName();
            sketchOrgan.customColor = info.GetCustomColor();
            sketchOrgan.roiType = info.GetRoiType();
            sketchOrgan.defaultOrganName = info.GetDefaultOrganName();
            sketchOrgan.isVisiable = info.GetIsVisiable();
            sketchOrgan.roiLabel = info.GetRoiLabel();
            sketchOrgan.roiParam = info.GetRoiParam();
            sketchOrganVec.push_back(sketchOrgan);
        }
    }

    //
    //add_empty_organ
    if (needOrganMap.contains(-1) == true)
    {
        QJsonArray sketch_organ;
        QList<DBOrganInfoConfig> infoVec = needOrganMap[-1];

        for (int i = 0; i < infoVec.size(); i++)
        {
            DBOrganInfoConfig& info = infoVec[i];
            ST_REQ_AutoSketchSuper_Organ sketchOrgan;
            sketchOrgan.customOrganName = info.GetCustomOrganName();
            sketchOrgan.customColor = info.GetCustomColor();
            sketchOrgan.roiType = info.GetRoiType();
            sketchOrgan.defaultOrganName = info.GetDefaultOrganName();
            sketchOrgan.isVisiable = info.GetIsVisiable();
            sketchOrgan.roiLabel = info.GetRoiLabel();
            sketchOrgan.roiParam = info.GetRoiParam();
            emptyOrganVec.push_back(sketchOrgan);
        }
    }

    //
    //train_sketch
    QJsonArray train_sketch;
    QMap<int/*modelId*/, DBAiModelInfo> aiModelInfoMap = DBDataUtils::GetAiModelInfoMap();

    for (QMap<int/*modelId*/, DBAiModelInfo>::iterator it = aiModelInfoMap.begin(); it != aiModelInfoMap.end(); it++)
    {
        int modelId = it.key();

        if (needOrganMap.contains(modelId) == true)
        {
            QJsonArray sketch_organ;
            QList<DBOrganInfoConfig> infoVec = needOrganMap[modelId];

            for (int i = 0; i < infoVec.size(); i++)
            {
                DBOrganInfoConfig& info = infoVec[i];
                ST_REQ_AutoSketchSuper_Organ sketchOrgan;
                sketchOrgan.customOrganName = info.GetCustomOrganName();
                sketchOrgan.customColor = info.GetCustomColor();
                sketchOrgan.roiType = info.GetRoiType();
                sketchOrgan.defaultOrganName = info.GetDefaultOrganName();
                sketchOrgan.isVisiable = info.GetIsVisiable();
                sketchOrgan.roiLabel = info.GetRoiLabel();
                sketchOrgan.roiParam = info.GetRoiParam();
                trainOrganVec.push_back(sketchOrgan);
            }
        }
    }

    return true;
}

