﻿#include "AccuComponentUi\Header\QMTTextWidget.h"
#include "ui_QMTTextWidget.h"
#include "CMtCoreDefine.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"

QMTTextWidget::QMTTextWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTTextWidget;
    ui->setupUi(this);
    _backStyle = "background-color: rgba(115,145,185,1);";
    SetLabelProperty(QssPropertyLabelSecondTitle);
}

QMTTextWidget::~QMTTextWidget()
{
    MT_DELETE(ui);
}

void QMTTextWidget::SetText(QString& text)
{
    ui->label_text->setTextElided(text);
}

void QMTTextWidget::ClearColor()
{
    ui->widget_background->setStyleSheet("");
}

void QMTTextWidget::SetColor(QColor& backColor, QColor& borderColor)
{
    QString backColorStr = QString("%1, %2, %3").arg(backColor.red()).arg(backColor.green()).arg(backColor.blue());
    QString borderColorStr = QString("%1, %2, %3").arg(borderColor.red()).arg(borderColor.green()).arg(borderColor.blue());
    QString widgetStyle = QString("#widget_background{background-color:rgb(%1);border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:1px;border-right-width:1px; border-color:rgb(%2);}").arg(backColorStr).arg(borderColorStr);
    ui->widget_background->setStyleSheet(widgetStyle);
}

void QMTTextWidget::SetLabelProperty(QString value)
{
    ui->label_text->setProperty(QssPropertyKey, value);
}
