<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LabelLibraryWidgetClass</class>
 <widget class="QWidget" name="LabelLibraryWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>956</width>
    <height>434</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>LabelLibraryWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>12</number>
       </property>
       <property name="bottomMargin">
        <number>8</number>
       </property>
       <item>
        <widget class="MtLineEdit" name="mtLineEdit">
         <property name="minimumSize">
          <size>
           <width>446</width>
           <height>26</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>446</width>
           <height>26</height>
          </size>
         </property>
         <property name="maxLength">
          <number>64</number>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLineEdit::lineedit1</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtToolButton" name="mtToolButton_flush">
         <property name="text">
          <string/>
         </property>
         <property name="iconSize">
          <size>
           <width>22</width>
           <height>22</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtToolButton::toolbutton1</enum>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_2">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </item>
     <item>
      <widget class="LabelLibraryTable" name="widget_table" native="true"/>
     </item>
     <item>
      <widget class="MtFrameEx" name="mtFrameEx_line">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>1</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>1</height>
        </size>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::frameEx3</enum>
       </property>
       <property name="mtType" stdset="0">
        <string notr="true">frameEx3</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="MtFrameEx" name="mtFrameEx">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>33</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>33</height>
        </size>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::frameEx3_30</enum>
       </property>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1,1,3,0,3,3">
        <property name="spacing">
         <number>16</number>
        </property>
        <property name="leftMargin">
         <number>16</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>16</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtToolButton" name="mtToolButton_add">
          <property name="minimumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="text">
           <string>...</string>
          </property>
          <property name="iconSize">
           <size>
            <width>22</width>
            <height>22</height>
           </size>
          </property>
          <property name="toolTipText">
           <string>新增标签</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtToolButton::toolbutton2</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtToolButton" name="mtToolButton_del">
          <property name="minimumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="text">
           <string>...</string>
          </property>
          <property name="iconSize">
           <size>
            <width>22</width>
            <height>22</height>
           </size>
          </property>
          <property name="toolTipText">
           <string>删除标签</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtToolButton::toolbutton2</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtPushButton" name="mtPushButton_import">
          <property name="text">
           <string>导入Eclipse模板</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtPushButton::pushbutton5</enum>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Expanding</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>12</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_2">
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <property name="spacing">
            <number>4</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtLabel" name="label_data_loading">
             <property name="text">
              <string>数据加载中</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1_1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MRotate" name="label_rotate">
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="MtCheckBox" name="mtCheckBox">
          <property name="text">
           <string>默认优先使用ROI库的名称、颜色、类型</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtCheckBox::checkbox1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MRotate</class>
   <extends>QLabel</extends>
   <header>AccuComponentUi\Header\mrotate.h</header>
  </customwidget>
  <customwidget>
   <class>LabelLibraryTable</class>
   <extends>QWidget</extends>
   <header>MTLabelLibraryDialog\LabelLibrarySub\labellibrarytable.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
