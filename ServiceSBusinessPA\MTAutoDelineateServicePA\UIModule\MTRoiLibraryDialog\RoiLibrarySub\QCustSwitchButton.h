﻿#pragma once

#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "ui_QCustSwitchButton.h"

//QCustSwitchButton 参数
class QCustSwitchButtonParam : public ICellWidgetParam
{
public:
    int _width = -1;
    int _height = -1;

    QCustSwitchButtonParam();
    ~QCustSwitchButtonParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustSwitchButtonParam)

class QCustSwitchButton : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    explicit QCustSwitchButton(QWidget* parent = Q_NULLPTR);
    ~QCustSwitchButton();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口

    //设置是否允许编辑
    virtual void SetEnableEdit(bool bEdit);

    virtual void setCheckedState(bool bChecked);

signals:
    void sigCheckStateChanged(bool bChecked);

protected slots:
    void slotBtnClicked(bool bChecked);

private:
    Ui::QCustSwitchButton ui;
};
