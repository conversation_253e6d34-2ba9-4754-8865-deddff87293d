﻿#pragma once

#include <QWidget>
#include "AccuComponentUi/Header/QMTUIDataStruct.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"



//QMTAbsComboBox 参数
class  QMTButtonWithOrderParam : public ICellWidgetParam
{
public:

    QString _orderKey;                  //key值
    bool _enable = true;
    bool _bDefaultOrder = false;        //是否默认排序
    //QString _text;
    QString _normalPixPath;         //普通状态图标
    QString _ascPixPath;            //升序图标
    QString _descPixPath;           //降序图标

    QString _normalHoverPixPath;         //普通状态图标
    QString _ascHoverPixPath;            //升序图标
    QString _descHoverPixPath;           //降序图标

    QMTButtonWithOrderParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTButtonWithOrderParam)

namespace Ui
{
class QMTButtonWithOrder;
}

class  QMTButtonWithOrder : public QWidget, QMTAbstractCellWidget
{
    Q_OBJECT

public:

    QMTButtonWithOrder(QWidget* parent = Q_NULLPTR, QString text = "", QString key = "", bool enable = false, bool bDefaultOrder = false);
    ~QMTButtonWithOrder();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口

    //初始化界面
    void SetupCellWidget(QMTButtonWithOrderParam& cellParam);

    void SetButtonText(QString);                //设置文案
    void SetTextLabelStyle(const QString& styleStr);             //设置文案样式
    void EnableOrder(bool enable);              //是否允许排序
    void SetButtonKey(QString);                 //设置key
    QString GetButtonKey();                     //获取key
    int GetButtonState();            //获取状态
    void ResetButton();             //重置排序状态
    void SetDefaultOrderState(int state = mtuiData::ButtonOrderState::State_desc);   //设置默认的排序字段状态
    bool IsDefaultOrder();          //是否为默认的排序字段

    QString GetStyleSheetStr(int state);

signals:
    void sigOrderStateChange(const QString& orderKye, int state);

protected:
    void InitTemplateStyle();       //初始化默认样式

private slots:
    void slotUpdateOrder();

private:
    Ui::QMTButtonWithOrder* ui;
    int _state = mtuiData::State_normal;    //0:默认状态,1：升序，2:降序
    QString _key;                           //唯一值
    bool _bDefaultOrder = false;

    QString _normalPixPath;         //普通状态图标
    QString _ascPixPath;            //升序图标
    QString _descPixPath;           //降序图标

    QString _normalHoverPixPath;         //普通状态图标
    QString _ascHoverPixPath;            //升序图标
    QString _descHoverPixPath;           //降序图标
};
