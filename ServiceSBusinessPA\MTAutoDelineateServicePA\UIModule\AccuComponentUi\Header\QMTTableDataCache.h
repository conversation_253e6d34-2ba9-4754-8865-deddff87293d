﻿#pragma once

#include <QJsonObject>
#include <QJsonArray>
#include <QMutex>
#include "QMTEnumDef.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "QMTUIDataStruct.h"


/*
一级列表数据缓存
QMTAbstractTableWidget和QMTAbstractTableView都会继承该类
*/

class  QMTTableDataCache
{
public:
    QMTTableDataCache();
    virtual ~QMTTableDataCache();

    /*********Init**************/
    /*行类型设置和获取接口*/
    virtual void SetRowItemType(const QString& rowValue, int type);    //设置行类型
    virtual int GetRowItemType(const QString& rowValue);               //获取某一行的类型,取的是json中TableItemRowItemTypeKey的值

    /*下拉框相关*/
    //初始化下拉框选项集合(只初始化内存，并未初始化单元格，因为不确定单元格对象，如果要实现更新界面单元格下拉框的值，那么需要子类实现)
    virtual void InitColumnComboBoxItems(int column, const QStringList& items);
    virtual QStringList GetColumnComboBoxItems(int column);

    /************add**************/
    void InsertRowCellWidgetParamMap(const QString& rowValue, const QMap<int, ICellWidgetParam*>& cellWidgetParamMap);

    /*********delete**************/
    virtual void ClearDataModel();                                  //清空所有数据
    virtual void DeleteRowItem(const QString& rowValue);            //删除的是缓存数据

    /***********update**********/
    virtual void SetCurRowValueList(const QStringList& rowValueList);       //设置当前有效行集合
    virtual void HideColumn(int column, bool bHide);        //某列是否隐藏
    //更新单元格参数的text
    virtual void UpdateCellParamText(const QString& rowValue, int column, const QString& newText);
    //更新单元格参数的state
    virtual void UpdateCellParamState(const QString& rowValue, int column, int state);
    //更新单元格参数的checked
    virtual void UpdateCellParamChecked(const QString& rowValue, int column, bool bChecked);

    /***********get**********/
    //获取所有行的主键值(如果有搜索或者排序，那么就是搜索和排序的结果)
    virtual QStringList GetAllRowUniqueValueList();
    //获取缓存的行主键值(包含隐藏和未创建界面的)
    virtual QStringList GetCacheRowValueList();
    //获取单元格参数
    virtual ICellWidgetParam* GetCellWidgetParam(const QString& rowUniqueValue, int column);
    //是否存在该行,数据层判断
    bool IsExistRowItem(const QString& rowValue);
    //获取tableItemIndex数据
    mtuiData::TableWidgetItemIndex GetRowItemIndexInfo(const QString& rowValue);

    /*搜索结果接口*/
    virtual void SetSearchResultStrList(const QString& searchText, const QStringList& rowValueList);    //设置搜索结果
    virtual void GetSearchResultStrList(QString& searchText, QStringList& rowValueList);    //设置搜索结果

    /*checkBox对应model接口*/
    virtual void SetCheckBoxStateInModel(QString rowValue, int column, int state);          //设置checkbox的状态
    virtual void RemoveCheckBoxStateModel(QString rowValue);                                //删除checkbox状态
    virtual QStringList GetRowItemWithCheckBoxStateInModel(int column, int state);          //获取checkbox的状态的行
    virtual int GetCheckBoxStateInModel(QString rowValue, int column);                      //获取某行某列的checkbox的状态
    virtual QList<int> GetRowItemStateList(int column, QStringList rowValueList);           //获取某几行行集合的状态
    virtual bool IsAllRowChecked(int column, QStringList rowValueList = QStringList());     //是否传入的行checkBox状态都处于checked状态，如果传入的为空，那么判断所有行
    virtual bool GetRowItemCheckStateMap(const QString& rowValue, QMap<int/*column*/, int/*state*/>& outColumnStateMap);    //获取某一行所有的列的checkbox状态
    virtual bool GetIsCheckBoxCellWidget(const QString& rowUniqueValue, int column);     //是否是checkbox单元格
    virtual void SetCheckBoxColumn(int column); //设置checkbox所在的列
    virtual int GetCheckBoxColumn();             //获取checkbox所在的列
    //清空所有的checkbox状态
    virtual void ClearAllCheckBoxState(int column);

    //小红点model接口
    virtual void SetReadStateInModel(QString uniqueValue, bool bRead);   //保存已读标识
    virtual void RemoveAllReadStateInModel();                            //清空所有已读缓存
    virtual bool GetRowItemIsRead(QString uniqueValue);                  //判断是否已读
    virtual void SetReadDirPath(const QString& path);                            //设置是否已读文件夹名称
    virtual QString GetReadTempPath();                                           //获取是否已读temp的文件夹路径，不存在会自动创建


    /*是否允许编辑修改*/
    virtual void SetEditEnable(bool isEditEnable);
    bool GetEditEnable();       //获取当前是否允许修改
    virtual void SetRowItemEditEnable(const QString& rowValue, bool bEdit);             //设置某一行都不允许编辑
    virtual void SetColumnEditEnable(int column, bool bEnable);                         //设置某一列是否都不允许编辑
    virtual void SetCellEditEnable(const QString& rowValue, int column, bool bEdit);    //设置某个单元格不允许编辑
    virtual bool GetCellEditEnable(const QString& rowValue, int column);                       //获取单元格是否允许编辑
    virtual void SetCanEditColumnList(QList<int>& columnList);  //设置哪些列是允许编辑的
    virtual QList<int> GetCanEditColumnList();                  //获取允许编辑的列
    virtual bool IsCanEnableEditType(int column, int cellType); //调用SetEditEnable能够作用的单元格类型

protected:
    virtual void DeleteCellParamMap(QMap<int, ICellWidgetParam*>& cellParamMap);    //delete cellWidgetParam
    int GetMtLabelTypeWithFontSize(int fontSize);                                   //根据字体大小获取MtLabel的mtType

protected:
    /****************数据层***********************/
    QMTAbsRowWidgetItemParam _perRowItemParam;  //UI参数
    bool _adaptResize = true;                   //是否自适应变化宽度

    //动态创建相关参数
    bool _enableDynamicCreate = false;          //是否动态创建Widget(默认不需要，如果数据量特别大再设置成true)
    int _InitPageRowCnt = 200;                  //初始化允许新增的行个数
    int _perPageRowCnt = 10;                    //每次滑动条滑动到最底端的时候创建行个数
    QList<int> _refreshRowWidgetIndexList;      //没有按照顺序创建的widget行号，用于后续刷新判断


    //行数据model
    QStringList _rowValueList;                      //行数据的唯一值数据集合
    QStringList _cachRowValueList;                  //用来记录当前行所有行缓存(包含隐藏和未创建的)
    QMap<QString/*rowValue*/, QMap<int/*column*/, ICellWidgetParam*>> _rowCellWidgetParamMapMap; //行和单元格参数映射
    QMutex _rowDataMutex;                           //数据model的锁
    QString _curRowItemValue;                       //当前的行
    QMap<QString/*rowValue*/, int/*rowType*/> _rowItemValueTypeMap;       //每一行与对应类型映射(设置接口SetRowItemType)
    QMap<QString, bool> _rowItemWidgetFlagMap;      //每一行是否创建了界面标识

    bool m_bLockClearDataModel = false;     //是否锁定正在清空数据。如果正在清空数据，那么不允许再次SetDataModel
    //下拉框
    QMap<int/*column*/, QStringList> _comboBoxColumnMap;      //下拉框值集合

    //用于存储checkBox的状态,支持N列数据存储
    QMap<QString/*uniqueValue*/, QMap<int/*column*/, int/*state*/>> _rowItemColumnCheckStateMapMap;  //checkbox状态缓存
    int _checkBoxColumn = 0;
    QMutex _checkBoxMutex;                           //checkbox状态的锁

    //已读状态缓存
    QMap<QString/*uniqueValue*/, bool/*bRead*/> _rowItemReadStateMap;    //是否已读状态缓存

    //排序相关，支持N列不同排序
    //QMap<int/*column*/, QString/*order key*/> _columnOrderKeyMap;        //某一列排序对应的key(后续废除)
    mtuiData::TableOrderParam _orderData;                                //排序相关数据

    //搜索相关
    QString _curSearchText;                     //当前搜索的关键字
    QStringList _searchResultStrList;           //搜索结果字符串

    /*是否已读*/
    QString _readDirPath;               //是否已读文件夹路径

    /*是否允许编辑修改*/
    bool _editEnable = true;            //全局是否允许编辑
    QList<int> _canEditColumnList;      //允许编辑的列
    QMap<QString/*uniqueValue*/, QMap<int/*column*/, bool/*enable*/>> _rowItemEnableEditMapMap;       //某个单元格是否允许编辑修改

    //列是否隐藏
    QMap<int/*column*/, bool/*bHide*/> _columnHideStateMap;
};

