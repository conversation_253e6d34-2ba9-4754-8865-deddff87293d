<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MMessageBox</class>
 <widget class="QWidget" name="MMessageBox">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>466</width>
    <height>198</height>
   </rect>
  </property>
  <property name="maximumSize">
   <size>
    <width>466</width>
    <height>312</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>MMessageBox</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#frame{
	background-color: rgb(56, 67, 85);
}

#widget_line{
	min-height:1px;
	max-height:1px;
	background-color: rgba(219,226,241,0.1);

}

QPushButton{    
	border-radius: 4px;
}

QFrame{
	border-radius:4px;
}

#MMessageBox{
	border-radius:4px;
}


#pushButton_yes
{
	min-width:80px;
	min-height:30px;
	max-width:80px;
	max-height:30px;
	background-color: rgb(78,156,213);
	color: rgb(255,255,255);
}

#pushButton_no
{
	min-width:80px;
	min-height:30px;
	max-width:80px;
	max-height:30px;
	background-color: rgb(56,67,85);
	color: rgba(188,186,186,1);
	border-style: outset; 
	border-width: 1px;  
	border-color: rgba(188,186,186,0.34);
}

#pushButton_save
{
	min-width:80px;
	min-height:30px;
	max-width:80px;
	max-height:30px;
	background-color: rgb(56,67,85);
	color: rgba(188,186,186,1);
	border-style: outset; 
	border-width: 1px;  
	border-color: rgba(188,186,186,0.34);
}

#pushButton_no:hover{ 
	background-color: rgba(216,216,216,0.1);
}

#pushButton_save:hover{ 
	background-color: rgba(216,216,216,0.1);
}

#pushButton_yes:hover{ 
background-color: rgb(60,143,203);
}

#label_image{
	min-width:51px;
	min-height:51px;
	max-width:51px;
	max-height:51px;
	image: url(:/AccuUIComponentImage/images/manteia.png);
}

#pushButton_cancel{
	border-image:url(:/AccuUIComponentImage/images/btn_close.png)
}

#pushButton_cancel:hover{ 
	background-color: rgba(216,216,216,0.1);
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>12</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>12</number>
      </property>
      <property name="spacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <layout class="QGridLayout" name="gridLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <item row="2" column="0">
         <widget class="Line" name="widget_line">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>1</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>1</height>
           </size>
          </property>
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QWidget" name="widget" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>90</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16777215</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_4">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>1</number>
           </property>
           <property name="rightMargin">
            <number>1</number>
           </property>
           <item>
            <spacer name="horizontalSpacer_8">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Expanding</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>42</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_2">
             <property name="spacing">
              <number>0</number>
             </property>
             <item>
              <spacer name="verticalSpacer_manteia1">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Maximum</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel" name="label_image">
               <property name="minimumSize">
                <size>
                 <width>51</width>
                 <height>51</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>51</width>
                 <height>51</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
               <property name="alignment">
                <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_manteia2">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="verticalSpacer_manteia3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Fixed</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>16</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout">
             <property name="spacing">
              <number>0</number>
             </property>
             <item>
              <spacer name="verticalSpacer_6">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>9</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel_Dot" name="messageLabel">
               <property name="maximumSize">
                <size>
                 <width>305</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>message1</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_9">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Fixed</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>10</width>
                 <height>10</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QLabel_Dot" name="messageLabel2">
               <property name="maximumSize">
                <size>
                 <width>305</width>
                 <height>16777215</height>
                </size>
               </property>
               <property name="text">
                <string>message2</string>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="verticalSpacer_7">
               <property name="orientation">
                <enum>Qt::Vertical</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Expanding</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>20</width>
                 <height>9</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <spacer name="horizontalSpacer_7">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Expanding</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>42</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="4" column="0">
         <widget class="QWidget" name="button" native="true">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>32</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_5">
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Maximum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>400</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_save">
             <property name="minimumSize">
              <size>
               <width>82</width>
               <height>32</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>82</width>
               <height>32</height>
              </size>
             </property>
             <property name="text">
              <string>Save</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_6">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Minimum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>16</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_no">
             <property name="maximumSize">
              <size>
               <width>82</width>
               <height>32</height>
              </size>
             </property>
             <property name="text">
              <string>No</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_4">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Minimum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>16</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_yes">
             <property name="maximumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>Yes</string>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_5">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Minimum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>42</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QWidget" name="cancel" native="true">
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>16</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_1">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Maximum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>440</width>
               <height>12</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <spacer name="verticalSpacer_2">
             <property name="orientation">
              <enum>Qt::Vertical</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Maximum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>20</width>
               <height>16</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="QPushButton" name="pushButton_cancel">
             <property name="minimumSize">
              <size>
               <width>16</width>
               <height>16</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16</width>
               <height>16</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeType">
              <enum>QSizePolicy::Minimum</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>12</width>
               <height>12</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
        <item row="3" column="0">
         <spacer name="verticalSpacer_4">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Fixed</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>10</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>QLabel_Dot</class>
   <extends>QLabel</extends>
   <header>AccuComponentUi\Header\QLabel_Dot.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
