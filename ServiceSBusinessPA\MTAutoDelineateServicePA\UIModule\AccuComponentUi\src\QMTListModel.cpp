﻿#include "AccuComponentUi\Header\QMTListModel.h"
#include <qDebug>
#include "AccuComponentUi\Header\QMTJsonValue.h"

QMTListModel::QMTListModel(QObject* parent)
    : QObject(parent)
{
    SetMainKey(0, "patientID");
    SetMainKey(1, "seriesUID");
    //qRegisterMetaType<QWidget>("QWidget");
}

QMTListModel::~QMTListModel()
{
}

void QMTListModel::DebugIsCTExist()
{
    int count = _listDataModel.size();

    for (int i1 = 0; i1 < count; ++i1)//patient
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();

        for (int j = 0; j < subRecordList.size(); ++j)//series
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            bool isDelete = subrecordHash.value(TOString(KeyDelete)).toBool();
            bool isCtExist = subrecordHash.value(TOString(isCTExist)).toBool();
            qDebug() << "patient:" << i1 << ", isCtExist:" << isCtExist;
            QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();

            for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)//rt
            {
                QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                bool isDelete = rtrecordHash.value(TOString(KeyDelete)).toBool();
            }
        }
    }
}

void QMTListModel::SetMainKey(int index, QString mainKey)
{
    _mainKeyMap.insert(index, mainKey);
}

int QMTListModel::GetPushOrder()
{
    return _pushOrder;
}
QList<QMTRowRecord>& QMTListModel::GetDataModel()
{
    return _listDataModel;
}

int QMTListModel::FirstRowRecordIndex(QMTRowRecord rowRecord)
{
    int index = -1;

    //判断一级是否存在
    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        QMTRowRecord tmp = _listDataModel.at(i);
        QString mainKey = _mainKeyMap.value(0);

        if (mainKey.size() > 0)//设置了主键情况下
        {
            if (tmp.Equal(rowRecord, mainKey))
            {
                index = i;
                return index;
            }
        }
    }

    return index;
}

int QMTListModel::FirstRowRecordIndex(QString patientID)
{
    int index = -1;

    //判断一级是否存在
    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        QMTRowRecord tmpRecord = _listDataModel.at(i);
        QString tempValue = tmpRecord.value(TOString(patientID)).toString();

        if (tempValue == patientID)//设置了主键情况下
        {
            index = i;
            return index;
        }
    }

    return index;
}


void QMTListModel::AddItem(QMTRowRecord rowRecord, bool isUpdate)
{
    QMutexLocker locker(&_mutex);

    if (1)//进行一次转换
    {
        QVariant srcSubs = rowRecord[TOString(subs)];

        if (srcSubs.canConvert(QMetaType::QVariantHash))
        {
            QList<QVariant> subList;
            rowRecord.remove(TOString(subs));
            subList.append(srcSubs);
            rowRecord.SetValue(TOString(subs), subList);
        }
    }

    int index = FirstRowRecordIndex(rowRecord);

    if (index < 0)//not exist
    {
        {
            QVariantList subList = rowRecord[TOString(subs)].value<QVariantList>();
            QVariantList newSubList;

            for (int i = 0; i < subList.size(); ++i)
            {
                QVariantHash subHash = subList.at(i).toHash();
                subHash.insert(TOString(isCTExist), true);
                newSubList.append(subHash);
            }

            rowRecord[TOString(subs)] = newSubList;
        }
        QJsonObject jsonObj = QJsonObject::fromVariantHash(rowRecord);

        if (Push_Front == _pushOrder)
        {
            _listDataModel.push_front(rowRecord);
        }
        else
        {
            _listDataModel.push_back(rowRecord);
        }

        QVariantHash temp = _listDataModel.at(0);

        if (isUpdate)
        {
            QString key = _mainKeyMap.value(0);
            QString patientID = rowRecord.value(key).toString();
            //locker.unlock();
            emit sigAddItem(patientID, jsonObj);
        }
    }
    else
    {
        QMTRowRecord desRecord = _listDataModel.at(index);
        QVariant srcSubs = rowRecord[TOString(subs)];
        QVariant dessubs = desRecord[TOString(subs)];
        QList<QVariant> srcsubList;
        QList<QVariant> dessubList;
        QMTRowRecord newRecord = rowRecord;
        QList<QVariant> newSubRecordList;

        if (srcSubs.canConvert(QMetaType::QVariantList))
            srcsubList = srcSubs.value<QVariantList>();

        if (dessubs.canConvert(QMetaType::QVariantList))
            dessubList = dessubs.value<QVariantList>();

        int subRow = -1;

        for (int i = 0; i < srcsubList.size(); ++i)
        {
            QVariantHash subSrcRecord;
            bool isExist = false;
            //int subRow = -1;

            if (srcsubList.at(i).canConvert(QMetaType::QVariantHash))
            {
                subSrcRecord = srcsubList.at(i).value<QVariantHash>();
                subSrcRecord.insert(TOString(isCTExist), true);
            }

            for (int j = 0; j < dessubList.size(); ++j)
            {
                QVariantHash subDesRecord;
                QString mainkey;

                if (dessubList.at(j).canConvert(QMetaType::QVariantHash))
                    subDesRecord = dessubList.at(j).value<QVariantHash>();

#if 0//不需要此判断

                if (subDesRecord.contains(TOString(isCTExist)) == false)
                    continue;

                bool isCTExist = subDesRecord.value(TOString(isCTExist)).toBool();

                if (false == isCTExist)
                    continue;

#endif
                mainkey = _mainKeyMap.value(1);

                if (subDesRecord[mainkey].toString() == subSrcRecord[mainkey].toString())
                {
                    //if (isCTExist)
                    {
                        subRow = j;
                        isExist = true;
                    }
                    break;
                }
            }

            if (!isExist)
            {
                newSubRecordList.append(subSrcRecord);
                dessubList.append(subSrcRecord);
                _listDataModel[index][TOString(subs)] = dessubList;
                //QMTRowRecord tmp = _listDataModel.at(0);
            }
            else//update
            {
                newSubRecordList.append(subSrcRecord);
                QVariantHash dessubRecord = dessubList.at(subRow).toHash();
                QVariantHash::iterator it;

                for (it = subSrcRecord.begin(); it != subSrcRecord.end(); ++it)
                {
                    QString key = it.key();
                    QVariant value = it.value();
                    dessubRecord.insert(key, value);
                }

                dessubRecord.insert(TOString(KeyDelete), false);
                dessubRecord.insert(TOString(isCTExist), true);
                dessubList[subRow] = dessubRecord;
                _listDataModel[index][TOString(subs)] = dessubList;
                /*
                QMTListViewModelIndex modelIndex(subRow, index);
                emit sigUpdateValue(modelIndex, QJsonObject::fromVariantHash(subSrcRecord));
                */
            }
        }

        if (newSubRecordList.size() > 0)
        {
            newRecord[TOString(subs)] = newSubRecordList;
            QJsonObject jsonObj = QJsonObject::fromVariantHash(newRecord);

            if (isUpdate)
            {
                QString key = _mainKeyMap.value(0);
                QString patientID = rowRecord.value(key).toString();
                //locker.unlock();
                emit sigAddItem(patientID, jsonObj);
            }
        }
    }
}

int QMTListModel::IsRtExist(QString sopInsUID, QVariantList rtHashList)
{
    for (int i = 0; i < rtHashList.size(); ++i)
    {
        QVariantHash rtRecord = rtHashList.at(i).toHash();
        QString tmpsopInsUID = rtRecord.value(TOString(sopInsUID)).toString();

        if (tmpsopInsUID == sopInsUID)
        {
            return i;
        }
    }

    return -1;
}


void QMTListModel::AddRtRecord(QMTRowRecord rowRecord)
{
    const char* RtFilesKey = TOString(RtFiles);
    const char* subsKey = TOString(subs);
    QMutexLocker locker(&_mutex);
    int row = -1;

    if (1)//进行一次转换
    {
        QVariant newSubs = rowRecord[subsKey];

        if (newSubs.canConvert(QMetaType::QVariantHash))
        {
            QList<QVariant> subList;
            rowRecord.remove(subsKey);
            subList.append(newSubs);
            rowRecord.SetValue(subsKey, subList);
        }
    }

    int index = FirstRowRecordIndex(rowRecord);
    row = index;

    if (index < 0)//not exist
    {
        {
            QVariantList subList = rowRecord[subsKey].value<QVariantList>();
            QVariantList newSubList;

            for (int i = 0; i < subList.size(); ++i)
            {
                QVariantHash subHash = subList.at(i).toHash();
                subHash.insert(TOString(isCTExist), false);
                newSubList.append(subHash);
            }

            rowRecord[subsKey] = newSubList;
        }

        if (Push_Front == _pushOrder)
        {
            _listDataModel.push_front(rowRecord);
        }
        else
        {
            _listDataModel.push_back(rowRecord);
        }

        QJsonObject jsonObj = QJsonObject::fromVariantHash(rowRecord);
        QString key = _mainKeyMap.value(0);
        QString patientID = rowRecord.value(key).toString();
        //locker.unlock();
        //DebugIsCTExist();
        emit sigAddRtFiles(patientID, jsonObj, 0);
        return;
    }
    else
    {
        QMTRowRecord desRecord = _listDataModel.at(index);
        QVariant newSubs = rowRecord[subsKey];
        QVariant dessubs = desRecord[subsKey];
        QList<QVariant> newSubList = newSubs.value<QVariantList>();
        QList<QVariant> dessubList = dessubs.value<QVariantList>();
        QMTRowRecord newRecord = rowRecord;
        QList<QVariant> newSubRecordList;
        int subRow = -1;
        int existState = 0;//0:rt and ct not exist, 1:ct not exist but rt exist, 2:ct and rt exist, 3

        for (int i = 0; i < newSubList.size(); ++i)
        {
            QVariantHash subNewRecord;
            subNewRecord = newSubList.at(i).value<QVariantHash>();
            int existStatetmp = 0;

            for (int j = 0; j < dessubList.size(); ++j)
            {
                QVariantHash subDesRecord;
                QString mainkey = _mainKeyMap.value(1);

                if (dessubList.at(j).canConvert(QMetaType::QVariantHash))
                    subDesRecord = dessubList.at(j).value<QVariantHash>();

                if (subNewRecord[mainkey].toString() != "unknown")
                {
                    if (subDesRecord[mainkey].toString() == subNewRecord[mainkey].toString())//ct exist
                    {
                        subRow = j;
                        existState = existState | 2;
                        existStatetmp = 2;
                        break;
                    }
                }
                else
                {
                    if (subDesRecord.value(RtMainKey).toString() == subNewRecord.value(RtMainKey).toString())
                    {
                        subRow = j;
                        existState = existState | 1;
                        existStatetmp = 1;
                        break;
                    }
                }
            }

            //对rtlist 进行一次转换
            QVariant rtVariant = subNewRecord.value(RtFilesKey);

            if (rtVariant.canConvert(QVariant::Hash))
            {
                QVariantList rtList;
                rtList.append(rtVariant);
                subNewRecord[RtFilesKey] = rtList;
            }

            if (0 == existStatetmp)
            {
                //inset data to new list
                newSubRecordList.append(subNewRecord);
                //update model data
                dessubList.append(subNewRecord);
                _listDataModel[index][subsKey] = dessubList;
            }
            else if (1 == existStatetmp)//CT not exist but rt exist
            {
                QVariantList newRtRecordList = subNewRecord.value(RtFilesKey).value<QVariantList>();
                QVariantHash subDesRecord = dessubList.at(subRow).toHash();
                QVariantList destRtRecordList = subDesRecord.value(RtFilesKey).value<QVariantList>();

                for (int j = 0; j < newRtRecordList.size(); ++j)
                {
                    QVariantHash newRtRecord = newRtRecordList.at(j).toHash();

                    if (Push_Front == _pushOrder)
                    {
                        destRtRecordList.push_front(newRtRecord);
                    }
                    else
                    {
                        // destRtRecordList.append(newRtRecord);
                        destRtRecordList.push_back(newRtRecord);
                    }
                }

                //update model
                subDesRecord[RtFilesKey] = destRtRecordList;
                dessubList[subRow] = subDesRecord;
                _listDataModel[index][subsKey] = dessubList;
                //add new list
                newSubRecordList.append(subNewRecord);
            }
            else
            {
                QVariantHash subOldRecord = dessubList.at(subRow).toHash();
                QVariantList rtFileList = subOldRecord.value(RtFilesKey).value<QVariantList>();
                QVariantList newRtFileList = subNewRecord.value(RtFilesKey).value<QVariantList>();
                bool hashAdd = false;

                //add new rt files
                for (int i = 0; i < newRtFileList.size(); ++i)
                {
                    QVariantHash newRtRecord = newRtFileList.at(i).toHash();
                    QString sopInsUID = newRtRecord.value(TOString(sopInsUID)).toString();
                    int rtIndex = IsRtExist(sopInsUID, rtFileList);

                    //判断rt是否存在，如果存在就更新数据，不存在继续走下面新增
                    if (rtIndex >= 0)
                    {
                        continue;
                    }

                    hashAdd = true;

                    if (Push_Front == _pushOrder)
                    {
                        rtFileList.push_front(newRtRecord);
                    }
                    else
                    {
                        // rtFileList.append(newRtRecord);
                        rtFileList.push_back(newRtRecord);
                    }
                }

                if (hashAdd)
                {
                    //add sub record to model
                    subOldRecord[RtFilesKey] = rtFileList;
                    dessubList[subRow] = subOldRecord;
                    _listDataModel[index][subsKey] = dessubList;
                    //add new record to list
                    newSubRecordList.append(subNewRecord);
                }
            }
        }

        if (newSubRecordList.size() > 0)
        {
            newRecord[subsKey] = newSubRecordList;
            QJsonObject jsonObj = QJsonObject::fromVariantHash(newRecord);
            QString key = _mainKeyMap.value(0);
            QString patientID = rowRecord.value(key).toString();
            //locker.unlock();
            emit sigAddRtFiles(patientID, jsonObj, existState);
        }
    }
}

void QMTListModel::AddFirstLevelWidget(QString firstValue, QWidget* widget)
{
    int row = -1;
    //QMutexLocker locker(&_mutex);

    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        QMTRowRecord patientRecord = _listDataModel.at(i);
        QString key = _mainKeyMap.value(0);
        QString value = patientRecord.value(key).toString();

        if (value == firstValue)
        {
            QVariant widgetVariant = QVariant::fromValue(widget);
            patientRecord.insert(TOString(widgetUi), widgetVariant);
            row = i;
            _listDataModel[row] = patientRecord;
            break;
        }
    }

    QList<QMTRowRecord> tempDataModel = _listDataModel;
    QMTRowRecord patientRecord = tempDataModel.at(0);
    //    QMTAbstractRowItemWidget* widget2 = patientRecord.value(TOString(widgetUi)).value<QMTAbstractRowItemWidget*>();
}

void QMTListModel::AddSecondLevelWidget(QString firstValue, QString secondValue, QWidget* widget)
{
    int row = -1;
    int seriesRow = -1;
    QMTRowRecord firstHash;
    //QMutexLocker locker(&_mutex);

    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        QMTRowRecord firstRecord = _listDataModel.at(i);
        QString key = _mainKeyMap.value(0);
        QString value = firstRecord.value(key).toString();

        if (value == firstValue)
        {
            QVariant widgetVariant = QVariant::fromValue(widget);
            firstHash = firstRecord;
            row = i;
            break;
        }
    }

    if (row >= 0)
    {
        QVariantList subRecordList = firstHash.value(TOString(subs)).value<QVariantList>();

        //insert series widget
        for (int i = 0; i < subRecordList.size(); ++i)
        {
            QVariantHash seriesRecord = subRecordList.at(i).toHash();
            QString key = _mainKeyMap.value(1);
            QString value = seriesRecord.value(key).toString();

            if (value == secondValue)
            {
                QVariant widgetVariant = QVariant::fromValue(widget);
                seriesRecord.insert(TOString(widgetUi), widgetVariant);
                seriesRow = i;
                subRecordList[seriesRow] = seriesRecord;
                break;
            }
        }

        //update list
        if (seriesRow >= 0)
        {
            firstHash[TOString(subs)] = subRecordList;
        }
    }

    if (row >= 0 && seriesRow >= 0)
    {
        _listDataModel[row] = firstHash;
    }
}

void QMTListModel::SetPushOrder(PushOrder order)
{
    _pushOrder = order;
}

void QMTListModel::AddRtFileWidget(QString patientID, QString seriesUID, QString referencedSeriesUID, QWidget* widget)
{
    int row = -1;
    int seriesRow = -1;
    int rtRow = -1;
    QMTRowRecord patientHash;
    //QMutexLocker locker(&_mutex);

    //find patient
    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        QMTRowRecord patientRecord = _listDataModel.at(i);
        QString key = _mainKeyMap.value(0);
        QString value = patientRecord.value(key).toString();

        if (value == patientID)
        {
            //QVariant widgetVariant = QVariant::fromValue(widget);
            //patientRecord.insert(TOString(widgetUi), widgetVariant);
            patientHash = patientRecord;
            row = i;
            break;
        }
    }

    QVariantList subRecordList; //series

    //find series
    if (row >= 0)
    {
        subRecordList = patientHash.value(TOString(subs)).value<QVariantList>();

        //insert series widget
        for (int i = 0; i < subRecordList.size(); ++i)
        {
            QVariantHash seriesRecord = subRecordList.at(i).toHash();
            QString key = _mainKeyMap.value(1);
            QString value = seriesRecord.value(key).toString();

            if (value == seriesUID)
            {
                //QVariant widgetVariant = QVariant::fromValue(widget);
                //seriesRecord.insert(TOString(widgetUi), widgetVariant);
                seriesRow = i;
                break;
            }
        }
    }

    //find rt and inset widget ui
    if (seriesRow >= 0)
    {
        QVariantHash seriesHash = subRecordList.at(seriesRow).toHash();
        QVariantList rtRecordList = seriesHash.value(TOString(RtFiles)).value<QVariantList>();

        //insert rt widget
        for (int i = 0; i < rtRecordList.size(); ++i)
        {
            QVariantHash rtRecord = rtRecordList.at(i).toHash();
            QString value = rtRecord.value(RtUniqueKey).toString();

            if (value == referencedSeriesUID)
            {
                QVariant widgetVariant = QVariant::fromValue(widget);
                rtRecord.insert(TOString(widgetUi), widgetVariant);
                rtRow = i;
                rtRecordList[rtRow] = rtRecord;
                seriesHash[TOString(RtFiles)] = rtRecordList;
                break;
            }
        }

        //update series list an patient
        if (rtRow >= 0)
        {
            subRecordList[seriesRow] = seriesHash;
            patientHash[TOString(subs)] = subRecordList;
        }
    }

    //update list data model
    if (row >= 0 && seriesRow >= 0 && rtRow >= 0)
    {
        _listDataModel[row] = patientHash;
    }
}

void QMTListModel::RemoveSelectItems()
{
}

bool QMTListModel::IsAllRtDelete(QVariantList rtList)
{
    bool ret = true;

    for (int i = 0; i < rtList.size(); ++i)
    {
        QVariantHash rtHash = rtList.at(i).toHash();
        bool isDelete = rtHash.value(TOString(KeyDelete)).toBool();

        if (!isDelete)
        {
            ret = false;
            break;
        }
    }

    return ret;
}

void QMTListModel::RemoveItemsWithDelete()
{
    int count = _listDataModel.size();

    for (int i1 = 0; i1 < count; ++i1)//patient
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        bool isDelete = recordHash.value(TOString(KeyDelete)).toBool();

        if (isDelete)
        {
            _listDataModel.removeOne(recordHash);
            RemoveItemsWithDelete();
            return;
        }

        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();

        for (int j = 0; j < subRecordList.size(); ++j)//series
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            bool isSubDelete = subrecordHash.value(TOString(KeyDelete)).toBool();
            bool isCtExist = subrecordHash.value(TOString(isCTExist)).toBool();

            if (isCtExist)
            {
                if (isSubDelete)//需要判断是否还有rt文件，如果有，那么不能删除ct结构
                {
                    QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();

                    if (IsAllRtDelete(rtRecordList))
                    {
                        subRecordList.removeOne(subrecordHash);
                        recordHash[TOString(subs)] = subRecordList;
                        _listDataModel[i1] = recordHash;
                        RemoveItemsWithDelete();
                        return;
                    }
                }

                QVariantList rtHashList;
                QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();
                int rtDeleteCnt = 0;

                for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)
                {
                    QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                    bool isRtDelete = rtrecordHash.value(TOString(KeyDelete)).toBool();

                    if (!isRtDelete)
                    {
                        rtHashList.append(rtrecordHash);
                    }
                    else
                    {
                        rtDeleteCnt++;
                    }
                }

                if (isSubDelete)
                {
                    subrecordHash.remove(TOString(widgetUi));
                    subrecordHash[TOString(isCTExist)] = false;
                }

                if (isSubDelete || rtDeleteCnt > 0)
                {
                    subrecordHash[TOString(RtFiles)] = rtHashList;
                    subRecordList[j] = subrecordHash;
                    recordHash[TOString(subs)] = subRecordList;
                    _listDataModel[i1] = recordHash;
                }

                continue;
            }
            else
            {
                QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();
                QVariantList rtRetainHashList;
                int rtDeleteCnt = 0;

                for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)//rt
                {
                    QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                    bool isDelete = rtrecordHash.value(TOString(KeyDelete)).toBool();

                    if (!isDelete)
                    {
                        rtRetainHashList.append(rtrecordHash);
                    }
                    else
                    {
                        rtDeleteCnt++;
                    }
                }

                if (rtDeleteCnt > 0)
                {
                    subrecordHash[TOString(RtFiles)] = rtRetainHashList;
                    subRecordList[j] = subrecordHash;
                    recordHash[TOString(subs)] = subRecordList;
                    _listDataModel[i1] = recordHash;
                }

                continue;
            }
        }
    }
}

void QMTListModel::ResetModel()
{
    emit sigResetModel();
}

void QMTListModel::SetSeriesDeleteFlag(QString value)
{
    int count = _listDataModel.size();

    for (int i1 = 0; i1 < count; ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString seriesUID = subrecordHash.value(TOString(seriesUID)).toString();

            if (value == seriesUID)
            {
                subrecordHash.insert(TOString(KeyDelete), true);
                subRecordList[j] = subrecordHash;
                recordHash[TOString(subs)] = subRecordList;
                _listDataModel[i1] = recordHash;
                return;
            }
        }
    }
}

void QMTListModel::SetRtDeleteFlag(QString value)
{
    const char* RtFilesKey = TOString(RtFiles);
    const char* subsKey = TOString(subs);
    //QMutexLocker locker(&_mutex);
    int count = _listDataModel.size();

    for (int i1 = 0; i1 < count; ++i1)//patient
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(subsKey).value<QVariantList>();

        for (int j = 0; j < subRecordList.size(); ++j)//series
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QVariantList rtRecordList = subrecordHash.value(RtFilesKey).value<QVariantList>();

            for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)//rt
            {
                QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                QString sopInsUID = rtrecordHash.value(TOString(sopInsUID)).toString();

                if (value == sopInsUID)
                {
                    rtrecordHash.insert(TOString(KeyDelete), true);
                    //subRecordList[j] = subrecordHash;
                    rtRecordList[rtIndex] = rtrecordHash;
                    subrecordHash[RtFilesKey] = rtRecordList;
                    subRecordList[j] = subrecordHash;
                    recordHash[subsKey] = subRecordList;
                    _listDataModel[i1] = recordHash;
                    return;
                }
            }
        }
    }
}

void QMTListModel::CheckFirstIsNeedDelete()
{
    int count = _listDataModel.size();

    for (int i1 = 0; i1 < count; ++i1)//patient
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        bool isAllChildDelete = true;

        for (int j = 0; j < subRecordList.size(); ++j)//series
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString seriesUID = subrecordHash.value(TOString(seriesUID)).toString();
            bool isCTExist = subrecordHash.value(TOString(isCTExist)).toBool();

            if (isCTExist)
            {
                bool subDelete = subrecordHash.value(TOString(KeyDelete)).toBool();

                if (!subDelete)
                {
                    isAllChildDelete = false;
                    break;
                }
                else//判断rt是否还有存在的
                {
                    QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();

                    for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)//rt
                    {
                        QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                        bool rtDelete = rtrecordHash.value(TOString(KeyDelete)).toBool();

                        if (!rtDelete)
                        {
                            isAllChildDelete = false;
                            break;
                        }
                    }
                }
            }
            else
            {
                QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();

                for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)//rt
                {
                    QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                    bool rtDelete = rtrecordHash.value(TOString(KeyDelete)).toBool();

                    if (!rtDelete)
                    {
                        isAllChildDelete = false;
                        break;
                    }
                }
            }
        }

        if (isAllChildDelete)
        {
            recordHash.insert(TOString(KeyDelete), true);
            _listDataModel[i1] = recordHash;
        }
    }
}

void QMTListModel::RemoveSeriesList(QStringList strList)
{
    if (0 == strList.size())
        return;

    _mutex.tryLock();

    for (int i = 0; i < strList.size(); ++i)
    {
        QString value = strList.at(i);
        SetSeriesDeleteFlag(value);
    }

    CheckFirstIsNeedDelete();
    emit sigRemoveItems(strList);
}

void QMTListModel::RemoveRtList(QStringList strList)
{
    if (0 == strList.size())
        return;

    _mutex.tryLock();

    for (int i = 0; i < strList.size(); ++i)
    {
        QString value = strList.at(i);
        SetRtDeleteFlag(value);
    }

    CheckFirstIsNeedDelete();
    emit sigRemoveRtItems(strList);
}


#if 0
void QMTListModel::SetRemoveItemDeleteStatus(int row, int parentRow)
{
    if (parentRow >= _listDataModel.size())
        return;

    QVariantHash widgetHash = _listDataModel.at(parentRow);
    QVariantList subList = widgetHash.value(TOString(subs)).value<QVariantList>();

    if (row < subList.size())
    {
        QVariantHash subwidgetHash = subList.at(row).toHash();
        subwidgetHash.insert(TOString(KeyDelete), true);
        subList[row] = subwidgetHash;
        _listDataModel[parentRow][TOString(subs)] = subList;
    }
}
#endif

void QMTListModel::RemoveModelItems()
{
    QJsonArray firstJsonArray;
    QList<QMTRowRecord> removeList;

    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList removeSubList;

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            bool isDelete = subrecordHash.value(TOString(KeyDelete)).toBool();

            if (isDelete)
            {
                removeSubList.append(subrecordHash);
            }
        }

        if (removeSubList.size() > 0)
        {
            for (int i = 0; i < removeSubList.size(); ++i)
            {
                subRecordList.removeOne(removeSubList.at(i));
            }

            if (subRecordList.size() > 0)
            {
                _listDataModel[i1][TOString(subs)] = subRecordList;
            }
            else
            {
                //_patientsWidgetList.removeOne(widgetHash);
                removeList.append(recordHash);
            }
        }
    }

    for (int i = 0; i < removeList.size(); ++i)
    {
        _listDataModel.removeOne(removeList.at(i));
    }
}
#if 0
void QMTListModel::slotRemoveListViewItems(QJsonObject obj)
{
    int count = obj.value(TOString(rootCount)).toInt();
    QJsonArray removArray = obj.value(TOString(roots)).toArray();

    for (int i = 0; i < removArray.size(); ++i)
    {
        QJsonArray subArray = removArray.at(i).toObject().value(TOString(subs)).toArray();

        for (int j = 0; j < subArray.size(); ++j)
        {
            QJsonObject removeObj = subArray.at(j).toObject();
            int row = removeObj.value(TOString(KeyRow)).toInt();
            int parentRow = removeObj.value(TOString(KeyParentRow)).toInt();
            //RmoveItem(row, parentRow);
            SetRemoveItemDeleteStatus(row, parentRow);
        }
    }

    RemoveModelItems();
}
#endif

void QMTListModel::slotResetListViewFinish()
{
#if 0
    _mutex.unlock();
    RemoveItemsWithDelete();
    emit sigRemoveItemsFinish();
#endif
}


void QMTListModel::slotRemoveItemsViewFinish()
{
    _mutex.unlock();
    RemoveItemsWithDelete();
    emit sigRemoveItemsFinish();
}

QVariantHash QMTListModel::GetFirstLevelHash(QString firstValue, int& index)
{
    QVariantHash retRecord;
    QString key = _mainKeyMap.value(0);

    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QVariantHash widgetHash = _listDataModel.at(i1);

        if (firstValue == widgetHash.value(key).toString())
        {
            retRecord = widgetHash;
            index = i1;
            break;
        }
    }

    return retRecord;
}

QVariantHash QMTListModel::GetSecondLevelItemHash(int row, QString value)
{
    QVariantHash ret;
    QVariantHash widgetHash = _listDataModel.at(row);
    QString secondKey = _mainKeyMap.value(1);
    //QString secondValue = widgetHash.value(secondKey).toString();
    QVariantList subWidgetList = widgetHash.value(TOString(subs)).value<QVariantList>();

    for (int j = 0; j < subWidgetList.size(); ++j)
    {
        QVariantHash subWidgetHash = subWidgetList.at(j).toHash();
        QString secondValue = subWidgetHash.value(secondKey).toString();

        if (secondValue == value)
        {
            ret = subWidgetHash;
            break;
        }
    }

    return ret;
}

QMTListViewModelIndex QMTListModel::ModelIndex(QString key, QVariant value)
{
    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList removeSubList;

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString tempValue = subrecordHash.value(key).toString();

            if (value == tempValue)
            {
                QMTListViewModelIndex modelIndex(j, i1, false);
                return modelIndex;
            }
        }
    }

    return QMTListViewModelIndex();
}
QList<QVariantHash>QMTListModel::valueList(int level)
{
    QList<QVariantHash> retList;

    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        retList.append(_listDataModel.at(i));
    }

    return retList;
}
QList<QVariantHash>QMTListModel::valueList(QList<QMTListViewModelIndex>& modelList)
{
    QList<QVariantHash> retList;

    for (int i = 0; i < modelList.size(); ++i)
    {
        QMTListViewModelIndex modelIndex = modelList.at(i);
        int row = modelIndex.Row();
        int parentRow = modelIndex.GetParentRow();
        bool isFirstLevel = modelIndex.IsFirstLevel();

        if (parentRow >= _listDataModel.size())
            return retList;

        QMTRowRecord record = _listDataModel.at(parentRow);
        QVariantList subRecords = record.value(TOString(subs)).value<QVariantList>();

        if (row >= subRecords.size())
            return retList;

        QVariantHash subRecord = subRecords.at(row).value<QVariantHash>();//这边如果转换成QMTRowRecord会出错
        retList << subRecord;
    }

    return retList;
}
QVariant QMTListModel::value(QMTListViewModelIndex modelIndex, QString key)
{
    int row = modelIndex.Row();
    int parentRow = modelIndex.GetParentRow();
    bool isFirstLevel = modelIndex.IsFirstLevel();

    if (parentRow >= _listDataModel.size())
        return QVariant();

    QMTRowRecord record = _listDataModel.at(parentRow);
    QVariantList subRecords = record.value(TOString(subs)).value<QVariantList>();

    if (row >= subRecords.size())
        return QVariant();

    QVariantHash subRecord = subRecords.at(row).value<QVariantHash>();//这边如果转换成QMTRowRecord会出错
    return subRecord.value(key);
}

QVariantHash QMTListModel::Value(QString seriesUID)
{
    QVariantHash retValue;

    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList removeSubList;

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString tempSeriesUID = subrecordHash.value(TOString(seriesUID)).toString();

            if (seriesUID == tempSeriesUID)
            {
                QMTRowRecord::iterator it;

                for (it = recordHash.begin(); it != recordHash.end(); ++it)
                {
                    QString key = it.key();

                    if (TOString(subs) != key)
                    {
                        QVariant value = it.value();
                        retValue.insert(key, value);
                    }
                }

                QVariantHash::iterator it2;

                for (it2 = subrecordHash.begin(); it2 != subrecordHash.end(); ++it2)
                {
                    QString key = it2.key();

                    if (TOString(subs) != key)
                    {
                        QVariant value = it2.value();
                        retValue.insert(key, value);
                    }
                }
            }
        }
    }

    return retValue;
}

QJsonObject QMTListModel::valueObj(QMTListViewModelIndex modelIndex)
{
    int row = modelIndex.Row();
    int parentRow = modelIndex.GetParentRow();
    bool isFirstLevel = modelIndex.IsFirstLevel();

    if (parentRow >= _listDataModel.size())
        return QJsonObject();

    QMTRowRecord record = _listDataModel.at(parentRow);
    QVariantList subRecords = record.value(TOString(subs)).value<QVariantList>();

    if (row >= subRecords.size())
        return QJsonObject();

    QVariantHash subRecord = subRecords.at(row).value<QVariantHash>();//这边如果转换成QMTRowRecord会出错
    return QJsonObject::fromVariantHash(subRecord);
}
void QMTListModel::UpdateValue(QString seriesUID, QString key, QVariant value)
{
    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList removeSubList;

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString tempSeriesUID = subrecordHash.value(TOString(seriesUID)).toString();

            if (seriesUID == tempSeriesUID)
            {
                QMTListViewModelIndex modelIndex(j, i1, false);
                UpdateValue(modelIndex, key, value);
            }
        }
    }
}

QVariantList QMTListModel::RtValueList(QString patientID, QString seriesUID)
{
    int row = -1;
    int subRow = -1;
    QVariantList retHashList;

    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QString firstKey = _mainKeyMap.value(0);
        QString tempPatientID = recordHash.value(firstKey).toString();

        if (tempPatientID == patientID)
        {
            row = i1;
            break;
        }
    }

    if (row >= 0 && row < _listDataModel.size())
    {
        QMTRowRecord recordHash = _listDataModel.at(row);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString secondKey = _mainKeyMap.value(1);
            QString tempSeriesUID = subrecordHash.value(secondKey).toString();

            if (tempSeriesUID == seriesUID)
            {
                retHashList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();
                break;
            }
        }
    }

    return retHashList;
}

void QMTListModel::UpdateValue(QString seriesUID, QJsonObject obj)
{
    QMutexLocker locker(&_mutex);

    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList removeSubList;

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString tempSeriesUID = subrecordHash.value(TOString(seriesUID)).toString();

            if (seriesUID == tempSeriesUID)
            {
                QMTListViewModelIndex modelIndex(j, i1, false);
                UpdateValue(modelIndex, obj);
            }
        }
    }
}

void QMTListModel::UpdateValue(QMTListViewModelIndex modelIndex, QString key, QVariant value)
{
    int row = modelIndex.Row();
    int parentRow = modelIndex.GetParentRow();
    bool isFirstLevel = modelIndex.IsFirstLevel();

    if (parentRow >= _listDataModel.size())
        return;

    QMTRowRecord record = _listDataModel.at(parentRow);
    QVariantList subRecordList = record.value(TOString(subs)).value<QVariantList>();

    if (row >= subRecordList.size())
        return;

    QVariantHash subRecord = subRecordList.at(row).value<QVariantHash>();

    if (subRecord.keys().size() > 0)
    {
        subRecord.remove(key);
        subRecord.insert(key, value);
        subRecordList[row] = subRecord;
        _listDataModel[parentRow][TOString(subs)] = subRecordList;
        emit sigUpdateValue(modelIndex, key, value);
    }
}

void QMTListModel::UpdateValue(QMTListViewModelIndex modelIndex, QJsonObject obj)
{
    int row = modelIndex.Row();
    int parentRow = modelIndex.GetParentRow();
    bool isFirstLevel = modelIndex.IsFirstLevel();

    if (row < 0 || parentRow < 0)
        return;

    if (parentRow >= _listDataModel.size())
        return;

    QMTRowRecord record = _listDataModel.at(parentRow);
    QVariantList subRecordList = record.value(TOString(subs)).value<QVariantList>();

    if (row >= subRecordList.size())
        return;

    QVariantHash subRecord = subRecordList.at(row).value<QVariantHash>();

    if (subRecord.keys().size() > 0)
    {
        QJsonObject::iterator it;

        for (it = obj.begin(); it != obj.end(); ++it)
        {
            QString key = it.key();
            QVariant value = it.value();
            //subRecord.remove(key);
            subRecord.insert(key, value);
        }

        subRecordList[row] = subRecord;
        record[TOString(subs)] = subRecordList;
        _listDataModel[parentRow] = record;
        emit sigUpdateValue(modelIndex, obj);
    }
}

void QMTListModel::UpdateModelValue(QMTListViewModelIndex modelIndex, QJsonObject updateObj)
{
    int row = modelIndex.Row();
    int parentRow = modelIndex.GetParentRow();
    bool isFirstLevel = modelIndex.IsFirstLevel();
    bool update = false;

    if (row < 0 || parentRow < 0)
        return;

    if (parentRow >= _listDataModel.size())
        return;

    QMTRowRecord record = _listDataModel.at(parentRow);
    QVariantList subRecordList = record.value(TOString(subs)).value<QVariantList>();

    if (row >= subRecordList.size())
        return;

    QVariantHash subRecord = subRecordList.at(row).value<QVariantHash>();

    if (modelIndex._type == Series_Normal)
    {
        if (subRecord.keys().size() > 0)
        {
            QJsonObject::iterator it;

            for (it = updateObj.begin(); it != updateObj.end(); ++it)
            {
                QString key = it.key();
                QVariant value = it.value();

                if (TOString(patientID) == key || TOString(seriesUID) == key)
                    continue;

                //subRecord.remove(key);
                subRecord.insert(key, value);
            }

            subRecordList[row] = subRecord;
            record[TOString(subs)] = subRecordList;
            _listDataModel[parentRow] = record;
            update = true;
        }
    }
    else if (modelIndex._type == Series_RtFile)
    {
        QVariantList rtRecordList = subRecord.value(TOString(RtFiles)).value<QVariantList>();

        if (modelIndex._secondChildIndex >= rtRecordList.size())
            return;

        QVariantHash rtRecord = rtRecordList.at(modelIndex._secondChildIndex).value<QVariantHash>();
        //update data
        QJsonObject::iterator it;
        //QVariantHash tmpUpdateObj = updateObj.toVariantHash();

        for (it = updateObj.begin(); it != updateObj.end(); ++it)
        {
            QString key = it.key();
            QVariant value = it.value();
            rtRecord.insert(key, value);
        }

        //update model
        rtRecordList[modelIndex._secondChildIndex] = rtRecord;
        subRecord[TOString(RtFiles)] = rtRecordList;
        subRecordList[row] = subRecord;
        record[TOString(subs)] = subRecordList;
        _listDataModel[parentRow] = record;
        update = true;
    }

    if (update)
        emit sigUpdateViewValue(modelIndex, updateObj);
}

void QMTListModel::UpdateRtValue(QString sopInsUID, QJsonObject updateObj)
{
    //QVariantHash tmp = updateObj.toVariantHash();
    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QMTRowRecord recordHash = _listDataModel.at(i1);
        QVariantList subRecordList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList removeSubList;

        for (int j = 0; j < subRecordList.size(); ++j)
        {
            QVariantHash subrecordHash = subRecordList.at(j).toHash();
            QString tempSeriesUID = subrecordHash.value(TOString(seriesUID)).toString();
            QVariantList rtRecordList = subrecordHash.value(TOString(RtFiles)).value<QVariantList>();

            for (int rtIndex = 0; rtIndex < rtRecordList.size(); ++rtIndex)
            {
                QVariantHash rtrecordHash = rtRecordList.at(rtIndex).toHash();
                QString tempsopInsUID = rtrecordHash.value(TOString(sopInsUID)).toString();

                if (tempsopInsUID == sopInsUID)
                {
                    QString patientID = recordHash.value(TOString(patientID)).toString();
                    QString seriesUID = subrecordHash.value(TOString(seriesUID)).toString();
                    QMTListViewModelIndex modelIndex(j, i1, false);
                    QString tmprtFileName = rtrecordHash.value(TOString(rtFileName)).toString();

                    if (tmprtFileName.toUpper().contains(QMTJsonValue::Filename_RT()))
                    {
                        modelIndex._type = Series_RtFile;
                    }

                    modelIndex._firstValue = patientID;
                    modelIndex._secondValue = seriesUID;
                    modelIndex._secondChildIndex = rtIndex;
                    modelIndex._secondChildValue = sopInsUID;
                    UpdateModelValue(modelIndex, updateObj);
                    return;
                }
            }
        }
    }
}

bool QMTListModel::UpdateFirstValue(int index, QString key, QString value)
{
    if (index >= _listDataModel.size() || index < 0)
        return false;

    QMTRowRecord widgetHash = _listDataModel.at(index);
    widgetHash.insert(key, value);
    _listDataModel[index] = widgetHash;
    return true;
}

void QMTListModel::UpdateFirstValue(QString firstValue, QString key, QString value)
{
    QString mainkey = _mainKeyMap.value(0);
    int index = -1;

    for (int i1 = 0; i1 < _listDataModel.size(); ++i1)
    {
        QVariantHash widgetHash = _listDataModel.at(i1);

        if (firstValue == widgetHash.value(mainkey).toString())
        {
            QString valueTmp = widgetHash.value(key).toString();

            if (value != valueTmp)
            {
                if (UpdateFirstValue(i1, key, value))
                {
                    index = i1;
                }

                break;
            }
        }
    }

    if (index >= 0)
    {
        emit sigUpdateFirstViewValue(index, key, value);
    }
}

void QMTListModel::SortPatientID(QStringList strList)
{
    QList<QMTRowRecord> listModelTemp;
    QMutexLocker locker(&_mutex);

    for (int i = 0; i < strList.size(); ++i)
    {
        QString value = strList.at(i);

        for (int index = 0; index < _listDataModel.size(); ++index)
        {
            QMTRowRecord record = _listDataModel.at(index);
            QString tempValue = record.value(TOString(patientID)).toString();

            if (tempValue == value)
            {
                listModelTemp.push_back(record);
                break;
            }
        }
    }

    _listDataModel.clear();
    _listDataModel = listModelTemp;
    emit sigSortPatientID(strList);
}

QVariantList QMTListModel::OrderHashByList(QString key, QVariantList hashList, QStringList strList)
{
    QVariantList retHash;

    for (int i = 0; i < strList.size(); ++i)
    {
        QString value = strList.at(i);

        for (int i = 0; i < hashList.size(); ++i)
        {
            QVariantHash hash = hashList.at(i).toHash();
            bool isCTExist = false;

            if (hash.contains(TOString(isCTExist)))
            {
                isCTExist = hash.value(TOString(isCTExist)).toBool();
            }
            else
            {
                isCTExist = true;
            }

            if (isCTExist)
            {
                QString tempvalue = hash.value(key).toString();

                if (tempvalue == value)
                {
                    retHash.append(hash);
                }
            }
        }
    }

    return retHash;
}

void QMTListModel::OrderBySeriesUID(QString patientID, QStringList seriesList)
{
    int patientIndex = FirstRowRecordIndex(patientID);

    if (patientIndex >= 0)
    {
        QMTRowRecord recordHash = _listDataModel.at(patientIndex);
        QVariantList subList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList subSortList;

        for (int i = 0; i < seriesList.size(); ++i)
        {
            QString seriesUID = seriesList.at(i);

            for (int i = 0; i < subList.size(); ++i)
            {
                QVariantHash subHash = subList.at(i).toHash();
                bool isCTExist = subHash.value(TOString(isCTExist)).toBool();

                if (isCTExist)
                {
                    QString tempvalue = subHash.value(TOString(seriesUID)).toString();

                    if (tempvalue == seriesUID)
                    {
                        subSortList.append(subHash);
                    }
                }
            }
        }

        recordHash[TOString(subs)] = subSortList;
        _listDataModel[patientIndex] = recordHash;
        emit sigSortBySeriesUID(patientID);
    }
}

QVariantList QMTListModel::GetRemainRtHash(QVariantList hashList, QList<std::pair<QString, QStringList>> seriesPairList)
{
    QVariantList retList;
    QStringList strList;

    for (int i = 0; i < seriesPairList.size(); ++i)
    {
        //QString seriesUID = seriesList.at(i);
        std::pair<QString, QStringList> seriesPair = seriesPairList.at(i);
        QString seriesUID = seriesPair.first;
        strList.append(seriesUID);
    }

    if (0 == strList.size())
        return retList;

    for (int i = 0; i < hashList.size(); ++i)
    {
        QVariantHash subHash = hashList.at(i).toHash();
        bool isCTExist = subHash.value(TOString(isCTExist)).toBool();
        //if (isCTExist)//不能判断，否则单独rt会异常
        {
            QString tempvalue = subHash.value(TOString(seriesUID)).toString();

            if (strList.indexOf(tempvalue) < 0)
            {
                retList.append(subHash);
            }
        }
    }

    return retList;
}

void QMTListModel::OrderBySeriesUID(QString patientID, QList<std::pair<QString, QStringList>> seriesPairList)
{
    int patientIndex = FirstRowRecordIndex(patientID);

    if (patientIndex >= 0)
    {
        QMTRowRecord recordHash = _listDataModel.at(patientIndex);
        QVariantList subList = recordHash.value(TOString(subs)).value<QVariantList>();
        QVariantList subSortList;
        QVariantList remainSubList = GetRemainRtHash(subList, seriesPairList);

        for (int i = 0; i < seriesPairList.size(); ++i)
        {
            //QString seriesUID = seriesList.at(i);
            std::pair<QString, QStringList> seriesPair = seriesPairList.at(i);
            QString seriesUID = seriesPair.first;

            for (int i = 0; i < subList.size(); ++i)
            {
                QVariantHash subHash = subList.at(i).toHash();
                bool isCTExist = subHash.value(TOString(isCTExist)).toBool();
                //if (isCTExist)//不能判断，否则单独rt会异常
                {
                    QString tempvalue = subHash.value(TOString(seriesUID)).toString();

                    if (tempvalue == seriesUID)
                    {
                        QVariantList rtList = subHash.value(TOString(RtFiles)).value<QVariantList>();

                        if (rtList.size() > 0)
                        {
                            QStringList rtStrList = seriesPair.second;
                            QVariantList rtHashList = OrderHashByList(TOString(sopInsUID), rtList, rtStrList);
                            subHash[TOString(RtFiles)] = rtHashList;
                        }

                        subSortList.append(subHash);
                    }
                }
            }
        }

        if (remainSubList.size() > 0)
            subSortList.append(remainSubList);

        recordHash[TOString(subs)] = subSortList;
        _listDataModel[patientIndex] = recordHash;
        emit sigSortBySeriesUID(patientID);
    }
}
void QMTListModel::FromJsonObject(QJsonObject obj)
{
    QJsonArray recordArray;

    if (obj.contains(TOString(roots)))
    {
        if (obj.value(TOString(roots)).isArray())
        {
            recordArray = obj.value(TOString(roots)).toArray();
        }
    }

    for (int i = 0; i < recordArray.size(); ++i)
    {
        QJsonObject recordObj = recordArray.at(i).toObject();
        QJsonObject::Iterator it;
        QMTRowRecord record;
        record.clear();

        for (it = recordObj.begin(); it != recordObj.end(); ++it)
        {
            QString key = it.key();
            record.SetValue(key, it.value().toVariant());
        }

        this->AddItem(record, false);
    }
}
QJsonObject QMTListModel::ToJsonObject()
{
    QJsonObject retObj;
    QJsonArray json_array;

    for (int i = 0; i < _listDataModel.size(); ++i)
    {
        QJsonObject jsonObj = QJsonObject::fromVariantHash(_listDataModel.at(i));
        json_array.append(jsonObj);
    }

    retObj.insert(TOString(rootCount), _listDataModel.size());
    retObj.insert(TOString(roots), json_array);
    return retObj;
}