<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AutoExportDialogClass</class>
 <widget class="QDialog" name="AutoExportDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>532</width>
    <height>224</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>AutoExportDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="horizontalSpacing">
      <number>16</number>
     </property>
     <item row="0" column="0">
      <widget class="MtLabel" name="mtLabel_2">
       <property name="text">
        <string>导出地址</string>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel1_1</enum>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="MtFrameEx" name="mtFrameEx">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>160</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>190</height>
        </size>
       </property>
       <property name="styleSheet">
        <string notr="true">#mtFrameEx{
	 margin-left: 1px;
 	 margin-right: 1px;
}</string>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::default_type</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <property name="spacing">
         <number>8</number>
        </property>
        <property name="leftMargin">
         <number>16</number>
        </property>
        <property name="topMargin">
         <number>8</number>
        </property>
        <property name="rightMargin">
         <number>16</number>
        </property>
        <property name="bottomMargin">
         <number>8</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <property name="spacing">
           <number>5</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <item>
             <widget class="MtRadioButton" name="mtRadioButton_scp">
              <property name="text">
               <string>远程节点</string>
              </property>
              <property name="elideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtRadioButton::radiobutton1</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QVBoxLayout" name="verticalLayout_5">
            <property name="leftMargin">
             <number>18</number>
            </property>
            <item>
             <widget class="MtComboBox" name="mtComboBox_scp">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>30</height>
               </size>
              </property>
              <property name="viewTextElideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtComboBox::combobox1</enum>
              </property>
             </widget>
            </item>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="MtPushButton" name="mtPushButton">
                <property name="text">
                 <string>添加-配置地址</string>
                </property>
                <property name="elideMode">
                 <enum>Qt::ElideRight</enum>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtPushButton::pushbutton5</enum>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </item>
           </layout>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>5</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <item>
             <widget class="MtRadioButton" name="mtRadioButton_dir">
              <property name="text">
               <string>共享文件夹</string>
              </property>
              <property name="elideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtRadioButton::radiobutton1</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <property name="spacing">
             <number>8</number>
            </property>
            <property name="leftMargin">
             <number>18</number>
            </property>
            <item>
             <widget class="MtRangeLineEdit" name="lineEdit_dir" native="true">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>30</height>
               </size>
              </property>
              <property name="placeholderText" stdset="0">
               <string>共享文件夹地址</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtPushButton" name="mtPushButton_dir">
              <property name="minimumSize">
               <size>
                <width>40</width>
                <height>30</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>40</width>
                <height>30</height>
               </size>
              </property>
              <property name="text">
               <string>...</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtPushButton::pushbutton13</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_6">
            <property name="leftMargin">
             <number>18</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="MtCheckBox" name="mtCheckBox_dir">
              <property name="text">
               <string>按患者创建子文件夹</string>
              </property>
              <property name="elideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
           </layout>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item row="1" column="0">
      <widget class="MtLabel" name="mtLabel_3">
       <property name="text">
        <string>导出格式</string>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel1_1</enum>
       </property>
      </widget>
     </item>
     <item row="1" column="1">
      <widget class="MtComboBox" name="mtComboBox_format">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>30</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>30</height>
        </size>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtComboBox::combobox1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtRadioButton</class>
   <extends>QRadioButton</extends>
   <header>MtRadioButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtRangeLineEdit</class>
   <extends>QWidget</extends>
   <header>mtrangelineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
