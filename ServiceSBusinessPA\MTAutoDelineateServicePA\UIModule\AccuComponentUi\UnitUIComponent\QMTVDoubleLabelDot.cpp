﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTVDoubleLabelDot.h"
#include "ui_QMTVDoubleLabelDot.h"
#include "CMtCoreDefine.h"
#include "AccuComponentUi\Header\QMTEnumDef.h"
#include "AccuComponentUi\Header\Language.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"


QMTVDoubleLabelDotParam::QMTVDoubleLabelDotParam()
{
    _cellWidgetType = DELEAGATE_QMTVDoubleLabelDot;
}

QWidget* QMTVDoubleLabelDotParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTVDoubleLabelDot* cellWidget = new QMTVDoubleLabelDot(parent);
    cellWidget->SetupCellWidget(*this);
    return cellWidget;
}

QMTVDoubleLabelDot::QMTVDoubleLabelDot(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTVDoubleLabelDot;
    ui->setupUi(this);
    ui->label_preFirst->hide();
    ui->label_preSecond->hide();
    //ui->label_preFirst->setFixedWidth(51);
    //ui->label_preSecond->setFixedWidth(51);
    //SetFirstQssProperty(QssPropertyLabelSecondTitle);
    //SetSecondQssProperty(QssPropertyLabelSecondTitle);
    // InitTemplateStyle();     //统一qss文件设置
}

QMTVDoubleLabelDot::~QMTVDoubleLabelDot()
{
    MT_DELETE(ui);
}

bool QMTVDoubleLabelDot::UpdateUi(const QVariant& updateData)
{
    if (updateData.canConvert<UpdateVDoubleLabelDotInfo>())
    {
        UpdateVDoubleLabelDotInfo updateInfo = updateData.value<UpdateVDoubleLabelDotInfo>();
        SetPreFirstValue(updateInfo._preFirstText);
        SetFirstValue(updateInfo._firstText);
        SetPreSecondValue(updateInfo._preSecondText);
        SetSecondValue(updateInfo._secondText);
    }

    return false;
}

void QMTVDoubleLabelDot::SetupCellWidget(QMTVDoubleLabelDotParam& cellWidgetParam)
{
    //前言部分
    if (true == cellWidgetParam._bShowPreLabel)
    {
        //设置前言样式
        QString preStyleSheetStr;
        QString textColor;
        int rgb[3];
        rgb[0] = cellWidgetParam._preBackgroundColor.red();
        rgb[1] = cellWidgetParam._preBackgroundColor.green();
        rgb[2] = cellWidgetParam._preBackgroundColor.blue();
        double alpha = cellWidgetParam._preBackgroundColor.alpha();
        alpha = alpha / 255;
        //textColor = GetBackgroundStyleSheetStr(QString::number(rgb[0]), QString::number(rgb[1]), QString::number(rgb[2]), QString::number(alpha, 'f', 1));
        rgb[0] = cellWidgetParam._preLableTextColor.red();
        rgb[1] = cellWidgetParam._preLableTextColor.green();
        rgb[2] = cellWidgetParam._preLableTextColor.blue();
        alpha = cellWidgetParam._preLableTextColor.alpha();
        alpha = alpha / 255;
        textColor += GetFontStyleSheetStr(cellWidgetParam._preLabelFontSize, QString::number(rgb[0]), QString::number(rgb[1]), QString::number(rgb[2]), QString::number(alpha, 'f', 1));
        //textColor += "border-style:outset;border-radius:4px;";
        preStyleSheetStr = "#label_preFirst,#label_preSecond{" + textColor + "}";
        SetPreLableStyleSheet(preStyleSheetStr);

        //设置前言文案的label
        if (cellWidgetParam._preLabelWidth > 0)
        {
            ui->label_preFirst->setFixedWidth(cellWidgetParam._preLabelWidth);
            ui->label_preSecond->setFixedWidth(cellWidgetParam._preLabelWidth);
        }

        if (cellWidgetParam._labelHeight > 0)
        {
            ui->label_preFirst->setFixedHeight(cellWidgetParam._labelHeight);
            ui->label_preSecond->setFixedHeight(cellWidgetParam._labelHeight);
        }

        SetPreFirstValue(cellWidgetParam._preFirstText);
        SetPreSecondValue(cellWidgetParam._preSecondText);
        //显示
        ui->label_preFirst->show();
        ui->label_preSecond->show();
    }

    //正文部分
    QString styleSheetStr;
    QString textColor;
    int rgb[3];
    rgb[0] = cellWidgetParam._lableTextColor.red();
    rgb[1] = cellWidgetParam._lableTextColor.green();
    rgb[2] = cellWidgetParam._lableTextColor.blue();
    double alpha = cellWidgetParam._lableTextColor.alpha();
    alpha = alpha / 255;
    textColor += GetFontStyleSheetStr(cellWidgetParam._labelTopFontSize, QString::number(rgb[0]), QString::number(rgb[1]), QString::number(rgb[2]), QString::number(alpha, 'f', 1));
    styleSheetStr = "#label_first{" + textColor + "}";
    ui->label_first->setStyleSheet(styleSheetStr);
    //
    textColor.clear();
    rgb[0] = cellWidgetParam._lableTextColor.red();
    rgb[1] = cellWidgetParam._lableTextColor.green();
    rgb[2] = cellWidgetParam._lableTextColor.blue();
    alpha = cellWidgetParam._lableTextColor.alpha();
    alpha = alpha / 255;
    textColor += GetFontStyleSheetStr(cellWidgetParam._labelBottomFontSize, QString::number(rgb[0]), QString::number(rgb[1]), QString::number(rgb[2]), QString::number(alpha, 'f', 1));
    styleSheetStr = "#label_second{" + textColor + "}";
    ui->label_second->setStyleSheet(styleSheetStr);
    //QString styleSheetStr;
    //QString textColor;
    //int rgb[3];
    //rgb[0] = cellWidgetParam._lableTextColor.red();
    //rgb[1] = cellWidgetParam._lableTextColor.green();
    //rgb[2] = cellWidgetParam._lableTextColor.blue();
    //double alpha = cellWidgetParam._lableTextColor.alpha();
    //alpha = alpha / 255;
    //textColor += GetFontStyleSheetStr(cellWidgetParam._preLabelFontSize, QString::number(rgb[0]), QString::number(rgb[1]), QString::number(rgb[2]), QString::number(alpha, 'f', 1));
    //styleSheetStr = "#label_first,#label_second{" + textColor + ";}";
    //SetPreLableStyleSheet(styleSheetStr);

    if (cellWidgetParam._labelHeight > 0)
    {
        ui->label_first->setFixedHeight(cellWidgetParam._labelHeight);
        ui->label_second->setFixedHeight(cellWidgetParam._labelHeight);
    }

    if (cellWidgetParam._verSpacing > 0)
    {
        ui->widget_cellContent->layout()->setSpacing(cellWidgetParam._verSpacing);
    }

    this->SetFirstValue(cellWidgetParam._firstText);
    this->SetSecondValue(cellWidgetParam._secondText);

    if (cellWidgetParam._styleSheetStr.size() > 0)
    {
        this->SetCustStyleSheet(cellWidgetParam._styleSheetStr);
    }
}

void QMTVDoubleLabelDot::SetPreFirstValue(const QString& text)
{
    ui->label_preFirst->setText(text);
}

void QMTVDoubleLabelDot::SetPreSecondValue(const QString& text)
{
    ui->label_preSecond->setText(text);
}

void QMTVDoubleLabelDot::SetFirstValue(const QString& text)
{
    //ui->label->setText(text);
    ui->label_first->setTextElided(text);
}

void QMTVDoubleLabelDot::SetSecondValue(const QString& text)
{
    //ui->label_2->setText(text);
    ui->label_second->setTextElided(text);
}

void QMTVDoubleLabelDot::SetPreLableStyleSheet(const QString& styleSheetStr)
{
    ui->label_preFirst->setStyleSheet(styleSheetStr);
    ui->label_preSecond->setStyleSheet(styleSheetStr);
}

void QMTVDoubleLabelDot::SetContentLableStyleSheet(const QString& styleSheetStr)
{
    ui->label_first->setStyleSheet(styleSheetStr);
    ui->label_second->setStyleSheet(styleSheetStr);
}

void QMTVDoubleLabelDot::SetCustStyleSheet(const QString& styleStr)
{
}

void QMTVDoubleLabelDot::SetFirstQssProperty(const QString& value)
{
    ui->label_first->setProperty(QssPropertyKey, value);
}

void QMTVDoubleLabelDot::SetSecondQssProperty(const QString& value)
{
    ui->label_second->setProperty(QssPropertyKey, value);
}

//void QMTVDoubleLabelDot::SetMode(QJsonObject& obj)
//{
//    if (obj.contains(TOString(QMTVDoubleLabelDot)) == false)
//        return;
//
//    QJsonObject subObj = obj.value(TOString(QMTVDoubleLabelDot)).toObject();
//
//    if (subObj.contains(TOString(value_1)))
//    {
//        QString text = subObj.value(TOString(value_1)).toString();
//        SetFirstValue(text);
//    }
//
//    if (subObj.contains(TOString(value_2)))
//    {
//        QString text = subObj.value(TOString(value_2)).toString();
//        SetSecondValue(text);
//    }
//}

void QMTVDoubleLabelDot::InitTemplateStyle()
{
    QString textColor;
    QString styleSheetStr;
    int fontSize = 14;

    if (Language::type == English)
    {
        fontSize = 12;
    }

    textColor += QString("color:rgba(219, 226, 241, 1);font-size:%1px;").arg(fontSize);
    styleSheetStr = "#label_first,#label_second{" + textColor + ";}";
    this->setStyleSheet(styleSheetStr);
}


