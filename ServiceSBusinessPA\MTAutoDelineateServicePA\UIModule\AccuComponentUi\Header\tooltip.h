﻿#ifndef CTOOLTIP_H
#define CTOOLTIP_H

#include <QWidget>
#include <QLabel>
#include <QPushButton>
#include <QGroupBox>
#include <QVBoxLayout>
#include <QHBoxLayout>


class  CToolTip : public QWidget
{
    Q_OBJECT
public:
    explicit CToolTip(QWidget* parent = 0);
    explicit CToolTip(QString title, QString msg, QWidget* parent = 0);
    ~CToolTip();
    void showMessage(QString& title, QString& info, QPoint& point);
    void showStop();
    void setMaxWidth(int width);
    void setTitleDisplayStatus(bool isHidden);

private:
    QLabel* labelTitle;
    QLabel* labelInfo;
    bool foreverStand = false;
    int m_screenWidth;
//protected:
//    void hoverEvent(QHoverEvent*);
};

#endif // CTOOLTIP_H