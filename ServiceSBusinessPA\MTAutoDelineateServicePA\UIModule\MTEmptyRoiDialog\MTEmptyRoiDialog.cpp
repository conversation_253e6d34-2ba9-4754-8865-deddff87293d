﻿#include "MTEmptyRoiDialog.h"
#include "ui_MTEmptyRoiDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MTEmptyRoiDialog\EmptyRoiSub\EmptyRoiWidget.h"
#include "CommonUtil.h"
#include "MtMessageBox.h"

namespace n_mtautodelineationdialog
{

MTEmptyRoiDialog::MTEmptyRoiDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTEmptyRoiDialogClass;
    ui->setupUi(this);
    //基本属性
    this->setMainLayout(ui->verticalLayout);            //设置布局
    this->setDialogWidthAndContentHeight(902, 456);    //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("空勾画设置"));                  //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    //getWidgetButton()->hide();                        //如果不想右下角有按钮
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(false);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTEmptyRoiDialog, " << errMsg.toStdString();
        }
    }
    //
    this->getButton(MtTemplateDialog::BtnRight1)->installEventFilter(this);
}

MTEmptyRoiDialog::~MTEmptyRoiDialog()
{
}

QDialog::DialogCode MTEmptyRoiDialog::showEmptyOrganSettingDlg(const QStringList& allRoiTypeList, const QStringList& allLabelList
                                                               , const QList<ST_Organ>& stOrganList
                                                               , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList
                                                               , QList<ST_Organ>& outStOrganList
                                                               , const ST_CallBack_EmptyRoiSetting& cb)
{
    EmptyRoiWidget* emptyRoiWidget = new EmptyRoiWidget(this);
    emptyRoiWidget->setImagePathHash(m_imagePathHash);
    emptyRoiWidget->init(allRoiTypeList.isEmpty() == true ? CommonUtil::getRoiTypeList() : allRoiTypeList, allLabelList, stOrganList, modelCollectionInfoList);
    ui->verticalLayout_2->addWidget(emptyRoiWidget);
    connect(emptyRoiWidget, &EmptyRoiWidget::sigRemoveRoi, this, [&](int roiID, const QString& roiName)
    {
        emit sigRemoveRoi(roiID, roiName);

        if (cb.cbRemoveRoi != nullptr)
        {
            cb.cbRemoveRoi(roiID, roiName);
        }
    });

    if (this->exec() == QDialog::Accepted)
    {
        outStOrganList = emptyRoiWidget->getAllEmptyOrganInfo();

        for (int i = 0; i < outStOrganList.size(); i++)
        {
            if (outStOrganList[i].id < 0)
            {
                outStOrganList[i].defaultColor = outStOrganList[i].customColor;
                outStOrganList[i].defaultOrganName = outStOrganList[i].customOrganName;
                outStOrganList[i].organChineseName = ("空勾画");
            }
        }

        return QDialog::Accepted;
    }

    return QDialog::Rejected;
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTEmptyRoiDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

/// <summary>
/// 关闭按钮
/// </summary>
void MTEmptyRoiDialog::onBtnCloseClicked()
{
    EmptyRoiWidget* widget = nullptr;

    for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
    {
        QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

        if (item)
        {
            widget = qobject_cast<EmptyRoiWidget*>(item->widget());

            if (nullptr != widget)
            {
                break;
            }
        }
    }

    bool bNeedSave2File = false;

    if (nullptr != widget)
    {
        bNeedSave2File = widget->isNeedSave2File();
    }

    if (/*this->getButton(MtTemplateDialog::BtnRight1)->isEnabled()*/bNeedSave2File && QMessageBox::Yes == MtMessageBox::NoIcon::question_Title(this, tr("是否保存本次修改？")))
    {
        this->accept();
    }
    else
    {
        this->reject();
    }
}

/// <summary>
/// 取消按钮
/// </summary>
void MTEmptyRoiDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void MTEmptyRoiDialog::onBtnRight1Clicked()
{
    EmptyRoiWidget* widget = nullptr;

    for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
    {
        QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

        if (item)
        {
            widget = qobject_cast<EmptyRoiWidget*>(item->widget());

            if (nullptr != widget)
            {
                break;
            }
        }
    }

    if (widget == nullptr)
    {
        return;
    }

    QSet<QString> roiNameSet;
    QList<n_mtautodelineationdialog::ST_Organ> tempList = widget->getAllEmptyOrganInfo();

    for (int i = 0; i < tempList.size(); i++)
    {
        if (tempList[i].customOrganName.isEmpty())
        {
            MtMessageBox::yellowWarning(this->window(), QString(tr("ROI名称不允许为空")), QString());
            return;
        }

        if (roiNameSet.contains(tempList[i].customOrganName.toLower()) == true)
        {
            MtMessageBox::yellowWarning(this->window(), QString(tr("ROI名称不允许重复")), QString());
            return;
        }

        if (tempList[i].id < 0)
        {
            tempList[i].defaultColor = tempList[i].customColor;
            tempList[i].defaultOrganName = tempList[i].customOrganName;
            //tempList[i].organChineseName = ("空勾画");
        }

        roiNameSet.insert(tempList[i].customOrganName.toLower());
    }

    this->accept();
}

bool MTEmptyRoiDialog::eventFilter(QObject* obj, QEvent* event)
{
    //点击保存和取消按钮时，让焦点设置到列表上，使列表输入框失去焦点而触发编辑完成事件
    if (obj == getButton(MtTemplateDialog::BtnRight1))
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent* mouse_event = static_cast<QMouseEvent*>(event);

            if (mouse_event->button() == Qt::LeftButton)
            {
                for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
                {
                    QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

                    if (item->widget())
                    {
                        EmptyRoiWidget* roiWidget = qobject_cast<EmptyRoiWidget*>(item->widget());

                        if (roiWidget)
                        {
                            roiWidget->removeFocusFromTable();
                        }
                    }
                }
            }
        }
    }

    return QWidget::eventFilter(obj, event);
}

}
