﻿#include <QDebug>
#include "AccuComponentUi\Header\UnitUIComponent\QMTAbsLineEdit.h"


QMTAbsLineEdit::QMTAbsLineEdit(QWidget* parent)
    : QLineEdit(parent)
{
    this->setReadOnly(true);//默认只读
    //setAttribute(Qt::WA_TransparentForMouseEvents);
    // 1. 编辑完成，光标靠左
    connect(this, &QLineEdit::editingFinished, this, [&]
    {
        qDebug() << "this->hasFocus()" << this->hasFocus();

        if (!this->hasFocus())
        {
            this->setCursorPosition(0);
        }
        this->setReadOnly(true);
        emit sigEditFinish(this->text());

    });
}


QMTAbsLineEdit::QMTAbsLineEdit(const QString& contents, QWidget* parent)
    : QLineEdit(contents, parent)
{
    this->setReadOnly(true);//默认只读
    //setAttribute(Qt::WA_TransparentForMouseEvents);
    // 1. 编辑完成，光标靠左
    connect(this, &QLineEdit::editingFinished, this, [&]
    {
        if (!this->hasFocus())
        {
            this->setCursorPosition(0);
        }
        this->setReadOnly(true);
        emit sigEditFinish(this->text());
    });
}

QMTAbsLineEdit::~QMTAbsLineEdit()
{
    if (_validator)
    {
        delete _validator;
        _validator = nullptr;
    }
}

void QMTAbsLineEdit::SetEnableEdit(bool enableEdit)
{
    setReadOnly(true);
    _isDClickEdit = enableEdit;
}


void QMTAbsLineEdit::SetRegExpStr(QString& regExpStr)
{
    if (0 == regExpStr.size())
        return;

    QRegExp regExp(regExpStr);
    _validator = new QRegExpValidator(regExp, this);
    this->setValidator(_validator);
}


void QMTAbsLineEdit::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (false == _isDClickEdit)
    {
        QLineEdit::mouseDoubleClickEvent(event);
    }
    else
    {
        this->setReadOnly(false);
        this->setCursorPosition(this->text().length());
        this->setFocus();
    }
}


void QMTAbsLineEdit::UpdateNewTextDot()
{
}

/// <summary>
/// program设置text，光标靠左
/// </summary>
/// <param name=""></param>
void QMTAbsLineEdit::setText(const QString& text)
{
    _fullString = text;
    QLineEdit::setText(text);//this->GetElidedText(this->font(),text,this->width()) 超出width的文本，为...，但会影响text，从而影响编辑

    if (this->isEnabled())
        this->setCursorPosition(this->text().length());
    else
        this->setCursorPosition(0);
}

/// <summary>
/// 使能后，光标位置重新靠右，禁能靠左
/// </summary>
/// <param name="event"></param>
void QMTAbsLineEdit::setEnabled(bool enable)
{
    if (enable)
        this->setCursorPosition(this->text().length());
    else
        this->setCursorPosition(0);

    QWidget::setEnabled(enable);
}

void QMTAbsLineEdit::setReadOnly(bool readOnly)
{
    if (readOnly)
    {
        _fullString = this->text();
        this->setCursorPosition(_fullString.length());
    }
    else
        this->setCursorPosition(0);

    QLineEdit::setReadOnly(readOnly);
}
