﻿#include "AccuComponentUi\Header\QMTButtonMenuWidget.h"
#include "ui_QMTButtonMenuWidget.h"
#include "CMtCoreDefine.h"
#include <qDebug>
#include "AccuComponentUi\Header\QMTAbstractMenu.h"


QMTButtonMenuWidgetParam::QMTButtonMenuWidgetParam()
{
    _cellWidgetType = DELEAGATE_QMTButtonMenu;
}

//QJsonObject QMTButtonMenuWidgetParam::ToJson()
//{
//    QJsonObject retObj;
//    retObj.insert("textList", _textList.join(DIV_STR));
//    retObj.insert("iconList", _iconList.join(DIV_STR));
//    retObj.insert("curIndex", _curIndex);
//    return retObj;
//}
//
//void QMTButtonMenuWidgetParam::ParseFromJson(QJsonObject& obj)
//{
//    this->_textList = obj.value("textList").toString().split(DIV_STR);
//    this->_iconList = obj.value("iconList").toString().split(DIV_STR);
//    this->_curIndex = obj.value("curIndex").toInt();
//}

QWidget* QMTButtonMenuWidgetParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTButtonMenuWidget* btnMenu = new QMTButtonMenuWidget(parent);
    btnMenu->AddItems(this->_iconList, this->_textList);
    btnMenu->SetCurIndex(this->_curIndex);
    return btnMenu;
}



QMTButtonMenuWidget::QMTButtonMenuWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTButtonMenuWidget;
    ui->setupUi(this);
    _selectMenu = new QMTAbstractMenu(this);
    connect(_selectMenu, SIGNAL(sigTriggered(int)), this, SLOT(slotSelectMenuTriggered(int)));
    ui->label_btn->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_comboBox.png"));
#if 0
    //con
    QStringList textList;
    textList << "1" << "2" << "3" << "4" << "5";
    AddItems(textList);
#endif
}

QMTButtonMenuWidget::~QMTButtonMenuWidget()
{
    MT_DELETE(ui);
}

bool QMTButtonMenuWidget::UpdateUi(const QVariant& updateData)
{
    return false;
}

void QMTButtonMenuWidget::AddItems(QStringList textList)
{
    _textList = textList;

    for (int i = 0; i < textList.size(); ++i)
    {
        _selectMenu->AddAction(textList.at(i));
    }

    int width = GetMaxStrWidth(textList) + 10;//这个就获得了字符串所占的像素宽度
    int itemWidth = this->width();

    if (width > this->width())
    {
        itemWidth = width;
    }

    _selectMenu->SetMenuFixedWidth(itemWidth);
    ui->label_icon->hide();
}

void QMTButtonMenuWidget::AddItems(QStringList iconPathList, QStringList textList)
{
    if (0 == iconPathList.size() || iconPathList.size() != textList.size())
    {
        AddItems(textList);
        return;
    }

    if (0 == iconPathList.size() ||
        0 == textList.size())
        return;

    _iconPathList = iconPathList;
    _textList = textList;

    for (int i = 0; i < textList.size(); ++i)
    {
        QString iconPath = iconPathList.at(i);
        _selectMenu->AddAction(QIcon(iconPath), textList.at(i));
    }

    int width = GetMaxStrWidth(textList) + 24;//这个就获得了字符串所占的像素宽度
    int itemWidth = this->width();

    if (width > this->width())
    {
        itemWidth = width;
    }

    _selectMenu->SetMenuFixedWidth(itemWidth);
    ui->label_icon->show();
}

void QMTButtonMenuWidget::SetCurIndex(int index)
{
    if (index < 0)
        return;

    if (index < _textList.size())
    {
        QString text = _textList.at(index);
        // ui->label_text->setText(text);
        ui->label_text->setTextElided(text);
    }

    if (index < _iconPathList.size())
    {
        QString iconPath = _iconPathList.at(index);
        ui->label_icon->setPixmap(QPixmap(iconPath));
    }
}

void QMTButtonMenuWidget::SetCurText(QString text)
{
    int index = _textList.indexOf(text);
    SetCurIndex(index);
}

void QMTButtonMenuWidget::mousePressEvent(QMouseEvent* event)
{
    QPoint pos = ui->menuWidgetBK->pos();//取子窗口的相对坐标
    int x = pos.x();
    int y = pos.y();
    pos.setY(y + this->geometry().height());
    //qDebug() << "x:" << x << ",y: " << y;
    //qDebug() << "global, x:" << this->mapToGlobal(pos).x() << ",y: " << this->mapToGlobal(pos).y();
    emit sigClicked(0);
    int width = this->width();
    _selectMenu->SetMenuFixedWidth(width);
    _selectMenu->PopMenu(this->mapToGlobal(pos));//mapToGlobal需要由父窗口调用
}

int QMTButtonMenuWidget::GetMaxStrWidth(const QStringList& textList)
{
    QString maxText = textList.at(0);

    //获取最大的字符串
    for (int i = 1; i < textList.size(); ++i)
    {
        if (maxText.size() < textList.at(i).size())
        {
            maxText = textList.at(i);
        }
    }

    QFont font = this->font();
    font.setPointSize(12);
    QFontMetrics fm(font);
    QRect rec = fm.boundingRect(maxText);
    int width = rec.width();//这个就获得了字符串所占的像素宽度
    return width;
}

void QMTButtonMenuWidget::slotSelectMenuTriggered(int index)
{
    if (_preIndex == index)
        return;

    int preIndex = _preIndex;
    QString preText = _textList[preIndex];
    QString curText = _textList[index];
    _preIndex = index;
    SetCurIndex(index);
    emit sigCurrentIndexChanged(index);
    emit sigCurrentTextChanged(curText);
}
