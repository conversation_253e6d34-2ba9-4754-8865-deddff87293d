﻿//*****************************************************************************
// Medical Imaging Solutions
// Contact number 17317554131
// xinbo.fu.
//
// Filename: ProgressDialog.h
//
//*****************************************************************************

#ifndef __ProgressDialog_h
#define __ProgressDialog_h

#include <QDialog>
#include <QString>

#include "Language.h"

class DarkeningWidget;

/**
 * @class ProgressDialog
 * <AUTHOR>
 * @brief Displays a modal progress dialog with configurable message text.
 *
 * This dialog contains one paragraph of message text and a progress bar. The message text can be set.
 * This dialog does not handle the translation of message text. The minimum and maximum values for the
 * progress bar can be set and the progress bar will display the percentage of the range between min
 * and max that has been accomplished.
 *
 * Usage:
 * ProgressDialog progressDialog;
 * progressDialog.show();
 * progressDialog.setRange(min, max);
 * while (doing something that takes a long time)
 * {
 *      do a portion of the task
 *      progressDialog.setValue(some value between min and max)
 * }
 * progressDialog.hide();
 *
 */
namespace Ui
{
class ProgressDialog;
};

class  ProgressDialog : public QDialog
{
    Q_OBJECT

public:
    //Construction, destruction
    ProgressDialog(QWidget* parent = 0);
    ProgressDialog(bool connectSlotPushButtonClose, QWidget* parent = nullptr);
    virtual ~ProgressDialog();

    //configurable text
    void setMessage(const QString& text);
    void setImportMessage();
    void setSegmentMessage();

    // Cancel button
    void showCancelButton(bool show);
    void enableCancelButton(bool enable); // 使能取消按钮

    //progress bar value
    void setRange(int min, int max);
    void setValue(int value);
    void hideProgress(bool); // 隐藏进度条
    void setButtonTest(const QString& text);// 原先错误的名称，后面会删除，建议使用setCancelButtonText
    void setCancelButtonText(const QString& text);
    int value();
    virtual void hideDialog(bool hide = true); // 隐藏dialog和darkenWidget

signals:
    void progressCancelled();

public slots:
    void changeValue(int value);
    void slotPushButtonClose(); // 会关闭窗体 this->Close()

protected:
    void Initialize(bool connectSlotPushButtonClose = true);
    virtual void showEvent(QShowEvent* show);
    virtual void hideEvent(QHideEvent* hide);
    void keyPressEvent(QKeyEvent* event) override;

private:
    DarkeningWidget* _darkeningWidget;
    Ui::ProgressDialog* ui;
};

#endif
