﻿// *********************************************************************************
// <remarks>
// FileName    : MTEmptyRoiDialog
// Author      : zlw
// CreateTime  : 2023-10-30
// Description : 空勾画设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include <iostream>
#include "MtTemplateDialog.h"
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTEmptyRoiDialogClass;
}

namespace n_mtautodelineationdialog
{

/// <summary>
/// 空勾画设置界面
/// </summary>
class MTEmptyRoiDialog : public MtTemplateDialog, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    MTEmptyRoiDialog(QWidget* parent = nullptr);
    ~MTEmptyRoiDialog();

    /// <summary>
    /// 显示空勾画设置弹窗
    /// </summary>
    /// <param name="allRoiTypeList">[IN]Roi类型集合(如果为空将采用内置的类型集合)</param>
    /// <param name="allLabelList">[IN]label标签集合</param>
    /// <param name="stOrganList">[IN]所有的空勾画器官信息</param>
    /// <param name="modelCollectionInfoList">[IN]所有的模板信息</param>
    /// <param name="outStOrganList">[OUT]输出修改后的空勾画器官信息</param>
    /// <param name="cb">[IN]回调处理，与信号处理二选一即可</param>
    /// <returns>QDialog::DialogCode</returns>
    QDialog::DialogCode showEmptyOrganSettingDlg(const QStringList& allRoiTypeList, const QStringList& allLabelList
                                                 , const QList<ST_Organ>& stOrganList
                                                 , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList
                                                 , QList<ST_Organ>& outStOrganList
                                                 , const ST_CallBack_EmptyRoiSetting& cb);

signals:
    /*********************************调用者处理，与回调方式二选一即可***************************************/
    /// <summary>
    /// 空勾画被删除
    /// </summary>
    /// <param name="roiID">[OUT]roiID</param>
    /// <param name="roiName">[OUT]roi名称</param>
    /// <returns></returns>
    void sigRemoveRoi(int roiID, const QString& roiName);

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

protected:
    virtual void onBtnCloseClicked() override;  //关闭按钮
    virtual void onBtnRight2Clicked() override; //取消按钮
    virtual void onBtnRight1Clicked() override; //确认按钮
    virtual bool eventFilter(QObject* obj, QEvent* event) override;

private:
    Ui::MTEmptyRoiDialogClass* ui;
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)
};

}
