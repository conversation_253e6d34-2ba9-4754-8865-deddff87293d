<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RoiLibraryHelp</class>
 <widget class="QWidget" name="RoiLibraryHelp">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>461</width>
    <height>149</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RoiLibraryHelp</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QVBoxLayout" name="verticalLayout">
      <item>
       <widget class="MtLabel" name="mtLabel">
        <property name="text">
         <string>模型和ROI设置维护系统内置ROI、模型导入ROI以及空勾画的相关信息，维护ROI名称、标签、颜色、分组、描述、后处理参数等；</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtLabel" name="mtLabel_2">
        <property name="text">
         <string>支持添加自定义分组以便于您更好地对ROI进行管理与选择；</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtLabel" name="mtLabel_3">
        <property name="text">
         <string>支持添加ROI描述信息，描述信息可以在区分不同来源的ROI、区分同名ROI等场景下为您提供帮助；</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtLabel" name="mtLabel_4">
        <property name="text">
         <string>分组及描述信息将在自动勾画模板中得到应用。</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
