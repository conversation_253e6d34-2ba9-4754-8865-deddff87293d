﻿#include "RoiCodeSettingDialog.h"
#include "MtRangeLineEdit.h"
#include "MtToolButton.h"
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"


RoiCodeSettingDialog::RoiCodeSettingDialog(const QString& manteiaRoiLabel, const QMap<n_mtautodelineationdialog::EM_Manufacturer, QMap<QString, QString>> roiCodeMap, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setTitle(tr("编码关系设置"));                 //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    this->setDialogWidthAndContentHeight(466, 260); //弹窗的宽度 内容的高度
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(false);
    //初始化数据
    m_manteiaRoiLabel = manteiaRoiLabel;
    //初始化UI
    {
        ui.lineEdit_label->setRegExpression(RegExp_CharNumber4);
        ui.lineEdit_code->setRegExpression(RegExp_CharNumber4);
        ui.lineEdit_observationLabel->setRegExpression(RegExp_CharNumber4);
        ui.lineEdit_schemeVersion->setRegExpression(RegExp_CharNumber2);
        ui.lineEdit_codeMeaning->setRegExpression(RegExp_CharNumber4);
        ui.lineEdit_label->setText(manteiaRoiLabel);

        if (roiCodeMap.contains(n_mtautodelineationdialog::Manufacturer_Eclipse) == true && roiCodeMap[n_mtautodelineationdialog::Manufacturer_Eclipse].isEmpty() == false)
        {
            m_mapTapValue = roiCodeMap[n_mtautodelineationdialog::Manufacturer_Eclipse];
            ui.lineEdit_code->setText(m_mapTapValue["code"]);
            ui.lineEdit_observationLabel->setText(m_mapTapValue["label"]);
            ui.lineEdit_schemeVersion->setText(m_mapTapValue["codeSchemeVer"]);
            ui.lineEdit_codeMeaning->setText(m_mapTapValue["codeMean"]);
            ui.mtComboBox_scheme->setCurrentText(m_mapTapValue["codeScheme"]);
        }

        connect(ui.lineEdit_label, SIGNAL(textChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
        connect(ui.lineEdit_code, SIGNAL(textChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
        connect(ui.lineEdit_observationLabel, SIGNAL(textChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
        connect(ui.lineEdit_schemeVersion, SIGNAL(textChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
        connect(ui.lineEdit_codeMeaning, SIGNAL(textChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
        connect(ui.mtComboBox_scheme, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
    }
}

RoiCodeSettingDialog::~RoiCodeSettingDialog()
{
}

/// <summary>
///  获取最新的RoiCode编码集合
/// </summary>
/// <param name="outManteiaRoiLabel">[OUT]Manteia-ROI标签名</param>
/// <param name="outRoiCodeMap">[OUT]最新的RoiCode</param>
void RoiCodeSettingDialog::getNewRoiCode(QString& outManteiaRoiLabel, QMap<n_mtautodelineationdialog::EM_Manufacturer, QMap<QString, QString>>& outRoiCodeMap)
{
    outManteiaRoiLabel = ui.lineEdit_label->text();
    m_mapTapValue["code"] = ui.lineEdit_code->text();
    m_mapTapValue["label"] = ui.lineEdit_observationLabel->text();
    m_mapTapValue["codeSchemeVer"] = ui.lineEdit_schemeVersion->text();
    m_mapTapValue["codeMean"] = ui.lineEdit_codeMeaning->text();
    m_mapTapValue["codeScheme"] = ui.mtComboBox_scheme->currentText();
    outRoiCodeMap.insert(n_mtautodelineationdialog::Manufacturer_Eclipse, m_mapTapValue);
}

/// <summary>
/// 关闭按钮
/// </summary>
void RoiCodeSettingDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void RoiCodeSettingDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void RoiCodeSettingDialog::onBtnRight1Clicked()
{
    ui.lineEdit_label->setRegExpression(RegExp_CharNumber2);
    ui.lineEdit_code->setRegExpression(RegExp_CharNumber2);
    ui.lineEdit_observationLabel->setRegExpression(RegExp_CharNumber2);
    ui.lineEdit_schemeVersion->setRegExpression(RegExp_CharNumber2);
    ui.lineEdit_codeMeaning->setRegExpression(RegExp_CharNumber2);

    if (ui.lineEdit_label->text().isEmpty())
    {
        ui.lineEdit_label->setWarningBorderStatus(tr("未填写ManteiaLabel"));
        return;
    }

    if (ui.lineEdit_code->text().isEmpty())
    {
        ui.lineEdit_code->setWarningBorderStatus(tr("未填写Code"));
        return;
    }

    if (ui.lineEdit_observationLabel->text().isEmpty())
    {
        ui.lineEdit_observationLabel->setWarningBorderStatus(tr("未填写Label"));
        return;
    }

    if (ui.lineEdit_schemeVersion->text().isEmpty())
    {
        ui.lineEdit_schemeVersion->setWarningBorderStatus(tr("未填写SchemeVersion"));
        return;
    }

    if (ui.lineEdit_codeMeaning->text().isEmpty())
    {
        ui.lineEdit_codeMeaning->setWarningBorderStatus(tr("未填写CodeMeaning"));
        return;
    }

    this->accept();
}

void RoiCodeSettingDialog::slotValueChanged(const QString& text)
{
    this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(true);
}