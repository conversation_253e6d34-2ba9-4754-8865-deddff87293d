<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTTreeGroupItem</class>
 <widget class="QWidget" name="QMTTreeGroupItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>172</width>
    <height>30</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>30</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QMTTreeGroupItem</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">#sliderTextWidget
{
font: 14px;
}

QLabel
{
    border:none;
    text-align : left;
}

QCheckBox {
	font: 14px;
    padding-top: 0px;
    padding-left: 1px;
    padding-right: 0px;
    padding-bottom:0px;
    border:none;
}

QCheckBox:pressed {
	padding-top: 2px; 
}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="frame">
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Plain</enum>
     </property>
     <property name="lineWidth">
      <number>1</number>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx5_30</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_3" native="true">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>0</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>1</number>
         </property>
         <property name="rightMargin">
          <number>6</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>10</width>
             <height>32</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="MtCheckBox" name="checkBox_Show">
           <property name="maximumSize">
            <size>
             <width>22</width>
             <height>22</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QCheckBox::indicator:unchecked {  
     image:  url(:/AccuUIComponentImage/images/eye_un_display.png);  
 }  
  
 QCheckBox::indicator:unchecked:hover {  
     image:  url(:/AccuUIComponentImage/images/eye_un_display_hover.png);  
 }  
  
 QCheckBox::indicator:unchecked:pressed {  
     image:  url(:/AccuUIComponentImage/images/eye_un_display.png);  
 }  
  
 QCheckBox::indicator:checked {  
     image: url(:/AccuUIComponentImage/images/eye_display.png);  
 }  
  
 QCheckBox::indicator:checked:hover {  
     image: url(:/AccuUIComponentImage/images/eye_display_hover.png);  
 }  
  
 QCheckBox::indicator:checked:pressed {  
     image:  url(:/AccuUIComponentImage/images/eye_display.png);  
 }  
  
 QCheckBox::indicator:indeterminate:hover {  
     image: url(:/AccuUIComponentImage/images/eye_display_hover.png);  
 }  
  
 QCheckBox::indicator:indeterminate:pressed {  
     image:  url(:/AccuUIComponentImage/images/eye_display.png);   
 }</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="iconSize">
            <size>
             <width>26</width>
             <height>26</height>
            </size>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtCheckBox::default_type</enum>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>8</width>
             <height>34</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <spacer name="horizontalSpacer_3">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeType">
            <enum>QSizePolicy::Fixed</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>8</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="SliderTextWidget" name="sliderTextWidget" native="true">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>34</height>
            </size>
           </property>
           <property name="autoFillBackground">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_ItemNum">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>24</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>35</width>
             <height>34</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MtCheckBox" name="checkBox_Expand">
           <property name="maximumSize">
            <size>
             <width>26</width>
             <height>34</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QCheckBox::indicator:unchecked {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_down.png);  
 }  
  
 QCheckBox::indicator:unchecked:hover {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_down_hover.png);  
 }  
  
 QCheckBox::indicator:unchecked:pressed {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_down.png);  
 }  
  
 QCheckBox::indicator:checked {  
     image: url(:/AccuUIComponentImage/images/treeGroup_up.png);  
 }  
  
 QCheckBox::indicator:checked:hover {  
     image: url(:/AccuUIComponentImage/images/treeGroup_up_hover.png);  
 }  
  
 QCheckBox::indicator:checked:pressed {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_up.png);  
 }  

 QCheckBox::indicator:indeterminate:hover {  
     image: url(:/AccuUIComponentImage/images/treeGroup_up_hover.png);  
 }  
  
 QCheckBox::indicator:indeterminate:pressed {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_up.png);   
 }</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="iconSize">
            <size>
             <width>26</width>
             <height>26</height>
            </size>
           </property>
           <property name="checked">
            <bool>true</bool>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>SliderTextWidget</class>
   <extends>QWidget</extends>
   <header location="global">AccuComponentUi\Header\SliderTextWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
