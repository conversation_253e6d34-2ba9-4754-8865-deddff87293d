<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTCheckBoxLabel</class>
 <widget class="QWidget" name="QMTCheckBoxLabel">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>146</width>
    <height>48</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTCheckBoxLabel</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="checkboxLabeBack">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>8</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtCheckBox" name="checkBox">
        <property name="maximumSize">
         <size>
          <width>24</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtCheckBox::checkbox1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="labelPix">
        <property name="minimumSize">
         <size>
          <width>10</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
