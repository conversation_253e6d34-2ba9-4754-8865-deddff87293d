﻿#pragma once

#include <QWidget>
#include <QColor>
#include <QTimer>
#include <QValidator>
#include "QMTAbstractMenu.h"

#define DerivedROILatestStatus "DerivedROILatestStatus"  //最新状态
#define DerivedROINeedUpdateStatus "DerivedROINeedUpdateStatus"  //需要更新状态
#define DerivedROIManualOverlayStatus "DerivedROIManualOverlayStatus"   //手动覆盖状态
#define DerivedROIFunctionExpand "DerivedROIFunctionExpand"
#define DerivedROIFunctionExtractWall "DerivedROIFunctionExtractWall"
#define DerivedROIFunctionTrim "DerivedROIFunctionTrim"
#define DerivedROIFunctionCombination "DerivedROIFunctionCombination"
#define DerivedROIFunctionDoseToDelineate "DerivedROIFunctionDoseToDelineate"

//用于新增一个item需要的数据内容
typedef struct ST_TreeSubItemAddUIInfo
{
    QString _groupKey;                  //group的唯一值
    int _packetKey = -1;                //靶区分组唯一值
    QString _uniqueValue;               //sub Item的唯一值
    QString _showText;                  //显示的内容
    int _rgb[3] = { 0 };                  //颜色
    bool _isShow = true;                //小眼睛是否显示
    bool _isNoData = false;              //是否有数据
    bool _isSelect = false;             //是否选中
    QString _algorithm = "AUTOMATIC";   //"MANUAL"或者"AUTOMATIC"
    //online tps中有的内容如下
    int _comBoxIndex = 0;               //下拉框选择位置
    bool _comboBoxEnable = true;        //是否允许点击下拉
    QString _itemTips = "";             //item的tips文字
    //QList<int> disableItemIndexList;    //不允许点击下拉框列表
    //QStringList _comboxTextList;        //下拉框显示内容
    //QStringList _comboxIconPathList;    //下拉框图标路径
    QMap<int, QString> _userDataMap; //用户临时数据Map 扩展用
} TreeSubItemAddUIInfo;

//更新数据操作类型
enum TreeSubItemOptType
{
    //修改
    Opt_IsShow,             //小眼睛状态
    Opt_ChangeColor,        //修改颜色
    Opt_ColorData,          //ROI是否有数据
    Opt_ChangeName,         //修改名称
    Opt_ChangeAlgorithm,    //修改AI类型
    Opt_ComboBoxChange,     //修改下拉框下标
};

/// <summary>
/// 衍生ROI的内置菜单
/// </summary>
enum EM_DerivedROIMenu
{
    /// <summary>
    /// 编辑菜单
    /// </summary>
    EditDerivedROIMenu = 0,
    /// <summary>
    /// 更新菜单
    /// </summary>
    UpdateDerivedROIMenu,
    /// <summary>
    /// 取消ROI衍生逻辑菜单
    /// </summary>
    CancelDerivedROIMenu
};
//更新数据需要内容
typedef struct ST_TreeSubItemUpdateInfo
{
    TreeSubItemOptType      _optType;
    TreeSubItemAddUIInfo    _uiInfo;
} TreeSubItemUpdateInfo;


//property
typedef struct ST_TreeSubItemProperty
{
    bool _isEnableChangeName = true;           //是否能够修改名称
    bool _isEnableChangeColor = true;           //是否能够修改颜色
    bool _isAIShow = true;           //AI图标是否显示
    bool _isEnableRightMenu = true;           //是否允许右键编辑等等
    bool _isComboBoxVisiable = false;          //下拉框是否显示
    bool _isEyeBtnVisiable = true;                  //小眼睛是否显示
    int _comboBoxWidth = 0;                     //下拉框的长和宽
    int _comboBoxHeight = 0;
    QStringList _menuSelectIconPathList;
    QStringList _menuUnselectIconPathList;
    QStringList _menuStrList;
} TreeSubItemProperty;

namespace Ui
{
class QMTTreeSubItem;
};

class  QMTTreeSubItem : public QWidget
{
    Q_OBJECT

public:
    QMTTreeSubItem(QWidget*, QWidget* parent = 0);
    ~QMTTreeSubItem();

    //设置界面数据内容
    void setMapString(const TreeSubItemAddUIInfo&);
    void setMapString(QString parentValue, QString uniqueValue, QString name, QColor color, QString type = "");
    // AC新增需求标签Label
    void setMapString(QString parentValue, QString uniqueValue, QString name, QColor color, QString type, QString label);

    //更新UI内容
    void UpdateUIInfo(const TreeSubItemUpdateInfo& info);
    void UpdateItemColor(QColor& color);                //更新颜色

    //设置属性
    void SetSubItemProperty(TreeSubItemProperty&);
    void SetParentUniqueValue(QString value);       //设置父类唯一标识

    void setChecked(bool state);                //按键checked状态,并且设置颜色按键为false
    void SetShowState(bool state, bool isEmit = false);//是否显示，是否发信号
    void SetSelectBtnTipString(const QString& tips, const QString& tipsEx); //设置右侧下拉箭头tips
    void SetShowHideBtnTipString(const QString& tips, const QString& tipsEx);   //设置显示隐藏按钮tips
    void setNoData(bool state);                 //设置是否有勾画内容
    void setFill(bool bfill);                   //设置当前项是否填充
    void setType(QString type);                 //设置类型
    void setLabel(const QString& label);
    void setGenerationAlgorithm(QString);       //设置自动勾画icon
    void setCheckColor();                       //更新checked状态颜色
    inline void setTipWidget(QWidget* pTipWidget);
    void setSelected(bool select);                    //设置选中
    void SetEnableChangeName(bool enable);      //是否允许修改名称
    void SetEnableChangeColor(bool enable);     //是否允许修改颜色
    void SetItemValidator(QValidator*);         //设置输入的规则
    void SetAIiconShow(bool isShow);            //设置AI图标是否允许显示
    void SetEnableClickeShowBtn(bool);          //设置是否允许点击小眼睛
    void SetEnableEdit(bool);                   //设置是否允许编辑，右键等等
    void SetEyeBtnVisible(bool isShow);                   //设置小眼睛是否显示
    void UpdateToolBtnColor(const int* rgb);    //设置按键颜色
    void UpdateShowText(const QString&);        //更新显示内容
    void SetComboBoxSelectStyle(QString styleSheetStr);     //更新下拉框样式
    void SetAILabelPixmap(QString unselectPix, QString selectPix, QString algorithm = "");//设置AI label显示的图标
    void SetAILabelTipString(const QString& tip);//设置悬浮提示
    /// <summary>
    /// 设置poi的图标类型
    /// </summary>
    /// <param name="type">图标类型</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetPoiItemIconType(const QString& type);

    void SetSchedule(int value);                    //设置进度
    void SetUserData(int userIndex, QString data);  //设置用户临时存储的数据
    //下拉框
    void SetMenuSelectIndex(int index);             //设置menu选择下标
    //void AddComboBoxTextList(QStringList strList, QStringList iconList, int index = -1);
    //void SetComboBoxText(QString);                //设置下拉框text
    // void SetComboBoxIndex(int);                  //设置下拉框下标位置
    void SetMenuSelectVisiable(bool isShow);        //下拉框是否显示
    void SetMenuSelectEnable(bool enable);          //设置下拉框是否允许下拉
    void SetMenuSelectItemEnable(int index, bool enable); //设置下拉框某一个item是否允许点击
    QList<int> GetMenuDisableIndexList();           //获取下拉框中需要disable的项
    //update
    void UpdateTextNameStyle();                     //更新textname区域的样式
    //get
    QString getType();
    QString getLabel();
    bool getSelected();
    QString getName();
    bool getIsShow();
    QString getUniqueValue();
    QString getParentUniqueValue();
    QString getGenerationAlgorithm();
    bool GetEnableEdit();           //是否允许修改
    bool isChecked();
    bool isNoData();                //是否有数据内容
    bool getFill();
    QColor getColor();              //获取颜色
    int GetMenuSelectIndex();         //获取menu选择下标
    QString GetUserData(int userIndex); //获取用户临时存储的数据
    /// <summary>
    /// Sets the name.
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetName(const QString& str);
    //20220523
    void SetOnlyMenuHide();  //只设置menu隐藏，但不屏蔽信号

    void hideUI();
    /// <summary>
    /// 更新衍生ROI的状态->btn按钮变化
    /// </summary>
    /// <param name="status">The status.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetDerivedROIBtnStatus(QString derivedRoiBtnStatus);
    /// <summary>
    /// 设置菜单是否可用-&gt;菜单置灰
    /// </summary>
    /// <param name="derivedMenuIndex">Index of the derived menu.</param>
    /// <param name="bEnabled">The b enabled.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetDerivedMenuEnabled(EM_DerivedROIMenu derivedMenuIndex, bool bEnabled);
    /// <summary>
    /// 设置衍生ROI是否可用
    /// </summary>
    /// <param name="bApproval">The b approval.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetIsEnabled(bool bApproval);
    /// <summary>
    /// 设置Tooltips
    /// </summary>
    /// <param name="tips">The tips.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetDerivedToolTips(const QString& tips);
    /// <summary>
    /// 设置TooptipsEx
    /// </summary>
    /// <param name="tips">The tips.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetDerivedToolTipsEx(const QString& tips);
    /// <summary>
/// Sets the text first hidden.
/// </summary>
/// <param name="bHide">The b hide.</param>
/// <remarks>[Version]:******* Change: </remarks>
    void SetTextFirstHidden(bool bHide);
    /// <summary>
    /// Sets the text second hidden.
    /// </summary>
    /// <param name="bHide">The b hide.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetTextSecondHidden(bool bHide);
    /// <summary>
    /// 初始化衍生ROI等UI状态
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void InitDerivedRoiStyle();
    /// <summary>
    /// 获取POI类型
    /// </summary>
    /// <returns>POI类型.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QString GetPoiType();

public slots:
    void slotCheckBoxShowClicked();         //QCheckBox小眼睛点击了
    void slotButtonColorClick();            //QToolButton按键点击了
    void slotTextNameEditFinish(QString oldText, QString newText);//双击修改了名称
    void slotComboBoxItemTextChange(const QString&);    //下拉框选择改变
    void slotComboBoxItemIndexChange(int);
    void slotComboBoxClicked(int);                      //点击了下拉框
    void slotToolButtonSelectClicked();                 //点击了选择按键
signals:
    void sigIsShowItemList(QString, QStringList, bool);//是否展示parentValue,
    void sigCurrentNameChange(QString, QString, QString);//名称修改,parentValue, old, new
    void sigCurrentColorChange(QString, QString, QColor, QColor);//颜色修改, parentValue, old, new
    void sigComboBoxTextChange(QString, QString, QString);//下拉框选择变化parentValue,uniquevalue,text
    void sigComboBoxIndexChange(QString, QString, int);//下拉框选择变化parentValue,uniquevalue,index
    void sigSelectButtonClicked(QString, QString);      //选择按键点击了parentValue,uniquevalue
    void sigSelectButtonClickedButNotShowMenu(QString, QString); //20220523 zlw选择按键点击了parentValue,uniquevalue但不现实Menu
    /// <summary>
    /// 点击了衍生ROI的菜单信号
    /// </summary>
    /// <param name="index">The index.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SigClickedDerivedMenu(QString parentStr, QString uniqueStr, EM_DerivedROIMenu index);
    /// <summary>
    /// Sigs the menu pop finished.
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void SigMenuPopFinished();
protected:
    void mouseDoubleClickEvent(QMouseEvent* event);
    void SetCurName(QString&);
    void ConnectComboBoxSignlas();
    void DisConnectComboBoxSignlas();
    /// <summary>
    /// 点击了编辑菜单
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void SlotClickedDerivedEditMenu();
    /// <summary>
    /// 点击了更新菜单
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void SlotClickedDerivedUpdateMenu();
    /// <summary>
    /// 点击了取消衍生逻辑菜单
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void SlotClickedDerivedCancelMenu();
    /// <summary>
    /// 点击按钮
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void SlotMenuClicked();
private:
    Ui::QMTTreeSubItem* ui;
    QColor _color;
    QString _uniqueValue;//唯一标识符
    QString _parentValue;//父唯一标识
    QString _type;
    QString _label;
    bool _isSelected;//是否选中
    bool _isCheck;//显示的颜色
    bool _isNoData;//是否有数据
    QString _algorithm;
    bool _TipWidgetShow;  //防止TIP闪烁多次显示
    QWidget* _TipWidget = nullptr;
    bool _bfill;//是否填充
    TreeSubItemProperty _subItemProperty;        //属性参数
    QMap<int, QString> _userDataMap;            //用户临时数据Map
    //AI Label相关
    QString _unselectPixmap;        //未选中的时候图标
    QString _selectPixmap;          //选中时候的图标
    int _selectIndex = 0;           //menu选择下标
    bool _menuSelectEnable = true;  //是否允许点击下拉menu
    QList<int> _menuDisableIndexList;//不允许点击的menu子项
    //进度条相关

    QLabel* _zebraPic = nullptr;
    QLabel* _backgroundColor = nullptr;
    bool _onlyMenuHide = false;
    /// <summary>
    /// ROI状态
    /// </summary>
    QString m_roiStatus = "";
    /// <summary>
    /// 衍生ROI是否可用
    /// </summary>
    bool m_isEnabled = false;
    /// <summary>
    /// The m derived roi menu
    /// </summary>
    QMTAbstractMenu* m_derivedROIMenu = nullptr;
    /// <summary>
    /// The b clicked menu
    /// </summary>
    bool bClickedMenu = false;
    /// <summary>
    /// poi的类型
    /// </summary>
    QString m_poiType = "";
};



