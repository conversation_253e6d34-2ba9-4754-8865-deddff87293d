﻿#include "UnattendSubTableEditItem.h"
#include "MTUnattended/RuleTableStyle/WidgetStyle/ExportRuleEditWidget.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "DataDefine/InnerStruct.h"


#define ExportItemHeight    30
#define ExportMaxNum        5


UnattendSubTableEditItem::UnattendSubTableEditItem(MtListWidget* parentListWidget, QListWidgetItem* curListWidgetItem, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    m_parentListWidget = parentListWidget;
    m_curListWidgetItem = curListWidgetItem;
    //禁用MtCombox鼠标滚动事件
    ui.mtComboBox_keyType->setEnabledWheelEvent(false);
    ui.mtComboBox_keyValue->setEnabledWheelEvent(false);
    ui.mtComboBox_modality->setEnabledWheelEvent(false);
    ui.mtComboBox_sex->setEnabledWheelEvent(false);
    ui.mtComboBox_template->setEnabledWheelEvent(false);
}

UnattendSubTableEditItem::~UnattendSubTableEditItem()
{
}

// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void UnattendSubTableEditItem::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="isAdd">[IN]true新增</param>
/// <param name="sketchIdentifyUniqueKey">[IN]勾画规则唯一标识</param>
/// <param name="templateNameMap">[IN]模板名称集合(key-templateId value-templateName)</param>
/// <param name="stSketchIdentify">[IN]勾画识别信息</param>
/// <returns>item高度</returns>
int UnattendSubTableEditItem::init(
    const bool isAdd,
    const QString sketchIdentifyUniqueKey,
    const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify,
    QList<n_mtautodelineationdialog::ST_AddrSimple>& allRemoteServerList,
    const QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*模板id*/, QString>>& templateNameMap)
{
    //初始化数据
    m_sketchIdentifyUniqueKey = sketchIdentifyUniqueKey;
    m_templateNameMap = templateNameMap;
    m_oldSketchIdentify = stSketchIdentify;
    m_allRemoteServerList = allRemoteServerList;
    //初始化UI
    int itemHeight = initUI(isAdd, m_oldSketchIdentify);
    //信号槽onMtCheckBox
    connect(ui.mtCheckBox, &QCheckBox::stateChanged, this, &UnattendSubTableEditItem::onMtCheckBox);    //复选框打勾
    connect(ui.mtComboBox_keyType, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyTypeChanged);    //识别类型下拉框文本变化
    connect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);  //识别部位下拉框文本变化
    connect(ui.mtComboBox_modality, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxModalityChanged);  //模态下拉框文本变化
    connect(ui.mtLineEdit_dicomValue, &MtRangeLineEdit::sigFocusOut, this, &UnattendSubTableEditItem::slotMtRangeLineEditFocusOut); //MtRangeLineEdit失去焦点事件
    return itemHeight;
}

/// <summary>
/// 获取勾画规则唯一标识
/// </summary>
QString UnattendSubTableEditItem::getSketchIdentifyUniqueKey()
{
    return m_sketchIdentifyUniqueKey;
}

/// <summary>
/// 获取是否是AI部位识别
/// </summary>
bool UnattendSubTableEditItem::getIsAI()
{
    return (ui.mtComboBox_keyType->currentIndex() == 0);
}

/// <summary>
/// 获取DICOM字段识别下的识别词
/// </summary>
/// <param name="outFlagStr">[OUT]标志 dcmTag.modality.sex</param>
///  <param name="outWordSet">[OUT]识别词集合(都转了大写)</param>
void UnattendSubTableEditItem::getDcmWord(QString& outFlagStr, QSet<QString>& outWordToUpperSet)
{
    QStringList valList = ui.mtLineEdit_dicomValue->text().split("/", QString::SkipEmptyParts);

    //收集识别词(统一转大写)
    for (int i = 0; i < valList.size(); i++)
    {
        outWordToUpperSet.insert(valList[i].toUpper());
    }

    // 部位标识.模态.性别 相同的情况下，查看关键词是否重复
    QString sex = ui.mtComboBox_sex->currentData().value<QString>();
    QString bodyTag = ui.mtComboBox_keyValue->currentData().value<QString>();
    outFlagStr = bodyTag.toUpper() + Def_Separator + ui.mtComboBox_modality->currentText().toUpper() + Def_Separator + sex.toUpper();
}

/// <summary>
/// 获取当前MtComboBox_keyValue选中项
/// </summary>
/// <param name="outIsAI">[OUT]true-AI识别</param>
/// <param name="outCodeKey">[OUT]识别码(AI部位-111111 DICOM字段-00180015)</param>
void UnattendSubTableEditItem::getCurrent_MtComboBox_keyValue(bool& outIsAI, QString& outCodeKey)
{
    outIsAI = (ui.mtComboBox_keyType->currentIndex() == 0);
    outCodeKey = ui.mtComboBox_keyValue->currentData().value<QString>();
}

/// <summary>
/// 设置AI部位识别下MtComboBox_keyValue可下拉的项
/// </summary>
/// <param name="codeKeySet">[IN]识别码(AI部位-111111)</param>
void UnattendSubTableEditItem::setAiItems_MtComboBox_keyValue(const QSet<QString>& codeKeySet)
{
    //当前Item是DICOM字段识别时直接返回
    if (ui.mtComboBox_keyType->currentIndex() == 1)
        return;

    QSet<QString> aiCodeKeySet = codeKeySet;
    //获取当前选中的项目
    QString curCodeKey = ui.mtComboBox_keyValue->currentData().value<QString>();
    aiCodeKeySet.insert(curCodeKey);
    //清空部位选择下拉框
    clear_MtComboBox_keyValue();
    //获取AI部位识别编码集合
    QStringList aiCodeList = CommonUtil::getCodeFromBodyPartAI();

    for (int i = 0; i < aiCodeList.size(); i++)
    {
        QString aiCode = aiCodeList[i];

        if (aiCodeKeySet.contains(aiCode) == true)
        {
            addItem_MtComboBox_keyValue(CommonUtil::getTextFromBodyPartAI(aiCode), aiCode);
        }
    }

    //选中一项
    setCurrent_MtComBox_KeyValue(true, curCodeKey);
}

/// <summary>
/// 删除前准备(用于将AI部位返还回可选)
/// </summary>
void UnattendSubTableEditItem::prepareToBeforeDelete()
{
    //AI部位识别模式下将选中的AI部位返还回可选
    if (ui.mtComboBox_keyType->currentIndex() == 0)
    {
        QString aiCodeKey = ui.mtComboBox_keyValue->currentData().value<QString>();
        reInitAI_MtComBox_KeyValue(aiCodeKey);
    }
}

/// <summary>
/// 获取最新的勾画识别信息
/// </summary>
bool UnattendSubTableEditItem::getNewSketchIdentify(QString& outSketchIdentifyUniqueKey, n_mtautodelineationdialog::ST_SketchIdentify& outStSketchIdentify, QString& errMsg)
{
    if (ui.mtComboBox_template->count() <= 0)
    {
        m_parentListWidget->scrollToItem(m_curListWidgetItem);
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择勾画模板"));
        return false;
    }

    outSketchIdentifyUniqueKey = m_sketchIdentifyUniqueKey;
    int tempateId = ui.mtComboBox_template->currentData().value<int>();
    outStSketchIdentify.sketchCollectionId = tempateId;
    outStSketchIdentify.modality = ui.mtComboBox_modality->currentText().toUpper();

    if (ui.mtComboBox_keyType->currentIndex() == 0)//AI部位识别
    {
        outStSketchIdentify.dcmTagSex.clear();
        outStSketchIdentify.dcmTagMap.clear();
        outStSketchIdentify.recognitionType = 1; //AI部位识别
        outStSketchIdentify.aiBodypart = ui.mtComboBox_keyValue->currentData().value<QString>();
    }
    else //DICOM字段匹配
    {
        if (checkIsNormal() == false)
            return false;

        outStSketchIdentify.aiBodypart.clear();
        outStSketchIdentify.recognitionType = 2; //DICOM字段
        outStSketchIdentify.dcmTagSex = ui.mtComboBox_sex->currentData().value<QString>();
        QString tag = ui.mtComboBox_keyValue->currentData().value<QString>();;
        outStSketchIdentify.dcmTagMap.clear();
        outStSketchIdentify.dcmTagMap.insert(tag, CommonUtil::strToStringList(ui.mtLineEdit_dicomValue->text()));
    }

    //导出地址
    for (int i = 0; i < ui.verticalLayout_3->count(); i++)
    {
        QLayoutItem* item = ui.verticalLayout_3->itemAt(i);

        if (item->widget() == nullptr)
            continue;

        QWidget* widget = item->widget();
        ExportRuleEditWidget* exportRuleEditWidget = qobject_cast<ExportRuleEditWidget*>(widget);

        if (exportRuleEditWidget == nullptr)
            continue;

        if (exportRuleEditWidget->getPageType() == 1) //文本页
        {
            QString addrStr, uniqueKey;
            n_mtautodelineationdialog::ST_AddrSimple newStAddr;
            exportRuleEditWidget->getNewAddrInfo(newStAddr, uniqueKey);
            outStSketchIdentify.addrInfoMap.insert(uniqueKey, newStAddr);
        }
    }

    return true;
}

/// <summary>
/// 获取Item打勾
/// </summary>
bool UnattendSubTableEditItem::getIsCheckedItem()
{
    return  ui.mtCheckBox->isChecked();
}

/// <summary>
/// 设置Item打勾
/// </summary>
void UnattendSubTableEditItem::setIsCheckedItem(bool ischeck)
{
    disconnect(ui.mtCheckBox, &QCheckBox::stateChanged, this, &UnattendSubTableEditItem::onMtCheckBox);    //复选框打勾
    ui.mtCheckBox->setChecked(ischeck);
    connect(ui.mtCheckBox, &QCheckBox::stateChanged, this, &UnattendSubTableEditItem::onMtCheckBox);    //复选框打勾
}

/// <summary>
/// 获取最小高度
/// </summary>
int UnattendSubTableEditItem::getItemMinHeight()
{
    if (ui.mtComboBox_keyType->currentIndex() == 0)
    {
        return getRowMinHeight(1);
    }

    return getRowMinHeight(2);
}

/// <summary>
/// 获取最小行高度
/// </summary>
/// <param name="recognitionType">[IN]部位识别类型(1:AI部位识别 2:DICOM字段)</param>
/// <returns>最小行高度</returns>
int UnattendSubTableEditItem::getRowMinHeight(const int recognitionType)
{
    if (recognitionType == 1)
    {
        return 36;
    }

    return 70;
}

/// <summary>
/// 获取AI部位识别模式规则数
/// </summary>
/// <returns>数目</returns>
int UnattendSubTableEditItem::getAIidentifyModuleNum()
{
    int num = 0;

    for (int i = 0; i < m_parentListWidget->count(); i++)
    {
        QListWidgetItem* listWidgetItem = m_parentListWidget->item(i);
        UnattendSubTableEditItem* ptrEditItem = getUnattendSubTableEditItem(listWidgetItem);

        if (ptrEditItem == nullptr)
            continue;

        if (ptrEditItem->getIsAI() == false)
            continue;

        num++;
    }

    return num;
}

/// <summary>
/// 初始化UI
/// </summary>
/// <param name="isAdd">[IN]true新增</param>
/// <param name="stSketchIdentify">[IN]勾画识别信息</param>
int UnattendSubTableEditItem::initUI(const bool isAdd, const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify)
{
    n_mtautodelineationdialog::ST_SketchIdentify tempSketchIdentify = stSketchIdentify;
    //性别
    ui.mtComboBox_sex->addItem(tr("通用"), QVariant::fromValue(QString("ALL")));
    ui.mtComboBox_sex->addItem(tr("男"), QVariant::fromValue(QString("M")));
    ui.mtComboBox_sex->addItem(tr("女"), QVariant::fromValue(QString("F")));

    if (isAdd == true) //新增
    {
        if (getAIidentifyModuleNum() == 18) //AI识别已满
        {
            //赋值识别类型
            tempSketchIdentify.recognitionType = 2;
            //初始化关键词
            ui.mtComboBox_keyType->setCurrentIndex(1);
            //实时初始化AI部位编码下拉框
            reInitDicom_MtComBox_KeyValue();
        }
        else //AI部位识别
        {
            //赋值识别类型
            tempSketchIdentify.recognitionType = 1;
            //初始化关键词
            ui.mtComboBox_keyType->setCurrentIndex(0);
            //实时初始化AI部位编码下拉框
            reInitAI_MtComBox_KeyValue(QString());
        }

        //初始化性别
        ui.mtComboBox_sex->setCurrentIndex(0);
        //初始化模板内容
        init_MtComBox_Temlate(Def_CT);
        ui.mtComboBox_modality->setCurrentText(Def_CT);

        //AI部位识别,提前选一下固定模板
        if (ui.mtComboBox_keyType->currentIndex() == 0)
        {
            QString language = (Language::type == Chinese ? "ch" : "en");
            QString aiCodeKey = ui.mtComboBox_keyValue->currentData().value<QString>();
            QString templateName = CommonUtil::getUnattendModelNameOfInerDefault(language, aiCodeKey);
            ui.mtComboBox_template->setCurrentText(templateName);
        }
    }
    else //编辑
    {
        if (tempSketchIdentify.recognitionType == 1) //AI部位识别
        {
            //初始化关键词
            ui.mtComboBox_keyType->setCurrentIndex(0);
            //实时初始化AI部位编码下拉框
            reInitAI_MtComBox_KeyValue(m_oldSketchIdentify.aiBodypart);
        }
        else if (tempSketchIdentify.recognitionType == 2) //DICOM字段识别
        {
            //初始化关键词
            ui.mtComboBox_keyType->setCurrentIndex(1);
            //实时初始化AI部位编码下拉框
            reInitDicom_MtComBox_KeyValue();
            //初始化性别
            int count = ui.mtComboBox_sex->count();

            for (int i = 0; i < count; i++)
            {
                QString sex = ui.mtComboBox_sex->itemData(i).value<QString>();

                if (sex == tempSketchIdentify.dcmTagSex.toUpper())
                {
                    ui.mtComboBox_sex->setCurrentIndex(i);
                    break;
                }
            }
        }

        //初始化模板内容
        QString modality = tempSketchIdentify.modality.toUpper();
        init_MtComBox_Temlate(modality);
        ui.mtComboBox_modality->setCurrentText(modality);

        //切换选中的模板
        if (ui.mtComboBox_template->count() > 0)
        {
            n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (modality == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);

            if (m_templateNameMap.contains(dcmTypeEnum) == true && m_templateNameMap[dcmTypeEnum].contains(tempSketchIdentify.sketchCollectionId) == true)
            {
                ui.mtComboBox_template->setCurrentText(m_templateNameMap[dcmTypeEnum][tempSketchIdentify.sketchCollectionId]);
            }
        }
    }

    //行最小高度
    int itemHeight = getRowMinHeight(tempSketchIdentify.recognitionType);
    //先挂载一个新增按钮
    ExportRuleEditWidget* itemWidget = new ExportRuleEditWidget(0, "ExportRuleEditWidget-addButton", n_mtautodelineationdialog::ST_AddrSimple(), m_allRemoteServerList, ui.verticalLayout_3, this);
    connect(itemWidget, &ExportRuleEditWidget::sigCreateNewAddr, this, [this](const n_mtautodelineationdialog::ST_AddrSimple stAddr) //新增了一个导出地址
    {
        QString newAddrUniqueKey = CommonUtil::getCurrentDateTime();
        ExportRuleEditWidget* exportRuleEditWidget = new ExportRuleEditWidget(1, newAddrUniqueKey, stAddr, m_allRemoteServerList, ui.verticalLayout_3, this);
        connect(exportRuleEditWidget, &ExportRuleEditWidget::sigDelOneAddr, this, [&](const QString outAddrUniqueKey) //删除了一个导出地址
        {
            for (int i = 0; i < ui.verticalLayout_3->count(); i++)
            {
                QLayoutItem* item = ui.verticalLayout_3->itemAt(i);

                if (item->widget() == nullptr)
                    continue;

                QWidget* widget = item->widget();
                ExportRuleEditWidget* tempWidget = qobject_cast<ExportRuleEditWidget*>(widget);

                if (tempWidget == nullptr)
                    continue;

                if (tempWidget->getAddrUniqueKey() == outAddrUniqueKey) //文本页
                {
                    ui.verticalLayout_3->removeItem(item);
                    delete tempWidget;
                    emit this->sigChangeHeight(m_sketchIdentifyUniqueKey, -ExportItemHeight);
                    return;
                }
            }
        });
        //
        exportRuleEditWidget->setImagePathHash(m_imagePathHash);
        ui.verticalLayout_3->addWidget(exportRuleEditWidget);
        emit this->sigChangeHeight(m_sketchIdentifyUniqueKey, ExportItemHeight);
    });
    //
    ui.verticalLayout_3->addWidget(itemWidget);

    //导出规则
    for (QMap<QString/*导出规则唯一标识(yyyyMMddhhmmss)*/, n_mtautodelineationdialog::ST_AddrSimple>::const_iterator iter_addr = tempSketchIdentify.addrInfoMap.begin(); iter_addr != tempSketchIdentify.addrInfoMap.end(); iter_addr++)
    {
        ExportRuleEditWidget* exportRuleEditWidget = new ExportRuleEditWidget(1, iter_addr.key(), iter_addr.value(), m_allRemoteServerList, ui.verticalLayout_3, this);
        connect(exportRuleEditWidget, &ExportRuleEditWidget::sigDelOneAddr, this, [=](const QString outAddrUniqueKey) //删除了一个导出地址
        {
            for (int i = 0; i < ui.verticalLayout_3->count(); i++)
            {
                QLayoutItem* item = ui.verticalLayout_3->itemAt(i);

                if (item->widget() == nullptr)
                    continue;

                QWidget* widget = item->widget();
                ExportRuleEditWidget* tempWidget = qobject_cast<ExportRuleEditWidget*>(widget);

                if (tempWidget == nullptr)
                    continue;

                if (tempWidget->getAddrUniqueKey() == outAddrUniqueKey) //文本页
                {
                    ui.verticalLayout_3->removeItem(item);
                    delete tempWidget;
                    emit this->sigChangeHeight(m_sketchIdentifyUniqueKey, -ExportItemHeight);
                    break;
                }
            }
        });
        //
        exportRuleEditWidget->setImagePathHash(m_imagePathHash);
        ui.verticalLayout_3->addWidget(exportRuleEditWidget);
        itemHeight += ExportItemHeight; //默认用30
    }

    //DICOM字段识别
    if (tempSketchIdentify.recognitionType == 2)
    {
        itemHeight -= 36;
    }

    //没有导出地址时
    if ((tempSketchIdentify.recognitionType == 1 && tempSketchIdentify.addrInfoMap.size() < 1) ||
        (tempSketchIdentify.recognitionType == 2 && tempSketchIdentify.addrInfoMap.size() < 2))
    {
        itemHeight = getRowMinHeight(tempSketchIdentify.recognitionType);
    }

    return itemHeight;
}

/// <summary>
/// 初始化选择模板下拉框
/// </summary>
/// <param name="modality">[IN]模态(CT/MR)</param>
void UnattendSubTableEditItem::init_MtComBox_Temlate(const QString& modality)
{
    ui.mtComboBox_template->clear();
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (modality == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    QMap<int/*模板id*/, QString> templateMap = m_templateNameMap[dcmTypeEnum];

    for (QMap<int/*模板id*/, QString>::iterator it = templateMap.begin(); it != templateMap.end(); it++)
    {
        ui.mtComboBox_template->addItem(it.value(), QVariant::fromValue(it.key()));
    }
}

/// <summary>
/// 清空MtComboBox_keyValue
/// </summary>
void UnattendSubTableEditItem::clear_MtComboBox_keyValue()
{
    disconnect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);
    ui.mtComboBox_keyValue->clear();
    connect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);
}

/// <summary>
/// 添加item
/// 直接添加会触发文本修改信号
/// </summary>
/// <param name="keyStr">[IN]显示的文本</param>
/// <param name="dataStr">[IN]实际数据</param>
void UnattendSubTableEditItem::addItem_MtComboBox_keyValue(const QString textStr, const QString dataStr)
{
    disconnect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);
    ui.mtComboBox_keyValue->addItem(textStr, QVariant::fromValue(dataStr));
    connect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);
}

/// <summary>
/// 设置当前MtComboBox_keyValue选中项
/// </summary>
/// <param name="outIsAI">[OUT]true-AI识别</param>
/// <param name="outCodeKey">[OUT]识别码(AI部位-111111 DICOM字段-00180015)</param>
void UnattendSubTableEditItem::setCurrent_MtComBox_KeyValue(const bool isAI, const QString& codeKey)
{
    disconnect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);

    for (int i = 0; i < ui.mtComboBox_keyValue->count(); i++)
    {
        QString val = ui.mtComboBox_keyValue->itemData(i).value<QString>();

        if (val == codeKey)
        {
            ui.mtComboBox_keyValue->setCurrentIndex(i);

            if (isAI == false)
                ui.mtLineEdit_dicomValue->setText(CommonUtil::stringListToStr(m_oldSketchIdentify.dcmTagMap[val]));

            break;
        }
    }

    connect(ui.mtComboBox_keyValue, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyValueChanged);
}

/// <summary>
/// 重新初始化Dicom字段识别时的MtComBox_keyValue下拉项
/// </summary>
void UnattendSubTableEditItem::reInitDicom_MtComBox_KeyValue()
{
    QMargins margins = ui.formLayout->contentsMargins();
    ui.formLayout->setContentsMargins(margins.left(), -1, margins.right(), margins.bottom());
    clear_MtComboBox_keyValue();
    ui.mtLineEdit_dicomValue->clear();
    ui.widget_dicom->setHidden(false);
    ui.mtComboBox_sex->setHidden(false);
    addItem_MtComboBox_keyValue("BodyPartExmined (0018,0015)", "00180015");
    addItem_MtComboBox_keyValue("Study Description (0008,1030)", "00081030");
    addItem_MtComboBox_keyValue("Series Description (0008,103E)", "0008103E");
    addItem_MtComboBox_keyValue("RequestedProduceDes (0032,1060)", "00321060");

    //编辑情况下要事先显示识别词
    if (m_oldSketchIdentify.dcmTagMap.empty() == false)
    {
        setCurrent_MtComBox_KeyValue(false, m_oldSketchIdentify.dcmTagMap.begin().key());
    }
}

/// <summary>
/// 重新初始化其他AI部位识别时的MtComBox_KeyValue下拉项
/// </summary>
/// <param name="curCodeKey">[IN]当前选择的识别码(AI部位-111111)</param>
/// <param name="isDel">[IN]是否是删除时触发</param>
void UnattendSubTableEditItem::reInitAI_MtComBox_KeyValue(const QString& curCodeKey, bool isDel)
{
    //清空界面
    ui.mtLineEdit_dicomValue->clear();
    ui.widget_dicom->setHidden(true);
    ui.mtComboBox_sex->setHidden(true);
    //AI部位识别时需向下移动4个像素
    QMargins margins = ui.formLayout->contentsMargins();
    ui.formLayout->setContentsMargins(margins.left(), 4, margins.right(), margins.bottom());
    //
    //剩下的AI部位识别码
    QSet<QString> m_remainAiCodeKeySet =
    {
        "100000", "010000", "001000", "000100", "000010", "000001", "110000", "101000", "010100",
        "001100", "000110", "000101", "110100", "101100", "010110", "001101", "110110", "101101"
    };

    //非删除模式下
    if (isDel == false)
    {
        m_remainAiCodeKeySet.remove(curCodeKey);
    }

    //统计已经被用到的部位编码
    for (int i = 0; i < m_parentListWidget->count(); i++)
    {
        QListWidgetItem* listWidgetItem = m_parentListWidget->item(i);
        UnattendSubTableEditItem* ptrEditItem = getUnattendSubTableEditItem(listWidgetItem);

        if (ptrEditItem == nullptr)
            continue;

        if (m_sketchIdentifyUniqueKey == ptrEditItem->getSketchIdentifyUniqueKey() == true || ptrEditItem->getIsAI() == false)
            continue;

        bool isAI;
        QString codeKey;
        ptrEditItem->getCurrent_MtComboBox_keyValue(isAI, codeKey);
        m_remainAiCodeKeySet.remove(codeKey);
    }

    //非删除模式下
    if (ui.mtComboBox_keyType->currentIndex() == 0)
    {
        if (isDel == false && curCodeKey.isEmpty() == true) //如果传入的当前AI部位是空的，则需从剩余的选项中挑出第一个
        {
            clear_MtComboBox_keyValue();
            QStringList bodypartList = CommonUtil::getCodeFromBodyPartAI();
            QString needOneAiCode;

            for (int i = 0; i < bodypartList.size(); i++)
            {
                QString aiCodeKey = bodypartList[i];

                if (m_remainAiCodeKeySet.contains(aiCodeKey) == true)
                {
                    if (needOneAiCode.isEmpty() == true)
                        needOneAiCode = aiCodeKey;

                    addItem_MtComboBox_keyValue(CommonUtil::getTextFromBodyPartAI(aiCodeKey), aiCodeKey);
                }
            }

            //选中第一项
            setCurrent_MtComBox_KeyValue(true, needOneAiCode);
            //从剩余中移除
            m_remainAiCodeKeySet.remove(needOneAiCode);
        }
        else if (isDel == false && curCodeKey.isEmpty() == false)  //如果传入的当前AI部位非空
        {
            QStringList bodypartList = CommonUtil::getCodeFromBodyPartAI();

            for (int i = 0; i < bodypartList.size(); i++)
            {
                QString aiCodeKey = bodypartList[i];

                if (m_remainAiCodeKeySet.contains(aiCodeKey) == true || curCodeKey == aiCodeKey)
                {
                    addItem_MtComboBox_keyValue(CommonUtil::getTextFromBodyPartAI(aiCodeKey), aiCodeKey);
                }
            }

            //选中指定项
            setCurrent_MtComBox_KeyValue(true, curCodeKey);
        }
    }

    //重新初始化全部的Item的AI部位下拉项(不用通知自己)
    for (int i = 0; i < m_parentListWidget->count(); i++)
    {
        QListWidgetItem* listWidgetItem = m_parentListWidget->item(i);
        UnattendSubTableEditItem* ptrEditItem = getUnattendSubTableEditItem(listWidgetItem);

        if (ptrEditItem == nullptr)
            continue;

        if (m_sketchIdentifyUniqueKey == ptrEditItem->getSketchIdentifyUniqueKey() == true || ptrEditItem->getIsAI() == false)
            continue;

        ptrEditItem->setAiItems_MtComboBox_keyValue(m_remainAiCodeKeySet);
    }
}

/// <summary>
/// 检查识别规则是否正常
/// </summary>
/// <returns>true正常</returns>
bool UnattendSubTableEditItem::checkIsNormal()
{
    //获取当前Item相关值
    QString curFlagStr; //dcmTag.modality.sex
    QSet<QString> curWordToUpperSet; //识别词集合
    getDcmWord(curFlagStr, curWordToUpperSet);

    if (curWordToUpperSet.isEmpty())
    {
        m_parentListWidget->scrollToItem(m_curListWidgetItem);
        ui.mtLineEdit_dicomValue->setWarningBorderStatus(tr("DICOM字段识别词不能为空"));
        return false;
    }

    //获取本类别其他规则下的DICOM识别词集合
    for (int i = 0; i < m_parentListWidget->count(); i++)
    {
        QListWidgetItem* listWidgetItem = m_parentListWidget->item(i);
        UnattendSubTableEditItem* ptrEditItem = getUnattendSubTableEditItem(listWidgetItem);
        QString tempSketchIdentifyUniqueKey = ptrEditItem->getSketchIdentifyUniqueKey();

        if (tempSketchIdentifyUniqueKey == m_sketchIdentifyUniqueKey || ptrEditItem->getIsAI() == true)
            continue;

        QString otherFlagStr; //dcmTag.modality.sex
        QSet<QString> otherWordToUpperSet; //识别词集合
        ptrEditItem->getDcmWord(otherFlagStr, otherWordToUpperSet);

        if (curFlagStr == otherFlagStr && otherWordToUpperSet == curWordToUpperSet)
        {
            m_parentListWidget->scrollToItem(m_curListWidgetItem);
            ui.mtLineEdit_dicomValue->setWarningBorderStatus(tr("DICOM字段识别规则重复"));
            return false;
        }
    }

    return true;
}

/// <summary>
/// 获取UnattendSubTableEditItem指针(编辑模式)
/// 为nullptr时代表没找到
/// </summary>
UnattendSubTableEditItem* UnattendSubTableEditItem::getUnattendSubTableEditItem(QListWidgetItem* listWidgetItem)
{
    if (listWidgetItem == nullptr)
        return nullptr;

    QWidget* customWidget = m_parentListWidget->itemWidget(listWidgetItem);

    if (customWidget == nullptr)
        return nullptr;

    UnattendSubTableEditItem* ptrUnattendSubTableEditItem = (UnattendSubTableEditItem*)customWidget;
    return ptrUnattendSubTableEditItem;
}

/// <summary>
/// 复选框打勾
/// </summary>
void UnattendSubTableEditItem::onMtCheckBox(int state)
{
    emit this->sigEditItemChecked(ui.mtCheckBox->isChecked());
}

/// <summary>
/// mtComboBox_modality下拉变化
/// </summary>
void UnattendSubTableEditItem::onMtComboBoxModalityChanged(const QString& text)
{
    disconnect(ui.mtComboBox_modality, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxModalityChanged);
    //重新初始化选择模板下拉框
    QString modality = ui.mtComboBox_modality->currentText();

    //AI部位不能进行MR部位识别
    if (ui.mtComboBox_modality->currentText() == "MR" && ui.mtComboBox_keyType->currentIndex() == 0)
    {
        MtMessageBox::NoIcon::information_Title(this, tr("MR无法进行AI部位识别"));
        ui.mtComboBox_modality->setCurrentText("CT");
    }
    else
    {
        init_MtComBox_Temlate(modality);
    }

    connect(ui.mtComboBox_modality, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxModalityChanged);
}

/// <summary>
/// mtComboBox_keyType下拉变化
/// </summary>
void UnattendSubTableEditItem::onMtComboBoxKeyTypeChanged(const QString& text)
{
    if (ui.mtComboBox_modality->currentText() == "MR")
    {
        if (ui.mtComboBox_keyType->currentIndex() == 0) //AI部位识别·
        {
            MtMessageBox::NoIcon::information_Title(this, tr("MR无法进行AI部位识别"));
            disconnect(ui.mtComboBox_keyType, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyTypeChanged);
            ui.mtComboBox_keyType->setCurrentIndex(1);
            connect(ui.mtComboBox_keyType, &QComboBox::currentTextChanged, this, &UnattendSubTableEditItem::onMtComboBoxKeyTypeChanged);
            return;
        }
    }

    int gapNum = getRowMinHeight(2) - getRowMinHeight(1); //DICOM字段识别最小高度-AI部位识别最小高度

    if (ui.mtComboBox_keyType->currentIndex() == 0) //AI部位识别
    {
        reInitAI_MtComBox_KeyValue(QString());

        if (ui.verticalLayout_3->count() == 1)
            emit this->sigChangeHeight(m_sketchIdentifyUniqueKey, -gapNum);
    }
    else if (ui.mtComboBox_keyType->currentIndex() == 1) //DICOM字段识别
    {
        reInitDicom_MtComBox_KeyValue();
        reInitAI_MtComBox_KeyValue(QString());
        ui.widget_dicom->setHidden(false);
        ui.mtComboBox_sex->setHidden(false);

        if (ui.verticalLayout_3->count() == 1)
            emit this->sigChangeHeight(m_sketchIdentifyUniqueKey, gapNum);
    }
}

/// <summary>
/// mtComboBox_keyValue下拉变化
/// </summary>
void UnattendSubTableEditItem::onMtComboBoxKeyValueChanged(const QString& text)
{
    printf("onMtComboBoxKeyValueChanged\n");

    if (ui.mtComboBox_keyType->currentIndex() == 0) //AI部位识别·
    {
        QString curCodeKey = ui.mtComboBox_keyValue->currentData().value<QString>();
        reInitAI_MtComBox_KeyValue(curCodeKey);
    }
}

/// <summary>
/// MtRangeLineEdit失去焦点事件
/// </summary>
void UnattendSubTableEditItem::slotMtRangeLineEditFocusOut()
{
    checkIsNormal();
}
