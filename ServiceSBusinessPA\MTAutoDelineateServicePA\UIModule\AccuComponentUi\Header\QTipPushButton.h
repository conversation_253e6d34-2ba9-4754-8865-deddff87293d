﻿#pragma once
#include <QString>
#include <QPushButton>
#include <QMouseEvent>
#include <QDebug>
#include <QTimer>
#include "tooltip.h"
#include "AccuComponentUi\Header\QMTUIModuleParam.h"


#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif

class CToolTip;

//QMTLineEdit参数
class  QTipPushButtonParam : public ICellWidgetParam
{
public:
    int _width;         //宽度
    int _height;        //高度
    QTipPushButtonParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);


};
Q_DECLARE_METATYPE(QTipPushButtonParam)

class  QTipPushButton : public QPushButton, public QMTAbstractCellWidget
{
    Q_OBJECT
public:
    explicit QTipPushButton(QWidget* parent = 0);
    ~QTipPushButton();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual QString GetCurText();

    void setTipText(QString  title, QString  info);
    void setTipText(QString  info);
    //设置标题是否隐藏
    void setTitleDisplayStatus(bool isHidden);
    //设置标题内容
    void setTipTitleText(QString  text);
    //设置消息内容
    void setTipInfoText(QString  text);
    //设置延迟多久显示tip
    void setDelay(int msec);
    //设置tip最长长度
    void setTipMaxWidth(int width);
protected:
    // void mouseMoveEvent(QMouseEvent* event); //必须设置鼠标追踪
    void enterEvent(QEvent*);
    void leaveEvent(QEvent*);
protected slots:
    void slotShowTips();
private:
    CToolTip* m_toolTip;
    QTimer* m_timer;
    QString m_title;
    QString m_info;
    QPoint m_point;
    int m_showDelay = 500; //默认500毫秒延迟，可以自主设置
    bool _bTipHide = false;
};

