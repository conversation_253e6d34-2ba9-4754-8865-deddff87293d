﻿#pragma once

#include <QDialog>
#include <QMessageBox>
#include <QGraphicsDropShadowEffect>
#include "Language.h"
#include "DarkeningWidget.h"
#include "QLabel_Dot.h"

/// <summary>
/// 操作提示框，从MessageDialog重构而来，建议用此类替代MessageDialog使用
/// </summary>

/*
已废弃此类，请使用MtMessageBox
*/
namespace Ui
{
class MMessageBox;
};
class  MMessageBox : public QDialog
{
    Q_OBJECT

    /// <summary>
    /// 属性
    /// </summary>
protected:
    QMessageBox::StandardButton resultButton;   //< 返回结果，不能用setResult和result返回，始终是0
    DarkeningWidget* darkenWidget;
    //virtual void showEvent(QShowEvent *) override;
    //bool hideDarken;
public:
    MMessageBox(QWidget* parent = Q_NULLPTR);
    ~MMessageBox();

    MMessageBox(QMessageBox::StandardButtons buttons, QList<QString> buttonText, QList<QString> message, bool hideLogo = true, QWidget* parent = Q_NULLPTR);
    QMessageBox::StandardButton getResultButton();
    static void Show(QString message, QString buttonText = tr("确认"), bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static void ShowCancel(QString message, QString buttonText = tr("确认"), bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static void ShowWarning2(QString message, QString buttonText = tr("确认"), bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static void ShowWarning(QString message, QString message2, QString buttonText = tr("确认"), bool hideDarken = true,  bool hideLogo = false, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton ShowWarning(bool cancel, QString message, QString message2 = "", QString noStyle = "", QString OkStyle = "", bool hideDarken = true, bool hideLogo = false, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton Show(QMessageBox::StandardButtons buttons, QList<QString> buttonText, QString message, bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton Show(QMessageBox::StandardButtons buttons, QList<QString> buttonText, QList<QString> message, bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton Show(QList<QString> buttonText, QList<QString> message, bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton ShowOKAndNo(bool cancel, QString message, QString message2 = "", QString noStyle = "", QString OkStyle = "", bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton ShowSaveAndNo(bool cancel, QString message, bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton ShowDeleteWarning(bool cancel, QString message, QString message2 = "", bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
protected:
    virtual void HideNoButton(bool value = true);
    virtual void HideYesButton(bool value = true);
    virtual void HideSaveButton(bool value = true);
    virtual void HideCancelButton(bool value = true);
    virtual void HideLogo(bool value = true);
    virtual void HideDarken(bool value = true);
    virtual void SetButtons(QMessageBox::StandardButtons buttons = QMessageBox::Yes);

    //style
    virtual void SetYesStyle(QString);
    virtual void SetNoStyle(QString);

    virtual void SetMessage1(QString message, bool hideLabel2 = true);
    virtual void SetMessage2(QString message);
    virtual void SetMessage(QList<QString> messages);
    virtual void SetButtonText(QList<QString> texts);
    virtual void SetIcon(QString urls);
    void SetMessageWidth(QString message, QLabel_Dot* label);
    void setEnglishLayout();
    //virtual bool eventFilter(QObject *, QEvent *) override;
    //private:
    //    Ui::MMessageBox ui;
private:
    Ui::MMessageBox* ui;
    QGraphicsDropShadowEffect* shadow_effect = nullptr;

};
