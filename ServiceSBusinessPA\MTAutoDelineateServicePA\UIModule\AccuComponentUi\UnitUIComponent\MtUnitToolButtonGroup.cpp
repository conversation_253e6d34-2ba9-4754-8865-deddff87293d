﻿#include "AccuComponentUi/Header/UnitUIComponent/MtUnitToolButtonGroup.h"
#include "ui_MtUnitToolButtonGroup.h"
#include "MtToolButton.h"
#include "CMtCoreDefine.h"
#include <qDebug>


MtUnitToolButtonGroupParam::MtUnitToolButtonGroupParam()
{
    _cellWidgetType = DELEAGATE_MtUnitToolButtonGroup;
}

MtUnitToolButtonGroupParam::~MtUnitToolButtonGroupParam()
{
}

QWidget* MtUnitToolButtonGroupParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    MtUnitToolButtonGroup* btns = new MtUnitToolButtonGroup(parent);
    btns->SetupCellWidget(*this);
    return btns;
}

MtUnitToolButtonGroup::MtUnitToolButtonGroup(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MtUnitToolButtonGroup;
    ui->setupUi(this);
}

MtUnitToolButtonGroup::~MtUnitToolButtonGroup()
{
    DeleteButtons();
    MT_DELETE(ui);
}

bool MtUnitToolButtonGroup::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();
    /*if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->SetButtonText(0, text);
        return true;
    }*/
    return false;
}

void MtUnitToolButtonGroup::SetEnableEdit(bool bEdit)
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        _buttonList[i]->setEnabled(bEdit);
    }
}

void MtUnitToolButtonGroup::SetupCellWidget(MtUnitToolButtonGroupParam& param)
{
    _pushBtnGroupParam = param;
    DeleteButtons();

    for (int i = 0; i < param._btnIconPathList.size(); ++i)
    {
        MtToolButton* button = new MtToolButton(this);
        ui->widget_btnsBK->layout()->addWidget(button);

        if (_pushBtnGroupParam._btnWidth > 0)
        {
            button->setFixedWidth(_pushBtnGroupParam._btnWidth);
        }
        else
        {
            //根据字符串自适应宽度
            /*QFont font = this->font();
            QFontMetrics fm(font);
            int width = fm.width(text) + 16;
            button->setFixedWidth(width);*/
        }

        if (_pushBtnGroupParam._btnHeight > 0)
        {
            button->setFixedHeight(_pushBtnGroupParam._btnHeight);
        }

        QString iconPath = param._btnIconPathList[i];
        button->setPixmapFilename(iconPath);

        if (_pushBtnGroupParam._btnIndexMtTypeMap.contains(i))
        {
            MtToolButton::MtType mtType = (MtToolButton::MtType)_pushBtnGroupParam._btnIndexMtTypeMap.value(i);
            button->setMtType(mtType);
        }

        if (_pushBtnGroupParam._btnIndexEnabledMap.contains(i))
        {
            bool enabled = _pushBtnGroupParam._btnIndexEnabledMap.value(i);
            button->setEnabled(enabled);
        }

        if (_pushBtnGroupParam._btnIndexTipMap.contains(i))
        {
            QString tipStr = _pushBtnGroupParam._btnIndexTipMap.value(i);
            button->setToolTipText(tipStr);
        }

        _buttonList.push_back(button);
        connect(button, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
    }
}

void MtUnitToolButtonGroup::DeleteButtons()
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        QWidget* widget = _buttonList.at(i);
        delete widget;
    }

    _buttonList.clear();
}

void MtUnitToolButtonGroup::HideButton(int index, bool bHide)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setHidden(bHide);
    }
    else
    {
    }
}

void MtUnitToolButtonGroup::SetButtonIconPathFile(int btnIndex, QString& iconPathFile)
{
    if (_buttonList.size() > 0 && btnIndex < _buttonList.size())
    {
        _buttonList[btnIndex]->setPixmapFilename(iconPathFile);
    }
    else
    {
    }
}

void MtUnitToolButtonGroup::SetButtonEnable(int index, bool enable)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setEnabled(enable);
    }
    else
    {
    }
}


bool MtUnitToolButtonGroup::GetCellChecked(int index)
{
    bool bChecked = false;

    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        bChecked = _buttonList[index]->isChecked();
    }

    return bChecked;
}


void MtUnitToolButtonGroup::SetButtonNoFocus(int btnIndex)
{
    if (_buttonList.size() > 0 && btnIndex < _buttonList.size())
    {
        _buttonList[btnIndex]->setFocusPolicy(Qt::NoFocus);
        _buttonList[btnIndex]->clearFocus();
    }
}

void MtUnitToolButtonGroup::slotButtonClicked(bool isChecked)
{
    QObject* sender = this->sender();
    int index = -1;

    for (int i = 0; i < _buttonList.size(); ++i)
    {
        MtToolButton* tempButton = _buttonList.at(i);

        if (tempButton == sender)
        {
            index = i;
            break;
        }
    }

    if (index >= 0)
    {
        emit sigClicked(index);
        emit sigButtonClicked(index, isChecked);
    }
}