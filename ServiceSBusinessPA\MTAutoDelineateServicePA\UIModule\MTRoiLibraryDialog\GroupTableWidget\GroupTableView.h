﻿#pragma once

//#include <QTableView>
#include <QLabel>
#include <QMouseEvent>
#include <QStandardItemModel>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"

class GroupTableView : public QMTAbstractTableView
{
    Q_OBJECT
public:
    enum
    {
        ColType_Move = 0,
        ColType_Name = 1,
        ColType_Type = 2,
        ColType_Operation
    };
    GroupTableView(QWidget* parent = Q_NULLPTR);

    /// <summary>
    /// 初始化列表
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="organInfoList">器官列表信息，用于删除组是判断是否可行</param>
    /// <param name="allOrganGroupList">分组列表信息</param>
    void initTableList(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList
                       , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allOrganGroupList);

    /// <summary>
    /// 添加新的组
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    void addNew(int newGroupId);

    /// <summary>
    /// 获取列表信息
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QList&lt;T&gt;.</returns>
    QList <n_mtautodelineationdialog::ST_OrganGroupInfo> getTableList();

protected slots:
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);

protected:
    //单元格创建完成回调(用于外部刷新定制化单元格界面)
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget) override;

protected:
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);
    void addRow(const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo);
    void insertRow(int rowIndex, const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo);

protected:
    //根据鼠标事件开启拖拽
    void mousePressEvent(QMouseEvent* event);
    void mouseMoveEvent(QMouseEvent* event);

    //拖拽响应事件
    void dragEnterEvent(QDragEnterEvent* event);
    void dragMoveEvent(QDragMoveEvent* event);
    void dropEvent(QDropEvent* event);

private:
    void DoDrag();                      //执行拖拽
    void MoveRow(int from, int to);     //真正执行移动行的功能

private:
    QList<n_mtautodelineationdialog::ST_Organ> m_organInfoList;   //缓存的器官信息

    QLabel* m_dragLine;         //高度设为2，用做指示线
    int m_rowHeight;            //表格的行高

    bool m_bValidPress;         //在鼠标移动时，判断之前按下时是否是在有效行上
    int m_rowFrom;              //拖动起始行
    int m_rowTo;                //拖动时(还没松开时)鼠标位置代表的行，会绘制一条指示线，类似QQ好友列表拖拽效果

    QString m_dragText;         //拖拽过程中跟随鼠标显示的内容
    QPoint m_dragPoint;         //拖拽起点
    QPoint m_dragPointAtItem;   //记录按下时鼠标相对该行的位置，在拖动过程中保持该相对位置
};
