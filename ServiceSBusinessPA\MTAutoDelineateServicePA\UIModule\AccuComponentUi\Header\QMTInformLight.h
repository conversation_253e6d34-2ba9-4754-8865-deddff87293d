﻿#ifndef QMTInformLight_H
#define QMTInformLight_H

/**
 * 呼吸点控件
 * 1:可设置呼吸间隔
 * 2:可设置颜色透明渐变步长
 * 3:可设置背景颜色
 */

#include <QWidget>
#include "QTipWidget.h"


#ifdef quc
#if (QT_VERSION < QT_VERSION_CHECK(5,7,0))
#include <QtDesigner/QDesignerExportWidget>
#else
#include <QtUiPlugin/QDesignerExportWidget>
#endif

class QDESIGNER_WIDGET_EXPORT QMTInformLight : public QWidget
#else
class  QMTInformLight : public QTipWidget //: public QWidget
#endif

{
    Q_OBJECT
    Q_PROPERTY(int step READ getStep WRITE setStep)
    Q_PROPERTY(int interval READ getInterval WRITE setInterval)
    Q_PROPERTY(QColor bgColor READ getBgColor WRITE setBgColor)

public:
    explicit QMTInformLight(QWidget* parent = 0);
    ~QMTInformLight();
    int getStep()                   const;
    int getInterval()               const;
    QColor getBgColor()             const;
    QSize sizeHint()                const;
    QSize minimumSizeHint()         const;
    void SetImagePath(int state, const QString&);        //设置底部显示图片
    void SetLightState(int);                        //设置呼吸灯状态,0：没有呼吸灯，1：呼吸灯
    void SetEnableBreathLight(bool bEnable);        //是否使能呼吸灯
    void SetValue(int);
    void AddOneValue(bool isWarn = false);          //右上角数字加一
    void DeleteOneValue();                          //右上角数字减一
    void ResetValue();                              //清空右上角数字
    void setElideValue(int n);
    int  elideValue()               const;
    void setMinValue(int n);
    int  minValue()               const;

    //是否显示动态图片
    void SetEnableMovie(bool bEnable);
    void SetMovieImgPathList(QStringList pathList);

public slots:
    //设置颜色透明渐变步长
    void setStep(int step);

    //设置定时器间隔
    void setInterval(int interval);

    //设置背景颜色
    void setBgColor(const QColor& bgColor);

    void slotTimeOut(); //定时器超时
protected:
    void paintEvent(QPaintEvent*) override;
    void DrawBgImage();
    void drawBg(QPainter* painter);
    void drawIdleBg(QPainter* painter);
    void mousePressEvent(QMouseEvent* event);
signals:
    void sigInformClicked();
private:
    int step;                       //颜色透明渐变步长
    int interval;                   //定时器间隔
    QColor _bgColor;                 //背景颜色
    QTimer* _timer;                  //绘制定时器
    int offset;                     //偏移量
    bool add;                       //是否增加
    //////
    //QString _imagePath;
    QMap<int, QString> _imagePahtMap;       //不同状态的背景图片
    QString _idleBgColor;       //无呼吸灯的时候背景颜色
    int _state = 0;             //0:无呼吸灯；1：有呼吸灯
    int _value = 0;
    int _minValue = 0;    // _value <= _minValue 则不显示
    int _elideValue = 99; // _value > _elideValue就补充 “+”:（ 如 "99+" ），初始化为 99

    bool _bEnableMovie = false;
    int _curMovieIndex = 0;
    QStringList _movieImgPathList;
};

#endif // QMTInformLight_H
