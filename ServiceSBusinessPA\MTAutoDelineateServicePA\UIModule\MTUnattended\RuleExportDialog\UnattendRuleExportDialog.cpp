﻿#include "UnattendRuleExportDialog.h"
#include "MtToolButton.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "DataDefine/InnerStruct.h"
#include <QFileDialog>
#include "MTUnattended/UnattendedSub/OptUnattendDataNew.h"


UnattendRuleExportDialog::UnattendRuleExportDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setDialogWidthAndContentHeight(466, 250); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("导出规则"));                 //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
}

UnattendRuleExportDialog::~UnattendRuleExportDialog()
{
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="isAdd">[IN]true新增</param>
/// <param name="stAddrInfo">[IN]导出地址</param>
/// <param name="usedExportScpMap">[IN]已被使用的地址信息(key-customId)</param>
/// <param name="allExportScpInfoList">[IN]所有导出到远程服务器信息</param>
void UnattendRuleExportDialog::init(const bool isAdd,
                                    const n_mtautodelineationdialog::ST_AddrSimple& stAddrInfo,
                                    const QMap<QString, n_mtautodelineationdialog::ST_AddrSimple>& usedExportInfoMap,
                                    const QList<n_mtautodelineationdialog::ST_AddrSimple>& allExportScpInfoList
)
{
    //初始化数据
    m_isAdd = isAdd;
    m_oldAddrInfo = stAddrInfo;
    ui.mtRadioButton_scp->setChecked(true);
    //导出范围
    ui.mtComboBox_range->addItem(tr("仅勾画"), QVariant::fromValue(2)); //2:只导出当前勾画RtStructure
    ui.mtComboBox_range->addItem(tr("全部"), QVariant::fromValue(3));   //3:本意导出当前勾画图像及RtStructure，这里意为全部
    ui.mtComboBox_range->setCurrentIndex(0);
    //统计已被使用的远程服务器名及共享文件夹名称
    QSet<QString> usedRemoteScpNameUpperSet;

    for (QMap<QString, n_mtautodelineationdialog::ST_AddrSimple>::const_iterator it = usedExportInfoMap.begin(); it != usedExportInfoMap.end(); it++)
    {
        n_mtautodelineationdialog::ST_AddrSimple stAddr = it.value();

        if (stAddr.addrType == 1) //共享文件夹
        {
            m_dirPathSet.insert(stAddr.stDirInfo.dirPath);
        }
        else if (stAddr.addrType == 4) //远程服务器
        {
            usedRemoteScpNameUpperSet.insert(stAddr.stScpInfo.serverName.toUpper());
        }
    }

    //检查*使用默认导出规则按钮*使能
    ui.mtPushButton_def->setEnabled(true);
    n_mtautodelineationdialog::ST_AddrSimple defaultAddr = OptUnattendDataNew::getDefaultExportAddr();

    if (defaultAddr.addrType == 4 || defaultAddr.addrType == 0)
        ui.mtPushButton_def->setEnabled(false);

    //远程服务器地址
    for (int i = 0; i < allExportScpInfoList.size(); i++)
    {
        n_mtautodelineationdialog::ST_AddrSimple stAddr = allExportScpInfoList[i];

        if (stAddr.addrType == 4 && usedRemoteScpNameUpperSet.contains(stAddr.stScpInfo.serverName.toUpper()) == false)
        {
            if (stAddr.stScpInfo.serverName == defaultAddr.stScpInfo.serverName)
            {
                ui.mtPushButton_def->setEnabled(true);
            }

            ui.mtComboBox_scp->addItem(stAddr.stScpInfo.serverName, QVariant::fromValue(stAddr));
        }
    }

    //判断远程节点是否已使用完
    if (ui.mtComboBox_scp->count() <= 0)
    {
        ui.mtRadioButton_scp->setEnabled(false);
        ui.mtRadioButton_dir->setChecked(true);
    }

    //导出格式
    ui.mtComboBox_format->addItem(tr("默认格式"), "0");
    ui.mtComboBox_format->addItem(tr("Eclipse 15+"), "3239DF85-89FB-419D-99BE-56E9C1D5DE50");
    ui.mtComboBox_format->setCurrentIndex(0);

    //编辑模式下切换选中的项目
    if (m_isAdd == false)
    {
        //导出范围(1:导出该患者所有数据 2:只导出当前勾画RtStructure 3:导出当前勾画图像及RtStructure 4:不导出)
        if (m_oldAddrInfo.exportRange == 2)
            ui.mtComboBox_range->setCurrentIndex(0);
        else if (m_oldAddrInfo.exportRange == 3)
            ui.mtComboBox_range->setCurrentIndex(1);

        //导出格式(0:普通格式  "3239DF85-89FB-419D-99BE-56E9C1D5DE50":Eclipse15  "7E42D45A-2447-4196-A1B9-20AA0B4BE6A1":Cyberknife)
        if (m_oldAddrInfo.exportFormat == "0")
            ui.mtComboBox_format->setCurrentIndex(0);
        else if (m_oldAddrInfo.exportFormat == "3239DF85-89FB-419D-99BE-56E9C1D5DE50")
            ui.mtComboBox_format->setCurrentIndex(1);

        if (m_oldAddrInfo.addrType == 1) //共享文件夹
        {
            ui.mtRadioButton_dir->setChecked(true);
            ui.lineEdit_dir->setText(m_oldAddrInfo.stDirInfo.dirPath);
            ui.mtCheckBox_dir->setChecked(m_oldAddrInfo.stDirInfo.mkSubType == 0 ? false : true);
            onMtRadioButton_scp(false);
        }
        else if (m_oldAddrInfo.addrType == 4) //远程SCP服务器
        {
            ui.mtRadioButton_scp->setChecked(true);

            for (int i = 0; i < ui.mtComboBox_scp->count(); i++)
            {
                n_mtautodelineationdialog::ST_AddrSimple stAddr = ui.mtComboBox_scp->itemData(i).value<n_mtautodelineationdialog::ST_AddrSimple>();

                if (stAddr.stScpInfo.serverName == m_oldAddrInfo.stScpInfo.serverName)
                {
                    ui.mtComboBox_scp->setCurrentIndex(i);
                    break;
                }
            }

            onMtRadioButton_scp(true);
        }
    }
    else
    {
        onMtRadioButton_scp(ui.mtComboBox_scp->count() <= 0 ? false : true);
    }

    //信号槽
    connect(ui.mtRadioButton_scp, &QRadioButton::toggled, this, &UnattendRuleExportDialog::onMtRadioButton_scp);    //远程服务器选中与否变化
    connect(ui.mtComboBox_scp, &QComboBox::currentTextChanged, this, &UnattendRuleExportDialog::onMtComboBox_scp);  //远程服务器下拉框文本变化
    connect(ui.mtPushButton_dir, &QPushButton::clicked, this, &UnattendRuleExportDialog::onMtPushButton_dir);       //配置共享目录
    connect(ui.mtPushButton_def, &QPushButton::clicked, this, &UnattendRuleExportDialog::onMtPushButton_def);       //使用默认导出规则
}

/// <summary>
/// 获取最新的导出地址信息
/// </summary>
/// <param name="outIsMod">[OUT]是否发生修改</param>
/// <param name="outAddrInfo">[OUT]导出地址信息</param>
void UnattendRuleExportDialog::getNewAddrInfo(bool& outIsMod, n_mtautodelineationdialog::ST_AddrSimple& outAddrInfo)
{
    outIsMod = m_isAdd;
    outAddrInfo.exportRange = ui.mtComboBox_range->currentData().value<int>();
    outAddrInfo.exportFormat = ui.mtComboBox_format->currentData().value<QString>();

    if (ui.mtRadioButton_scp->isChecked() == true) //远程服务器
    {
        outAddrInfo.addrType = 4;
        outAddrInfo.stScpInfo.serverName = ui.mtComboBox_scp->currentText();
    }
    else //共享文件夹
    {
        outAddrInfo.addrType = 1;
        outAddrInfo.stDirInfo.dirPath = ui.lineEdit_dir->text();
        outAddrInfo.stDirInfo.mkSubType = (ui.mtCheckBox_dir->isChecked() == true ? 1 : 0);
    }

    //判断是否发生修改
    if (outIsMod == true)
        return;

    if (outAddrInfo.addrType != m_oldAddrInfo.addrType || outAddrInfo.exportRange != m_oldAddrInfo.exportRange || outAddrInfo.exportFormat != m_oldAddrInfo.exportFormat)
    {
        outIsMod = true;
        return;
    }

    if (outAddrInfo.addrType == 4 && outAddrInfo.stScpInfo.serverName != m_oldAddrInfo.stScpInfo.serverName)
    {
        outIsMod = true;
        return;
    }

    if (outAddrInfo.addrType == 1 && (outAddrInfo.stDirInfo.dirPath != m_oldAddrInfo.stDirInfo.dirPath || outAddrInfo.stDirInfo.mkSubType != m_oldAddrInfo.stDirInfo.mkSubType))
    {
        outIsMod = true;
        return;
    }

    return;
}

/// <summary>
/// 远程节点选中
/// </summary>
void UnattendRuleExportDialog::onMtRadioButton_scp(bool checked)
{
    if (checked)
    {
        ui.mtComboBox_scp->setEnabled(true);
        ui.lineEdit_dir->setEnabled(false);
        ui.mtPushButton_dir->setEnabled(false);
        ui.mtCheckBox_dir->setEnabled(false);
        ui.mtComboBox_format->setEnabled(false);
        showExportFormatCombox(true, ui.mtComboBox_scp->currentText());
    }
    else
    {
        ui.mtComboBox_scp->setEnabled(false);
        ui.lineEdit_dir->setEnabled(true);
        ui.mtPushButton_dir->setEnabled(true);
        ui.mtCheckBox_dir->setEnabled(true);
        ui.mtComboBox_format->setEnabled(true);
        showExportFormatCombox(false, QString());
    }
}

/// <summary>
/// 远程节点下拉变化
/// </summary>
void UnattendRuleExportDialog::onMtComboBox_scp(const QString& text)
{
    showExportFormatCombox(true, text);
}

/// <summary>
/// 选择共享目录
/// </summary>
void UnattendRuleExportDialog::onMtPushButton_dir()
{
    QFileDialog* fileDialog = new QFileDialog(this);
    fileDialog->resize(240, 320);
    fileDialog->setFileMode(QFileDialog::Directory);
    QString path = fileDialog->getExistingDirectory();

    if (path.isEmpty())
        return;

    ui.lineEdit_dir->setText(path);
    ui.lineEdit_dir->setCursorPosition(0);
    this->repaint();
}

/// <summary>
/// 使用默认导出规则
/// </summary>
void UnattendRuleExportDialog::onMtPushButton_def()
{
    disconnect(ui.mtRadioButton_scp, &QRadioButton::toggled, this, &UnattendRuleExportDialog::onMtRadioButton_scp);
    n_mtautodelineationdialog::ST_AddrSimple defAddr = OptUnattendDataNew::getDefaultExportAddr();

    if (defAddr.addrType == 1) //共享文件夹
    {
        onMtRadioButton_scp(false);
        ui.mtRadioButton_dir->setChecked(true);
        ui.mtRadioButton_scp->setChecked(false);
        ui.lineEdit_dir->setText(defAddr.stDirInfo.dirPath);
        ui.mtCheckBox_dir->setChecked(defAddr.stDirInfo.mkSubType == 0 ? false : true);

        if (defAddr.exportFormat == "0")
            ui.mtComboBox_format->setCurrentIndex(0);
        else if (defAddr.exportFormat == "3239DF85-89FB-419D-99BE-56E9C1D5DE50")
            ui.mtComboBox_format->setCurrentIndex(1);
    }
    else if (defAddr.addrType == 4) //远程SCP服务器
    {
        onMtRadioButton_scp(true);
        ui.mtRadioButton_dir->setChecked(false);
        ui.mtRadioButton_scp->setChecked(true);
        ui.mtComboBox_scp->setCurrentText(defAddr.stScpInfo.serverName);
    }

    if (defAddr.exportRange == 2)
        ui.mtComboBox_range->setCurrentIndex(0);
    else if (defAddr.exportRange == 3)
        ui.mtComboBox_range->setCurrentIndex(1);

    connect(ui.mtRadioButton_scp, &QRadioButton::toggled, this, &UnattendRuleExportDialog::onMtRadioButton_scp);
}

/// <summary>
/// 显示导出格式的选中内容
/// </summary>
/// <param name="isSelectScpRadio">[IN]是否选中SCP服务器RadioButton</param>
/// <param name="scpServerName">[IN]远程SCP名称</param>
void UnattendRuleExportDialog::showExportFormatCombox(const bool isSelectScpRadio, const QString& scpServerName)
{
    if (isSelectScpRadio) //远程服务器
    {
        int count = ui.mtComboBox_scp->count();

        for (int i = 0; i < count; i++)
        {
            n_mtautodelineationdialog::ST_AddrSimple stAddr = ui.mtComboBox_scp->itemData(i).value<n_mtautodelineationdialog::ST_AddrSimple>();

            if (stAddr.stScpInfo.serverName == scpServerName)
            {
                for (int m = 0; m < ui.mtComboBox_format->count(); m++)
                {
                    QString formatStr = ui.mtComboBox_format->itemData(m).value<QString>();

                    if (formatStr == stAddr.exportFormat)
                    {
                        ui.mtComboBox_format->setCurrentIndex(m);
                        return;
                    }
                }
            }
        }
    }
    else //共享文件夹
    {
        for (int i = 0; i < ui.mtComboBox_format->count(); i++)
        {
            QString formatStr = ui.mtComboBox_scp->itemData(i).value<QString>();

            if (formatStr == m_oldAddrInfo.exportFormat)
            {
                ui.mtComboBox_format->setCurrentIndex(i);
                return;
            }
        }
    }

    return;
}

/// <summary>
/// 关闭按钮
/// </summary>
void UnattendRuleExportDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void UnattendRuleExportDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void UnattendRuleExportDialog::onBtnRight1Clicked()
{
    if (ui.mtRadioButton_scp->isChecked() == true)
    {
        if (ui.mtComboBox_scp->count() <= 0)
        {
            MtMessageBox::NoIcon::information_Title(this, tr("远程节点已使用完"), QString());
            return;
        }
    }
    else if (ui.mtRadioButton_dir->isChecked() == true)
    {
        if (ui.lineEdit_dir->text().isEmpty() == true || ui.lineEdit_dir->text().left(2) != "//")
        {
            MtMessageBox::NoIcon::information_Title(this, tr("请选择共享文件夹地址"), QString());
            return;
        }

        if (m_dirPathSet.contains(ui.lineEdit_dir->text()) == true)
        {
            MtMessageBox::NoIcon::information_Title(this, tr("该地址已存在，请选择其他共享文件夹地址"), QString());
            return;
        }
    }

    this->accept();
}
