﻿#include "UnattendSubTableShowItem.h"
#include "MTUnattended/RuleTableStyle/WidgetStyle/ExportRuleShowWidget.h"
#include "CommonUtil.h"
#include "DataDefine/InnerStruct.h"


UnattendSubTableShowItem::UnattendSubTableShowItem(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
}

UnattendSubTableShowItem::~UnattendSubTableShowItem()
{
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="uniqueKey">[IN]唯一标识</param>
/// <param name="allTemplateNameMap">[IN]模板名称集合(key-templateId value-templateName)</param>
/// <param name="stSketchRule">[IN]勾画规则</param>
/// <returns>item高度</returns>
int UnattendSubTableShowItem::init(const QString uniqueKey, const QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>>& allTemplateNameMap, const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify)
{
    m_uniqueKey = uniqueKey;
    //行最小高度
    int itemHeight = getRowMinHeight(stSketchIdentify.recognitionType);
    //模态
    ui.mtLabel_modality->setText(stSketchIdentify.modality.toUpper());

    //识别规则
    if (stSketchIdentify.recognitionType == 1) //AI部位识别
    {
        ui.mtLabel_dicomRec->setText(tr("AI部位识别") + " - ");
        ui.mtLabel_part->setText(CommonUtil::getTextFromBodyPartAI(stSketchIdentify.aiBodypart));
        ui.formLayout->removeRow(1);
    }
    else if (stSketchIdentify.recognitionType == 2) //DICOM字段
    {
        itemHeight = 66;

        if (stSketchIdentify.dcmTagMap.isEmpty() == true)
            return 0;

        ui.mtLabel_dicomRec->setText(tr("Dicom字段识别") + " - ");
        ui.mtLabel_part->setText(CommonUtil::getTextFromDicomTag(stSketchIdentify.dcmTagMap.begin().key()));
        ui.mtLabel_word->setText(CommonUtil::getTextFromDicomSex(stSketchIdentify.dcmTagSex) + "   " + CommonUtil::stringListToStr(stSketchIdentify.dcmTagMap.begin().value()));
    }

    //模板名称
    if (allTemplateNameMap[n_mtautodelineationdialog::OptDcmType_CT].contains(stSketchIdentify.sketchCollectionId) == true)
        ui.mtLabel_model->setText(allTemplateNameMap[n_mtautodelineationdialog::OptDcmType_CT][stSketchIdentify.sketchCollectionId]);
    else if (allTemplateNameMap[n_mtautodelineationdialog::OptDcmType_MR].contains(stSketchIdentify.sketchCollectionId) == true)
        ui.mtLabel_model->setText(allTemplateNameMap[n_mtautodelineationdialog::OptDcmType_MR][stSketchIdentify.sketchCollectionId]);

    //导出规则
    if (stSketchIdentify.addrInfoMap.isEmpty() == true)
    {
        ExportRuleShowWidget* itemWidget = new ExportRuleShowWidget(QString(), QString(), QString(), this);
        ui.verticalLayout_3->addWidget(itemWidget);
    }
    else
    {
        for (QMap<QString/*唯一标识(yyyyMMddhhmmss)*/, n_mtautodelineationdialog::ST_AddrSimple>::const_iterator iter_addr = stSketchIdentify.addrInfoMap.begin(); iter_addr != stSketchIdentify.addrInfoMap.end(); iter_addr++)
        {
            n_mtautodelineationdialog::ST_AddrSimple stAddr = iter_addr.value();
            QString addrStr;

            if (stAddr.addrType == 1)
                addrStr = QString(tr("共享文件夹")) + " - " + stAddr.stDirInfo.dirPath;
            else if (stAddr.addrType == 4)
                addrStr = QString(tr("远程节点")) + " - " + stAddr.stScpInfo.serverName;

            ExportRuleShowWidget* itemWidget = new ExportRuleShowWidget(
                CommonUtil::getTextFromExportRange(stAddr.exportRange),
                CommonUtil::getTextFromExportFormat(stAddr.exportFormat),
                addrStr,
                this
            );
            //
            itemHeight += 30; //默认用33
            ui.verticalLayout_3->addWidget(itemWidget);
        }

        itemHeight -= 30;

        if ((stSketchIdentify.recognitionType == 1 && stSketchIdentify.addrInfoMap.size() == 1) ||
            (stSketchIdentify.recognitionType == 2 && stSketchIdentify.addrInfoMap.size() <= 2))
        {
            itemHeight = getRowMinHeight(stSketchIdentify.recognitionType);
        }
    }

    return itemHeight;
}

/// <summary>
/// 获取最小行高度
/// </summary>
/// <param name="recognitionType">[IN]部位识别类型(1:AI部位识别 2:DICOM字段)</param>
/// <returns>最小行高度</returns>
int UnattendSubTableShowItem::getRowMinHeight(const int recognitionType)
{
    if (recognitionType == 1)
    {
        return 36;
    }

    return 66;
}
