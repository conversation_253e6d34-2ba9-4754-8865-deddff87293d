﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QValidator>
#include "AccuComponentUi\Header\QMTUIModuleParam.h"
#include "MtRangeLineEdit.h"


//QCustMtLineEdit2参数
class QCustMtLineEdit2Param : public ICellWidgetParam
{
public:
    QList<QPair<double, double>> disableRangeList;
    QString _regExpStr;         //正则表达式
    int _maxLength = -1;        //允许输入最大长度
    QString _placeholderText;   //占位字符串

    double minValue;                //最小值
    double maxValue;                //最大值
    double initValue;               //初始值
    int decimals = 2;               //小数点位数
    bool bContainMin = true;        //是否允许包含最小值。true:表示"[min,";false:表示"(min"
    bool bContainMax = true;        //是否允许包含最大值。true:表示"max]";false:表示"max)"

    double seperateLeftValue = 360.00;    //分节值左侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
    double seperateRightValue = 0.00;     //分节值右侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
    QString unit;                         //单位

    QCustMtLineEdit2Param();
    ~QCustMtLineEdit2Param();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustMtLineEdit2Param)

namespace Ui
{
class QCustMtLineEdit2;
}
class QCustMtLineEdit2 : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustMtLineEdit2(QWidget* parent = Q_NULLPTR);
    virtual ~QCustMtLineEdit2();
    void SetupCellWidget(QCustMtLineEdit2Param& cellWidgetParam); //初始化单元格界面

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);      //更新界面接口
    virtual QString GetCurText();                           //获取当前界面展示文案
    virtual void SetEnableEdit(bool bEdit);                 //设置是否允许编辑

    MtRangeLineEdit* GetLineEdit();         //这边获取只允许用来设置范围
    void SetEditRange(const QCustMtLineEdit2Param& inputInfo);
    void SetRegExpStr(QString&);            //设置正则表达式
    void SetItemValidator(QValidator*);     //设置输入的规则
    void setText(const QString&);           //设置字符串
    void SetMyStyleSheet(QString&);         //设置样式


    QString getText();                      //获取当前text
signals:
    void sigClicked(int);
    void currentTextChanged(const QString& newText);         //文案改变了
    void sigEditFinish(QString newText, QString oldText);

protected:
    bool eventFilter(QObject* obj, QEvent* evt);
    //void resizeEvent(QResizeEvent* event);          //保证...
    void mousePressEvent(QMouseEvent* event);

    void UpdateLineEditText(const QString& text);     //只更新编辑框文案
private slots:
    void slotLineEditingFinished();

private:
    Ui::QCustMtLineEdit2* ui = nullptr;
    QString _oldText;
    QValidator* _validator = nullptr;
};
