<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTSeparateText</class>
 <widget class="QWidget" name="QMTSeparateText">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>68</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTSeparateText</string>
  </property>
  <property name="styleSheet">
   <string notr="true">QFrame#line,QFrame#line_2
{
background:rgba(45,52,64,0.5);
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>9</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="Line" name="line">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>1</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>1</height>
      </size>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QLabel" name="label">
     <property name="text">
      <string>TextLabel</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="Line" name="line_2">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>1</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>1</height>
      </size>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
