﻿// *********************************************************************************
// <remarks>
// FileName    : UnattendSubTableShowItem
// Author      : zlw
// CreateTime  : 2024-05-22
// Description : 无人值守规则界面列表item(内嵌于: UnattendSubWidget页签下的mtListWidget_right)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "AccuComponentUi\Header\QMTUIModuleParam.h"
#include "ui_UnattendSubTableShowItem.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


/// <summary>
/// tableWidget-item组件
/// </summary>
class UnattendSubTableShowItem : public QWidget
{
    Q_OBJECT

public:
    UnattendSubTableShowItem(QWidget* parent = nullptr);
    ~UnattendSubTableShowItem();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="uniqueKey">[IN]唯一标识</param>
    /// <param name="templateNameMap">[IN]模板名称集合(key-templateId value-templateName)</param>
    /// <param name="stSketchRule">[IN]勾画规则</param>
    /// <returns>item高度</returns>
    int init(const QString uniqueKey, const QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>>& allTemplateNameMap, const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify);

protected:
    /// <summary>
    /// 获取最小行高度
    /// </summary>
    /// <param name="recognitionType">[IN]部位识别类型(1:AI部位识别 2:DICOM字段)</param>
    /// <returns>最小行高度</returns>
    int getRowMinHeight(const int recognitionType);

private:
    Ui::UnattendSubTableShowItemClass ui;
    QString m_uniqueKey;
};
