﻿#include "AccuComponentUi/Header/Language.h"
#include <QFont>

int Language::type = Chinese;
bool Language::customize = false;
void Language::SelectionControl(QObject* obj1, QString sFontSize)
{
    if (obj1->inherits("QPushButton"))
    {
        switch (Language::type)
        {
            case English:
                sFontSize = ENGLISH_SIZE_BUTTON;
                break;

            case Chinese:
                sFontSize = CHINESE_SIZE_BUTTON;
                break;
        }
    }

    if (!sFontSize.contains("font-size"))
    {
        return;
    }

    // 1. 如果是自定义继承QPushButton的类QPushButton_***，那么不允许在Language::setFontSize中统一修改。
    QString className = obj1->metaObject()->className();

    if (className.indexOf("QToolButton_") != -1
        || className.indexOf("QComboBox_") != -1)
    {
        return;
    }

    if (obj1->inherits("QPushButton")
        || obj1->inherits("QToolButton")
        || obj1->inherits("QLabel")
        || obj1->inherits("QLineEdit")
        || obj1->inherits("QRadioButton")
        || obj1->inherits("QTabBar")
        )
    {
        QWidget* widget = qobject_cast<QWidget*>(obj1);
        // QFont font = widget->font();
        // sFontSize = sFontSize.replace("font-size:", "");
        // sFontSize = sFontSize.replace("px", "");
        // sFontSize = sFontSize.replace(";", "");
        // font.setPixelSize(sFontSize.toInt());
        // widget->setFont(font);
        widget->setStyleSheet(sFontSize);// 只能使用setStyleSheet，直接setFont还是会被父窗体styleSheet影像。
    }
}
#if IGNORE
#endif