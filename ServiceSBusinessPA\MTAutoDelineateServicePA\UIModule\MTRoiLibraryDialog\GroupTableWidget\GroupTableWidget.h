﻿#pragma once

#include <QWidget>
#include "ui_GroupTableWidget.h"
#include "MtTemplateDialog.h"


class GroupTableWidget : public MtTemplateDialog
{
    Q_OBJECT

public:
    GroupTableWidget(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList,
                     const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allOrganGroupList,
                     const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
                     QWidget* parent = nullptr);
    ~GroupTableWidget();

    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getTableListInfo();

protected slots:
    void on_mtToolButton_add_clicked();
    void on_mtToolButton_add2_clicked();

protected:
    virtual void onBtnRight1Clicked();

private:
    Ui::GroupTableWidget ui;

    int m_newGroupId = -1;      //新增分组ID，从-1递减
};
