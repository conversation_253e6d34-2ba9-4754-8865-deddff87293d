﻿#pragma once

#include <QObject>
#include <QJsonObject>
#include <QMap>
#include <QColor>
#include "Language.h"

//UI define的数据

/*
Qss property名称定义
QssPropertyKey,界面设置属性的key
QssProperty... 界面设置属性的value，根据不同的value映射到不同的界面样式
*/
namespace QssProperty
{
#define QssPropertyKey             "styleType"
//QWidget
#define QssPropertyWidgetMain      "WidgetMain"            //background-color:rgba(26,29,35,1);
#define QssPropertyWidgetSecond    "WidgetSecond"          //background-color:rgba(37,41,48,1);
#define QssPropertyWidgetThird     "WidgetThird"            //background-color:rgba(25,28,34,1);
#define QssPropertyWidgetDialog    "WidgetDialog"          //一般是弹窗的background-color: rgb(56, 67, 85);

//QLabel（默认统一颜色为219, 226, 241）
#define QssPropertyLabelFont20               "LabelFont20"                    //字体20号
#define QssPropertyLabelMainTitle            "LabelMainTitle"                 //字体16号,
#define QssPropertyLabelSecondTitle          "LabelSecondTitle"               //字体14号
#define QssPropertyLabelThirdTitle           "LabelThirdTitle"                //字体12号
#define QssLabelSecondTitlComment            "LabelContent_bigger_comment"    //字体14号，透明度0.6
#define QssLabelThirdTitleComment            "LabelThirdTitle_comment"        //字体12号，透明度0.6

//QPushButton
#define QssPropertyPushButtonBlue       "PushButtonBlue"        //蓝色按键
#define QssPropertyPushButtonGray       "PushButtonGray"        //灰色按键
#define QssPropertyPushButtonRed        "PushButtonRed"         //警告红色按键

//QToolButton,默认的是工具栏样式
#define QssPropertyToolBtnMenu          "ButtonMenu"            //菜单栏按键
#define QssPropertyToolBtnLink          "ButtonLink"            //链接到某个网址的按键样式
#define QssPropertyToolBtnCommom        "ButtonCommom"          //

//QLineEdit样式
#define QssPropertyLineEidtComment      "Edit_comment"          //透明度0.6
#define QssPropertyLineEidtSearch       "Edit_search"           //搜索输入框
#define QssPropertyLineEidtdoubleEdit   "Edit_doubleEdit"       //双击修改样式

//checkbox
#define QssPropertyCheckBox1616         "Checkbox1616"          //设置该属性，以防点击后闪动

//QRadioButton（默认统一颜色为219, 226, 241）
#define QssPropertyRadioButtonComment   "RadioButton_comment"       //字体透明度0.6

}

#define DIV_STR   "|"   //存于json的字符串分隔线
#define ROWITEM_READ_PATH  ("Temp/Read/")       //是否已读默认文件夹路径

/********************字体样式******************************/
//默认字体
#define DefaultFontFamily  QString("font-family: \"Microsoft Yahei\",Arial;")
//字体样式
#define GetFontStyleSheetStr(fontSize, red, green, blue, alpha) (QString("font-size:%1px;color:rgba(%2,%3,%4,%5);%6").arg(fontSize).arg(red).arg(green).arg(blue).arg(alpha).arg(DefaultFontFamily))
//背景色
#define GetBackgroundStyleSheetStr(red, green, blue, alpha) (QString("background:rgba(%1,%2,%3,%4);").arg(red).arg(green).arg(blue).arg(alpha))
