﻿#include "ModelOrganWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtFrame.h"
#include "MtLineLabel.h"
#include "ModelOrganItem.h"


ModelOrganWidget::ModelOrganWidget(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,ModelOrganWidget, " << errMsg.toStdString();
        }
    }
}

ModelOrganWidget::~ModelOrganWidget()
{
}

/// <summary>
/// 设置标题
/// </summary>
/// <param name="str">[IN]文本</param>
void ModelOrganWidget::setModelTitle(const QString& str)
{
    ui.mtLabel_modelName->setText(str);
}

/// <summary>
/// 清空所有item
/// </summary>
void ModelOrganWidget::clearAllItem()
{
    ui.mtListWidget_roi->clear();
}

/// <summary>
/// 添加roiItem集合
/// </summary>
/// <param name="uniqueKey">[IN]唯一标识(模型:modelName.id 模板:templateName.id)</param>
/// <param name="titleStr">[IN]小标题(为空时不会有中心小标题出现)</param>
/// <param name="sketchModel">[IN]模型信息</param>
void ModelOrganWidget::addRoiItem(const QString& uniqueKey, const QString& titleStr, const n_mtautodelineationdialog::ST_SketchModel& sketchModel)
{
    //显示小标题
    if (titleStr.isEmpty() == false)
    {
        MtLineLabel* lineLabel = new MtLineLabel();
        lineLabel->setStyleSheet("background-color:transparent;");
        lineLabel->setLineColor(QColor(212, 212, 212, 25));
        lineLabel->setText(titleStr);
        QListWidgetItem* listItem = new QListWidgetItem;
        ui.mtListWidget_roi->addItem(listItem);
        ui.mtListWidget_roi->setItemWidget(listItem, lineLabel);
        listItem->setSizeHint(QSize(0, 26));
    }

    //显示器官
    QStringList organCheckedList;
    organCheckedList.reserve(sketchModel.organList.size() + 1);

    for (int i = 0; i < sketchModel.organList.size(); i++)
    {
        if (sketchModel.organList[i].isVisiable == true)
        {
            organCheckedList.push_back(sketchModel.organList[i].customOrganName);
        }
    }

    for (int j = 0; j < organCheckedList.size(); j++)
    {
        QString organName1;
        QString organName2;
        organName1 = organCheckedList[j];

        if (++j < organCheckedList.size())
        {
            organName2 = organCheckedList[j];
        }

        ModelOrganItem* modelOrganItem = new ModelOrganItem(organName1, organName2);
        QListWidgetItem* listItem = new QListWidgetItem;
        ui.mtListWidget_roi->addItem(listItem);
        ui.mtListWidget_roi->setItemWidget(listItem, modelOrganItem);
        listItem->setSizeHint(QSize(0, 26));
    }
}
