﻿#pragma once

#include <QObject>
#include <QJsonObject>
#include <QMap>
#include <QColor>
#include <QMetaType>
#include <qDebug>
#include "QMTEnumDef.h"
#include "Language.h"

#include "CMtCoreWidgetUtil.h"

/***************此处罗列了表格支持的单元格********************************/
//所有支持的单元格参数
class QTipPushButtonParam;
class QLabelParam;
class QMTLineEditParam;
class QMTAbsComboBoxParam;
class QMTAbsHorizontalBtnsParam;
class QMTCheckBoxParam;
class QMTButtonMenuWidgetParam;
class QMTVDoubleLabelDotParam;
class QMTProcessUiParam;

//所有支持的单元格界面类
class QTipPushButton;
class QLabel_Dot;
class QMTLineEdit;
class QMTAbsComboBox;
class QMTAbsHorizontalBtns;
class QMTCheckBox;
class QMTButtonMenuWidget;
class QMTVDoubleLabelDot;
class QMTProcessUi;

//新版本UI使用的单元格
class QCustMtLabel;
class QCustMtLineEdit;
class QCustMtComboBox;
class QCustMtSearchComboBox;
class MtUnitPushButtonGroup;
class MtUnitToolButtonGroup;
class MtUnitLabelWithColor;


/********单元格界面基类************/
class  QMTAbstractCellWidget
{

public:

    enum CellWidgetRole
    {
        Role_MainValue = Qt::UserRole + 1,      //唯一标识
        Role_ParentValue,                       //父类值
        Role_Column,                            //第几列
        Role_RowType,                           //行类型
        Role_DelegateType,                      //单元格委托类型
        Role_User,                              //用户自定义
    };
    QMTAbstractCellWidget();
    virtual~QMTAbstractCellWidget();
    /**********单元格公共接口***********/
    //更新界面接口(每个子类都必须实现)
    virtual bool UpdateUi(const QVariant& updateData) = 0;
    //设置是否允许编辑(有是否允许编辑状态的子控件必须实现)
    virtual void SetEnableEdit(bool bEdit);
    //按键使能设置
    virtual void SetButtonEnable(int btnIndex/**/, bool bEnable);

    //获取当前界面展示文案(有显示文案的空间必须实现，比如label，combobox，lineEdit)
    virtual QString GetCurText();
    //获取状态
    virtual int GetCellState();
    //获取checked
    virtual bool GetCellChecked(int index);

    /********************************/
    //设置和获取单元格数据
    virtual void SetCellData(int role, const QString& data);    //role见CellWidgetRole
    virtual QString GetCellData(int role);

private:
    QMap<int, QString> _custDataMap;     //存储自定义的数据
};

/********单个组件参数基类(需要一级列表支持的单元格参数都需要继承此类)************/
class  ICellWidgetParam
{
public:
    virtual ~ICellWidgetParam()  //必须使用析构，防止内存泄露
    {
        //qWarning() << "~ICellWidgetParam() : " << _cellWidgetType;
    }
    virtual QWidget* CreateUIModule(QWidget* parent = NULL) = 0;    //创建对应的组件

public:
    int _cellWidgetType = 0;  //单元格的类型（必填,在子类构造函数中赋值）参见枚举CellWidgetDelegateType，头文件QMTEnumDef.h
    int _mtType = -1;         //单元格的mtType类型。默认为-1，使用默认的，否则使用对应的mtType
    int _cellHeight = -1;     //单元格的高度(选填，如果大于0，那么单元格高度会设置这个值)
    int _paddingLeft = -1;    //往左缩进像素（没有设置的话按照表格自动对齐）
    QString _rowValue;        //单元格对应行的唯一值(外部无需赋值，可空)
    QString _parentValue;     //预留父级对应的唯一值(二级甚至多级列表中用到)（可空）
    int _column = 0;          //第几列（外部无需赋值，可空）
    QString _text;            //显示的text（可空）
    bool _isChecked = false;   //单元格checked状态，一般按键使用。
    int _state = 0;           //单元格状态，一般checkbox使用，转换成Qt::CheckState
    QString _styleSheetStr;   //样式字符串（可空）
    bool _bUpdate = false;    //是否有更新（可空）
    QVariant _updateData;     //更新的数据（可空）
};

/*****************一级列表参数***************************/
//表头参数
class  QMTAbsTableHeadParam
{
public:
    QMTAbsTableHeadParam();
    //QMap<int/*column index*/, int/*CellWidgetDelegateType*/> _colDelegateTypeMap;   //委托类型参见CellWidgetDelegateType(后续废除)
    QStringList _headStrList;                     //表头文案
    int _defaultColumn = 1;                       //列个数
    QMap<int/*column index*/, ICellWidgetParam*> _headCellParamMap;     //定制化单元格
    QMap<int/*column index*/, int/*column width*/> _columnWidthMap;     //宽度
    //
    int _headHeight = 30;
    int _fontSize = 12;          //默认是color4的颜色。
    int _paddingLeft = 8;        //左侧偏移像素
    QColor _backGroundColor;     //表头背景色颜色
    QColor _textColor;           //字体颜色
    bool _isHideHeadCloumnLine = false;//是否隐藏网格竖线-表头.
    bool _isShowFirstCloumnLeftLine = false;//是否显示第一列左侧竖线--只有_isHideHeadCloumnLine = false的时候才会该值为true才会生效。否则默认隐藏
};

//列表行UI参数
class  QMTAbsRowWidgetItemParam
{
public:
    QMTAbsRowWidgetItemParam();
    static int GetCurrentType();
    static int s_type;                      //用于前一个rowitem的类型

    /********************数据层****************************/
    int _templateWidgetType = 0;    //此参数是界面模板必须重设置，使用GetCurrentType()
    //ui
    QMTAbsTableHeadParam _headParam;                //表头参数
    int _rowWidgetHeight = 76;                      //行高
    int _rightMargin = 16;                          //右侧间距
    int _leftMargin = 16;                           //左侧间距
    int _rowSpacing = 1;                            //各行间距
    int _itemLayouSpacing = 8;                      //各个column间隔
    bool _showGrid = true;                          //栅格线是否显示
    bool _isHideCloumnLine = false;                 //是否隐藏网格竖线(_showGrid = false,_isHideCloumnLine = true:隐藏了表内容的竖栅格线)
    bool _enableFlex = true;                        //是否可以拉伸
    int _fontsize = 12;                             //字体大小，默认是color4的颜色。如果需要其它颜色外部指定mtType

    QColor _textColor;             //字体颜色
    QColor _gridColor;             //边框颜色
    QColor _canvasBackColor;       //画布背景色。=QColor(),什么都不传,isValid=false则颜色为透明
    QColor _unselectBackColor;     //未选中背景色。=QColor(),什么都不传,isValid=false则颜色为透明
    QColor _selectBackColor;       //选中背景色
    QColor _selectBorderColor;     //选中的border的颜色
    QColor _borderColor;           //悬浮边框色
    bool _isEnableHoverChangeBorderColor = true;        //悬浮是否改变边框色
    bool _isEnableHoverChangeBackColor = false;         //悬浮是否改变背景色

    bool _isEnableSelectChangeStyle = true;             //选中的背景色和border颜色是否生效
    void Init();
};

/***********************************************/