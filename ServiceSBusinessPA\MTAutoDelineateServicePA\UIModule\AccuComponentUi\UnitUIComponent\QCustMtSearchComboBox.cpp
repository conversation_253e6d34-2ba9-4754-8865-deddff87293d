﻿#include "AccuComponentUi\Header\UnitUIComponent\QCustMtSearchComboBox.h"
#include "ui_QCustMtComboBox.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include <QMouseEvent>
#include <QPainter>
#include "CMtCoreDefine.h"
#include "MtCustSearchComboBox.h"



/*********************单元组件的Param*********************************/
QCustMtSearchComboBoxParam::QCustMtSearchComboBoxParam()
{
    _cellWidgetType = DELEAGATE_QCustMtSearchComboBox;
}

QCustMtSearchComboBoxParam::~QCustMtSearchComboBoxParam()
{
}
QWidget* QCustMtSearchComboBoxParam::CreateUIModule(QWidget* parent)
{
    QCustMtSearchComboBox* comboBox = new QCustMtSearchComboBox(parent, m_newStyle);
    comboBox->SetupCellWidget(*this);
    return comboBox;
}
/*****************************************************************/


/********************单元组件****************************/
QCustMtSearchComboBox::QCustMtSearchComboBox(QWidget* parent, bool bNewStyle/* = false*/)
    : QWidget(parent)
{
    ui = new Ui::QCustMtComboBox;
    ui->setupUi(this);
    m_comboBox = new MtCustSearchComboBox(this);
    ui->widget_lineBK->layout()->addWidget(m_comboBox);
    m_comboBox->setMtType(MtCustSearchComboBox::EM_MtType::searchcombobox1);
    m_comboBox->setElideMode(Qt::ElideRight);
    m_comboBox->setViewTextElideMode(Qt::ElideRight);
    m_comboBox->setEnabledWheelEvent(false);
    m_comboBox->installEventFilter(this);
    connect(m_comboBox, &MtCustSearchComboBox::sigClicked, this, &QCustMtSearchComboBox::sigClicked);
}

QCustMtSearchComboBox::~QCustMtSearchComboBox()
{
    disconnect(m_comboBox, &MtCustSearchComboBox::currentTextChanged, this, &QCustMtSearchComboBox::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
    MT_DELETE(m_comboBox);
    MT_DELETE(ui);
}

void QCustMtSearchComboBox::SetupCellWidget(QCustMtSearchComboBoxParam& cellWidgetParam)
{
    AddItems(cellWidgetParam._textList, cellWidgetParam._userDataList);

    //为Max和Min选项添加蓝绿icon
    for (int i = 0; i < cellWidgetParam.listMaxMin.size(); ++i)
    {
        if (cellWidgetParam.listMaxMin[i] == 0)
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/color_min.png"), Qt::DecorationRole);
        }
        else if (cellWidgetParam.listMaxMin[i] == 1)
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/color_max.png"), Qt::DecorationRole);
        }
        else
        {
            m_comboBox->setItemIcon(i, QIcon());
        }
    }

    disconnect(m_comboBox, &MtCustSearchComboBox::currentTextChanged, this, &QCustMtSearchComboBox::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));

    if (cellWidgetParam._comboBoxIndex >= 0)
    {
        m_comboBox->setCurrentIndex(cellWidgetParam._comboBoxIndex);
    }
    else if (cellWidgetParam._text.size() >= 0 && cellWidgetParam._textList.indexOf(cellWidgetParam._text) >= 0)
    {
        m_comboBox->setCurrentText(cellWidgetParam._text);
    }
    else
    {
        m_comboBox->setCurrentIndex(-1);
    }

    connect(m_comboBox, &MtCustSearchComboBox::currentTextChanged, this, &QCustMtSearchComboBox::slotCurrentTextChanged);
    connect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));

    if (cellWidgetParam.m_newStyle)
    {
        QString styleStr = "QComboBox QAbstractItemView::item:hover { background-color: rgba(@colorA2,1); }";
        CMtCoreWidgetUtil::formatStyleSheet(styleStr);
        m_comboBox->setStyleSheet(styleStr);
    }
}

bool QCustMtSearchComboBox::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        QString preText = m_comboBox->currentText();
        m_comboBox->blockSignals(true);

        if (text != preText)    //防止多次抛出信号
        {
            QStringList itemStrList = GetAllItemStrList();

            if (itemStrList.indexOf(text) < 0)
            {
                m_comboBox->setCurrentIndex(-1);
            }
            else
            {
                m_comboBox->setCurrentText(text);
            }
        }

        m_comboBox->blockSignals(false);
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        m_comboBox->setEnabled(bEditEnable);
        return true;
    }
    else if (QMetaType::Int == userType)
    {
        bool index = updateData.toInt();
        m_comboBox->setCurrentIndex(index);
        return true;
    }

    return false;
}

QString QCustMtSearchComboBox::GetCurText()
{
    return m_comboBox->currentText();
}

QString QCustMtSearchComboBox::currentText()
{
    return m_comboBox->currentText();
}

void QCustMtSearchComboBox::SetEnableEdit(bool bEdit)
{
    m_comboBox->setEnabled(bEdit);
}

void QCustMtSearchComboBox::setCurrentIndex(int index)
{
    m_comboBox->setCurrentIndex(index);
}

void QCustMtSearchComboBox::setCurrentText(const QString& text)
{
    m_comboBox->setCurrentText(text);
}

QStringList QCustMtSearchComboBox::GetAllItemStrList()
{
    QStringList itemStrList;

    for (int idx = 0; idx < m_comboBox->count(); idx++)
    {
        QString itemName = m_comboBox->itemText(idx);
        itemStrList << itemName;
    }

    return itemStrList;
}

void QCustMtSearchComboBox::AddItem(const QString& itemStr, const QVariant& auserData)
{
    m_comboBox->addItem(itemStr, auserData);
}

void QCustMtSearchComboBox::AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList/* = QList<QVariant>()*/)
{
    if (0 == userDataList.size() || itemStrList.size() != userDataList.size())
    {
        m_comboBox->addItems(itemStrList);
    }
    else if (itemStrList.size() == userDataList.size())
    {
        for (int i = 0; i < itemStrList.size(); ++i)
        {
            m_comboBox->addItem(itemStrList[i], userDataList[i]);
        }
    }
}

void QCustMtSearchComboBox::RemoveItem(const QString& itemStr)
{
    QStringList itemStrList = GetAllItemStrList();
    int index = itemStrList.indexOf(itemStr);
    m_comboBox->removeItem(index);
}

void QCustMtSearchComboBox::ClearItems()
{
    m_comboBox->clear();
}

MtCustSearchComboBox* QCustMtSearchComboBox::GetMtComboBox()
{
    return m_comboBox;
}

bool QCustMtSearchComboBox::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (m_comboBox == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
        }
    }

    return QWidget::eventFilter(obj, evt);
}

void QCustMtSearchComboBox::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void QCustMtSearchComboBox::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit sigClicked(1);
    }

    QWidget::mousePressEvent(event);
}

void QCustMtSearchComboBox::slotCurrentTextChanged(const QString& text)
{
    emit currentTextChanged(text);
}

void QCustMtSearchComboBox::slotCurrentIndexChanged(int index)
{
    emit currentIndexChanged(index);
}




