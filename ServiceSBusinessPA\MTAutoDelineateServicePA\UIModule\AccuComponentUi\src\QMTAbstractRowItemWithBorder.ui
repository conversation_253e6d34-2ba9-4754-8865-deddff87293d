﻿<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTAbstractRowItemWithBorder</class>
 <widget class="QWidget" name="QMTAbstractRowItemWithBorder">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>733</width>
    <height>75</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTAbstractRowItemWithBorder</string>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
	border-style:none solid solid none;/*上右下左*/	
	border-width:1px;
	border-color:rgba(191,200,215,0.1);
	background-color: rgb(44, 50, 61);
	border-radius:0px;
	color:rgba(219,226,241,1);
	padding:0px 7px;
}

QLabel
{
	border-style:none solid solid none;/*上右下左*/	
	border-width:1px;
	border-color:rgba(191,200,215,0.1);
	background-color: rgb(44, 50, 61);
	border-radius:0px;
	color:rgba(219,226,241,1);
	padding:0px 7px;

	color: rgb(219,226,241);
	padding-left: 5px;
    font-size:14px;
}

QComboBox {
	color: rgb(219,226,241);
	padding-left: 5px;
	border: 0px;
    font-size:14px;
}

QComboBox::drop-down {
    
	border: 0;
	padding-right: 10px;
	padding-left: 10px;
	
}

QComboBox::QlineEdit {
	color: rgb(219,226,241);
    font-size:14px;
}

QComboBox::down-arrow {
	image:url(:/AccuUIComponentImage/images/icon_pull-down.png);

}

QComboBox QAbstractItemView {
	color: rgb(219,226,241);
	background-color: rgb(37, 41, 48);
	selection-background-color: #4E9CD5;
	max-height: 120px;
	border:0px;
	font-size:14px;
    outline: none;
}
QComboBox QAbstractItemView::Item {
	min-height: 24px;
	height: 24px;
	font-size:14px;
}
</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_2" native="true">
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_3" native="true">
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_4" native="true">
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_5">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_5" native="true">
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_6">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_6" native="true">
     <property name="minimumSize">
      <size>
       <width>66</width>
       <height>0</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_7">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>6</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>6</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>334</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
