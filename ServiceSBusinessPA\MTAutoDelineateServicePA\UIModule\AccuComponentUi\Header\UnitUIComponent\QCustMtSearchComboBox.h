﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QColor>
#include <QVariant>
#include <QStyledItemDelegate>
#include <QPainter>
#include <QProxyStyle>
#include <QStyle>
#include <QList>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"

#include "MtCustSearchComboBox.h"

/*******
由QMtComboBox组成
*********/

class QCustMtComboBoxDelegate;
class NoIconComboBox;

//QCustMtComboBox参数
class  QCustMtSearchComboBoxParam : public ICellWidgetParam
{
public:

    QStringList _textList;              //下拉框集合
    QList<QVariant> _userDataList;      //下拉框的业务数据
    int _comboBoxIndex = -1;            //下拉框的下标
    bool _bEnabaleDrawSquare = false;   //true:文案后面绘制正方形，使用对象MtComboBoxDrawSquareColor；false：只有下拉框，使用对象MtComboBox
    /// <summary>
    /// 记录最大最小值的顺序
    /// </summary>
    QList<int> listMaxMin;
    /// <summary>
    /// 是否使用新样式
    /// </summary>
    bool m_newStyle = false;
    QCustMtSearchComboBoxParam();
    ~QCustMtSearchComboBoxParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustMtSearchComboBoxParam)

namespace Ui
{
class QCustMtComboBox;
}
class MtComboBox;
class MtCustSearchComboBox;

class  QCustMtSearchComboBox :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustMtSearchComboBox(QWidget* parent = Q_NULLPTR, bool bNewStyle = false);
    virtual ~QCustMtSearchComboBox();
    void SetupCellWidget(QCustMtSearchComboBoxParam& cellWidgetParam);

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual QString GetCurText();                            //获取当前界面展示文案
    virtual void SetEnableEdit(bool bEdit);                  //设置是否允许编辑

    virtual QString currentText();                     //获取当前界面展示文案
    virtual void setCurrentIndex(int index);
    virtual void setCurrentText(const QString& text);


    /*新增业务接口*/
    QStringList GetAllItemStrList();            //获取下拉框所有值的接口
    void AddItem(const QString& itemStr, const QVariant& auserData = QVariant());       //添加下拉框的值
    void AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList = QList<QVariant>());       //添加下拉框的值
    void RemoveItem(const QString& itemStr);    //清空下拉框的值
    void ClearItems();                          //清空下拉框所有值

    /*获取界面*/
    MtCustSearchComboBox* GetMtComboBox();

signals:
    void sigClicked(int);
    void currentTextChanged(const QString& newText);    //文案改变了
    void currentIndexChanged(int index);
    void sigUIUpdated(int index);

protected slots:
    void slotCurrentTextChanged(const QString& text);
    void slotCurrentIndexChanged(int index);
protected:
    bool eventFilter(QObject* obj, QEvent* evt);
    void resizeEvent(QResizeEvent* event);          //保证...
    void mousePressEvent(QMouseEvent* event);

private:
    Ui::QCustMtComboBox* ui = nullptr;
    MtCustSearchComboBox* m_comboBox = nullptr;
};
