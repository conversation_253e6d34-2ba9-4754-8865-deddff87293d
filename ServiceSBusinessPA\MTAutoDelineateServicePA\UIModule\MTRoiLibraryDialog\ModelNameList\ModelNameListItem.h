﻿#pragma once

#include "ui_ModelNameListItem.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include <QMouseEvent>


class ModelNameListItem : public QWidget
{
    Q_OBJECT

public:
    ModelNameListItem(QWidget* parent = nullptr);
    ~ModelNameListItem();

    void init(int modelId, const QString& text, const QString& pixmapPath);
    void updateItemText(const QString& text);

    void setChecked(bool bChecked);
    bool isChecked();

    QString text();

signals:
    void sigItemClicked(int modelId);
    void sigEditModel(int modelId, const QString& modelName);
    void sigDeleteModel(int modelId, const QString& modelName);

protected slots:
    void on_btnModelItemOperate_clicked();
    void slotRightMenu();

protected:
    virtual void mouseReleaseEvent(QMouseEvent* event);
    virtual void enterEvent(QEvent* event);
    virtual void leaveEvent(QEvent* event);

    void updateStatus(bool bHover = false);
    void showPopmenu(const QPoint& pt);

private:
    Ui::ModelNameListItem ui;
    bool m_bChecked = false;
    int m_modelID = -1;
    MtMenu* m_menu = nullptr;
};
