﻿// *********************************************************************************
// <remarks>
// FileName    : RoiLibraryWidget
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : Roi库页签(内嵌RoiLibraryTable)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include <QCompleter>
#include <QStringListModel>
#include "ui_RoiLibraryWidget.h"
#include "MtProgressDialog.h"


class RoiLibraryWidget : public QWidget
{
    Q_OBJECT

public:
    RoiLibraryWidget(QWidget* parent = nullptr);
    ~RoiLibraryWidget();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="roiTypeList">[IN]所有Roi类型集合</param>
    /// <param name="stRoiLabelInfoList">[IN]全部标签</param>
    /// <param name="allGroupList">[IN]全部分组信息</param>
    /// <param name="stOrganList">[IN]Organ信息集合</param>
    /// <param name="modelInfoMap">[IN]模型信息集合</param>
    /// <param name="modelCollectionInfoList">[IN]模板信息集合</param>
    void init(const QStringList& allRoiTypeList,
              const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList,
              const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
              const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
              const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 窗口销毁前调用，该方法会等待列表创建线程退出，所以若该窗口的父窗口没有独立的消息处理队列，可能出现卡住问题，该情况下可使用deleteSafe方法.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void widgetDestroying();

    /// <summary>
    /// 销毁窗口，调用该方法时，窗口已被父窗口移除，否则会出现多次销毁的异常.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void deleteSafe();

    /// <summary>
    /// 获取所有Organ信息
    /// 发生修改的optTypeEnum会设置为OptType_Mod
    /// </summary>
    /// <returns>所有Organ信息</returns>
    QList<n_mtautodelineationdialog::ST_Organ> getAllOrganInfo();

    /// <summary>
    /// 返回所有的模板信息
    /// </summary>
    /// <returns>所有模板信息</returns>
    const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& getAllModelCollectionInfoList();

    /// <summary>
    /// 返回所有的标签信息
    /// </summary>
    /// <returns>所有标签信息</returns>
    const QList<n_mtautodelineationdialog::ST_RoiLabelInfo> getAllLabelInfoList();

    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getAllOrganGroupInfo();

    /// <summary>
    /// 获取列表控件
    /// </summary>
    /// <returns>所有分组信息</returns>
    RoiLibraryTable* getTableWidget();

    void removeFocusFromTable();

    bool isNeedSave2File();

    void resetNeedSave2FileStatus();

signals:
    /*******************************************************分组设置********************************************************************/
    /// <summary>
    /// 更新ROI分组信息到数据库，并重新读取组信息
    /// </summary>
    /// <param name="curGroupList">当前所有ROI分组，包括更新了组名和新增（组id小于0）的分组</param>
    /// <param name="delGroupList">删除的ROI分组</param>
    /// <param name="updtedGroupList">传回的分组信息，因为有新增和删除分组，组id会不合法。所以在入库后需要重新读取组信息</param>
    /// <returns></returns>
    void sigUpdateRoiGroup(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList
                           , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList
                           , QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList);

    /********************************************************模型操作*******************************************************************/
    /// <summary>
    /// 发送信号，以获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN][OUT]返回从标签库中获取的所有记录信息</param>
    void sigGetLabelLibraryInfo(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 发送信号，以获取器官ROI默认设置
    /// </summary>
    /// <param name="stOrganDefaultList">[IN][OUT]返回从获取的器官默认设置信息</param>
    void sigGetOrganDefaultInfo(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 发送信号，通知外部进行导入模型
    /// </summary>
    /// <param name="modelPath">[IN][模型文件路径</param>
    void sigModelImport(const QString& modelPath);

    /// <summary>
    /// 模型导入进度信号，用于更新模型导入进度条
    /// </summary>
    /// <param name="value">[IN]模型导入进度</param>
    void sigModelImportProgress(int value);

    /// <summary>
    /// 模型导入是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="bSuccess">[IN]导入是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelImportFinish(bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 模型导入入库完成信号，用于更新模型列表
    /// </summary>
    /// <param name="stOrganInfoVec">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    void sigModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 模型删除信号
    /// </summary>
    /// <param name="modelId">[IN]要删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    void sigDeleteModel(const QString& modelId, const QString& modelName);

    /// <summary>
    /// 模型删除是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="modelId">[IN]删除的模型ID</param>
    /// <param name="modelName">[IN]删除的模型名</param>
    /// <param name="bSuccess">[IN]删除是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 保存模型信息.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">The model identifier.</param>
    /// <param name="modelName">Name of the model.</param>
    /// <param name="modelName">description of the model.</param>
    /// <param name="result">The result，0：success.</param>
    void sigSaveModelInfo(const QString& modelId, const QString& modelName, const QString& desc, int& result);

    void sigDeleteSelf();

public slots:
    /// <summary>
    /// 搜索按钮
    /// </summary>
    void on_mtPushButton_search_clicked();

    /// <summary>
    /// 刷新按钮
    /// </summary>
    void on_mtPushButton_resetSearch_clicked();

    /// <summary>
    /// 恢复默认设置
    /// </summary>
    void on_btnRecoverROI_clicked();

    /// <summary>
    /// 加入模板
    /// </summary>
    void on_btnROI2Template_clicked();

    /// <summary>
    /// 批量设置ROI
    /// </summary>
    void on_btnBatchROISetting_clicked();

    /// <summary>
    /// 创建空勾画
    /// </summary>
    void on_btnCreateEmptyROI_clicked();

    /// <summary>
    /// 删除空勾画
    /// </summary>
    void on_btnDeleteEmptyROI_clicked();

    /// <summary>
    /// 帮助按钮
    /// </summary>
    void on_mtPushButton_help_clicked();

    /// <summary>
    /// 切换显式ROI
    /// </summary>
    void slotVisibleROITypeChange(int type);

    /// <summary>
    /// 导入模型
    /// </summary>
    void slotImportModel();

    /// <summary>
    /// 编辑模型
    /// </summary>
    void slotEditModel(int modelId, const QString& modelName);

    /// <summary>
    /// 删除模型
    /// </summary>
    void slotDeleteModel(int modelId, const QString& modelName);

    /// <summary>
    /// 模型导入进度信号，用于更新模型导入进度条
    /// </summary>
    /// <param name="value">[IN]模型导入进度</param>
    void slotModelImportProgress(int value);

    /// <summary>
    /// 模型导入是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="bSuccess">[IN]导入是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void slotModelImportFinish(bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 模型导入入库完成信号，用于更新模型列表
    /// </summary>
    /// <param name="stOrganInfoVec">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    void slotModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 模型删除是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="modelId">[IN]删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    /// <param name="bSuccess">[IN]删除是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void slotModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 列表信息发生变化
    /// </summary>
    void slotTableInfoChanged(const QString& defOrganName, int col, const QString& newText);

    /// <summary>
    /// 器官缓存信息修改.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="organInfo">修改的器官信息.</param>
    void slotOrganInfoChanged(const n_mtautodelineationdialog::ST_Organ& organInfo);

    /// <summary>
    /// 更新ROI分组信息到数据库，并重新读取组信息
    /// </summary>
    /// <param name="curGroupList">当前所有ROI分组，包括更新了组名和新增（组id小于0）的分组</param>
    /// <param name="delGroupList">删除的ROI分组</param>
    /// <param name="updtedGroupList">传回的分组信息，因为有新增和删除分组，组id会不合法。所以在入库后需要重新读取组信息</param>
    /// <returns></returns>
    void slotUpdateRoiGroup(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList
                            , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList
                            , QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList);

    /// <summary>
    /// ROI名搜索框文本变化
    /// </summary>
    void slotSearchNameChanged(const QString& text);

    /// <summary>
    /// ROI名搜索列表触发
    /// </summary>
    void slotSearchNameTriggered(const QString& text);

    /// <summary>
    /// ROI标签搜索框文本变化
    /// </summary>
    void slotSearchLabelChanged(const QString& text);

    /// <summary>
    /// ROI标签搜索列表触发
    /// </summary>
    void slotSearchLabelTriggered(const QString& text);

    /// <summary>
    /// ROI中文名搜索框文本变化
    /// </summary>
    void slotSearchChNameChanged(const QString& text);

    /// <summary>
    /// ROI中文名搜索列表触发
    /// </summary>
    void slotSearchChNameTriggered(const QString& text);

    /// <summary>
    /// 模型名列表项点击
    /// </summary>
    void slotModelNameListCurrentItemChanged(QListWidgetItem* current, QListWidgetItem* previous);
    void slotModelNameListItemClicked(QListWidgetItem* current);

    void slotDeleteSelf();

protected:
    void setTableChangedStatus();
    void setSearchFilterChanged();
    void initNameFilterEdit(const QStringList& filterNameList);
    void initLabelFilterEdit(const QStringList& filterLabelList);
    void initChNameFilterEdit(const QStringList& filterChNameList);
    void initGroupFilterBox(const QStringList& filterGroupList);
    void initModelNameList(const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);
    void showLoadingState(bool bShow);
    void updateRoiTableInfo(int modelId);//刷新右侧roi表信息
    QListWidgetItem* addModelItem(int modelId, const QString& modelName);
    void removeModelItem(int modelId);
    void updateModelItem(int modelId, const QString& modelName);
    void updateModelItemWidth();

    void showEvent(QShowEvent* event) override;

private:
    Ui::RoiLibraryWidgetClass ui;
    QStringList                                                     m_allRoiTypeList;           //ROI所有的类型
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo>             m_curAllGroupList;          //当前所有的分组信息
    QList<n_mtautodelineationdialog::ST_Organ>                      m_allOrganList;             //所有的器官信息
    QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel> m_modelInfoMap;             //所有的模型信息
    QList<n_mtautodelineationdialog::ST_SketchModelCollection>      m_modelCollectionInfoList;  //所有的模板信息
    QMap<QString/*roiDefName*/, QString/*roiCustName*/>             m_roiListNameMap;           //所有的器官默认名对应的自定义名称
    QMap<QString/*roiDefName*/, QString/*roiChName*/>               m_roiListChNameMap;         //所有的器官默认名对应的中文名
    QWidget* m_parentDialog = nullptr;

    bool m_bNeedSave2File;  //是否需要保存到数据库

    //搜索控件
    QCompleter*         m_completerName = nullptr;
    QCompleter*         m_completerChName = nullptr;
    QCompleter*         m_completerLabel = nullptr;
    QStringListModel*   m_completerNameStrListModel = nullptr;
    QStringListModel*   m_completerChNameStrListModel = nullptr;
    QStringListModel*   m_completerLabelStrListModel = nullptr;
    bool                m_bFilterTable = false;    //列表是否处于检索结果状态

    QString             m_modelNameEditBtnPixmap;
    QString             m_icon_success_green2Path;
    QListWidgetItem*    m_preSelectedModelNameItem = nullptr;

    MtProgressDialog*   m_progressDialog = nullptr;
    int                 m_tempOrganId = -1;     //新增的器官id，因为新增器官也会加入到模板，不同id用于区分
};
