﻿#include "MTExportRoiTemplateWidget.h"
#include "ui_MTExportRoiTemplateWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"
#include "MTAutoDelineationDialog/AutoSketchSub/AutoSketchTemplateWidget.h"
#include "MTAutoDelineationDialog/AutoSketchSub/OptSketchCollection.h"
#include "CommonUtil.h"


namespace n_mtautodelineationdialog
{

MTExportRoiTemplateWidget::MTExportRoiTemplateWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MTExportRoiTemplateWidgetClass;
    ui->setupUi(this);
    //页面设置
    ui->widget->setEnableButtonUnattend(true);          //默认无人值守按钮势能
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTExportRoiTemplateWidget, " << errMsg.toStdString();
        }
    }
}

MTExportRoiTemplateWidget::~MTExportRoiTemplateWidget()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        OptSketchCollection* temp = (OptSketchCollection*)m_ptrOptSketchCollection;
        delete temp;
        m_ptrOptSketchCollection = nullptr;
        temp = nullptr;
    }
}

/// <summary>
/// 设置右侧roi-item的宽度
/// </summary>
/// <param name="widthNum">[IN]右侧roi-item的宽度</param>
void MTExportRoiTemplateWidget::SetRoiItemWidth(const int widthNum)
{
    ui->widget->setRoiItemWidth(widthNum);
}

/// <summary>
/// 设置是否显示无人值守模板页签
/// 不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true:显示</param>
void MTExportRoiTemplateWidget::SetIsShowUnattendTab(const bool isShow)
{
    ui->widget->setEnableButtonUnattend(isShow);
}

/// <summary>
/// 设置是否显示选择ROI进行勾画/选择模板进行勾画页签
/// \n不设置: 默认全显示
/// </summary>
/// <param name="isShow">true显示</param>
void MTExportRoiTemplateWidget::SetShowSelectRadioSelectBtn(const bool isShow)
{
    ui->widget->setShowSelectRadioSelectBtn(isShow);
}

void MTExportRoiTemplateWidget::SetShowModalityComboBox(bool bShow)
{
    ui->widget->SetShowModalityComboBox(bShow);
}

/// <summary>
/// 设置当前选中的模态下拉框
/// \n不设置: 默认显示CT 可下拉
/// </summary>
void MTExportRoiTemplateWidget::SetMtComboBoxModality(const QString& modality, const bool enable)
{
    ui->widget->setMtComboBoxModality(modality, enable);
}

/// <summary>
/// 设置需提前选中的模板id
/// \n不设置: 默认不选中
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void MTExportRoiTemplateWidget::SetSelectTemplateId(const int templateId)
{
    ui->widget->setSelectTemplateId(templateId);
}

/// <summary>
/// 设置提前选中的页签类型
/// \n 不设置: 默认选中2-选择模板进行勾画
/// </summary>
/// <param name="pageType">[IN]页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
void MTExportRoiTemplateWidget::SetSelectRadioPageType(const int pageType)
{
    ui->widget->setSelectRadioPageType(pageType);
}

/// <summary>
/// 设置当模板被无人值守使用时，是否显示提示框
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true显示</param>
void MTExportRoiTemplateWidget::SetIsShowTipOfModUnattendUsed(const bool isShow)
{
    m_IsShowTipUnattendUsed = isShow;
}

/// <summary>
/// 设置虚拟模板
/// \n用于=选择ROI进行勾画=页签进行器官提前选中
/// </summary>
void MTExportRoiTemplateWidget::SetVirtualSketchCollection(ST_SketchModelCollection& stSketchCollection)
{
    ui->widget->setVirtualSketchCollection(stSketchCollection);
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="allGroupInfoList">[IN]所有分组信息</param>
/// <param name="allSketchModelList">[IN]所有勾画模型</param>
/// <param name="allSketchCollectionList">[IN]所有勾画模板</param>
/// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
void MTExportRoiTemplateWidget::Init(
    const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
    const QList<ST_SketchModel>& allSketchModelList,
    const QMap<EM_OptDcmType, QList<ST_SketchModelCollection>>& allSketchCollectionMap,
    ST_CallBack_AutoSketch& stCallBackAutoSketch)
{
    //数据初始化
    m_stCallBackAutoSketch = stCallBackAutoSketch;

    if (m_ptrOptSketchCollection != nullptr)
    {
        delete m_ptrOptSketchCollection;
        m_ptrOptSketchCollection = nullptr;
    }

    m_ptrOptSketchCollection = new OptSketchCollection();
    ((OptSketchCollection*)m_ptrOptSketchCollection)->init(allGroupInfoList, allSketchModelList, allSketchCollectionMap, n_mtautodelineationdialog::ST_SketchModelCollection());
    //勾画模板
    ui->widget->setImagePathHash(m_imagePathHash);
    ui->widget->setMargin(0, 0, 0, 0);
    ui->widget->initData((OptSketchCollection*)m_ptrOptSketchCollection, stCallBackAutoSketch);
}

/// <summary>
/// 是否处于编辑状态
/// </summary>
/// <returns>true是</returns>
bool MTExportRoiTemplateWidget::IsEditState(bool showErrDlg)
{
    bool ret = ui->widget->isEditState();

    if (ret == true && showErrDlg == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请先保存勾画模板"));
    }

    return ret;
}

bool MTExportRoiTemplateWidget::CheckOutCollectionAvail()
{
    if (ui->widget->isSelectOneCollection() == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要勾画的模板"));
        return false;
    }

    //检查是否只有空勾画
    bool onlyEmptyRoi = true;
    QSet<int> emptyOrganIdSet = ((OptSketchCollection*)m_ptrOptSketchCollection)->getEmptyOrganIdSet();
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = ui->widget->getSelectSketchCollection();

    if (curSketchCollection.showGroupIdMap.isEmpty() == true)
    {
        if (curSketchCollection.id == Def_TempIdOfSelectTemplate || curSketchCollection.id == Def_TempIdOfSelectRoi)
            MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要自动勾画的ROI"));
        else
            MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许模板为空，请选择其他模板"));

        return false;
    }

    for (QMap<int, QSet<int>>::iterator it = curSketchCollection.showGroupIdMap.begin(); it != curSketchCollection.showGroupIdMap.end(); it++)
    {
        if (emptyOrganIdSet.contains(it.key()) == false)
        {
            onlyEmptyRoi = false;
            break;
        }
    }

    if (onlyEmptyRoi == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许只选择空勾画，请选择其他模板"));
        return false;
    }

    return true;
}

/// <summary>
/// 获取待勾画模板信息
/// </summary>
/// <returns>待勾画模板信息</returns>
ST_SketchModelCollection MTExportRoiTemplateWidget::GetSelectCollection()
{
    ST_SketchModelCollection outSketchCollection;
    outSketchCollection = ui->widget->getSelectSketchCollection();
    return outSketchCollection;
}

/// <summary>
/// 获取最新的虚拟模板
/// \n用于*选择ROI进行勾画*页签进行器官提前选中
/// </summary>
/// <returns>最新的虚拟模板</returns>
ST_SketchModelCollection MTExportRoiTemplateWidget::GetVirtualSketchCollection()
{
    return ui->widget->getVirtualSketchCollection();
}

/// <summary>
/// 获取最新的模板排序信息
/// \n随时调用拿到的都是当前最新的排序顺序
/// </summary>
/// <returns>最新的模板排序信息</returns>
QMap<EM_OptDcmType, QList<int>> MTExportRoiTemplateWidget::GetNewTemplateIdSortMap()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        return ((OptSketchCollection*)m_ptrOptSketchCollection)->getTemplateIdSortMap();
    }

    return QMap<EM_OptDcmType, QList<int>>();
}

/// <summary>
/// 获取当模板被无人值守使用时，是否显示提示框
/// </summary>
bool MTExportRoiTemplateWidget::GetNewIsShowTipOfModUnattendUsed()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        return ((OptSketchCollection*)m_ptrOptSketchCollection)->getIsShowTipOfModUnattendUsed();
    }

    return true;
}

/// <summary>
/// 获取选中的页签类型
/// </summary>
/// <returns>页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</returns>
int MTExportRoiTemplateWidget::GetSelectRadioPageType()
{
    return ui->widget->getSelectRadioPageType();
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTExportRoiTemplateWidget::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

}
