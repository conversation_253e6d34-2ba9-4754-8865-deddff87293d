﻿#include "AccuComponentUi/Header/UnitUIComponent\MtUnitPushButtonGroup.h"
#include "ui_MtUnitPushButtonGroup.h"
#include "MtPushButton.h"
#include "CMtCoreDefine.h"
#include <qDebug>


MtUnitPushButtonGroupParam::MtUnitPushButtonGroupParam()
{
    _cellWidgetType = DELEAGATE_MtUnitPushButtonGroup;
}

MtUnitPushButtonGroupParam::~MtUnitPushButtonGroupParam()
{
}

QWidget* MtUnitPushButtonGroupParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    MtUnitPushButtonGroup* btns = new MtUnitPushButtonGroup(parent);
    btns->SetupCellWidget(*this);
    return btns;
}

MtUnitPushButtonGroup::MtUnitPushButtonGroup(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MtUnitPushButtonGroup;
    ui->setupUi(this);
}

MtUnitPushButtonGroup::~MtUnitPushButtonGroup()
{
    DeleteButtons();
    MT_DELETE(ui);
}

bool MtUnitPushButtonGroup::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();
    /*if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->SetButtonText(0, text);
        return true;
    }*/
    return false;
}

void MtUnitPushButtonGroup::SetEnableEdit(bool bEdit)
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        _buttonList[i]->setEnabled(bEdit);
    }
}

void MtUnitPushButtonGroup::SetupCellWidget(MtUnitPushButtonGroupParam& param)
{
    _pushBtnGroupParam = param;
    DeleteButtons();

    for (int i = 0; i < param._btnTextStrList.size(); ++i)
    {
        MtPushButton* button = new MtPushButton(this);
        ui->widget_btnsBK->layout()->addWidget(button);
        QString text = param._btnTextStrList[i];

        if (_pushBtnGroupParam._btnWidth > 0)
        {
            button->setFixedWidth(_pushBtnGroupParam._btnWidth);
        }
        else
        {
            //根据字符串自适应宽度
            /*QFont font = this->font();
            QFontMetrics fm(font);
            int width = fm.width(text) + 16;
            button->setFixedWidth(width);*/
        }

        if (_pushBtnGroupParam._btnHeight > 0)
        {
            button->setFixedHeight(_pushBtnGroupParam._btnHeight);
        }

        button->setText(text);

        if (_pushBtnGroupParam._btnIndexMtTypeMap.contains(i))
        {
            MtPushButton::MtType mtType = (MtPushButton::MtType)_pushBtnGroupParam._btnIndexMtTypeMap.value(i);
            button->setMtType(mtType);
        }

        if (_pushBtnGroupParam._btnIndexEnabledMap.contains(i))
        {
            bool enabled = _pushBtnGroupParam._btnIndexEnabledMap.value(i);
            button->setEnabled(enabled);
        }

        _buttonList.push_back(button);
        connect(button, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
    }
}

void MtUnitPushButtonGroup::DeleteButtons()
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        QWidget* widget = _buttonList.at(i);
        delete widget;
    }

    _buttonList.clear();
}

void MtUnitPushButtonGroup::HideButton(int index, bool bHide)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setHidden(bHide);
    }
    else
    {
    }
}

void MtUnitPushButtonGroup::SetButtonText(int index, QString& text)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setText(text);
    }
    else
    {
    }
}

void MtUnitPushButtonGroup::SetButtonEnable(int index, bool enable)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setEnabled(enable);
    }
    else
    {
    }
}


bool MtUnitPushButtonGroup::GetCellChecked(int index)
{
    bool bChecked = false;

    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        bChecked = _buttonList[index]->isChecked();
    }

    return bChecked;
}


void MtUnitPushButtonGroup::slotButtonClicked(bool isChecked)
{
    QObject* sender = this->sender();
    int index = -1;

    for (int i = 0; i < _buttonList.size(); ++i)
    {
        MtPushButton* tempButton = _buttonList.at(i);

        if (tempButton == sender)
        {
            index = i;
            break;
        }
    }

    if (index >= 0)
    {
        emit sigClicked(index);
        emit sigButtonClicked(index, isChecked);
    }
}