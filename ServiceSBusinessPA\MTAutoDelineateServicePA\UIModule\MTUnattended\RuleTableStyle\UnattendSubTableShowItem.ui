<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UnattendSubTableShowItemClass</class>
 <widget class="QWidget" name="UnattendSubTableShowItemClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1152</width>
    <height>141</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>UnattendSubTableShowItem</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="leftMargin">
      <number>8</number>
     </property>
     <property name="rightMargin">
      <number>8</number>
     </property>
     <item>
      <widget class="MtLabel" name="mtLabel_modality">
       <property name="minimumSize">
        <size>
         <width>63</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>63</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>MtTextLabel</string>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>443</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>443</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QFormLayout" name="formLayout">
        <property name="formAlignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="horizontalSpacing">
         <number>0</number>
        </property>
        <property name="verticalSpacing">
         <number>12</number>
        </property>
        <property name="leftMargin">
         <number>8</number>
        </property>
        <property name="rightMargin">
         <number>8</number>
        </property>
        <item row="0" column="0">
         <widget class="MtLabel" name="mtLabel_dicomRec">
          <property name="text">
           <string>MtTextLabel</string>
          </property>
          <property name="elideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="MtLabel" name="mtLabel_part">
          <property name="text">
           <string>MtTextLabel</string>
          </property>
          <property name="elideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="MtLabel" name="mtLabel_word">
          <property name="text">
           <string>MtTextLabel</string>
          </property>
          <property name="elideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_2">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="leftMargin">
      <number>8</number>
     </property>
     <property name="rightMargin">
      <number>8</number>
     </property>
     <item>
      <widget class="MtLabel" name="mtLabel_model">
       <property name="minimumSize">
        <size>
         <width>188</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>188</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="text">
        <string>MtTextLabel</string>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_3">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="sizeConstraint">
        <enum>QLayout::SetMinimumSize</enum>
       </property>
       <property name="leftMargin">
        <number>8</number>
       </property>
       <property name="rightMargin">
        <number>8</number>
       </property>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
