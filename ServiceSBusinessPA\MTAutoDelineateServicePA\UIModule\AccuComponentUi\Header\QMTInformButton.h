﻿#pragma once

#include <QWidget>


namespace Ui
{
class QMTInformButton;
}

class  QMTInformButton : public QWidget
{
    Q_OBJECT

public:
    QMTInformButton(QWidget* parent = Q_NULLPTR);
    ~QMTInformButton();
    void SetValue(int);
    void AddOneValue(bool isWarn = false);
    void DeleteOneValue();
    void ResetValue();
    void SetBackColor(QColor);
protected:
    void mousePressEvent(QMouseEvent* event);
signals:
    void sigInformClicked();
private slots:
    void slotButtonClicked();
private:
    Ui::QMTInformButton* ui;
    bool _isWarn = false;
};
