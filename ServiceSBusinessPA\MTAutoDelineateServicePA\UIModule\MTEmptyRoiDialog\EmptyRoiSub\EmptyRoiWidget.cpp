﻿#include "EmptyRoiWidget.h"
#include "MtMessageBox.h"
#include "MtToolButton.h"


EmptyRoiWidget::EmptyRoiWidget(QWidget* parent)
    : QWidget(parent)
    , m_parentDialog(parent)
    , m_bNeedSave2File(false)
{
    ui.setupUi(this);
    //信号槽
    connect(ui.mtToolButton_add, &QToolButton::clicked, this, &EmptyRoiWidget::onMtToolButton_add);              //添加按钮
    connect(ui.mtToolButton_del, &QToolButton::clicked, this, &EmptyRoiWidget::onMtToolButton_del);              //删除按钮
    connect(ui.widget_table, &EmptyRoiTable::sigTableInfoChanged, this, &EmptyRoiWidget::slotTableInfoChanged);   //表格信息发生了变化
}

EmptyRoiWidget::~EmptyRoiWidget()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathMap">[IN]图片资源路径(key-name value-图片相对路径)</param>
void EmptyRoiWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    ui.mtToolButton_add->setPixmapFilename(imagePathHash["icon_add"]);
    ui.mtToolButton_del->setPixmapFilename(imagePathHash["icon_del"]);
}

void EmptyRoiWidget::init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
                          const QList<n_mtautodelineationdialog::ST_Organ>& stEmptyOrganList,
                          const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    ui.widget_table->init(allRoiTypeList, allLabelList, stEmptyOrganList, modelCollectionInfoList);
}

QList<n_mtautodelineationdialog::ST_Organ> EmptyRoiWidget::getAllEmptyOrganInfo()
{
    return ui.widget_table->getAllEmptyOrganInfo();
}

void EmptyRoiWidget::removeFocusFromTable()
{
    ui.widget_table->setFocus();
}

bool EmptyRoiWidget::isNeedSave2File()
{
    return m_bNeedSave2File;
}

void EmptyRoiWidget::setTableChangedStatus()
{
    m_bNeedSave2File = true;
    return;
    MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_parentDialog);

    if (nullptr != parentDialog)
    {
        parentDialog->getButton(MtTemplateDialog::BtnRight1)->setEnabled(true);
    }
}

void EmptyRoiWidget::onMtToolButton_add()
{
    n_mtautodelineationdialog::ST_Organ stOrgan;
    stOrgan.id = m_newRoiId--;
    stOrgan.modelId = -1;
    stOrgan.isVisiable = true;
    stOrgan.isAssociateTemplate = false;
    stOrgan.organChineseName = "";
    stOrgan.defaultOrganName = "";
    stOrgan.customOrganName = "";
    stOrgan.defaultColor = "ff0000";
    stOrgan.customColor = "ff0000";
    stOrgan.roiType = "ORGAN";
    stOrgan.roiLabel = "";
    stOrgan.roiParam = "";
    ui.widget_table->addRow(stOrgan);
}

void EmptyRoiWidget::onMtToolButton_del()
{
    QString organId, organName;

    if (ui.widget_table->delCurrentRow(organId, organName))
    {
        emit sigRemoveRoi(organId.toInt(), organName);

        if (organId.toInt() > -1)
        {
            setTableChangedStatus();
        }
    }
}

void EmptyRoiWidget::slotTableInfoChanged(const QString& defOrganName, int col, const QString& newText)
{
    setTableChangedStatus();
}
