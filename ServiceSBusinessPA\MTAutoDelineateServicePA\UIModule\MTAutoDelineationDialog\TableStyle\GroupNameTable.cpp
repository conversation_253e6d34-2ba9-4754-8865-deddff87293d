﻿#include "GroupNameTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLineEdit.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtComboBox.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTAbsHorizontalBtns.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include <QColorDialog>
#include <QPainter>
#include <QScrollBar>

/// <summary>
/// 构造函数
/// </summary>
GroupNameTable::GroupNameTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    //创建拖拽背景
    initItemDragShade();
}

GroupNameTable::~GroupNameTable()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void GroupNameTable::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 初始化
/// </summary>
void GroupNameTable::init()
{
    initTableView();
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="GroupNameTableInfo">[IN]模板名称信息</param>
/// <param name="isSelect">[IN]是否选中</param>
void GroupNameTable::addRow(const GroupNameTableInfo& groupNameTableInfo, bool isSelect, bool isEmit)
{
    QMap<int/*column*/, ICellWidgetParam*> cellWidgetParamMap;
    getNewCellParamMap(groupNameTableInfo, cellWidgetParamMap);
    QString rowValue = groupNameTableInfo.groupDefName;
    m_allGroupNameTableInfoMap.insert(groupNameTableInfo.groupDefName, groupNameTableInfo);
    this->AddRowItem(rowValue, cellWidgetParamMap);
    this->scrollToTop();

    if (isSelect == true)
    {
        this->SetCurrentRow(rowValue);
        m_preSelectRowValue = rowValue;

        if (isEmit == true)
            emit this->sigItemSelect(m_preSelectRowValue, Qt::LeftButton, QPoint());
    }
    else
    {
        this->SetCurrentRow("");
    }
}

/// <summary>
/// 隐藏一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
/// <param name="isHide">[IN]true隐藏</param>
void GroupNameTable::hideRow(const QString& rowValue, const bool isHide)
{
    this->HideRowItem(rowValue, isHide);
}

/// <summary>
/// 删除一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
void GroupNameTable::delRow(const QString& rowValue)
{
    this->DeleteRowItem(rowValue);
    this->clearSelection();
    this->SetCurrentRow("");
}

/// <summary>
/// 删除所有行
/// </summary>
void GroupNameTable::delAllRow()
{
    int rowNum = this->GetRowCount();
    QList<QString> rowValueList;
    rowValueList.reserve(rowNum + 1);

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        rowValueList.push_back(rowValue);
    }

    for (int i = 0; i < rowValueList.size(); i++)
    {
        this->DeleteRowItem(rowValueList[i]);
    }

    this->clearSelection();
    this->SetCurrentRow("");
}

/// <summary>
/// 置顶一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
void GroupNameTable::topRow(const QString& rowValue)
{
    if (m_allGroupNameTableInfoMap.contains(rowValue) == false)
        return;

    //获取当前行号
    int curIndex = this->GetRowIndex(rowValue);

    if (curIndex <= 0)
        return;

    GroupNameTableInfo curInfo = m_allGroupNameTableInfoMap[rowValue];
    //删除原行
    delRow(rowValue);
    //插入到首行
    this->insertRow(0, curInfo);
    this->SetCurrentRow(rowValue);
    emit this->sigSortOccurs();
}

/// <summary>
/// 清空选中
/// </summary>
void GroupNameTable::clearSelect()
{
    this->clearSelection();
    this->SetCurrentRow("");
}

/// <summary>
/// 获取当前选中的行
/// </summary>
/// <returns>rowValue</returns>
QString GroupNameTable::getCurSelectRow()
{
    return this->GetCurUniqueValue();
}

/// <summary>
/// 获取指定的行
/// </summary>
/// <param name="row">[IN]行号</param>
/// <returns>rowValue</returns>
QString GroupNameTable::getCurSelectRow(int row)
{
    return this->GetRowUniqueValue(row);;
}

QList<QString> GroupNameTable::getCurGroupSortList()
{
    int rowNum = this->GetRowCount();
    QList<QString> sortList;
    sortList.reserve(rowNum + 1);

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);

        if (rowValue.isEmpty() == false)
        {
            sortList.push_back(rowValue);
        }
    }

    return sortList;
}

/// <summary>
/// 是否是首位item
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
/// <returns>true是</returns>
bool GroupNameTable::isTopItem(const QString& rowValue)
{
    int curIndex = this->GetRowIndex(rowValue);
    return curIndex <= 0 ? true : false;
}

/// <summary>
/// 设置拖拽使能
/// </summary>
/// <param name="enable">[IN]true使能</param>
void GroupNameTable::setIsDropEnable(const bool enable)
{
    m_dropEnable = enable;

    if (m_dropEnable)
    {
        initItemDragShade();
    }
    else
    {
        delete m_dragItemShade;
        m_dragItemShade = nullptr;
    }
}

/// <summary>
/// 初始化表格
/// </summary>
void GroupNameTable::initTableView()
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._defaultColumn = 3;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 0;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    firstViewParam._headParam._columnWidthMap.insert(0, 36);
    firstViewParam._headParam._columnWidthMap.insert(1, 170);
    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._gridColor = QColor(55, 55, 55);
    firstViewParam._canvasBackColor = QColor(0, 0, 0, 128);
    firstViewParam._rowWidgetHeight = m_rowHeight;
    firstViewParam._isHideCloumnLine = true;
    firstViewParam._showGrid = false;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

void GroupNameTable::initItemDragShade()
{
    m_dragItemShade = new QLabel(this);
    m_dragItemShade->setScaledContents(true);
    m_dragItemShade->setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    m_dragItemShade->setAttribute(Qt::WA_TranslucentBackground);
    m_dragItemShade->hide();
}

void GroupNameTable::MoveRow(const QString& defaultName, int destRow)
{
    //删除原行
    DeleteRowItem(defaultName);

    if (m_allGroupNameTableInfoMap.contains(defaultName) == true)
    {
        GroupNameTableInfo curInfo = m_allGroupNameTableInfoMap[defaultName];
        {
            if (destRow >= 0)
            {
                //插入到目标行
                this->insertRow(destRow, curInfo);
            }
            else
            {
                //拖拽到末尾，插入到最后面
                addRow(curInfo);
            }

            //设置当前行
            this->SetCurrentRow(defaultName);
        }
        emit this->sigSortOccurs();//发生排序
    }
}

/// <summary>
/// 获取一个新的CellParamMap
/// </summary>
/// <param name="GroupNameTableInfo">[IN]模板名称信息</param>
/// <returns>CellParamMap</returns>
void GroupNameTable::getNewCellParamMap(const GroupNameTableInfo& GroupNameTableInfo, QMap<int/*column*/, ICellWidgetParam*>& outCellParamMap)
{
    //拖动图标
    QCustMtLabelParam* moveLabelParam = new QCustMtLabelParam();
    moveLabelParam->_showPix = true;
    moveLabelParam->_pixPath = m_imagePathHash["icon_move2"];
    outCellParamMap.insert(COL_Move, moveLabelParam);
    //模板名称
    QCustMtLabelParam* GroupNameLabelParam = new QCustMtLabelParam();
    GroupNameLabelParam->_text = GroupNameTableInfo.groupName;
    GroupNameLabelParam->_styleSheetStr = QString("font-size:13px");
    outCellParamMap.insert(COL_GroupName, GroupNameLabelParam);
}

/// <summary>
/// 插入行
/// </summary>
/// <param name="row">[IN]行号</param>
/// <param name="GroupNameTableInfo">[IN]行信息</param>
void GroupNameTable::insertRow(const int row, GroupNameTableInfo& GroupNameTableInfo)
{
    QMap<int/*column*/, ICellWidgetParam*> cellWidgetParamMap;
    getNewCellParamMap(GroupNameTableInfo, cellWidgetParamMap);
    InsertRowItem(row, GroupNameTableInfo.groupDefName, cellWidgetParamMap);
}

/// <summary>
/// 拖拽事件
/// </summary>
/// <param name="event"></param>
void GroupNameTable::dropEvent(QDropEvent* event)
{
    //qDebug() << "IOERoiClinicalObjectivesTableWidget::dropEvent";
    GroupNameTable* sourceTable = dynamic_cast<GroupNameTable*>(event->source());
    int srcRow = sourceTable->currentRow();                        // 原行号
    int dstRow = this->rowAt(event->pos().y());                     //鼠标落点对应行号
    //解析原行的内容
    QString defaultName = sourceTable->GetRowUniqueValue(srcRow);
    //删除原行
    sourceTable->DeleteRowItem(defaultName);

    if (m_allGroupNameTableInfoMap.contains(defaultName) == true)
    {
        GroupNameTableInfo curInfo = m_allGroupNameTableInfoMap[defaultName];
        {
            if (dstRow >= 0)
            {
                //插入到目标行
                this->insertRow(dstRow, curInfo);
            }
            else
            {
                //拖拽到末尾，插入到最后面
                addRow(curInfo);
            }

            //设置当前行
            this->SetCurrentRow(defaultName);
            //this->setCurrentCell(dstRow, 0);
        }
        event->accept();
        emit this->sigSortOccurs();//发生排序
    }
}

/// <summary>
/// 鼠标按下事件
/// </summary>
void GroupNameTable::mousePressEvent(QMouseEvent* event)
{
    //qDebug() << "IOERoiClinicalObjectivesTableWidget::mousePressEvent";
    int col = this->columnAt(event->pos().x());
    m_dragging = false;
    Qt::MouseButton button = event->button();

    if (button == Qt::LeftButton)
    {
        if (col == COL_Move && m_dropEnable)
        {
            m_dragging = true;
            //显示拖动阴影
            QString GroupName = GetColumnText(GetRowUniqueValue(rowAt(event->pos().y())), COL_GroupName);
            QPixmap drag_img(width(), m_rowHeight);
            QMTAbsRowWidgetItemParam& param = GetPerRowItemParam();
            drag_img.fill(/*CMtCoreWidgetUtil::formatColor("rgba(@color0,0.5)")*/param._selectBackColor);
            QPainter painter(&drag_img);
            painter.setPen(param._borderColor);
            painter.drawRect(0, 0, width() - 1, m_rowHeight - 1);
            painter.setPen(CMtCoreWidgetUtil::formatColor("rgba(@color4,1)"));
            painter.drawText(QRectF(40, 0, width(), m_rowHeight), GroupName, QTextOption(Qt::AlignVCenter));
            m_dragItemShade->setPixmap(drag_img);
            m_dragItemShade->move(event->globalPos());
            m_dragItemShade->show();
        }
    }

    QTableWidget::mousePressEvent(event);
    m_preSelectRowValue = this->GetCurUniqueValue();
    emit this->sigItemSelect(m_preSelectRowValue, button, event->pos());
}

/// <summary>
/// 鼠标移动事件
/// </summary>
void GroupNameTable::mouseMoveEvent(QMouseEvent* event)
{
    if (m_dragging)
    {
        //QTableWidget::mouseMoveEvent(event);
        m_dragItemShade->move(event->globalPos());
        //
        int first_visible_index = rowAt(verticalScrollBar()->value());
        int ptY = event->pos().y();

        if (ptY < m_rowHeight / 2 && first_visible_index > 0)//移到了顶部，上滚
        {
            verticalScrollBar()->setValue(--first_visible_index);
        }
        else if ((ptY > height() - horizontalHeader()->height() - m_rowHeight / 2)
                 && (rowCount() > (height() - horizontalHeader()->height()) / m_rowHeight + first_visible_index)) //移到了底部，下滚
        {
            verticalScrollBar()->setValue(++first_visible_index);
        }

        return;
    }
}

/// <summary>
/// 鼠标释放事件
/// </summary>
void GroupNameTable::mouseReleaseEvent(QMouseEvent* event)
{
    if (m_dragging)
    {
        m_dragging = false;
        m_dragItemShade->hide();
        QTableWidget::mouseReleaseEvent(event);
        //移动列表项
        QRect rect = geometry();

        //超出列表范围不进行移动
        if (event->pos().rx() < 0 || event->pos().rx() > rect.width()
            || event->pos().ry() < 0 || event->pos().ry() > rect.height())
        {
            return;
        }

        int dstRow = this->rowAt(event->pos().y());                     //鼠标落点对应行号
        MoveRow(m_preSelectRowValue, dstRow);
        return;
    }
}
