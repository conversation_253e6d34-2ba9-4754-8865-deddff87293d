﻿#include "AccuComponentUi\Header\QMTInformLight.h"
#include <QFileInfo>
#include <QMovie>
#include <QApplication>
#include "qpainter.h"
#include "qevent.h"
#include "qtimer.h"
#include "AccuComponentUi\Header\Language.h"
#include "CMtCoreWidgetUtil.h"
QMTInformLight::QMTInformLight(QWidget* parent) : QTipWidget(parent)
{
    SetImagePath(0, QString(":/AccuUIComponentImage/images/informIdle3.png"));
    SetImagePath(1, QString(":/AccuUIComponentImage/images/informTasking.png"));
    _movieImgPathList.append(":/AccuUIComponentImage/images/informTasking3_0.png");
    _movieImgPathList.append(":/AccuUIComponentImage/images/informTasking3_1.png");
    _movieImgPathList.append(":/AccuUIComponentImage/images/informTasking3_2.png");
    _movieImgPathList.append(":/AccuUIComponentImage/images/informTasking3_3.png");
    step = 10;
    interval = 100;
    _bgColor = QColor(CMtCoreWidgetUtil::formatColor("rgb(@colorDoseIso2)"));
    _timer = new QTimer(this);
    connect(_timer, SIGNAL(timeout()), this, SLOT(slotTimeOut()));
    _timer->start(interval);
    offset = 0;
    add = true;
}

QMTInformLight::~QMTInformLight()
{
    if (_timer->isActive())
    {
        _timer->stop();
    }
}

void QMTInformLight::mousePressEvent(QMouseEvent* event)
{
    emit sigInformClicked();
}

void QMTInformLight::DrawBgImage()
{
    QPainter painter(this);
    // 绘制准备工作 启用反锯齿
    painter.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
    // 平移坐标轴中心,
    painter.translate(rect().width() / 2, rect().height() / 2);
    QImage sourceImage;
    QString imagePath = _imagePahtMap.value(_state);

    if (0 == _state)
    {
        drawIdleBg(&painter);
        sourceImage.load(imagePath);
    }
    else if (1 == _state)
    {
        if (_bEnableMovie)
        {
            imagePath = _movieImgPathList[_curMovieIndex];
            drawIdleBg(&painter);
            sourceImage.load(imagePath);
        }
        else
        {
            //绘制背景
            drawBg(&painter);
            sourceImage.load(imagePath);
        }
    }

    int width = sourceImage.width();
    width = width / 2;
    painter.drawImage(-width, -width, sourceImage);
}

void QMTInformLight::paintEvent(QPaintEvent* e)
{
    DrawBgImage();

    if (_value <= _minValue)
    {
        return;
    }

    // 1. 准备文本
    QString text = QString::number(_value);
    QFont f = QFont(qApp->font().family(), 8);
    int texty = 0;
    int textx = 0;
    int textwidth = 0;
    int textheight = 0;

    // 1.1 特殊显示
    if (_value > _elideValue)
    {
        text = QString::number(_elideValue) + "+"; // "..."; …
        //texty = 0;
        //textx = 0;
        //textwidth = 0;
        //textheight = 0;
    }

    QFontMetrics fontWidth(f);
    int wordWidth = fontWidth.width(text);
    QPainter painter2(this);
    {
        // 2. 绘制准备工作
        // 2.1启用反锯齿
        painter2.setRenderHints(QPainter::Antialiasing | QPainter::TextAntialiasing);
        // 2.1 平移坐标轴中心,
        painter2.translate(rect().width() / 2, rect().height() / 2);
    }
    // 3. 绘制背景
    int expandWidth = 8;// 在 wordWidth 上再加这么多，以显示半圆
    int radius = 7;     // 半径
    int realHeight = radius + radius + textheight;
    int realWidth = expandWidth + wordWidth + textwidth;
    int x = 2 + (20 - realWidth) / 2 + textx;  // 2 为偏移量
    int y = -15 + texty;  // -15 为偏移量
    {
        // 3.1 画两端的半圆，中间矩形
        painter2.setPen(Qt::NoPen);
        painter2.setBrush(QBrush(QColor("#D95A55")));
        QRectF outRectEllipse1(x, y, realHeight, realHeight); // x,width 会变化，y,heght 不会变化
        painter2.drawEllipse(outRectEllipse1);
        QRectF outRectEllipse2(x + realWidth - realHeight, y, realHeight, realHeight);
        painter2.drawEllipse(outRectEllipse2);
        QRectF outRect1(x + radius, y, realWidth - realHeight, realHeight);
        painter2.drawRect(outRect1);
    }
    {
        // 3.2 画文字
        QRectF outRect(x, y, realWidth, realHeight);
        painter2.setFont(f);
        painter2.setPen(QColor("#ffffff"));
        painter2.drawText(outRect, Qt::AlignCenter, text);
    }
}

void QMTInformLight::drawBg(QPainter* painter)
{
    // 半径为当前 宽 或者 高 的一半
    int radius = qMin(rect().width(), rect().height()) / 2 + 10;
    // 保存当前painter
    painter->save();
    // 以点为中心的渐变色
    QRadialGradient g(QPoint(0, 0), radius);
    // 循环加减
    (offset < 100 && add) ? (offset += step) : (add = false);
    (offset > 0 && !add) ? (offset -= step) : (add = true);
    // 按照 点范围[0.0,1.0] 对于 每点的颜色
    _bgColor.setAlpha(200 + offset > 255 ? 255 : 200 + offset);
    g.setColorAt(0.0, _bgColor);
    _bgColor.setAlpha(140 + offset);
    g.setColorAt(0.2, _bgColor);
    _bgColor.setAlpha(80 + offset);
    g.setColorAt(0.4, _bgColor);
    _bgColor.setAlpha(20 + offset >= 0 ? 20 + offset : 0);
    g.setColorAt(0.6, _bgColor);
    _bgColor.setAlpha(-60 + offset >= 0 ? -50 + offset : 0);
    g.setColorAt(0.8, _bgColor);
    _bgColor.setAlpha(0);
    g.setColorAt(1.0, _bgColor);
    // 设置 画笔 图形的边界线
    painter->setPen(Qt::NoPen);
    // 设置 画刷 画刷为 点向外辐射的渐变色
    painter->setBrush(g);
    // 画椭圆，长=宽 为原型
    painter->drawEllipse(-radius, -radius, radius * 2, radius * 2);
    // 回复保存的
    painter->restore();
    ///////add
    ///
    QRectF inRect(-20, -20, 40, 40);
#if 0
    /// //画遮罩
    painter->setBrush(palette().window().color());
    painter->drawEllipse(inRect);
#endif
    //画文字
}

void QMTInformLight::drawIdleBg(QPainter* painter)
{
    // 半径为当前 宽 或者 高 的一半
    int radius = qMin(rect().width(), rect().height()) / 2;
    painter->eraseRect(-radius, -radius, radius * 2, radius * 2);
#if 0
    // 半径为当前 宽 或者 高 的一半
    int radius = qMin(rect().width(), rect().height()) / 2;
    painter->setBrush(QColor(37, 41, 48));
    // 画椭圆，长=宽 为原型
    painter->drawEllipse(-radius, -radius, radius * 2, radius * 2);
#endif
    /*painter->setRenderHint(QPainter::Antialiasing);
    int m_rotateAngle = 360 * 100 / 100;
    int side = qMin(width(), height());
    QRectF outRect(0, 0, side, side);
    QSize m_arcSize = QSize(30, 30);
    int arcWidth = m_arcSize.width();
    int arcHeight = m_arcSize.height();
    QRectF inRect(arcWidth, arcHeight, side - arcWidth * 2, side - arcHeight * 2);
    //QString valueStr = QString("%1").arg(QString::number(m_value));
    //画外圆
    QColor m_arcColor = QColor(97, 117, 118);
    painter->setPen(Qt::NoPen);
    painter->setBrush(QBrush(m_arcColor));//p.setBrush(QBrush(QColor(97, 117, 118)));
    painter->drawEllipse(outRect);
    painter->setBrush(QBrush(m_arcColor));
    painter->drawPie(outRect, (90 - m_rotateAngle) * 16, m_rotateAngle * 16);*/
}

int QMTInformLight::getStep() const
{
    return this->step;
}

int QMTInformLight::getInterval() const
{
    return this->interval;
}

QColor QMTInformLight::getBgColor() const
{
    return this->_bgColor;
}

QSize QMTInformLight::sizeHint() const
{
    return QSize(100, 100);
}

QSize QMTInformLight::minimumSizeHint() const
{
    return QSize(5, 5);
}

void QMTInformLight::setStep(int step)
{
    if (this->step != step)
    {
        this->step = step;
        update();
    }
}

void QMTInformLight::setInterval(int interval)
{
    if (this->interval != interval)
    {
        this->interval = interval;
        _timer->setInterval(interval);
        update();
    }
}

void QMTInformLight::setBgColor(const QColor& bgColor)
{
    if (this->_bgColor != bgColor)
    {
        this->_bgColor = bgColor;
        update();
    }
}

void QMTInformLight::slotTimeOut()
{
    if (1 == _state)
    {
        if (++_curMovieIndex >= _movieImgPathList.size())
        {
            _curMovieIndex = 0;
        }

        DrawBgImage();
        this->repaint();
    }

    this->update();
}

void QMTInformLight::SetImagePath(int state, const QString& path)
{
    _imagePahtMap.insert(state, path);
    this->update();
}

void QMTInformLight::SetLightState(int state)
{
    if (this->_state != state)
    {
        this->_state = state;
        update();
    }

    QString imagePath = _imagePahtMap.value(state);

    if (0 == state)
    {
        if (_timer->isActive())
        {
            _timer->stop();
        }
    }
    else
    {
        if (false == _timer->isActive())
        {
            _timer->start(interval);
        }
    }
}

void QMTInformLight::SetEnableBreathLight(bool bEnable)
{
    if (false == bEnable)
    {
        if (_timer->isActive())
        {
            _timer->stop();
        }
    }
    else
    {
        _timer->start(interval);
    }
}

void QMTInformLight::SetValue(int value)
{
    _value = value;
    update();
}

void QMTInformLight::AddOneValue(bool isWarn)
{
    _value++;
    update();
}

void QMTInformLight::DeleteOneValue()
{
    if (_value > 0)
    {
        _value--;
        update();
    }
}

void QMTInformLight::ResetValue()
{
    _value = 0;
    update();
}

void QMTInformLight::setElideValue(int n)
{
    this->_elideValue = n;
}
int QMTInformLight::elideValue() const
{
    return this->_elideValue;
}
void QMTInformLight::setMinValue(int n)
{
    this->_minValue = n;
}
int QMTInformLight::minValue() const
{
    return this->_minValue;
}

void QMTInformLight::SetEnableMovie(bool bEnable)
{
    _bEnableMovie = bEnable;
}

void QMTInformLight::SetMovieImgPathList(QStringList pathList)
{
    _movieImgPathList = pathList;
}
