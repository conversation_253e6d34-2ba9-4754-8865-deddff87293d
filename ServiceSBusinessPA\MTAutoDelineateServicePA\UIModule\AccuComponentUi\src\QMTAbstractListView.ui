<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTAbstractListView</class>
 <widget class="QWidget" name="QMTAbstractListView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>889</width>
    <height>752</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTAbstractListView</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#QMTAbstractListView,#widget_content,#widget_header,#scrollArea,#scrollAreaWidgetContents{
background-color: rgb(37, 41, 48);
}

QLabel
{
	border-style:none solid solid none;/*上右下左*/
	border-width:1px;
	border-color:rgba(191,200,215,0.1);
	background-color: rgb(37,41,48);
	border-radius:0px;
	color:rgba(146,155,170,1);
	font-size:14px;
	padding: 0px 6px;
}

#label,#label_2,#label_3,#label_4,#label_5,#label_6,#label_7{
	border-style:none solid solid none;/*上右下左*/
	border-width:1px;
	border-color:rgba(191,200,215,0.1);
	background-color: rgb(37,41,48);
	border-radius:0px;
	color:rgba(146,155,170,1);
	font-size:14px;
	padding: 0px 6px;
}

QScrollBar:vertical
{
    width:6px;
    background:rgb(37,41,48);
    margin:0px,0px,0px,0px;
    padding-top:0px;   /*上预留位置*/
    padding-bottom:0px;    /*下预留位置*/
}

/*滚动条中滑块的样式*/
QScrollBar::handle:vertical
{
    width:6px;
    background:rgb(91,100,116);
    border-radius:3px;
    min-height:20px;
}
/*鼠标触及滑块样式*/
/*
QScrollBar::handle:vertical:hover
{
    width:6px;
    background:rgb(37,41,48);
    border-radius:1px;
    min-height:20;
}*/


/*当滚动条滚动的时候，上面的部分和下面的部分*/
QScrollBar::add-page:vertical,QScrollBar::sub-page:vertical
{
    background:rgb(37,41,48);
    border-radius:3px;
}

QScrollBar:horizontal
{
    height:6px;
    background:rgb(37,41,48);
    margin:0px,0px,0px,0px;
    padding-top:0px;   /*上预留位置*/
    padding-bottom:0px;    /*下预留位置*/
}

/*滚动条中滑块的样式*/
QScrollBar::handle:horizontal
{
    height:6px;
    background:rgb(91,100,116);
    border-radius:3px;
    min-width:20px;
}

/*当滚动条滚动的时候，上面的部分和下面的部分*/
QScrollBar::add-page:horizontal,QScrollBar::sub-page:horizontal
{
    background:rgb(37,41,48);
    border-radius:3px;
}
QScrollArea{border:none;}</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_header" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>40</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>40</height>
      </size>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_headerList" native="true">
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QLabel" name="label">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_2">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_3">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_4">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_7">
           <property name="text">
            <string>TextLabel</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>305</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_content" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>1</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QScrollArea" name="scrollArea">
        <property name="widgetResizable">
         <bool>true</bool>
        </property>
        <widget class="QWidget" name="scrollAreaWidgetContents">
         <property name="geometry">
          <rect>
           <x>0</x>
           <y>0</y>
           <width>889</width>
           <height>711</height>
          </rect>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <property name="spacing">
           <number>1</number>
          </property>
          <property name="leftMargin">
           <number>0</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <spacer name="verticalSpacer">
            <property name="orientation">
             <enum>Qt::Vertical</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>20</width>
              <height>40</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
