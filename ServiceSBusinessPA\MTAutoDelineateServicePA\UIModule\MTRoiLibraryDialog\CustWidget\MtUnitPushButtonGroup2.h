﻿#pragma once

#include <QWidget>
#include <QPushButton>
#include <QToolButton>
#include <QMap>
#include "AccuComponentUi\Header\QMTUIModuleParam.h"
#include "MtPushButton.h"

namespace Ui
{
class MtUnitPushButtonGroup2;
}
class MtPushButton;

//MtUnitPushButtonGroup2 参数
class MtUnitPushButtonGroup2Param : public ICellWidgetParam
{
public:
    QStringList _btnTextStrList;        //按键文案。这边有多少个，按键就有多少个
    int _btnWidth = -1;
    int _btnHeight = -1;
    QMap<int/*btnIndex*/, int/*mtType*/> _btnIndexMtTypeMap;    //按键下标对应的mtType
    QMap<int/*btnIndex*/, bool/*enabled*/> _btnIndexEnabledMap; //按键可用状态

    MtUnitPushButtonGroup2Param();
    ~MtUnitPushButtonGroup2Param();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(MtUnitPushButtonGroup2Param)

/*
MtPushButton集合
*/

class MtUnitPushButtonGroup2 :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT
public:

    MtUnitPushButtonGroup2(QWidget* parent = Q_NULLPTR);
    ~MtUnitPushButtonGroup2();

    /*单元格公共接口*/
    //更新界面接口
    virtual bool UpdateUi(const QVariant& updateData);
    //设置是否允许编辑(有是否允许编辑状态的子控件必须实现)
    virtual void SetEnableEdit(bool bEdit);
    //按键使能设置
    virtual void SetButtonEnable(int btnIndex/**/, bool bEnable);
    //获取checked
    virtual bool GetCellChecked(int index);

    /******************ui************************/
    void SetupCellWidget(MtUnitPushButtonGroup2Param& param);
    void HideButton(int btnIndex, bool bHide);          //是否隐藏按键
    void SetButtonText(int btnIndex, QString& text);    //设置某个按键的文案

signals:
    void sigClicked(int);
    void sigButtonClicked(int/*btnIndex*/, bool/*ischecked*/);  //某个按键点击了

protected:
    /*******************delete*************************/
    void DeleteButtons();

private slots:
    void slotButtonClicked(bool);

private:
    Ui::MtUnitPushButtonGroup2* ui;
    MtUnitPushButtonGroup2Param _pushBtnGroupParam;
    QList<MtPushButton*> _buttonList;

};
