﻿#include "AccuComponentUi\Header\tooltip.h"
#include <QDebug>
#include <QApplication>
#include <QDesktopWidget>
CToolTip::CToolTip(QWidget* parent) :
    QWidget(parent)
{
    //获取桌面大小。超出显示在左边
    QDesktopWidget* desktopWidget = QApplication::desktop();
    //获取设备屏幕大小
    m_screenWidth = desktopWidget->screenGeometry().width();
    // 设置窗口鼠标穿透
    setAttribute(Qt::WA_TransparentForMouseEvents, true);
    setAttribute(Qt::WA_TranslucentBackground);//设置背景透明
    //tool任务栏无图标
    setWindowFlags(Qt::WindowStaysOnTopHint | Qt::Tool | Qt::FramelessWindowHint | Qt::WindowSystemMenuHint | Qt::WindowMinMaxButtonsHint);
    //用网格布局使得窗口随控件变化
    QVBoxLayout* verlayout = new QVBoxLayout(this);
    verlayout->setSpacing(0);
    verlayout->setContentsMargins(0, 0, 0, 0);
    //标题
    labelTitle = new QLabel("", this);
    labelTitle->setScaledContents(true);
    labelTitle->setWordWrap(true);
    QFont font(qApp->font().family());
    font.setPixelSize(12);
    labelTitle->setFont(font);
    labelTitle->setStyleSheet("background-color: rgba(219,226,241,0.9);padding-top:4px;padding-left:7px;padding-right:7px;\
						color:rgba(37,41,48,1);border-top-left-radius:2px;border-top-right-radius:2px;");
    verlayout->addWidget(labelTitle, 0);
    //内容
    labelInfo = new QLabel("", this);
    labelInfo->setScaledContents(true);
    labelInfo->setWordWrap(true);
    labelInfo->setFont(font);
    labelInfo->setStyleSheet("background-color: rgba(219,226,241,0.9);padding-top:2px;padding-left:7px;padding-right:7px;padding-bottom:7px;\
						color:rgba(37,41,48,0.5);border-bottom-left-radius:2px;border-bottom-right-radius:2px;");
    verlayout->addWidget(labelInfo, 0);
    setLayout(verlayout);
    hide();
}

CToolTip::CToolTip(QString title, QString msg, QWidget* parent) :
    QWidget(parent)

{
    //获取桌面大小。超出显示在左边
    QDesktopWidget* desktopWidget = QApplication::desktop();
    //获取设备屏幕大小
    m_screenWidth = desktopWidget->screenGeometry().width();
    // 设置窗口鼠标穿透
    setAttribute(Qt::WA_TransparentForMouseEvents, true);
    setAttribute(Qt::WA_TranslucentBackground);//设置背景透明
    //tool任务栏无图标
    setWindowFlags(Qt::WindowStaysOnTopHint | Qt::Tool | Qt::FramelessWindowHint | Qt::WindowSystemMenuHint | Qt::WindowMinMaxButtonsHint);
    //用网格布局使得窗口随控件变化
    QVBoxLayout* verlayout = new QVBoxLayout(this);
    verlayout->setSpacing(0);
    verlayout->setContentsMargins(0, 0, 0, 0);
    //标题
    labelTitle = new QLabel(title, this);
    labelTitle->setScaledContents(true);
    labelTitle->setWordWrap(true);
    QFont font(qApp->font().family());
    font.setPixelSize(12);
    labelTitle->setFont(font);
    labelTitle->setStyleSheet("background-color: rgba(219,226,241,0.9);padding-top:4px;padding-left:7px;padding-right:7px;\
						color:rgba(37,41,48,1);border-top-left-radius:2px;border-top-right-radius:2px;");
    verlayout->addWidget(labelTitle, 0);
    //内容
    labelInfo = new QLabel(msg, this);
    labelInfo->setScaledContents(true);
    labelInfo->setWordWrap(true);
    labelInfo->setFont(font);
    labelInfo->setStyleSheet("background-color: rgba(219,226,241,0.9);padding-top:2px;padding-left:7px;padding-right:7px;padding-bottom:7px;\
						color:rgba(37,41,48,0.5);border-bottom-left-radius:2px;border-bottom-right-radius:2px;");
    verlayout->addWidget(labelInfo, 0);
    setLayout(verlayout);
    hide();
}

CToolTip::~CToolTip()
{
    delete this->labelTitle;
    delete this->labelInfo;
    labelTitle = nullptr;
    labelInfo = nullptr;
}

// 显示ToolTip消息
void CToolTip::showMessage(QString& title, QString& info, QPoint& point)
{
    labelTitle->setText(title);
    labelInfo->setText(info);
    //先show出来。就可以根据内容改变长度。从而判断
    show();

    if (point.rx() + this->width() > m_screenWidth)
    {
        if (!foreverStand)
        {
            move(QPoint(point.rx() - this->width(), point.ry() + 20));
        }
    }
    else
    {
        if (!foreverStand)
        {
            move(QPoint(point.rx(), point.ry() + 20));
        }
    }

    foreverStand = true;
}

void CToolTip::showStop()
{
    this->hide();
    foreverStand = false;
}


void CToolTip::setMaxWidth(int width)
{
    labelTitle->setMaximumWidth(width);
    labelInfo->setMaximumWidth(width);
}

void CToolTip::setTitleDisplayStatus(bool isHidden)
{
    if (isHidden)
    {
        labelTitle->setHidden(true);
        labelInfo->setStyleSheet("background-color: rgba(219,226,241,0.9);padding-top:2px;padding-left:7px;padding-right:7px;padding-bottom:2px;\
						color:rgba(37,41,48,0.5);border-bottom-left-radius:2px;border-bottom-right-radius:2px;border-top-left-radius:2px;\
							border-top-right-radius:2px;");
    }
    else
    {
        labelTitle->setHidden(false);
        labelInfo->setStyleSheet("background-color: rgba(219,226,241,0.9);padding-top:2px;padding-left:7px;padding-right:7px;padding-bottom:7px;\
						color:rgba(37,41,48,0.5);border-bottom-left-radius:2px;border-bottom-right-radius:2px;");
    }
}






