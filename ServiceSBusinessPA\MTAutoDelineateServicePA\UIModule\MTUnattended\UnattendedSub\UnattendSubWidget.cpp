﻿#include "UnattendSubWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "DataDefine/InnerStruct.h"


#define Def_TempRuleName   QObject::tr("新建无人值守规则")  //临时规则名
#define Def_RightTableItemWidth 1330 //右侧表格item宽度


UnattendSubWidget::UnattendSubWidget(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    //输入限定
    ui.lineEdit_imageNum_filter->setRegExpression(RegExp_Number5);
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,UnattendSubWidget, " << errMsg.toStdString();
        }
    }
    ui.scrollArea->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    ui.scrollArea->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    connect(ui.mtListWidget_right->horizontalScrollBar(), &MtScrollBar::valueChanged, this, [&](int value)
    {
        ui.scrollArea->horizontalScrollBar()->setValue(value);
    });
    ui.scrollArea->setWidgetResizable(true);
    ui.mtLabel_15->setMinimumWidth(1000);
}

UnattendSubWidget::~UnattendSubWidget()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void UnattendSubWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
    ui.mtToolButton_add->setPixmapFilename(imagePathHash["icon_add"]);
    ui.mtToolButton_copy->setPixmapFilename(imagePathHash["icon_copy"]);
    ui.mtToolButton_del->setPixmapFilename(imagePathHash["icon_del2"]);
    ui.mtToolButton_search_source->setPixmapFilename(imagePathHash["icon_setting"]);
    ui.mtToolButton_question_filter->setPixmapFilename(imagePathHash["icon_quesion"]);
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="ptrOptUnattendData">[IN]无人值守信息</param>
/// <param name="stCallBackUnattended">[OUT]数据回调</param>
void UnattendSubWidget::init(OptUnattendDataNew* ptrOptUnattendData, n_mtautodelineationdialog::ST_CallBack_Unattended& stCallBackUnattended)
{
    //清空及切换界面
    enterEditPage(false);
    //初始化数据
    setCurFlagModule(ST_UnattendSubWidgetModule());
    m_ptrOptUnattendData = ptrOptUnattendData;
    m_stCallBackUnattended = stCallBackUnattended;
    QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig> unattendedConfigMap = ptrOptUnattendData->getUnattendedConfigMap();

    //如果无人值守规则为空，则自动加入一条到内存
    if (unattendedConfigMap.isEmpty() == true)
    {
        //1:共享文件夹 2:FTP服务器 4:SCP服务器
        QMap<int, QStringList> tempLocalMap = ptrOptUnattendData->getAllLocalServerMap();

        if (tempLocalMap.contains(4) == true && tempLocalMap[4].isEmpty() == false)
        {
            //更新内存
            if (m_stCallBackUnattended.updateUnattendedCallBack != nullptr)
            {
                n_mtautodelineationdialog::ST_UnattendedConfig stNewUnattendedConfig;
                stNewUnattendedConfig.stLocalServerRule.curServerType = 4;
                stNewUnattendedConfig.stLocalServerRule.curServerName = tempLocalMap[4][0];
                stNewUnattendedConfig.stSketchRule = createDefaultAISketchRule();
                QString imageSourceUniqueKey = CommonUtil::getCurrentDateTime();
                QString errMsg;

                if (m_stCallBackUnattended.updateUnattendedCallBack(n_mtautodelineationdialog::OptType_Add, imageSourceUniqueKey, stNewUnattendedConfig, errMsg) == true)
                {
                    ptrOptUnattendData->addUnattendedConfig(imageSourceUniqueKey, stNewUnattendedConfig);
                    unattendedConfigMap = ptrOptUnattendData->getUnattendedConfigMap();
                }
            }
        }
    }

    //初始化UI
    ui.widget_right->setHidden(true);
    ui.mtListWidget_left->clear();
    ui.mtListWidget_right->clear();

    //添加左侧影像来源
    for (QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>::iterator it = unattendedConfigMap.begin(); it != unattendedConfigMap.end(); it++)
    {
        n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig = it.value();
        //添加左侧数据来源列表
        addRowLeftImageSourceTable(it.key(), stUnattendedConfig.isEnable, stUnattendedConfig.stLocalServerRule.curServerName, stUnattendedConfig.stLocalServerRule.curServerType);
    }

    //清空右侧选中
    ui.mtListWidget_right->setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    ui.mtListWidget_right->clearSelection();
    ui.mtListWidget_right->setCurrentRow(-1);
    //zlw 暂时屏蔽 由于层数的逻辑和关键字过滤的逻辑不同，暂时先屏蔽层数
    {
        ui.mtCheckBox_imageNum_filter->hide();
        ui.lineEdit_imageNum_filter->hide();
        ui.mtLabel_11->hide();
        ui.mtLabel_10->hide();
        ui.mtLabel_9->hide();
        ui.mtLabel_imageNum_filter->hide();
        ui.horizontalSpacer_9->changeSize(0, 0);
    }
    //信号槽
    connnectSignal(false);
    connnectSignal(true);
    //影像来源选中第一个
    ui.mtListWidget_left->setCurrentRow(0);
}

/// <summary>
/// 是否处于编辑状态
/// </summary>
/// <returns>true是</returns>
bool UnattendSubWidget::isEditState()
{
    bool enable = ui.mtToolButton_add->isEnabled();
    return !enable;
}

/// <summary>
/// 设置右侧规则列表最小高度
/// </summary>
/// <param name="minHeightNum">[IN]最小高度</param>
void UnattendSubWidget::setMinHeight_widget_table_sketch(const int minHeightNum)
{
    ui.widget_table_sketch->setMinimumHeight(minHeightNum);
}

/// <summary>
/// 影像来源-新增按钮
/// </summary>
void UnattendSubWidget::onMtToolButton_add()
{
    //是否可以新增一条规则(可用模板)
    if (isCanAddOneRule() == false)
        return;

    disconnect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
    //进入编辑页面模式
    enterEditPage(true, true);
    //创建新的影像来源唯一标识
    QString newImageSourceUniqueKey = CommonUtil::getCurrentDateTime();
    //存储标识数据
    ST_UnattendSubWidgetModule stModule;
    stModule.setValue(true, newImageSourceUniqueKey);
    setCurFlagModule(stModule);
    //判断此时是否有规则存在
    n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig;

    if (ui.mtListWidget_left->count() <= 0)
        stUnattendedConfig.stSketchRule = createDefaultAISketchRule();

    //左侧影像来源新增一行
    int rowNum = addRowLeftImageSourceTable(newImageSourceUniqueKey, false, Def_TempRuleName, -1);
    ui.mtListWidget_left->setCurrentRow(rowNum);
    //刷新右侧界面(编辑模式)
    updateRightWidgetEdit(true, stUnattendedConfig);
    //关闭开关使能
    ui.mtSwitchButton_title->setEnabled(false);
    connect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
}

/// <summary>
/// 影像来源-拷贝按钮
/// </summary>
void UnattendSubWidget::onMtToolButton_copy()
{
    //是否可以新增一条规则(可用模板)
    if (isCanAddOneRule() == false)
        return;

    //判断是否有选中
    ImageSourceWidget* ptrImageSourceWidget = getItemLeftImageSourceTable(ui.mtListWidget_left->currentItem());

    if (ptrImageSourceWidget == nullptr)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要拷贝的影像来源"));
        return;
    }

    //获取拷贝原始数据
    QString imageSourceUniqueKey = ptrImageSourceWidget->getImageSourceUniqueKey();
    QList<n_mtautodelineationdialog::ST_UnattendedConfig> infoList = m_ptrOptUnattendData->getUnattendedConfig(imageSourceUniqueKey);

    if (infoList.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要拷贝的影像来源"));
        return;
    }

    n_mtautodelineationdialog::ST_UnattendedConfig newUnattendedConfig = infoList[0];
    //替换规则里的唯一标识
    QMap<QString, n_mtautodelineationdialog::ST_SketchIdentify> tempSketchIdentifyMap = newUnattendedConfig.stSketchRule.sketchIdentifyMap;
    newUnattendedConfig.stSketchRule.sketchIdentifyMap.clear();

    for (QMap<QString, n_mtautodelineationdialog::ST_SketchIdentify>::Iterator it = tempSketchIdentifyMap.begin(); it != tempSketchIdentifyMap.end(); it++)
    {
        //将导出规则的customId换个新的
        QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> newAddrInfoMap;

        for (QMap<QString, n_mtautodelineationdialog::ST_AddrSimple>::iterator iter_addr = it.value().addrInfoMap.begin(); iter_addr != it.value().addrInfoMap.end(); iter_addr++)
        {
            newAddrInfoMap.insert(CommonUtil::getCurrentDateTime(), iter_addr.value());
        }

        it.value().addrInfoMap = newAddrInfoMap;
        //将勾画规则的customId换个新的
        newUnattendedConfig.stSketchRule.sketchIdentifyMap.insert(CommonUtil::getCurrentDateTime(), it.value());
    }

    //界面刷新
    disconnect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
    //进入编辑页面模式
    enterEditPage(true, true);
    //创建新的影像来源唯一标识
    QString newImageSourceUniqueKey = CommonUtil::getCurrentDateTime();
    //存储标识数据
    ST_UnattendSubWidgetModule stModule;
    stModule.setValue(true, newImageSourceUniqueKey);
    setCurFlagModule(stModule);
    //左侧影像来源新增一行
    int rowNum = addRowLeftImageSourceTable(newImageSourceUniqueKey, false, newUnattendedConfig.stLocalServerRule.curServerName, newUnattendedConfig.stLocalServerRule.curServerType);
    ui.mtListWidget_left->setCurrentRow(rowNum);
    //刷新右侧界面(编辑模式)
    updateRightWidgetEdit(true, newUnattendedConfig);
    //关闭开关使能
    ui.mtSwitchButton_title->setEnabled(false);
    connect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
}

/// <summary>
/// 影像来源-删除按钮
/// </summary>
void UnattendSubWidget::onMtToolButton_del()
{
    //判断是否有选中
    int rowIndex = ui.mtListWidget_left->currentRow();
    QListWidgetItem* listWidgetItem = ui.mtListWidget_left->currentItem();
    ImageSourceWidget* ptrImageWidget = getItemLeftImageSourceTable(listWidgetItem);

    if (ptrImageWidget == nullptr)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要删除的影像来源"));
        return;
    }

    QString imageSourceUniqueKey = ptrImageWidget->getImageSourceUniqueKey();
    QString msg = QString(tr("是否确定删除该无人值守规则？")); //.arg(ptrImageWidget->getServerName()) 您确定要删除[%1]影像来源吗？
    int ret = MtMessageBox::NoIcon::question_Title(this, msg);

    if (QMessageBox::Yes == ret)
    {
        if (m_stCallBackUnattended.updateUnattendedCallBack != nullptr)
        {
            n_mtautodelineationdialog::ST_UnattendedConfig delUnattendedConfig;
            QList<n_mtautodelineationdialog::ST_UnattendedConfig> tempConfigVec = m_ptrOptUnattendData->getUnattendedConfig(imageSourceUniqueKey);

            if (tempConfigVec.isEmpty() == false)
                delUnattendedConfig = tempConfigVec[0];

            QString errMsg;

            if (m_stCallBackUnattended.updateUnattendedCallBack(n_mtautodelineationdialog::OptType_Del, imageSourceUniqueKey, delUnattendedConfig, errMsg) == false)
            {
                MtMessageBox::yellowWarning(this, tr("删除影像来源失败"), errMsg);
                return;
            }

            //删除内存
            m_ptrOptUnattendData->delUnattendedConfig(imageSourceUniqueKey);
            //删除控件
            QListWidgetItem* removeItem = ui.mtListWidget_left->takeItem(rowIndex);

            if (removeItem != nullptr)
                delete removeItem;

            //退出编辑页面模式
            enterEditPage(false, true);
            //清空选中
            clearSelectRowLeftImageSourceTable();
        }
    }
}

/// <summary>
/// 影像来源-开关按钮按钮
/// </summary>
void UnattendSubWidget::onMtSwitchButton_title(bool ischecked)
{
    if (ui.mtStackedWidget_title->currentWidget() == ui.page_show_title)
    {
        //获取操作的原始数据
        ST_UnattendSubWidgetModule stModule = getCurFlagModule();
        QList<n_mtautodelineationdialog::ST_UnattendedConfig> infoList = m_ptrOptUnattendData->getUnattendedConfig(stModule.imageSourceUniqueKey);

        if (infoList.isEmpty() == true)
            return;

        //判断是否可以启用
        if (ischecked == true)
        {
            QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig> allUnattendedMap = m_ptrOptUnattendData->getUnattendedConfigMap();
            allUnattendedMap.remove(stModule.imageSourceUniqueKey);

            for (QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>::iterator it = allUnattendedMap.begin(); it != allUnattendedMap.end(); it++)
            {
                if (infoList[0].stLocalServerRule.curServerName == it.value().stLocalServerRule.curServerName && it.value().isEnable == true)
                {
                    MtMessageBox::NoIcon::information_Title(this->window(), tr("同一节点同时只能有一个无人值守规则生效！"));
                    disconnect(ui.mtSwitchButton_title, &MtSwitchButton::clicked, this, &UnattendSubWidget::onMtSwitchButton_title);
                    ui.mtSwitchButton_title->setChecked(false);
                    connect(ui.mtSwitchButton_title, &MtSwitchButton::clicked, this, &UnattendSubWidget::onMtSwitchButton_title);
                    return;
                }
            }
        }

        //影像来源删除按钮使能(启动状态下不允许删除和拷贝)
        ui.mtToolButton_copy->setEnabled(!ui.mtSwitchButton_title->isChecked());
        ui.mtToolButton_del->setEnabled(!ui.mtSwitchButton_title->isChecked());
        //关闭编辑规则按钮使能
        ui.mtPushButton_edit_title->setEnabled(!ischecked);
        //更新左侧列表
        updateLeftImageSourceTable(stModule.imageSourceUniqueKey, ischecked, QString(), -1);

        //数据回调
        if (m_stCallBackUnattended.ruleEnableCallBack != nullptr)
        {
            QString errMsg;
            m_stCallBackUnattended.ruleEnableCallBack(ischecked, stModule.imageSourceUniqueKey, errMsg);
            infoList[0].isEnable = ischecked;
            m_ptrOptUnattendData->updateUnattendedConfig(stModule.imageSourceUniqueKey, infoList[0]);
        }
    }
}

/// <summary>
/// 影像来源-编辑规则按钮
/// </summary>
void UnattendSubWidget::onMtPushButton_edit_title()
{
    ST_UnattendSubWidgetModule stModule = getCurFlagModule();

    if (stModule.imageSourceUniqueKey.isEmpty() == true)
        return;

    QList<n_mtautodelineationdialog::ST_UnattendedConfig> unattendedConfigList = m_ptrOptUnattendData->getUnattendedConfig(stModule.imageSourceUniqueKey);

    if (unattendedConfigList.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请先选择要编辑的影像来源"));
        return;
    }

    //关闭开关使能
    ui.mtSwitchButton_title->setEnabled(false);
    //进入编辑页面模式
    enterEditPage(true);
    //刷新右侧界面(编辑模式)
    updateRightWidgetEdit(false, unattendedConfigList[0]);
}

/// <summary>
/// 影像来源-保存规则按钮
/// </summary>
void UnattendSubWidget::onMtPushButton_save_title()
{
    ST_UnattendSubWidgetModule stModule = getCurFlagModule();

    if (stModule.imageSourceUniqueKey.isEmpty() == true)
        return;

    //组装数据
    n_mtautodelineationdialog::ST_UnattendedConfig stNewUnattendedConfig;
    stNewUnattendedConfig.isEnable = false;
    //影像来源updateUnattendedCallBack
    QMap<int/*本地服务器类型*/, QString/*服务器名*/> comboBoxData = ui.mtComboBox_server_source->currentData().value<QMap<int, QString>>();

    if (comboBoxData.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请配置接收节点"));
        return;
    }

    stNewUnattendedConfig.stLocalServerRule.curServerType = comboBoxData.begin().key();
    stNewUnattendedConfig.stLocalServerRule.curServerName = comboBoxData.begin().value();
    stNewUnattendedConfig.stLocalServerRule.numfilterEnable = ui.mtCheckBox_imageNum_filter->isChecked();
    stNewUnattendedConfig.stLocalServerRule.imagefilterEnable = ui.mtCheckBox_word_filter->isChecked();
    stNewUnattendedConfig.stLocalServerRule.numNotSketch = ui.lineEdit_imageNum_filter->text().toInt();
    stNewUnattendedConfig.stLocalServerRule.imagefilterMap["0008103E"] = CommonUtil::strToStringList(ui.lineEdit_word_filter->text());
    //zlw 暂时屏蔽
    /* if (stNewUnattendedConfig.stLocalServerRule.numfilterEnable == true && (stNewUnattendedConfig.stLocalServerRule.numNotSketch < 2 || ui.lineEdit_imageNum_filter->text().isEmpty() == true))
     {
         ui.lineEdit_imageNum_filter->setWarningBorderStatus(tr("请配置影像层数(2~99)"));
         return;
     }*/

    if (stNewUnattendedConfig.stLocalServerRule.imagefilterEnable == true && stNewUnattendedConfig.stLocalServerRule.imagefilterMap["0008103E"].isEmpty() == true)
    {
        ui.lineEdit_word_filter->setWarningBorderStatus(tr("请配置识别词"));
        return;
    }

    if (stNewUnattendedConfig.stLocalServerRule.numNotSketch == false && ui.lineEdit_imageNum_filter->text().isEmpty() == false)
    {
        stNewUnattendedConfig.stLocalServerRule.numNotSketch = -1;
    }

    if (ui.mtListWidget_right->count() <= 0)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请配置影像识别、勾画与导出"));
        return;
    }

    //勾画规则
    for (int i = 0; i < ui.mtListWidget_right->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_right->item(i);
        UnattendSubTableEditItem* ptrUnattendSubTableEditItem = getItemRightRuleTableEdit(listWidgetItem);

        if (ptrUnattendSubTableEditItem == nullptr)
            continue;

        QString outSketchIdentifyUniqueKey, errMsg;
        n_mtautodelineationdialog::ST_SketchIdentify outStSketchIdentify;

        if (ptrUnattendSubTableEditItem->getNewSketchIdentify(outSketchIdentifyUniqueKey, outStSketchIdentify, errMsg) == true)
        {
            stNewUnattendedConfig.stSketchRule.sketchIdentifyMap.insert(outSketchIdentifyUniqueKey, outStSketchIdentify);
        }
        else
        {
            //MtMessageBox::NoIcon::information_Title(this->window(), tr("影像识别规则输入有误"));
            //ui.mtListWidget_right->scrollToItem(listWidgetItem);
            return;
        }
    }

    //更新内存
    if (m_stCallBackUnattended.updateUnattendedCallBack != nullptr)
    {
        QString errMsg;
        n_mtautodelineationdialog::EM_OptType optTypeEnum = (stModule.isAdd == false ? n_mtautodelineationdialog::OptType_Mod : n_mtautodelineationdialog::OptType_Add);

        if (m_stCallBackUnattended.updateUnattendedCallBack(optTypeEnum, stModule.imageSourceUniqueKey, stNewUnattendedConfig, errMsg) == false)
        {
            MtMessageBox::yellowWarning(this, tr("更新无人值守信息失败"), errMsg);
            return;
        }

        if (optTypeEnum == n_mtautodelineationdialog::OptType_Mod)
            m_ptrOptUnattendData->updateUnattendedConfig(stModule.imageSourceUniqueKey, stNewUnattendedConfig);
        else
            m_ptrOptUnattendData->addUnattendedConfig(stModule.imageSourceUniqueKey, stNewUnattendedConfig);
    }

    //退出编辑页面模式
    enterEditPage(false);
    //打开开关使能
    ui.mtSwitchButton_title->setEnabled(true);
    //更新左侧影像来源
    updateLeftImageSourceTable(stModule.imageSourceUniqueKey, false, stNewUnattendedConfig.stLocalServerRule.curServerName, stNewUnattendedConfig.stLocalServerRule.curServerType);
    //切换右侧到展示模式
    updateRightWidgetShow(stNewUnattendedConfig);
}

/// <summary>
/// 影像来源-取消编辑按钮
/// </summary>
void UnattendSubWidget::onMtPushButton_cancel_title()
{
    ST_UnattendSubWidgetModule stModule = getCurFlagModule();

    if (stModule.imageSourceUniqueKey.isEmpty() == true)
        return;

    if (stModule.isAdd == true) //新增模式下,删除无用的左侧列表记录
    {
        //删除临时记录
        delRowLeftImageSourceTable(stModule.imageSourceUniqueKey);
        //退出编辑页面模式
        enterEditPage(false, true);
        //清空左侧影像来源选中
        clearSelectRowLeftImageSourceTable();

        //切换到第一个
        if (ui.mtListWidget_left->count() > 0)
        {
            ui.mtListWidget_left->setCurrentRow(0);
        }
    }
    else //编辑模式下,恢复非编辑状态
    {
        //退出编辑页面模式
        enterEditPage(false);
        //切换到非编辑模式
        QList<n_mtautodelineationdialog::ST_UnattendedConfig> unattendedConfigVec = m_ptrOptUnattendData->getUnattendedConfig(stModule.imageSourceUniqueKey);

        if (unattendedConfigVec.isEmpty() == false)
        {
            updateRightWidgetShow(unattendedConfigVec[0]);
        }
    }

    //打开开关使能
    ui.mtSwitchButton_title->setEnabled(true);
}

/// <summary>
/// 影像接收-新增远程节点按钮
/// </summary>
void UnattendSubWidget::onMtToolButton_search_source()
{
    if (m_stCallBackUnattended.editLocalServerCallBack != nullptr)
    {
        QMap<int/*serverType*/, QStringList> newLocalServerNameMap = m_stCallBackUnattended.editLocalServerCallBack();

        if (newLocalServerNameMap.isEmpty() == true)
            return;

        //更新内存
        m_ptrOptUnattendData->replaceLocalServerNameMap(newLocalServerNameMap);
        //更新界面
        initMtComboBox_server_source(newLocalServerNameMap);
    }
}

/// <summary>
/// 关键字过滤复选框
/// </summary>
void UnattendSubWidget::onMtCheckBox_word_filterChanged(int state)
{
    ui.lineEdit_word_filter->setEnabled(ui.mtCheckBox_word_filter->isChecked());
}

/// <summary>
/// 勾画规则-新增按钮
/// </summary>
void UnattendSubWidget::onMtPushButton_add_sketch()
{
    QString newSketchIdentifyUniqueKey = CommonUtil::getCurrentDateTime();
    n_mtautodelineationdialog::ST_SketchIdentify stSketchIdentify;
    n_mtautodelineationdialog::ST_AddrSimple defaultAddr = OptUnattendDataNew::getDefaultExportAddr();

    if (defaultAddr.addrType == 1) //共享文件夹
    {
        stSketchIdentify.addrInfoMap.insert(CommonUtil::getCurrentDateTime(), defaultAddr);
    }

    if (defaultAddr.addrType == 4) //远程Scp服务器
    {
        QList<n_mtautodelineationdialog::ST_AddrSimple> remoteScpList = m_ptrOptUnattendData->getAllRemoteServerList();

        for (int i = 0; i < remoteScpList.size(); i++)
        {
            if (remoteScpList[i].stScpInfo.serverName == defaultAddr.stScpInfo.serverName &&
                remoteScpList[i].exportFormat == defaultAddr.exportFormat)
            {
                stSketchIdentify.addrInfoMap.insert(CommonUtil::getCurrentDateTime(), defaultAddr);
                break;
            }
        }
    }

    addRowRightRuleTableEdit(true, newSketchIdentifyUniqueKey, stSketchIdentify);
    ui.mtListWidget_right->scrollToBottom();
}

/// <summary>
/// 勾画规则-删除按钮
/// </summary>
void UnattendSubWidget::onMtPushButton_del_sketch()
{
    bool isExistCheckItem = false;

    for (int i = 0; i < ui.mtListWidget_right->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_right->item(i);
        UnattendSubTableEditItem* ptrEditItem = getItemRightRuleTableEdit(listWidgetItem);

        if (ptrEditItem == nullptr)
            continue;

        if (ptrEditItem->getIsCheckedItem() == true)
        {
            isExistCheckItem = true;
            break;
        }
    }

    if (isExistCheckItem == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请先选择要删除的规则"));
        return;
    }

    QString msg = QString(tr("是否确定删除勾选的规则？"));
    int ret = MtMessageBox::NoIcon::question_Title(this, msg);

    if (QMessageBox::Yes == ret)
    {
        for (int i = 0; i < ui.mtListWidget_right->count();)
        {
            QListWidgetItem* listWidgetItem = ui.mtListWidget_right->item(i);
            UnattendSubTableEditItem* ptrEditItem = getItemRightRuleTableEdit(listWidgetItem);

            if (ptrEditItem == nullptr)
                continue;

            if (ptrEditItem->getIsCheckedItem() == true)
            {
                QListWidgetItem* deleteItem = ui.mtListWidget_right->takeItem(i);
                delete deleteItem;
                continue;
            }

            i++;
        }

        //重置右侧规则信息全选打勾情况
        setCheckState_mtCheckBox_all_sketch(Qt::Unchecked);
    }
}

/// <summary>
/// 勾画规则-全选按钮
/// </summary>
void UnattendSubWidget::onMtCheckBox_all_sketch(int state)
{
    disconnect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);
    bool ischeck = (state == Qt::Unchecked ? false : true);

    if (ui.mtListWidget_right->count() <= 0)
    {
        ui.mtCheckBox_all_sketch->setCheckState(Qt::Unchecked);
        connect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);
        return;
    }

    ui.mtCheckBox_all_sketch->setCheckState(ischeck == true ? Qt::Checked : Qt::Unchecked);

    for (int i = 0; i < ui.mtListWidget_right->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_right->item(i);
        UnattendSubTableEditItem* ptrEditItem = getItemRightRuleTableEdit(listWidgetItem);

        if (ptrEditItem == nullptr)
            continue;

        ptrEditItem->setIsCheckedItem(ischeck);
    }

    connect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);
}

/// <summary>
/// 左侧影像来源Item选中变化
/// </summary>
void UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged(QListWidgetItem* current, QListWidgetItem* previous)
{
    disconnect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);

    if (ui.mtStackedWidget_title->currentWidget() == ui.page_show_title)//非编辑模式
    {
        if (current != nullptr)
        {
            QWidget* customWidget = ui.mtListWidget_left->itemWidget(current);

            if (customWidget != nullptr)
            {
                ImageSourceWidget* ptrImageWidget = (ImageSourceWidget*)customWidget;
                QString imageSourceUniqueKey = ptrImageWidget->getImageSourceUniqueKey();
                QList<n_mtautodelineationdialog::ST_UnattendedConfig> unattendedConfigVec = m_ptrOptUnattendData->getUnattendedConfig(imageSourceUniqueKey);

                if (unattendedConfigVec.isEmpty() == false)
                {
                    //更新右侧界面
                    ui.widget_right->setHidden(false);
                    updateRightWidgetShow(unattendedConfigVec[0]);
                    //存储标识数据
                    ST_UnattendSubWidgetModule stModule;
                    stModule.setValue(false, imageSourceUniqueKey);
                    setCurFlagModule(stModule);
                }
            }
        }

        //影像来源删除按钮使能(启动状态下不允许删除和拷贝)
        ui.mtToolButton_copy->setEnabled(!ui.mtSwitchButton_title->isChecked());
        ui.mtToolButton_del->setEnabled(!ui.mtSwitchButton_title->isChecked());
    }

    connect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
}

/// <summary>
/// 获取当前标识数据,存储于ui.mtLabel_title中
/// </summary>
ST_UnattendSubWidgetModule UnattendSubWidget::getCurFlagModule()
{
    return ui.mtLabel_title->modelData().value<ST_UnattendSubWidgetModule>();
}

/// <summary>
/// 设置当前标识数据,存储于ui.mtLabel_title中
/// </summary>
void UnattendSubWidget::setCurFlagModule(const ST_UnattendSubWidgetModule stModule)
{
    ui.mtLabel_title->setModelData(QVariant::fromValue(stModule));
}

/// <summary>
/// 是否可以新增一条规则(可用模板)
/// </summary>
/// <returns>true可以</returns>
bool UnattendSubWidget::isCanAddOneRule()
{
    //判断是否有模板资源
    if (m_ptrOptUnattendData->getAllTempateNameMap().isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this, tr("未检索到无人值守模板，请新增无人值守模板后继续操作"));
        return false;
    }

    return true;
}

/// <summary>
/// 初始化影像来源下拉框
/// </summary>
/// <param name="allLocalServerNameMap">所有本地服务器名集合(包括空闲和不空闲)</param>
void UnattendSubWidget::initMtComboBox_server_source(const QMap<int, QStringList>& allLocalServerNameMap)
{
    auto addComboBox = [&](const int& serverType, const QStringList& serverList)->void
    {
        if (allLocalServerNameMap.contains(serverType) == false)
            return;

        for (int i = 0; i < serverList.size(); i++)
        {
            QMap<int/*本地服务器类型*/, QString/*服务器名*/> userData = { {serverType, serverList[i]} }; //自定义数据
            ui.mtComboBox_server_source->addItem(CommonUtil::getTextFromLocalAddrType(serverType) + "-" + serverList[i], QVariant::fromValue(userData));
        }
    };
    //
    ui.mtComboBox_server_source->clear();
    addComboBox(4, allLocalServerNameMap[4]);
    addComboBox(1, allLocalServerNameMap[1]);
    addComboBox(2, allLocalServerNameMap[2]);
}

/// <summary>
/// 获取UnattendSubTableEditItem指针
/// 为nullptr时代表没找到
/// </summary>
ImageSourceWidget* UnattendSubWidget::getItemLeftImageSourceTable(QListWidgetItem* listWidgetItem)
{
    if (listWidgetItem == nullptr)
        return nullptr;

    QWidget* customWidget = ui.mtListWidget_left->itemWidget(listWidgetItem);

    if (customWidget == nullptr)
        return nullptr;

    ImageSourceWidget* ptrSourceItem = (ImageSourceWidget*)customWidget;
    return ptrSourceItem;
}

/// <summary>
/// 添加左侧影像来源
/// </summary>
/// <param name="uniqueKey">[IN]影像来源唯一标识</param>
/// <param name="enable">[IN]true开启</param>
/// <param name="serverName">[IN]本地服务器名</param>
/// <param name="serverType">[IN]本地服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log 4:SCP服务器)</param>
/// <returns>插入的行号</returns>
int UnattendSubWidget::addRowLeftImageSourceTable(const QString& imageSourceUniqueKey, const bool enable, const QString& serverName, const int serverType)
{
    ImageSourceWidget* widget = new ImageSourceWidget(imageSourceUniqueKey);
    widget->updateUI(enable == true ? m_imagePathHash["icon_circleOpen"] : m_imagePathHash["icon_circleClose"], serverName, CommonUtil::getTextFromLocalAddrType(serverType));
    //
    QListWidgetItem* listItem = new QListWidgetItem(ui.mtListWidget_left);
    listItem->setSizeHint(QSize(ui.mtListWidget_left->width(), 70));
    ui.mtListWidget_left->addItem(listItem);
    ui.mtListWidget_left->setItemWidget(listItem, widget);
    return ui.mtListWidget_left->row(listItem);
}

/// <summary>
/// 删除左侧影像来源
/// </summary>
/// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
void UnattendSubWidget::delRowLeftImageSourceTable(const QString& imageSourceUniqueKey)
{
    for (int i = 0; i < ui.mtListWidget_left->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_left->item(i);
        ImageSourceWidget* ptrSourceItem = getItemLeftImageSourceTable(listWidgetItem);

        if (ptrSourceItem == nullptr)
            continue;

        if (ptrSourceItem->getImageSourceUniqueKey() == imageSourceUniqueKey)
        {
            //删除控件
            QListWidgetItem* removeItem = ui.mtListWidget_left->takeItem(i);

            if (removeItem != nullptr)
                delete removeItem;

            return;
        }
    }
}

/// <summary>
/// 选中左侧影像来源
/// </summary>
/// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
void UnattendSubWidget::selectRowLeftImageSourceTable(const QString& imageSourceUniqueKey)
{
    for (int i = 0; i < ui.mtListWidget_left->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_left->item(i);
        ImageSourceWidget* ptrSourceItem = getItemLeftImageSourceTable(listWidgetItem);

        if (ptrSourceItem == nullptr)
            continue;

        if (ptrSourceItem->getImageSourceUniqueKey() == imageSourceUniqueKey)
        {
            ui.mtListWidget_left->setCurrentRow(i);
            return;
        }
    }
}

/// <summary>
/// 清空左侧影像来源选中状态
/// </summary>
void UnattendSubWidget::clearSelectRowLeftImageSourceTable()
{
    disconnect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
    ui.mtListWidget_left->clearSelection();
    ui.mtListWidget_left->setCurrentRow(-1);
    connect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged);
}

/// <summary>
/// 更新左侧影像来源
/// </summary>
/// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
/// <param name="enable">[IN]true开启</param>
/// <param name="serverName">[IN]本地服务器名</param>
/// <param name="serverType">[IN]本地服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log 4:SCP服务器)</param>
void UnattendSubWidget::updateLeftImageSourceTable(const QString& imageSourceUniqueKey, const bool enable, const QString& serverName, const int serverType)
{
    for (int i = 0; i < ui.mtListWidget_left->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_left->item(i);
        ImageSourceWidget* ptrSourceItem = getItemLeftImageSourceTable(listWidgetItem);

        if (ptrSourceItem == nullptr)
            continue;

        if (ptrSourceItem->getImageSourceUniqueKey() == imageSourceUniqueKey)
        {
            ptrSourceItem->updateUI(enable == true ? m_imagePathHash["icon_circleOpen"] : m_imagePathHash["icon_circleClose"],
                                    serverName,
                                    serverType < 0 ? QString() : CommonUtil::getTextFromLocalAddrType(serverType));
            return;
        }
    }
}

/// <summary>
/// 添加右侧规则信息(展示模式)
/// </summary>
/// <param name="sketchIdentifyUniqueKey">[IN]勾画规则唯一标识</param>
/// <param name="stSketchIdentify">[IN]勾画规则信息</param>
void UnattendSubWidget::addRowRightRuleTableShow(const QString& sketchIdentifyUniqueKey, const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify)
{
    UnattendSubTableShowItem* widget = new UnattendSubTableShowItem;
    int itemHight = widget->init(sketchIdentifyUniqueKey, m_ptrOptUnattendData->getAllTempateNameMap(), stSketchIdentify);

    if (itemHight > 0)
    {
        QListWidgetItem* listItem = new QListWidgetItem(ui.mtListWidget_right);
        listItem->setSizeHint(QSize(Def_RightTableItemWidth, itemHight));
        ui.mtListWidget_right->addItem(listItem);
        ui.mtListWidget_right->setItemWidget(listItem, widget);
    }
}

/// <summary>
/// 刷新右侧界面(展示模式)
/// </summary>
/// <param name="stUnattendedConfig">无人值守信息</param>
void UnattendSubWidget::updateRightWidgetShow(const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig)
{
    //清空及切换界面
    ui.mtStackedWidget_title->setCurrentWidget(ui.page_show_title);
    ui.mtStackedWidget_source->setCurrentWidget(ui.page_show_source);
    ui.mtStackedWidget_filter->setCurrentWidget(ui.page_show_filter);
    ui.widget_sketch->setHidden(true);
    ui.mtCheckBox_all_sketch->setHidden(true);
    ui.mtFrameEx_10->setHidden(true);
    ui.mtListWidget_right->clear();
    //影像接收
    n_mtautodelineationdialog::ST_LocalServerRule stLocalServerRule = stUnattendedConfig.stLocalServerRule;
    QString imageSourceStr = CommonUtil::getTextFromLocalAddrType(stLocalServerRule.curServerType) + "-" + stLocalServerRule.curServerName;
    ui.mtLabel_title->setText(imageSourceStr);
    ui.mtSwitchButton_title->setChecked(stUnattendedConfig.isEnable);
    ui.mtPushButton_edit_title->setEnabled(!stUnattendedConfig.isEnable);
    ui.mtLabel_server_source->setText(imageSourceStr);
    //影像过滤
    int num = 1;
    ui.mtLabel_imageNum_filter->clear();
    ui.mtLabel_word_filter->clear();
    //ui.mtLabel_imageNum_filter->setHidden(!stLocalServerRule.numfilterEnable); //zlw 暂时屏蔽
    ui.mtLabel_word_filter->setHidden(!stLocalServerRule.imagefilterEnable);

    if (stLocalServerRule.numfilterEnable == true)
    {
        ui.mtLabel_imageNum_filter->setText(QString("(%1) ").arg(num) + (tr("影像层数少于：%1 层")).arg(stLocalServerRule.numNotSketch));
        num++;
    }

    if (stLocalServerRule.imagefilterEnable == true && stLocalServerRule.imagefilterMap.isEmpty() == false)
    {
        ui.mtLabel_word_filter->setText(QString(tr("DICOM文件的序列描述(0008,103E)字段含有：%1")).arg(CommonUtil::stringListToStr(stLocalServerRule.imagefilterMap.begin().value())));
    }

    if (stLocalServerRule.numfilterEnable == false)
    {
        //ui.mtLabel_imageNum_filter->setHidden(false); //zlw 暂时屏蔽
        //ui.mtLabel_imageNum_filter->setText(tr("无"));
    }

    if (stLocalServerRule.imagefilterEnable == false)
    {
        ui.mtLabel_word_filter->setHidden(false);
        ui.mtLabel_word_filter->setText(tr("无"));
    }

    //勾画规则
    n_mtautodelineationdialog::ST_SketchRule stSketchRule = stUnattendedConfig.stSketchRule;

    for (QMap<QString, n_mtautodelineationdialog::ST_SketchIdentify>::const_iterator it = stSketchRule.sketchIdentifyMap.begin(); it != stSketchRule.sketchIdentifyMap.end(); it++)
    {
        addRowRightRuleTableShow(it.key(), it.value());
    }
}

/// <summary>
/// 获取UnattendSubTableEditItem指针(编辑模式)
/// 为nullptr时代表没找到
/// </summary>
UnattendSubTableEditItem* UnattendSubWidget::getItemRightRuleTableEdit(QListWidgetItem* listWidgetItem)
{
    if (listWidgetItem == nullptr)
        return nullptr;

    QWidget* customWidget = ui.mtListWidget_right->itemWidget(listWidgetItem);

    if (customWidget == nullptr)
        return nullptr;

    UnattendSubTableEditItem* ptrUnattendSubTableEditItem = (UnattendSubTableEditItem*)customWidget;
    return ptrUnattendSubTableEditItem;
}

/// <summary>
/// 添加右侧规则信息(编辑模式)
/// </summary>
/// <param name="isAdd">[IN]true新增</param>
/// <param name="sketchIdentifyUniqueKey">[IN]当前勾画规则唯一标识</param>
/// <param name="stSketchIdentify">[IN]当前勾画规则信息</param>
void UnattendSubWidget::addRowRightRuleTableEdit(const bool isAdd, const QString& sketchIdentifyUniqueKey, const n_mtautodelineationdialog::ST_SketchIdentify& curStSketchIdentify)
{
    QListWidgetItem* listItem = new QListWidgetItem(ui.mtListWidget_right);
    UnattendSubTableEditItem* widget = new UnattendSubTableEditItem(ui.mtListWidget_right, listItem);
    widget->setImagePathHash(m_imagePathHash);
    int itemHight = widget->init(isAdd, sketchIdentifyUniqueKey, curStSketchIdentify, m_ptrOptUnattendData->getAllRemoteServerList(), m_ptrOptUnattendData->getAllTempateNameMap());
    connect(widget, &UnattendSubTableEditItem::sigChangeHeight, this, [this](const QString sketchIdentifyUniqueKey, const int heightNum) //增加高度
    {
        for (int i = 0; i < ui.mtListWidget_right->count(); i++)
        {
            QListWidgetItem* listWidgetItem = ui.mtListWidget_right->item(i);
            UnattendSubTableEditItem* ptrUnattendSubTableEditItem = getItemRightRuleTableEdit(listWidgetItem);

            if (ptrUnattendSubTableEditItem == nullptr)
                continue;

            if (ptrUnattendSubTableEditItem->getSketchIdentifyUniqueKey() != sketchIdentifyUniqueKey)
                continue;

            QRect itemRect = ui.mtListWidget_right->visualItemRect(listWidgetItem);
            int itemHeight = itemRect.height() + heightNum;
            int minHeight = ptrUnattendSubTableEditItem->getItemMinHeight();
            listWidgetItem->setSizeHint(QSize(Def_RightTableItemWidth, (minHeight > itemHeight ? minHeight : itemHeight)));
            return;
        }
    });
    connect(widget, &UnattendSubTableEditItem::sigEditItemChecked, this, [this](const bool ischeck) //Item打勾
    {
        Qt::CheckState curState = getCheckStateRightRuleTableEdit();
        setCheckState_mtCheckBox_all_sketch(curState);
    });

    if (itemHight > 0)
    {
        listItem->setSizeHint(QSize(Def_RightTableItemWidth, itemHight));
        ui.mtListWidget_right->addItem(listItem);
        ui.mtListWidget_right->setItemWidget(listItem, widget);
    }
}

/// <summary>
/// 刷新右侧界面(编辑模式)
/// </summary>
/// <param name="isAdd">[IN]true新增</param>
/// <param name="stUnattendedConfig">无人值守信息</param>
void UnattendSubWidget::updateRightWidgetEdit(const bool isAdd, const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig)
{
    //清空及切换界面
    ui.mtStackedWidget_title->setCurrentWidget(ui.page_edit_title);
    ui.mtStackedWidget_source->setCurrentWidget(ui.page_edit_source);
    ui.mtStackedWidget_filter->setCurrentWidget(ui.page_edit_filter);
    ui.widget_sketch->setHidden(false);
    ui.mtCheckBox_all_sketch->setHidden(false);
    ui.mtFrameEx_10->setHidden(false);
    ui.mtListWidget_right->clear();
    ui.lineEdit_word_filter->clear();
    //影像接收&影像过滤
    initMtComboBox_server_source(m_ptrOptUnattendData->getAllLocalServerMap());
    n_mtautodelineationdialog::ST_LocalServerRule stLocalServerRule = stUnattendedConfig.stLocalServerRule;

    if (isAdd == true && stUnattendedConfig.stLocalServerRule.curServerName.isEmpty() == true)
    {
        ui.mtLabel_title->setText(Def_TempRuleName);
        ui.mtCheckBox_imageNum_filter->setChecked(true);
        ui.mtCheckBox_word_filter->setChecked(false);
        ui.lineEdit_word_filter->setEnabled(false);
        ui.lineEdit_imageNum_filter->setText("11");
    }
    else
    {
        QString imageSourceStr = CommonUtil::getTextFromLocalAddrType(stLocalServerRule.curServerType) + "-" + stLocalServerRule.curServerName;
        ui.mtLabel_title->setText(imageSourceStr);
        ui.mtComboBox_server_source->setCurrentText(imageSourceStr);
        ui.mtCheckBox_imageNum_filter->setChecked(stLocalServerRule.numfilterEnable);
        ui.lineEdit_imageNum_filter->setText(stLocalServerRule.numNotSketch <= 0 ? "11" : QString::number(stLocalServerRule.numNotSketch));
        ui.mtCheckBox_word_filter->setChecked(stLocalServerRule.imagefilterEnable);
        ui.lineEdit_word_filter->setEnabled(stLocalServerRule.imagefilterEnable);
        ui.lineEdit_word_filter->setText(stLocalServerRule.imagefilterMap.isEmpty() == true ? "" : CommonUtil::stringListToStr(stLocalServerRule.imagefilterMap.begin().value()));
    }

    //勾画规则
    ui.mtListWidget_right->clear();
    n_mtautodelineationdialog::ST_SketchRule stSketchRule = stUnattendedConfig.stSketchRule;

    for (QMap<QString, n_mtautodelineationdialog::ST_SketchIdentify>::const_iterator it = stSketchRule.sketchIdentifyMap.begin(); it != stSketchRule.sketchIdentifyMap.end(); it++)
    {
        addRowRightRuleTableEdit(false, it.key(), it.value());
    }
}

/// <summary>
/// 进入编辑页面模式
/// </summary>
/// <param name="isEnter">true进入</param>
/// <param name="isAdd">true新增模式</param>
void UnattendSubWidget::enterEditPage(const bool isEnter, bool isAdd)
{
    ui.mtListWidget_left->setEnabled(!isEnter);
    ui.mtToolButton_add->setEnabled(!isEnter);
    ui.mtToolButton_copy->setEnabled(!isEnter);
    ui.mtToolButton_del->setEnabled(!isEnter);

    if (isEnter == true)
    {
        ui.mtStackedWidget_title->setCurrentWidget(ui.page_edit_title);
        ui.mtStackedWidget_source->setCurrentWidget(ui.page_edit_source);
        ui.mtStackedWidget_filter->setCurrentWidget(ui.page_edit_filter);
        ui.widget_sketch->setHidden(false);
        ui.mtCheckBox_all_sketch->setHidden(false);
        ui.mtFrameEx_10->setHidden(false);
        ui.mtListWidget_right->clear();
    }
    else
    {
        ui.mtStackedWidget_title->setCurrentWidget(ui.page_show_title);
        ui.mtStackedWidget_source->setCurrentWidget(ui.page_show_source);
        ui.mtStackedWidget_filter->setCurrentWidget(ui.page_show_filter);
        ui.widget_sketch->setHidden(true);
        ui.mtCheckBox_all_sketch->setHidden(true);
        ui.mtFrameEx_10->setHidden(true);
        ui.mtListWidget_right->clear();
    }

    if (isAdd == true)
        ui.widget_right->setHidden(!isEnter);
}

/// <summary>
/// 设置右侧规则信息全选打勾情况
/// </summary>
void UnattendSubWidget::setCheckState_mtCheckBox_all_sketch(Qt::CheckState state)
{
    disconnect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);
    ui.mtCheckBox_all_sketch->setCheckState(state);
    connect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);
}

/// <summary>
/// 右侧规则信息打勾情况
/// </summary>
Qt::CheckState UnattendSubWidget::getCheckStateRightRuleTableEdit()
{
    int checkNum = 0;

    for (int i = 0; i < ui.mtListWidget_right->count(); i++)
    {
        QListWidgetItem* listWidgetItem = ui.mtListWidget_right->item(i);
        UnattendSubTableEditItem* ptrEditItem = getItemRightRuleTableEdit(listWidgetItem);

        if (ptrEditItem->getIsCheckedItem() == true)
        {
            checkNum++;
        }
    }

    if (checkNum == ui.mtListWidget_right->count())
    {
        return Qt::Checked;
    }
    else if (checkNum < ui.mtListWidget_right->count())
    {
        return Qt::PartiallyChecked;
    }

    return Qt::Unchecked;
}

/// <summary>
/// 创建默认的18条勾画规则
/// </summary>
/// <returns>默认的18条勾画规则</returns>
n_mtautodelineationdialog::ST_SketchRule UnattendSubWidget::createDefaultAISketchRule()
{
    auto createSketchIdentify = [](const QString& aiBodypart, const int sketchCollectionId)
    {
        n_mtautodelineationdialog::ST_SketchIdentify stSketchIdentify;
        stSketchIdentify.recognitionType = 1;
        stSketchIdentify.sketchCollectionId = sketchCollectionId;
        stSketchIdentify.modality = "CT";
        stSketchIdentify.aiBodypart = aiBodypart;
        return stSketchIdentify;
    };
    //
    //获取所有模板
    QString language = (Language::type == Chinese ? "ch" : "en");
    QMap<QString/*templateName*/, int/*templateId*/> tempateNameIdRelationMap;
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>> templateNameMap = m_ptrOptUnattendData->getAllTempateNameMap();

    if (templateNameMap.contains(n_mtautodelineationdialog::OptDcmType_CT) == true)
    {
        QMap<int, QString> tempMap = templateNameMap[n_mtautodelineationdialog::OptDcmType_CT];

        for (QMap<int, QString>::iterator it = tempMap.begin(); it != tempMap.end(); it++)
        {
            tempateNameIdRelationMap.insert(it.value(), it.key());
        }
    }

    //组装勾画规则
    n_mtautodelineationdialog::ST_SketchRule stSketchRule;
    QStringList aibodyPartList = CommonUtil::getCodeFromBodyPartAI();

    for (int i = 0; i < aibodyPartList.size(); i++)
    {
        QString innerTemplateName = CommonUtil::getUnattendModelNameOfInerDefault(language, aibodyPartList[i]);

        if (innerTemplateName.isEmpty() == true)
        {
            stSketchRule.sketchIdentifyMap.insert(CommonUtil::getCurrentDateTime(), createSketchIdentify(aibodyPartList[i], -1));
        }
        else
        {
            int templateId = -1;

            if (tempateNameIdRelationMap.empty() == false)
                templateId = tempateNameIdRelationMap.begin().value();

            if (tempateNameIdRelationMap.contains(innerTemplateName) == true)
                templateId = tempateNameIdRelationMap[innerTemplateName];

            stSketchRule.sketchIdentifyMap.insert(CommonUtil::getCurrentDateTime(), createSketchIdentify(aibodyPartList[i], templateId));
        }
    }

    return stSketchRule;
}

/// <summary>
/// 信号槽
/// </summary>
void UnattendSubWidget::connnectSignal(const bool isConnect)
{
    if (isConnect == true)
    {
        connect(ui.mtToolButton_add, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_add);                          //影像来源-新增按钮
        connect(ui.mtToolButton_copy, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_copy);                        //影像来源-拷贝按钮
        connect(ui.mtToolButton_del, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_del);                          //影像来源-删除按钮
        connect(ui.mtSwitchButton_title, &MtSwitchButton::clicked, this, &UnattendSubWidget::onMtSwitchButton_title);               //影像来源-开关按钮
        connect(ui.mtPushButton_edit_title, &QToolButton::clicked, this, &UnattendSubWidget::onMtPushButton_edit_title);            //影像来源-编辑规则按钮
        connect(ui.mtPushButton_save_title, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_save_title);            //影像来源-保存规则按钮
        connect(ui.mtPushButton_cancel_title, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_cancel_title);        //影像来源-取消规则按钮
        connect(ui.mtToolButton_search_source, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_search_source);      //影像接收-新增本地节点按钮
        connect(ui.mtCheckBox_word_filter, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_word_filterChanged);    //关键字过滤复选框
        connect(ui.mtPushButton_add_sketch, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_add_sketch);            //勾画规则-新增按钮
        connect(ui.mtPushButton_del_sketch, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_del_sketch);            //勾画规则-删除按钮
        connect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);             //勾画规则-全选按钮
        connect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged); //左侧影像来源Item选中变化
    }
    else
    {
        disconnect(ui.mtToolButton_add, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_add);                       //影像来源-新增按钮
        disconnect(ui.mtToolButton_copy, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_copy);                     //影像来源-拷贝按钮
        disconnect(ui.mtToolButton_del, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_del);                       //影像来源-删除按钮
        disconnect(ui.mtSwitchButton_title, &MtSwitchButton::clicked, this, &UnattendSubWidget::onMtSwitchButton_title);            //影像来源-开关按钮
        disconnect(ui.mtPushButton_edit_title, &QToolButton::clicked, this, &UnattendSubWidget::onMtPushButton_edit_title);         //影像来源-编辑规则按钮
        disconnect(ui.mtPushButton_save_title, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_save_title);         //影像来源-保存规则按钮
        disconnect(ui.mtPushButton_cancel_title, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_cancel_title);     //影像来源-取消规则按钮
        disconnect(ui.mtToolButton_search_source, &QToolButton::clicked, this, &UnattendSubWidget::onMtToolButton_search_source);   //影像接收-新增本地节点按钮
        disconnect(ui.mtCheckBox_word_filter, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_word_filterChanged); //关键字过滤复选框
        disconnect(ui.mtPushButton_add_sketch, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_add_sketch);         //勾画规则-新增按钮
        disconnect(ui.mtPushButton_del_sketch, &QPushButton::clicked, this, &UnattendSubWidget::onMtPushButton_del_sketch);         //勾画规则-删除按钮
        disconnect(ui.mtCheckBox_all_sketch, &QCheckBox::stateChanged, this, &UnattendSubWidget::onMtCheckBox_all_sketch);          //勾画规则-全选按钮
        disconnect(ui.mtListWidget_left, &QListWidget::currentItemChanged, this, &UnattendSubWidget::slotmtListWidgetLeft_currentItemChanged); //左侧影像来源Item选中变化
    }
}
