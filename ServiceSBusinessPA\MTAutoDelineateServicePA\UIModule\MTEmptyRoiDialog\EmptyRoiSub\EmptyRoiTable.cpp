﻿#include "EmptyRoiTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLineEdit.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtComboBox.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTAbsHorizontalBtns.h"
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"
#include <QColorDialog>
#include <thread>

/// <summary>
/// 构造函数
/// </summary>
EmptyRoiTable::EmptyRoiTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    SetEnableDynamicCreateUi(false);        //都不需要动态创建
    connect(this, &EmptyRoiTable::sigCellWidgetButtonClicked, this, &EmptyRoiTable::slotCellWidgetButtonClicked); //某个按键点击了
    connect(this, &EmptyRoiTable::sigCellWidgetTextChange, this, &EmptyRoiTable::slotCellWidgetTextChange); //表格中的编辑框文本发生了改变
    connect(this, &EmptyRoiTable::sigCreateTableRowItem, this, &EmptyRoiTable::slotCreateTableRowItem, Qt::BlockingQueuedConnection);
}

EmptyRoiTable::~EmptyRoiTable()
{
    m_bDestroyed = true;
}

void EmptyRoiTable::init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
                         const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
                         const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    m_allRoiTypeList = allRoiTypeList;
    m_allLabelList = allLabelList;
    m_modelCollectionInfoList = modelCollectionInfoList;
    initTableView(
        { tr("ROI名称"), tr("标签"), tr("颜色"), tr("ROI类型"), tr("ROI描述"), tr("关联模板") },
        { 160, 165, 75, 165, 184, 99 });
    //使用线程发消息方式创建，不然创建列表要很久
    m_bDestroyed = false;
    std::thread t([&](QList<n_mtautodelineationdialog::ST_Organ> stOrganList)
    {
        //初始化列表数据
        for (int i = 0; i < stOrganList.size() && !m_bDestroyed; i++)
        {
            emit sigCreateTableRowItem(stOrganList[i]);
        }
    }, stOrganList);
    t.detach();
}

/// <summary>
/// 获取所有空勾画信息
/// </summary>
/// <returns>所有空勾画信息</returns>
QList<n_mtautodelineationdialog::ST_Organ> EmptyRoiTable::getAllEmptyOrganInfo()
{
    QList<n_mtautodelineationdialog::ST_Organ> organList;
    organList.reserve(m_allOrganMap.size() + 1);

    for (QMap<int, n_mtautodelineationdialog::ST_Organ>::iterator it = m_allOrganMap.begin(); it != m_allOrganMap.end(); it++)
    {
        organList.push_back(it.value());
    }

    return organList;
}

void EmptyRoiTable::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
    QMTAbstractTableView::CreateCellWidgtFinishCallBack(rowValue, column, cellType, cellWidget);
}

/// <summary>
/// 初始化表格
/// </summary>
/// <param name="headList">[IN]表头文本集合</param>
/// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
void EmptyRoiTable::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="stEmptyOrgan">[IN]空勾画信息</param>
void EmptyRoiTable::addRow(const n_mtautodelineationdialog::ST_Organ stEmptyOrgan)
{
    m_allOrganMap.insert(stEmptyOrgan.id, stEmptyOrgan);
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //ROI名称
    QCustMtLineEditParam* lineEditParam = new QCustMtLineEditParam();
    lineEditParam->_maxLength = 64;
    lineEditParam->_text = stEmptyOrgan.customOrganName;
    lineEditParam->_regExpStr = RegExp_CharNumber4;
    lineEditParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Name, lineEditParam);
    //标签
    QCustMtComboBoxParam* comboBoxLabelParam = new QCustMtComboBoxParam();
    comboBoxLabelParam->_textList = m_allLabelList;
    int index = m_allLabelList.indexOf(stEmptyOrgan.roiLabel);
    comboBoxLabelParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxLabelParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Label, comboBoxLabelParam);
    //Roi颜色
    QMTAbsHorizontalBtnsParam* btnsParam = new QMTAbsHorizontalBtnsParam();
    btnsParam->_pixPathList.clear();
    btnsParam->_width = 58;
    btnsParam->_height = 16;
    btnsParam->_btnBackgroundColor = QColor(QString("#") + stEmptyOrgan.customColor);
    cellWidgetParamMap.insert(ColType_Color, btnsParam);
    //Roi类型
    QCustMtComboBoxParam* comboBoxTypeParam = new QCustMtComboBoxParam();
    comboBoxTypeParam->_textList = m_allRoiTypeList;
    index = m_allRoiTypeList.indexOf(stEmptyOrgan.roiType);
    comboBoxTypeParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxTypeParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Type, comboBoxTypeParam);
    //ROI描述
    QCustMtLineEditParam* lineEditDescParam = new QCustMtLineEditParam();
    lineEditDescParam->_maxLength = 32;
    lineEditDescParam->_text = stEmptyOrgan.roiDesc.isEmpty() ? "-" : stEmptyOrgan.roiDesc;
    //lineEditDescParam->_regExpStr = RegExp_CharNumber2;
    lineEditDescParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Desc, lineEditDescParam);
    //是否关联模板
    QCustMtLabelParam* relatedParam = new QCustMtLabelParam();
    relatedParam->_text = tr("否");

    for (const n_mtautodelineationdialog::ST_SketchModelCollection& modelItemInfo : m_modelCollectionInfoList)
    {
        if (modelItemInfo.showGroupIdMap.contains(stEmptyOrgan.id))
        {
            relatedParam->_text = tr("是");
            break;
        }
    }

    cellWidgetParamMap.insert(ColType_TemplateRelated, relatedParam);
    this->AddRowItem(QString::number(stEmptyOrgan.id), cellWidgetParamMap);
}

bool EmptyRoiTable::delCurrentRow(QString& organId, QString& organName)
{
    QString id = this->GetCurUniqueValue();

    if (id.isEmpty())
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要删除的ROI"));
        return false;
    }

    QString msg = QString(tr("您确认要删除该ROI吗？"));
    int ret = MtMessageBox::NoIcon::question_Title(this, msg);

    if (QMessageBox::Yes == ret)
    {
        QString stateStr = this->GetColumnText(id, ColType_TemplateRelated);

        if (stateStr == tr("是"))
        {
            if (QMessageBox::Yes != MtMessageBox::NoIcon::question_Title(this->window(), QString(tr("该ROI正在被使用，确定要删除吗？")), QString()))
            {
                return false;
            }
        }

        organName = this->GetColumnText(id, ColType_Name);

        if (m_allOrganMap.contains(id.toInt()))
        {
            m_allOrganMap.remove(id.toInt());
        }

        this->DeleteCurRow();
    }

    organId = id;
    return true;
}

/// <summary>
/// 某个按键点击了
/// </summary>
void EmptyRoiTable::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    int organId = cellItemIndex._uniqueValue.toInt();

    if (cellItemIndex._column == ColType_Color) //roi颜色按钮
    {
        QColor color = Qt::red;

        if (m_allOrganMap.contains(organId))
            color = QString("#") + m_allOrganMap[organId].customColor;

        QColor newColor = QColorDialog::getColor(color/*, this*/);//输入框样式会被父窗口影像，不设置父窗口

        if (newColor.isValid() == false || color == newColor)
            return;

        this->UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Color, QVariant::fromValue(newColor));

        if (m_allOrganMap.contains(organId))
        {
            m_allOrganMap[organId].customColor = newColor.name().remove("#");
            emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_Color, m_allOrganMap[organId].customColor);
        }
    }
}

void EmptyRoiTable::slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText)
{
    int organID = cellItemIndex._uniqueValue.toInt();

    if (m_allOrganMap.contains(organID))
    {
        switch (cellItemIndex._column)
        {
            case ColType_Name://ROI名称
                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(m_allOrganMap[organID].customOrganName));
                    MtMessageBox::warning(this, tr("ROI名称不能为空"));
                    return;
                }

                m_allOrganMap[organID].customOrganName = newText;
                break;

            case ColType_Label://标签
                m_allOrganMap[organID].roiLabel = newText == "NONE" ? "" : newText;
                break;

            case ColType_Type://Roi类型
                m_allOrganMap[organID].roiType = newText == "NONE" ? "" : newText;
                break;

            case ColType_Desc://ROI描述
                m_allOrganMap[organID].roiDesc = newText == "-" ? "" : newText;

                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(QString("-")));
                }

                break;

            default:
                break;
        }
    }

    emit sigTableInfoChanged(m_allOrganMap[organID].defaultOrganName, cellItemIndex._column, newText);
}

void EmptyRoiTable::slotCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo)
{
    addRow(organInfo);
}