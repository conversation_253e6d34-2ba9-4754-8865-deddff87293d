<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UnattendSubTableEditItemClass</class>
 <widget class="QWidget" name="UnattendSubTableEditItemClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>972</width>
    <height>138</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>UnattendSubTableEditItem</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_5">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="leftMargin">
      <number>7</number>
     </property>
     <property name="rightMargin">
      <number>1</number>
     </property>
     <item>
      <widget class="MtCheckBox" name="mtCheckBox">
       <property name="text">
        <string/>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtCheckBox::checkbox1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_4">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="leftMargin">
      <number>8</number>
     </property>
     <property name="rightMargin">
      <number>8</number>
     </property>
     <item>
      <widget class="MtComboBox" name="mtComboBox_modality">
       <property name="minimumSize">
        <size>
         <width>64</width>
         <height>26</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>64</width>
         <height>26</height>
        </size>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="viewTextElideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtComboBox::combobox1</enum>
       </property>
       <item>
        <property name="text">
         <string>CT</string>
        </property>
       </item>
       <item>
        <property name="text">
         <string>MR</string>
        </property>
       </item>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_3">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <property name="minimumSize">
      <size>
       <width>443</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>443</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_6">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QFormLayout" name="formLayout">
        <property name="formAlignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
        <property name="horizontalSpacing">
         <number>8</number>
        </property>
        <property name="verticalSpacing">
         <number>5</number>
        </property>
        <property name="leftMargin">
         <number>8</number>
        </property>
        <property name="rightMargin">
         <number>8</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="MtComboBox" name="mtComboBox_keyType">
          <property name="minimumSize">
           <size>
            <width>136</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>136</width>
            <height>26</height>
           </size>
          </property>
          <property name="elideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="viewTextElideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtComboBox::combobox1</enum>
          </property>
          <item>
           <property name="text">
            <string>AI部位识别</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>DICOM字段识别</string>
           </property>
          </item>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="MtComboBox" name="mtComboBox_keyValue">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>26</height>
           </size>
          </property>
          <property name="elideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="viewTextElideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtComboBox::combobox1</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QWidget" name="widget_dicom" native="true">
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtComboBox" name="mtComboBox_sex">
             <property name="minimumSize">
              <size>
               <width>86</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>26</height>
              </size>
             </property>
             <property name="elideMode">
              <enum>Qt::ElideRight</enum>
             </property>
             <property name="viewTextElideMode">
              <enum>Qt::ElideRight</enum>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtComboBox::combobox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtRangeLineEdit" name="mtLineEdit_dicomValue">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>26</height>
              </size>
             </property>
             <property name="maxLength">
              <number>128</number>
             </property>
             <property name="placeholderText">
              <string>用/隔开多个识别词</string>
             </property>
             <property name="toolTipText" stdset="0">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_2">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="leftMargin">
      <number>8</number>
     </property>
     <property name="rightMargin">
      <number>8</number>
     </property>
     <item>
      <widget class="MtComboBox" name="mtComboBox_template">
       <property name="minimumSize">
        <size>
         <width>188</width>
         <height>26</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>188</width>
         <height>26</height>
        </size>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="viewTextElideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtComboBox::combobox1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <property name="minimumSize">
      <size>
       <width>1</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>1</width>
       <height>16777215</height>
      </size>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx3</enum>
     </property>
    </widget>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_4">
     <property name="spacing">
      <number>0</number>
     </property>
     <property name="topMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="spacing">
        <number>4</number>
       </property>
       <property name="sizeConstraint">
        <enum>QLayout::SetMinimumSize</enum>
       </property>
       <property name="leftMargin">
        <number>8</number>
       </property>
       <property name="rightMargin">
        <number>8</number>
       </property>
      </layout>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>179</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtRangeLineEdit</class>
   <extends>QLineEdit</extends>
   <header>mtrangelineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
