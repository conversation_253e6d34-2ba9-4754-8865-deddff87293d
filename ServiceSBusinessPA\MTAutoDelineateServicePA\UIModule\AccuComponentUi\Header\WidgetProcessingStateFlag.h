﻿// ************************************************************
// <remarks>
// Author      : Qiuchh
// CreateTime  : 2023-12-22
// Description : 主界面处理状态逻辑
// </remarks>
// ************************************************************
#pragma once
#include <QObject>

/// <summary>
/// Class
/// </summary>
class WidgetProcessingStateFlag
{
public:
    //处理状态
    /// <summary>
    /// Enum ProcessingState
    /// </summary>
    enum WidgetProcessState
    {
        /// <summary>
        /// 闲置状态
        /// </summary>
        State_IDLE,
        /// <summary>
        /// 正在初始化
        /// </summary>
        State_Init,
        /// <summary>
        /// 正在删除某一行
        /// </summary>
        State_DeleteRow,
        /// <summary>
        /// 正在清空界面
        /// </summary>
        State_ClearView,
        /// <summary>
        /// 最大值
        /// </summary>
        State_Max,
    };
    /// <summary>
    /// Initializes a new instance of the <see cref="WidgetProcessingStateFlag"/> class.
    /// </summary>
    /// <param name="stateObject">The state object.</param>
    /// <param name="initState">State of the initialize.</param>
    WidgetProcessingStateFlag(WidgetProcessingStateFlag::WidgetProcessState* processStateObj, WidgetProcessingStateFlag::WidgetProcessState initState);
    /// <summary>
    /// Initializes a new instance of the <see cref="WidgetProcessingStateFlag"/> class.
    /// </summary>
    /// <param name="lockFalg">The lock falg.</param>
    WidgetProcessingStateFlag(bool* lockFalg);
    /// <summary>
    /// Finalizes an instance of the <see cref="PDUIModuleObjectMgr" /> class.
    /// </summary>
    ~WidgetProcessingStateFlag();

public:
    /// <summary>
    /// 界面处理状态
    /// </summary>
    WidgetProcessingStateFlag::WidgetProcessState* m_processStateObj = nullptr;
    /// <summary>
    /// 是否正在忙状态
    /// </summary>
    bool* m_lockFalg = nullptr;
};

