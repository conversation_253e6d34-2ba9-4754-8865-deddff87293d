﻿/********************************************************
* 通用功能接口
*********************************************************/
#pragma once

#include <QObject>
#include <QDir>
#include <QFont>
#include <QThread>
#include <QHostInfo>
#include <QDateTime>
#include <QSettings>
#include <QStandardPaths>
#include <QFileInfo>
#include <QFontMetrics>
#include <QProcessEnvironment>
#include <QJsonDocument>
#include <QJsonArray>

#include <stdio.h>
#include <shlobj.h>
#include <shlwapi.h>
#include <tcpmib.h>
#include <stdint.h>
#include <IPHlpApi.h>
#include <tlhelp32.h>
#include <Windows.h>
#include <iostream>


class CommonUtil : public QObject
{
    Q_OBJECT

public:
    /// <summary>
    /// 设置客户端配置文件路径
    /// </summary>
    static void SetClientConfigPath(const QString& cfgPath);
    /// <summary>
    /// 获取客户端配置文件路径
    /// </summary>
    static QString GetClientConfigPath();
    /// <summary>
    /// 设置器官默认设置配置文件路径
    /// </summary>
    static void SetOrganDefaultConfigPath(const QString& cfgPath);
    /// <summary>
    /// 获取官默认设置配置文件路径
    /// </summary>
    static QString GetOrganDefaultConfigPath();
    //*************************** Func
    /// <summary>
    /// 获取服务器ip+port
    /// </summary>
    /// <param name="ip">ip地址</param>
    /// <param name="port">端口</param>
    /// <param name="clientId">客户端id</param>
    /// <remarks>[Version]:******* Change: </remarks>
    static bool GetServerIpPortClientId(QString& ip, int& port, QString& clientId);
    /// <summary>
    /// 获取主机名
    /// </summary>
    static QString getLocalHostName();

    /// <summary>
    /// 获取Tcp端口状态*nStateID=0时空闲
    /// </summary>
    static BOOL GetTcpPortState(ULONG nPort, ULONG* nStateID);

    /// <summary>
    /// 获取五位随机数
    /// </summary>
    static QString getRandValue();

    /// <summary>
    /// 获取指定范围内的随机数
    /// </summary>
    static int getRandValue(int max, int min);

    /// <summary>
    /// 16进制字符串转10进制
    /// </summary>
    static int hexToDecimal(char s[]);

    /// <summary>
    /// 格式化CSS样式
    /// </summary>
    static QString formatStyleByCMtCoreWidgetUtil(const QString& str);

    /// <summary>
    /// 获取当前时间(间隔10ms)
    /// </summary>
    static QString getCurrentDateTime();

    /// <summary>
    /// 获取默认roiType集合
    /// </summary>
    static QStringList getRoiTypeList();

    /// <summary>
    /// stringList转string(/隔开)
    /// </summary>
    static QString stringListToStr(const QStringList& stringList);

    /// <summary>
    /// string转stringList(/隔开)
    /// </summary>
    static QStringList strToStringList(const QString& str);

    /// <summary>
    /// 获取导出范围对应的文本
    /// 1:导出该患者所有数据 2:只导出当前勾画RtStructure 3:导出当前勾画图像及RtStructure
    /// </summary>
    /// <param name="exportRange">[IN]导出范围</param>
    /// <returns>对应文本</returns>
    static QString getExportRangeText(const int exportRange);

    /// <summary>
    /// 获取影像来源对应的显示文本
    /// </summary>
    /// <param name="addrType">[IN]影像来源类型(1:共享文件夹 2:FTP服务器 3:加速器log[该项目不存在] 4:SCP服务器)</param>
    static QString getTextFromLocalAddrType(const int addrType);

    /// <summary>
    /// 获取导出内容对应的显示文本( | xxx)
    /// </summary>
    /// <param name="exportRange">[IN]导出内容(2:仅勾画 3:影像及勾画 4:不导出)</param>
    static QString getTextFromExportRange(const int exportRange);

    /// <summary>
    /// 获取导出格式对应的text文本
    /// </summary>
    /// <param name="exportFormat">[IN]导出格式("0":默认 "3239DF85-89FB-419D-99BE-56E9C1D5DE50":Eclipse 15 Struct Code)</param>
    static QString getTextFromExportFormat(const QString exportFormat);

    /// <summary>
    /// 检查盘符是否合法
    /// </summary>
    static bool checkDrives(const QString& dirPath);

    //********* Ui
    /// <summary>
    /// 获取显示在界面上的文本，超出部分用...替代
    /// </summary>
    static QString getElidedText(QFont font, QString str, int MaxWidth);

    /// <summary>
    /// 获取Roi分组名对应的英文名
    /// </summary>
    /// <returns>const QMap&lt;QString,QString&gt;&.</returns>
    static const QMap<QString/*数据库内置分组名*/, QString/*对应翻译名*/>& getRoiGroupNameMap();

    /// <summary>
    /// 获取AI部位识别对应的文本
    /// </summary>
    /// <param name="dcmTag">[IN]部位:111111 head chest chest_female abdomen prostate pelvis</param>
    static QString getTextFromBodyPartAI(const QString& bodypart);

    /// <summary>
    /// 获取AI部位识别编码集合
    /// </summary>
    /// <returns>AI部位识别编码集合(例:111111 head chest chest_female abdomen prostate pelvis)</returns>
    static QStringList getCodeFromBodyPartAI();

    /// <summary>
    /// 获取DICOM字段匹配对应的文本
    /// </summary>
    /// <param name="dcmTag">[IN]DICOM-Tag:00180015</param>
    static QString getTextFromDicomTag(const QString& dcmTag);

    /// <summary>
    /// 获取DICOM性别匹配对应的文本
    /// </summary>
    /// <param name="dcmTag">[IN]DICOM性别:ALL F M</param>
    static QString getTextFromDicomSex(const QString& sex);

    /// <summary>
    /// 获取无人值守内部能识别的模板名称(用于添加勾画规则)
    /// </summary>
    /// <param name="language">[IN]语言(ch en)</param>
    /// <param name="dcmTag">[IN]部位:111111 head chest chest_female abdomen prostate pelvis</param>
    /// <returns>无人值守内部能识别的模板名称</returns>
    static QString getUnattendModelNameOfInerDefault(const QString& language, const QString& bodypart);

    //*************************** Json
    /// <summary>
    /// QString转QJsonObject
    /// </summary>
    static QJsonObject qStringToqJsonObject(const QString jsonString);

    /// <summary>
    /// QString转QJsonArray
    /// </summary>
    static QJsonArray qStringToqJsonArray(const QString jsonString);

    /// <summary>
    /// QJsonObject转QString
    /// </summary>
    static QString qJsonObjectToqString(const QJsonObject& jsonObject);

    /// <summary>
    /// QVariantList 转QStringList
    /// </summary>
    static QStringList qVariantListToQStringList(const QVariantList& variantList);

    //*************************** Dir/File
    /// <summary>
    /// 获取指定目录下的所有文件完整路径
    /// </summary>
    /// <param name="path">[IN]搜索路径</param>
    /// <param name="ignoreDirVec">[IN]忽略的目录名</param>
    /// <param name="isRecursion">[IN]是否递归</param>
    /// <returns>指定目录下的所有文件完整路径</returns>
    static QFileInfoList getFileList(const QString& path, QVector<QString> ignoreDirVec = QVector<QString>(), bool isRecursion = true);

    /// <summary>
    /// 创建文件夹，返回实际文件夹路径
    /// </summary>
    static QString makPathDir(const QString& dirPath);

    /// <summary>
    /// 删除文件
    /// </summary>
    /// <param name="filePath">[IN]文件完整路径</param>
    /// <returns>true:成功</returns>
    static bool removeFile(const QString& filePath);

    /// <summary>
    /// 删除文件夹(包括子文件夹)
    /// </summary>
    /// <param name="dirPath">[IN]文件夹完整路径</param>
    /// <returns>true:成功</returns>
    static bool removeDir(const QString& dirPath);

    /// <summary>
    /// 拷贝文件到指定路径下
    /// </summary>
    /// <param name="srcFilePath">[IN]源文件完整路径</param>
    /// <param name="dstFilePath">[IN]目的文件完整路径</param>
    /// <param name="repalceSameFile">[IN]是否替换同名文件</param>
    /// <returns>成功true</returns>
    static bool copyFile(const QString& srcFilePath, const QString& dstFilePath, const bool repalceSameFile);

protected:
    CommonUtil(QObject* parent);
    ~CommonUtil();

private:
    /// <summary>
    /// 客户端配置文件路径，c:/manteia.ini
    /// </summary>
    static QString m_clientCfgPath;
    /// <summary>
    /// 器官默认配置文件路径
    /// </summary>
    static QString m_organDefaultConfigInfoPath;
};