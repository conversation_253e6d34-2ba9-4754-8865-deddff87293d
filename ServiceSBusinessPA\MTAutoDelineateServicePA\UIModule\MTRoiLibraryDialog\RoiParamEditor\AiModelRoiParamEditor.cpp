﻿#include "AiModelRoiParamEditor.h"
#include <QAbstractButton>
#include <QRegExp>
#include <QJsonDocument>


AiModelRoiParamEditor::AiModelRoiParamEditor(int modelId, const QString& roiParam, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //
    this->setMainLayout(ui.horizontalLayout);
    this->setTitle(tr("后处理参数"));
    //this->setMargin(38, 16, 38, 0);
    this->setFixedSize(496, 496);
    this->getButton(ButtonIndex::BtnRight1)->setText(tr("确定"));
    this->getButton(ButtonIndex::BtnRight2)->setText(tr("取消"));
    this->setAllowDrag(true);
    //
    QRegExp regExp("^(\\-|\\+)?\\d+(\\.\\d+)?$");
    ui.mtLineEdit->setValidator(new QRegExpValidator(regExp));
    ui.mtLineEdit_2->setValidator(new QRegExpValidator(regExp));
    ui.mtLineEdit_3->setValidator(new QRegExpValidator(regExp));
    ui.mtLineEdit_4->setValidator(new QRegExpValidator(regExp));
    //
    QRegExp regExp1("^[1-9]|([1-2]\\d)|30$");
    ui.mtLineEdit_5->setValidator(new QRegExpValidator(regExp1));
    ui.mtLineEdit_6->setValidator(new QRegExpValidator(regExp1));
    //
    QRegExp regExp2("^[0-9](\\.\\d{0,2})?|([1-2]\\d)(\\.\\d{0,2})?|30$");
    ui.mtLineEdit_7->setValidator(new QRegExpValidator(regExp2));

    if (modelId == 0 || modelId == -1)//内置模型roi，后处理过滤参数不可编辑
    {
        ui.mtLineEdit->setEnabled(false);
        ui.mtLineEdit_2->setEnabled(false);
        ui.mtLineEdit_3->setEnabled(false);
        ui.mtLineEdit_4->setEnabled(false);
    }

    init(roiParam);
}

void AiModelRoiParamEditor::SetFilterParamEditable(bool editable /*= true*/)
{
    ui.mtLineEdit->setEnabled(editable);
    ui.mtLineEdit_2->setEnabled(editable);
    ui.mtLineEdit_3->setEnabled(editable);
    ui.mtLineEdit_4->setEnabled(editable);
}

QString AiModelRoiParamEditor::getParamInfo()
{
    _roiParamObj;
    QJsonDocument doc(_roiParamObj);
    return doc.toJson(QJsonDocument::Compact);
}

void AiModelRoiParamEditor::init(const QString& roiParam)
{
    int nMaxNumCC3D = -1;
    int nMinNumSlice = 2;
    int nMinDiameter = -1;
    int nMinVolume = 1;
    int nClearX = -1;
    int nClearY = -1;
    double nErodeDist = -1;
    double nDilateDist = -1;
    QJsonDocument jsonDocument = QJsonDocument::fromJson(roiParam.toStdString().c_str());

    if (!jsonDocument.isNull())
    {
        _roiParamObj = jsonDocument.object();

        if (_roiParamObj.contains("MaxNumCC3D"))
        {
            nMaxNumCC3D = _roiParamObj["MaxNumCC3D"].toInt();
            ui.mtLineEdit->setText(QString::number(nMaxNumCC3D));
        }

        if (_roiParamObj.contains("MinNumSlice"))
        {
            nMinNumSlice = _roiParamObj["MinNumSlice"].toInt();
            ui.mtLineEdit_2->setText(QString::number(nMinNumSlice));
        }

        if (_roiParamObj.contains("MinDiameter"))
        {
            nMinDiameter = _roiParamObj["MinDiameter"].toInt();
            ui.mtLineEdit_3->setText(QString::number(nMinDiameter));
        }

        if (_roiParamObj.contains("MinVolume"))
        {
            nMinVolume = _roiParamObj["MinVolume"].toInt();
            ui.mtLineEdit_4->setText(QString::number(nMinVolume));
        }

        if (_roiParamObj.contains("ClearX"))
        {
            nClearX = _roiParamObj["ClearX"].toInt();
            ui.mtLineEdit_5->setText(QString::number(nClearX));
        }

        if (_roiParamObj.contains("ClearY"))
        {
            nClearY = _roiParamObj["ClearY"].toInt();
            ui.mtLineEdit_6->setText(QString::number(nClearY));
        }

        if (_roiParamObj.contains("ErodeDist"))
        {
            nErodeDist = _roiParamObj["ErodeDist"].toDouble();
            ui.mtLineEdit_7->setText(QString::number(nErodeDist, 'f', 2));
            ui.cmb_type->setCurrentIndex(0);//内缩
        }
        else  if (_roiParamObj.contains("DilateDist"))
        {
            nDilateDist = _roiParamObj["DilateDist"].toDouble();
            ui.mtLineEdit_7->setText(QString::number(nDilateDist, 'f', 2));
            ui.cmb_type->setCurrentIndex(1);//外扩
        }
    }
}

void AiModelRoiParamEditor::onBtnCloseClicked()
{
    this->reject();
}

void AiModelRoiParamEditor::onBtnRight2Clicked()
{
    this->reject();
}

void AiModelRoiParamEditor::onBtnRight1Clicked()
{
    QString strMaxNumCC3D = ui.mtLineEdit->text();
    QString strMinNumSlice = ui.mtLineEdit_2->text();
    QString strMinDiameter = ui.mtLineEdit_3->text();
    QString strMinVolume = ui.mtLineEdit_4->text();
    QString strClearX = ui.mtLineEdit_5->text();
    QString strClearY = ui.mtLineEdit_6->text();
    QString strDist = ui.mtLineEdit_7->text();

    if (!strMaxNumCC3D.isEmpty())
    {
        _roiParamObj["MaxNumCC3D"] = strMaxNumCC3D.toInt();
    }
    else
    {
        _roiParamObj.remove("MaxNumCC3D");
    }

    if (!strMinNumSlice.isEmpty())
    {
        _roiParamObj["MinNumSlice"] = strMinNumSlice.toInt();
    }
    else
    {
        _roiParamObj.remove("MinNumSlice");
    }

    if (!strMinDiameter.isEmpty())
    {
        _roiParamObj["MinDiameter"] = strMinDiameter.toInt();
    }
    else
    {
        _roiParamObj.remove("MinDiameter");
    }

    if (!strMinVolume.isEmpty())
    {
        _roiParamObj["MinVolume"] = strMinVolume.toInt();
    }
    else
    {
        _roiParamObj.remove("MinVolume");
    }

    if (!strClearX.isEmpty())
    {
        _roiParamObj["ClearX"] = strClearX.toInt();
    }
    else
    {
        _roiParamObj.remove("ClearX");
    }

    if (!strClearY.isEmpty())
    {
        _roiParamObj["ClearY"] = strClearY.toInt();
    }
    else
    {
        _roiParamObj.remove("ClearY");
    }

    if (!strDist.isEmpty())
    {
        double dist = strDist.toDouble();

        if (ui.cmb_type->currentIndex() == 0)//内缩
        {
            _roiParamObj.remove("DilateDist");
            _roiParamObj["ErodeDist"] = dist;
        }
        else//外扩
        {
            _roiParamObj.remove("ErodeDist");
            _roiParamObj["DilateDist"] = dist;
        }
    }
    else
    {
        _roiParamObj.remove("ErodeDist");
        _roiParamObj.remove("DilateDist");
    }

    this->accept();
}