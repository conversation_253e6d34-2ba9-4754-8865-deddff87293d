﻿#ifndef SLIDERTEXTWIDGET_H
#define SLIDERTEXTWIDGET_H

#include <QWidget>
#include <QColor>
#include <QResizeEvent>
#include "TextManage.h"


namespace Ui
{
class SliderTextWidget;
}

class  SliderTextWidget : public QWidget, public TextManage
{
    Q_OBJECT

public:
    SliderTextWidget(QWidget* parent = 0);
    ~SliderTextWidget();

    void setColor(QColor color);
    void setName(QString name);
    void setTextElided(const QString& text);
    void setText(QString name);
    //void resizeEvent(QResizeEvent* size);

signals:
    void sigSliderValueChange(int);
private:
    Ui::SliderTextWidget* ui;
    QString _name;
    // QString geteElidedText(QFont font, QString str, int MaxWidth);
};

#endif // SLIDERTEXTWIDGET_H
