﻿#include "RoiLibraryTable.h"

#include <QColorDialog>
#include <Windows.h>

#include "MTRoiLibraryDialog/RoiParamEditor/AiModelRoiParamEditor.h"
#include "CMtCoreWidgetUtil.h"
#include "CMtWidgetManager.h"
#include "MTRoiLibraryDialog/MultiSelectComboBox/CustMultiSelectComboBox.h"
#include "GroupColumnButton.h"
#include "MTRoiLibraryDialog/GroupTableWidget/GroupTableWidget.h"
#include "DataDefine/InnerStruct.h"
#include "LabelSyncSettingWidget.h"
#include "MtMessageBox.h"
#include "MtMessageDialog.h"
#include "MtProgressDialog.h"
#include "AccuComponentUi\Header\UnitUIComponent/MtUnitPushButtonGroup.h"
#include "MTRoiLibraryDialog/CustWidget/QCustMtComboBox2.h"
#include "MTRoiLibraryDialog\CustWidget\QCustMtLabel2.h"
#include "MTRoiLibraryDialog\CustWidget\QCustMtLineEdit2.h"
#include "QCustSwitchButton.h"
#include "MTRoiLibraryDialog\CustWidget\QMTAbsHorizontalBtns2.h"
#include "ROIInsert2TemplateDlg.h"
#include "MTRoiModelDialog\ModelSub\RoiSettingDialog.h"
#include "Skin/CMtSkinManager.h"
#include "SwitchLabelReminderDialog.h"

bool RoiLibraryTable::m_bSyncInfo2Label = true;

/// <summary>
/// 构造函数
/// </summary>
RoiLibraryTable::RoiLibraryTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    SetEnableDynamicCreateUi(false);        //都不需要动态创建
    connect(this, &RoiLibraryTable::sigCellWidgetButtonClicked, this, &RoiLibraryTable::slotCellWidgetButtonClicked); //某个按键点击了
    connect(this, &RoiLibraryTable::sigCellWidgetTextChange, this, &RoiLibraryTable::slotCellWidgetTextChange); //表格中的编辑框文本发生了改变
    connect(this, &RoiLibraryTable::sigCellItemClicked, this, &RoiLibraryTable::slotCellItemClicked);
    connect(this, &RoiLibraryTable::sigCreateTableRowItem, this, &RoiLibraryTable::slotCreateTableRowItem, Qt::QueuedConnection);
    connect(this, &RoiLibraryTable::sigClearTableItems, this, &RoiLibraryTable::slotClearTableItems, Qt::BlockingQueuedConnection);
}

RoiLibraryTable::~RoiLibraryTable()
{
}

void RoiLibraryTable::setFrameDialog(QWidget* parent)
{
    m_frameDialog = parent;
}

void RoiLibraryTable::tableDestroying()
{
    m_bDestroyed = true;
    m_bTableItemCreated = true;

    if (m_thrUpdateTable.joinable())
    {
        m_cvCreateTableItem.notify_one();
        m_thrUpdateTable.join();
    }

    if (m_thrMgrUpdateTable.joinable())
    {
        m_cvNewUpdtTask.notify_one();
        m_thrMgrUpdateTable.join();
    }
}

bool RoiLibraryTable::isTableInitialized()
{
    return m_bInitialized;
}

void RoiLibraryTable::init(const QStringList& allRoiTypeList,
                           const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList,
                           const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
                           const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
                           const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
                           const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    m_allRoiTypeList = allRoiTypeList;
    m_allOrganGroupList = allGroupList;
    m_modelInfoMap = modelInfoMap;
    m_modelCollectionInfoList = modelCollectionInfoList;
    m_modelCollectionInfoList2 = modelCollectionInfoList;
    m_allLabelList = QStringList() << "NONE";

    for (const auto& item : stRoiLabelInfoList)
    {
        m_roiLabelInfoMap[item.manteiaRoiLabel] = item;
        m_allLabelList.append(item.manteiaRoiLabel);
    }

    for (const n_mtautodelineationdialog::ST_Organ& organItem : stOrganList)
    {
        m_allOrganMap[organItem.id] = organItem;
    }

    initTableView(
        { tr(""), tr(""), tr(""), tr("器官名称"), tr("颜色"), tr("ROI类型"), tr(""), tr("来源"), tr("ROI描述"), tr("适用模态"), tr("状态"), tr("操作") },
        { 28, 160, 144, 130, 68, 150, 137, 135, 170, 80, 74, 92 });

    if (CMtLanguageUtil::type == english)
    {
        HideColumn(ColType_ChName);
    }

    createUpdateTableThreadMgr();
}

void RoiLibraryTable::createUpdateTableThreadMgr()
{
    m_thrMgrUpdateTable = std::thread([&]()
    {
        bool bStopUpdating = false;

        while (!m_bDestroyed)
        {
            std::unique_lock<std::mutex> lock(m_mtxNewUpdtTask);
            m_cvNewUpdtTask.wait(lock, [&]
            {
                return (m_updateOrganDataList.size() > 0 || m_bDestroyed);
            });

            if (m_bDestroyed)
            {
                break;
            }

            QList<n_mtautodelineationdialog::ST_Organ> stOrganList = m_updateOrganDataList.back();
            m_updateOrganDataList.clear();

            if (m_thrUpdateTable.joinable())
            {
                bStopUpdating = true;
                m_thrUpdateTable.join();
            }

            //使用线程发消息方式创建，不然创建列表要很久
            m_bInitialized = false;
            bStopUpdating = false;
            m_thrUpdateTable = std::thread([&](QList<n_mtautodelineationdialog::ST_Organ> stOrganList)
            {
                //刷新列表前，先清空表格
                emit sigClearTableItems();
                //
                int nOrganCount = stOrganList.size();

                if (nOrganCount > 0)
                {
                    for (int i = 0; i < nOrganCount && !m_bDestroyed && !bStopUpdating; ++i)
                    {
                        m_bTableItemCreated = false;
                        emit sigCreateTableRowItem(stOrganList[i], i == stOrganList.size() - 1);
                        //
                        std::unique_lock<std::mutex> itemLock(m_mtxCreateTableItem);
                        m_cvCreateTableItem.wait(itemLock, [&]
                        {
                            return m_bTableItemCreated;
                        });
                    }
                }
                else
                {
                    emit sigCreateTableRowItem(n_mtautodelineationdialog::ST_Organ(), true);//用于告知列表创建完成，可继续后续操作
                }
            }, stOrganList);
        }
    });
}

void RoiLibraryTable::updateTable(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList)
{
    std::lock_guard<std::mutex> lock(m_mtxNewUpdtTask);
    m_updateOrganDataList.append(stOrganList);
    m_cvNewUpdtTask.notify_one(); // 通知等待的线程
    //清除表头选中状态
    SetHeadCheckBoxState(ColType_CheckBox, Qt::Unchecked);
    //     //清空表格
    //     ClearDataModel();
    //     //使用线程发消息方式创建，不然创建列表要很久
    //     m_bInitialized = false;
    //     m_bDestroyed = false;
    //     m_thrUpdateTable = std::thread([&](QList<n_mtautodelineationdialog::ST_Organ> stOrganList)
    //     {
    //         for (int i = 0; i < stOrganList.size() && !m_bDestroyed; ++i)
    //         {
    //             emit sigCreateTableRowItem(stOrganList[i], i == stOrganList.size() - 1);
    //             Sleep(36);
    //         }
    //     }, stOrganList);
}

void RoiLibraryTable::filterTable(const QString& custName, const QString& label, const QString& chName, const QString& group)
{
    int rowNum = this->GetRowCount();

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        QString nameStr = this->GetColumnText(rowValue, ColType_Name);
        QString labelStr = this->GetColumnText(rowValue, ColType_Label);
        QString nameChStr = this->GetColumnText(rowValue, ColType_ChName);
        QWidget* groupCell = this->GetCellWidget(rowValue, ColType_Group);
        QStringList groupList;

        if (rowValue.isEmpty() || nameStr.isEmpty() && labelStr.isEmpty())
        {
            continue;
        }

        if (nullptr == groupCell)
        {
            CustMultiSelectComboBoxParam* groupParam = static_cast<CustMultiSelectComboBoxParam*>(this->GetCellWidgetParam(rowValue, ColType_Group));

            if (nullptr != groupParam)
            {
                for (int index : groupParam->_comboBoxIndexList)
                {
                    groupList.append(groupParam->_textList[index]);
                }
            }
            else
            {
                QCustMtLabel2Param* groupParam = static_cast<QCustMtLabel2Param*>(this->GetCellWidgetParam(rowValue, ColType_Group));

                if (nullptr != groupParam)
                {
                    groupList = groupParam->_text.split(";", QString::SkipEmptyParts);
                }
            }
        }
        else
        {
            CustMultiSelectComboBox* gComboBox = qobject_cast<CustMultiSelectComboBox*>(groupCell);

            if (nullptr != gComboBox)
            {
                groupList = gComboBox->currentText();
            }
            else
            {
                QCustMtLabel2* groupLabel = qobject_cast<QCustMtLabel2*>(groupCell);

                if (nullptr != groupLabel)
                {
                    groupList = groupLabel->GetCurText().split(";", QString::SkipEmptyParts);
                }
            }
        }

        if (!custName.isEmpty() && !nameStr.toLower().contains(custName.toLower()))
        {
            this->setRowHidden(i, true);
            continue;
        }

        if (!label.isEmpty() && !labelStr.toLower().contains(label.toLower()))
        {
            this->setRowHidden(i, true);
            continue;
        }

        if (!chName.isEmpty() && !nameChStr.toLower().contains(chName.toLower()))
        {
            this->setRowHidden(i, true);
            continue;
        }

        if (!group.isEmpty() && group != tr("全部分组") && !groupList.contains(group))
        {
            this->setRowHidden(i, true);
            continue;
        }

        this->setRowHidden(i, false);
    }
}

void RoiLibraryTable::resetROIDefaultInfo()
{
    if (!isTableInitialized())
    {
        MtMessageBox::NoIcon::information(this, tr("请等数据加载完成后再进行操作"));
        return;
    }

    if (!isItemSelected())
    {
        MtMessageBox::NoIcon::information_Title(this, tr("请先选择需要恢复设置的器官"));
        return;
    }

    if (QMessageBox::Yes != MtMessageBox::NoIcon::question_Title(this, tr("是否恢复所勾选的ROI的默认设置？")))
    {
        return;
    }

    //发送信号获取默认模型器官信息
    QList<n_mtautodelineationdialog::ST_Organ> stOrganDefaultList;
    emit sigGetOrganDefaultInfo(stOrganDefaultList);

    if (stOrganDefaultList.size() != 0)
    {
        resetDefaultInfo(stOrganDefaultList);
    }
}

void RoiLibraryTable::batchROISetting()
{
    if (!isItemSelected())
    {
        MtMessageBox::NoIcon::information_Title(this, tr("请先选择需要设置的器官"));
        return;
    }

    RoiSettingDialog dlg(m_allOrganGroupList, this);

    if (QDialog::Rejected == dlg.exec())
        return;

    //获取批量设置信息
    bool bLabel = dlg.isEnableLabel();
    bool bRoiType = dlg.isEnableROIType();
    bool bColor = dlg.isEnableColor();
    bool bChName = dlg.isEnableChineseName();
    bool bDesc = dlg.isEnableDescription();
    QStringList groupList = dlg.getGroupList();
    QString desc = dlg.getDescription();
    //发送信息获取标签库信息
    //QVector<n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoVec;
    //emit sigGetLabelLibraryInfo(stRoiLabelInfoVec);
    //更新roi列表
    //if (stRoiLabelInfoVec.size() != 0)
    {
        syncLabelLibraryInfo(/*stRoiLabelInfoVec,*/ bLabel, bRoiType, bColor, bChName, groupList, bDesc, desc);
    }
}

void RoiLibraryTable::addROI2Template()
{
    if (!isItemSelected())
    {
        MtMessageBox::NoIcon::information_Title(this, tr("请先选择需要加入到模板的器官"));
        return;
    }

    //1. 获取器官和组ID
    QMap<int/*organId*/, QList<int/*groupId*/>> selOrganGroupMap4CT;    //适用于CT模态的器官
    QMap<int/*organId*/, QList<int/*groupId*/>> selOrganGroupMap4MR;    //适用于MR模态的器官
    int nRowCount = GetRowCount();

    for (int i = 0; i < nRowCount; ++i)
    {
        if (GetCheckBoxState(i, ColType_CheckBox) == Qt::Checked && GetCellWidget(GetRowUniqueValue(i), ColType_CheckBox)->isVisible())
        {
            QString strModality = GetColumnText(GetRowUniqueValue(i), ColType_Modality);
            int organId = GetRowUniqueValue(i).toInt();
            QList<int> gList = m_allOrganMap[organId].organGroupInfoMap.keys();

            if (strModality.contains("CT"))
            {
                selOrganGroupMap4CT[organId].append(gList);
            }

            if (strModality.contains("MR"))
            {
                selOrganGroupMap4MR[organId].append(gList);
            }
        }
    }

    //2. 获取可添加的模板
    ROIInsert2TemplateDlg dlg(this);
    QMap<int/*templateID*/, QString/*templateName*/> templateIdNameMap;

    for (const auto& item : m_modelCollectionInfoList)
    {
        if (item.modality == "CT" && !selOrganGroupMap4CT.isEmpty()
            || item.modality == "MR" && !selOrganGroupMap4MR.isEmpty())
        {
            templateIdNameMap[item.id] = item.templateName;
        }
    }

    dlg.initList(templateIdNameMap);

    if (dlg.exec() == QDialog::Accepted)
    {
        QList<int/*templateId*/> templateIdList = dlg.getSelectTemplateId();
        //修改模板缓存信息
        int nCount = 0;//添加到模板的的ROI组次数

        for (int i = 0; i < m_modelCollectionInfoList.size(); ++i)
        {
            if (templateIdList.contains(m_modelCollectionInfoList[i].id))
            {
                QMap<int/*organId*/, QList<int/*groupId*/>>::const_iterator itor;

                if (m_modelCollectionInfoList[i].modality == "CT")
                {
                    for (itor = selOrganGroupMap4CT.constBegin(); itor != selOrganGroupMap4CT.constEnd(); ++itor)
                    {
                        for (int gid : itor.value())
                        {
                            m_modelCollectionInfoList[i].showGroupIdMap[itor.key()].insert(gid);
                            m_modelCollectionInfoList[i].groupIdSet.insert(gid);
                            ++nCount;
                        }
                    }
                }
                else if (m_modelCollectionInfoList[i].modality == "MR")
                {
                    for (itor = selOrganGroupMap4MR.constBegin(); itor != selOrganGroupMap4MR.constEnd(); ++itor)
                    {
                        for (int gid : itor.value())
                        {
                            m_modelCollectionInfoList[i].showGroupIdMap[itor.key()].insert(gid);
                            m_modelCollectionInfoList[i].groupIdSet.insert(gid);
                            ++nCount;
                        }
                    }
                }
            }
        }

        if (nCount > 0)
        {
            emit sigTableInfoChanged("", -1, "");
            MtMessageBox::NoIcon::information_Title(this, tr("添加完成！"));
        }
        else
        {
            MtMessageBox::NoIcon::information_Title(this, tr("未添加到任何模板！"));
        }
    }
}

void RoiLibraryTable::resetDefaultInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
{
    int nCount = this->GetRowCount();

    for (int i = 0; i < rowCount(); ++i)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        int state = this->GetCheckBoxState(i, ColType_CheckBox);
        int organID = rowValue.toInt();

        if (!m_allOrganMap.contains(organID) || Qt::Checked != state)
            continue;

        const QString& defaultName = m_allOrganMap[organID].defaultOrganName;

        //查找默认值
        for (const auto& defaultItem : stOrganDefaultList)
        {
            if (defaultItem.defaultOrganName == defaultName)
            {
                UpdateCellWidget(rowValue, ColType_Name, QVariant::fromValue(defaultItem.customOrganName));//20240311 zlw 用organinfo.json中原始的customOrganName填充
                UpdateCellWidget(rowValue, ColType_Label, QVariant::fromValue(defaultItem.roiLabel.isEmpty() ? "NONE" : defaultItem.roiLabel));
                UpdateCellWidget(rowValue, ColType_ChName, QVariant::fromValue(defaultItem.organChineseName.isEmpty() ? "-" : defaultItem.organChineseName));
                UpdateCellWidget(rowValue, ColType_Type, QVariant::fromValue(defaultItem.roiType.isEmpty() ? "NONE" : defaultItem.roiType));
                UpdateCellWidget(rowValue, ColType_Color, QVariant::fromValue(QColor(QString("#") + defaultItem.defaultColor)));
                UpdateCellWidget(rowValue, ColType_Desc, QVariant::fromValue(defaultItem.roiDesc.isEmpty() ? "-" : defaultItem.roiDesc));
                //更新临时缓存
                m_allOrganMap[organID].customOrganName = defaultItem.customOrganName;
                m_allOrganMap[organID].roiLabel = defaultItem.roiLabel;
                m_allOrganMap[organID].organChineseName = defaultItem.organChineseName;
                m_allOrganMap[organID].roiType = defaultItem.roiType;
                m_allOrganMap[organID].customColor = defaultItem.defaultColor;
                m_allOrganMap[organID].roiDesc = defaultItem.roiDesc;
                m_allOrganMap[organID].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                //同步缓存
                emit sigOrganInfoChanged(m_allOrganMap[organID]);
                break;
            }
        }
    }

    emit sigTableInfoChanged("", -1, "");
}

void RoiLibraryTable::syncLabelLibraryInfo(/*const QVector<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec,*/
                                           bool bLabel, bool bRoiType, bool bColor, bool bChName,
                                           const QStringList& groupList,
                                           bool bDesc, const QString& desc)
{
    int nCount = GetRowCount();

    for (int i = 0; i < rowCount(); ++i)
    {
        QString organId = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(organId, ColType_Name);
        int state = this->GetCheckBoxState(i, ColType_CheckBox);

        if (nullptr != widget && Qt::Checked == state)
        {
            int nOrganId = organId.toInt();
            QString roiNameLower = GetColumnText(organId, ColType_Name).toLower();

            for (const auto& labelItemObj : /*stRoiLabelInfoVec*/m_roiLabelInfoMap.values())
            {
                QStringList roiAliasNameList = labelItemObj.roiAlias.isEmpty() ? QStringList() : labelItemObj.roiAlias.toLower().split(';', QString::SkipEmptyParts);

                //通过标签库中的别名进行匹配
                if (roiAliasNameList.contains(roiNameLower))
                {
                    if (bLabel)
                    {
                        UpdateCellWidget(organId, ColType_Label, QVariant::fromValue(labelItemObj.manteiaRoiLabel));
                    }

                    if (bRoiType)
                    {
                        UpdateCellWidget(organId, ColType_Type, QVariant::fromValue(labelItemObj.roiType));
                    }

                    if (bColor)
                    {
                        UpdateCellWidget(organId, ColType_Color, QVariant::fromValue(QColor(QString("#") + labelItemObj.roiColor)));
                    }

                    if (bChName)
                    {
                        UpdateCellWidget(organId, ColType_ChName, QVariant::fromValue(labelItemObj.roiChName.isEmpty() ? "-" : labelItemObj.roiChName));
                    }

                    //更新缓存信息
                    m_allOrganMap[nOrganId].roiLabel = labelItemObj.manteiaRoiLabel;
                    m_allOrganMap[nOrganId].roiType = labelItemObj.roiType;
                    m_allOrganMap[nOrganId].customColor = labelItemObj.roiColor;
                    m_allOrganMap[nOrganId].organChineseName = labelItemObj.roiChName;
                    m_allOrganMap[nOrganId].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                    break;
                }
            }

            if (groupList.size() != 0)
            {
                if (m_editingOrganID == nOrganId)
                {
                    UpdateCellWidget(organId, ColType_Group, QVariant::fromValue(groupList));
                }
                else
                {
                    UpdateCellWidget(organId, ColType_Group, QVariant::fromValue(groupList.join(";")));
                }

                rowGroupInfoChanging(organId, groupList.join(";"));
                m_allOrganMap[nOrganId].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
            }

            if (bDesc)
            {
                UpdateCellWidget(organId, ColType_Desc, QVariant::fromValue(desc.isEmpty() ? "-" : desc));
                m_allOrganMap[nOrganId].roiDesc = desc;
                m_allOrganMap[nOrganId].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
            }

            emit sigTableInfoChanged(m_allOrganMap[nOrganId].defaultOrganName, -1, "");
            emit sigOrganInfoChanged(m_allOrganMap[nOrganId]);
        }
    }
}

QList<n_mtautodelineationdialog::ST_Organ> RoiLibraryTable::getAllOrganInfo()
{
    /*int rowNum = this->GetRowCount();

    for (int i = 0; i < rowNum; i++)
    {
        QString organId = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(organId, 0);

        if (nullptr == widget)
        {
            //ROI名称
            QString roiName = this->GetColumnText(organId, 0);
            //标签
            QString roiLabel;
            QCustMtComboBoxParam* roiLabelParam = static_cast<QCustMtComboBoxParam*>(this->GetCellWidgetParam(organId, 1));

            if (nullptr != roiLabelParam && -1 < roiLabelParam->_comboBoxIndex && roiLabelParam->_comboBoxIndex < roiLabelParam->_textList.size())
            {
                roiLabel = roiLabelParam->_textList[roiLabelParam->_comboBoxIndex];
            }

            //Roi颜色
            QString roiColor = this->GetColumnText(organId, 2);
            //Roi类型
            QString roiType;
            QCustMtComboBoxParam* roiTypeParam = static_cast<QCustMtComboBoxParam*>(this->GetCellWidgetParam(organId, 3));

            if (nullptr != roiTypeParam && -1 < roiTypeParam->_comboBoxIndex && roiTypeParam->_comboBoxIndex < roiTypeParam->_textList.size())
            {
                roiType = roiTypeParam->_textList[roiTypeParam->_comboBoxIndex];
            }

            //分组
            QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupMap;
            CustMultiSelectComboBoxParam* groupParam = static_cast<CustMultiSelectComboBoxParam*>(this->GetCellWidgetParam(organId, 4));

            if (nullptr != groupParam)
            {
                for (int index : groupParam->_comboBoxIndexList)
                {
                    organGroupMap[m_allOrganGroupList[index].id] = m_allOrganGroupList[index];
                }
            }

            //来源
            QString source = this->GetColumnText(organId, 5);
            //ROI描述
            QString desc = this->GetColumnText(organId, 6);
        }
    }*/
    //返回数据
    QList<n_mtautodelineationdialog::ST_Organ> organList;
    organList.reserve(m_allOrganMap.size() + 1);

    for (QMap<int, n_mtautodelineationdialog::ST_Organ>::iterator it = m_allOrganMap.begin(); it != m_allOrganMap.end(); it++)
    {
        organList.push_back(it.value());
    }

    return organList;
}

QList<n_mtautodelineationdialog::ST_OrganGroupInfo> RoiLibraryTable::getAllOrganGroupInfo()
{
    return m_allOrganGroupList;
}

const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& RoiLibraryTable::getAllModelCollectionInfoList()
{
    return m_modelCollectionInfoList;
}

const QList<n_mtautodelineationdialog::ST_RoiLabelInfo> RoiLibraryTable::getAllLabelInfoList()
{
    if (m_bSyncInfo2Label)
    {
        return m_roiLabelInfoMap.values();
    }
    else
    {
        QList<n_mtautodelineationdialog::ST_RoiLabelInfo> retROILabelList;

        for (auto labelInfoItem : m_roiLabelInfoMap.values())
        {
            labelInfoItem.optTypeEnum = n_mtautodelineationdialog::OptType_None;
            retROILabelList.append(labelInfoItem);
        }

        return retROILabelList;
    }
}

void RoiLibraryTable::setGroupColumnBtnImage(const QString& imagePath)
{
    m_groupColBtnImagePath = imagePath;
}

void RoiLibraryTable::mergeNewOrganInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec)
{
    for (const auto& newItem : stOrganInfoVec)
    {
        if (!m_allOrganMap.contains(newItem.id))
        {
            m_allOrganMap.insert(newItem.id, newItem);
        }
    }
}

void RoiLibraryTable::mergeNewModelInfo(const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap)
{
    QMap<int, n_mtautodelineationdialog::ST_SketchModel>::const_iterator itor = modelInfoMap.constBegin();

    for (; itor != modelInfoMap.constEnd(); ++itor)
    {
        //if (m_modelInfoMap.contains(itor.key()))
        {
            m_modelInfoMap[itor.key()] = itor.value();
        }
    }
}

void RoiLibraryTable::removeOrganAndModelCacheItem(int modelId)
{
    m_modelInfoMap.remove(modelId);
    QList<int> organIdList = m_allOrganMap.keys();

    for (int organId : organIdList)
    {
        if (m_allOrganMap[organId].modelId == modelId)
        {
            m_allOrganMap.remove(organId);
        }
    }
}

void RoiLibraryTable::updateModelInfo(int modelId, const QString& modelName, const QString& modelDesc)
{
    if (m_modelInfoMap.contains(modelId))
    {
        m_modelInfoMap[modelId].modelName = modelName;
        m_modelInfoMap[modelId].modelDesc = modelDesc;
    }
}

void RoiLibraryTable::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
    if (DELEAGATE_TYPE_User + 3 == cellType)
    {
        CustMultiSelectComboBox* custMultiCheckbox = static_cast<CustMultiSelectComboBox*>(cellWidget);

        if (nullptr != custMultiCheckbox)
        {
            connect(custMultiCheckbox->GetMtComboBox(), &n_mtautodelineationdialog::MultiSelectComboBox::sigSelectionChange, this, &RoiLibraryTable::slotGroupChanged);
            connect(custMultiCheckbox->GetMtComboBox(), &n_mtautodelineationdialog::MultiSelectComboBox::sigSelectedTextChange, this, [=](const QString& editText)
            {
                mtuiData::TableWidgetItemIndex cellItemIndex;
                cellItemIndex._column = column;
                cellItemIndex._type = cellType;
                cellItemIndex._uniqueValue = rowValue;
                slotCellWidgetTextChange(cellItemIndex, editText);
            });
        }
    }
    else if (DELEAGATE_TYPE_User + 4 == cellType)
    {
        QCustSwitchButton* switchBtn = qobject_cast<QCustSwitchButton*>(cellWidget);

        if (nullptr != switchBtn)
        {
            connect(switchBtn, &QCustSwitchButton::sigCheckStateChanged, this, [=](bool bChecked)
            {
                mtuiData::TableWidgetItemIndex cellItemIndex;
                cellItemIndex._column = column;
                cellItemIndex._type = cellType;
                cellItemIndex._uniqueValue = rowValue;
                slotCellWidgetButtonClicked(cellItemIndex, 0, bChecked);
            });
        }
    }
    else
    {
        QMTAbstractTableView::CreateCellWidgtFinishCallBack(rowValue, column, cellType, cellWidget);
    }
}

void RoiLibraryTable::CreateRowWidgetFinishCallBack(const QString& rowValue, int rowItemType)
{
    //     QCustMtComboBox* cellLabel = qobject_cast<QCustMtComboBox*>(GetCellWidget(rowValue, ColType_Label));
    //     QCustMtComboBox* cellType = qobject_cast<QCustMtComboBox*>(GetCellWidget(rowValue, ColType_Type));
    //     //CustMultiSelectComboBox* cellGroup = qobject_cast<CustMultiSelectComboBox*>(GetCellWidget(rowValue, ColType_Group));
    //     //暂停信号
    //     cellLabel->blockSignals(true);
    //     cellType->blockSignals(true);
    //     //
    //     cellLabel->AddItems(m_allLabelList);
    //     int index = m_allLabelList.indexOf(m_stOrganInfoAdding.roiLabel);
    //     cellLabel->setCurrentIndex(index < 0 ? 0 : index);
    //     //
    //     cellType->AddItems(m_allRoiTypeList);
    //     index = m_allRoiTypeList.indexOf(m_stOrganInfoAdding.roiType);
    //     cellType->setCurrentIndex(index < 0 ? 0 : index);
    //     //恢复信号
    //     cellLabel->blockSignals(false);
    //     cellLabel->blockSignals(false);
}

void RoiLibraryTable::ConnectHeadWidgetSignals(int cellWidgetType, QWidget* cellWidget)
{
    if (cellWidgetType == DELEAGATE_TYPE_User + 2)
    {
        GroupColumnButton* btnWidget = qobject_cast<GroupColumnButton*>(cellWidget);

        if (nullptr != btnWidget)
        {
            if (btnWidget->GetColumnNumber() == ColType_Group)
            {
                connect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotGroupButtonClicked(int, bool)));
            }
            else if (btnWidget->GetColumnNumber() == ColType_Label)
            {
                connect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotLabelButtonSettingClicked(int, bool)));
            }
        }
    }
    else
    {
        QMTAbstractTableView::ConnectHeadWidgetSignals(cellWidgetType, cellWidget);
    }
}
/// <summary>
/// 初始化表格
/// </summary>
/// <param name="headList">[IN]表头文本集合</param>
/// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
void RoiLibraryTable::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    firstViewParam._headParam._paddingLeft = 9;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    QMTCheckBoxParam* chkboxParam = new QMTCheckBoxParam();
    chkboxParam->_mtType = MtCheckBox::checkbox1;
    firstViewParam._headParam._headCellParamMap.insert(ColType_CheckBox, chkboxParam);
    GroupColumnButtonParam* roiNameWidgetParam = new GroupColumnButtonParam();
    roiNameWidgetParam->_text = tr("ROI名称");
    roiNameWidgetParam->_column = ColType_Name;
    roiNameWidgetParam->_prefixText = "*";
    firstViewParam._headParam._headCellParamMap.insert(ColType_Name, roiNameWidgetParam);
    GroupColumnButtonParam* labelWidgetParam = new GroupColumnButtonParam();
    labelWidgetParam->_text = tr("标签");
    labelWidgetParam->_column = ColType_Label;
    labelWidgetParam->_btnPixelPath = m_groupColBtnImagePath;
    labelWidgetParam->_btnTipStr = tr("信息同步设置");
    firstViewParam._headParam._headCellParamMap.insert(ColType_Label, labelWidgetParam);
    GroupColumnButtonParam* groupWidgetParam = new GroupColumnButtonParam();
    groupWidgetParam->_text = tr("分组");
    groupWidgetParam->_column = ColType_Group;
    groupWidgetParam->_btnPixelPath = m_groupColBtnImagePath;
    groupWidgetParam->_btnTipStr = tr("分组管理");
    firstViewParam._headParam._headCellParamMap.insert(ColType_Group, groupWidgetParam);

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="stEmptyOrgan">[IN]Organ信息</param>
void RoiLibraryTable::addRow(const n_mtautodelineationdialog::ST_Organ& stOrganInfo, int nRowIndex/* = -1*/)
{
    m_allOrganMap.insert(stOrganInfo.id, stOrganInfo);
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //勾选
    QMTCheckBoxParam* chkboxParam = new QMTCheckBoxParam();
    chkboxParam->_mtType = MtCheckBox::checkbox1;
    cellWidgetParamMap.insert(ColType_CheckBox, chkboxParam);
    //ROI名称
    QCustMtLineEdit2Param* lineEditParam = new QCustMtLineEdit2Param();
    lineEditParam->_maxLength = 64;
    lineEditParam->_text = stOrganInfo.customOrganName;
    lineEditParam->_regExpStr = RegExp_CharNumber4;
    lineEditParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Name, lineEditParam);
    //标签
    QCustMtComboBox2Param* comboBoxLabelParam = new QCustMtComboBox2Param();
    comboBoxLabelParam->_textList = m_allLabelList;
    int index = m_allLabelList.indexOf(stOrganInfo.roiLabel);
    comboBoxLabelParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxLabelParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Label, comboBoxLabelParam);
    //中文名称
    QCustMtLineEdit2Param* lineEditParamChName = new QCustMtLineEdit2Param();
    lineEditParamChName->_maxLength = 64;
    lineEditParamChName->_text = stOrganInfo.organChineseName.isEmpty() ? "-" : stOrganInfo.organChineseName;
    lineEditParamChName->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_ChName, lineEditParamChName);
    //Roi颜色
    QMTAbsHorizontalBtns2Param* btnsParam = new QMTAbsHorizontalBtns2Param();
    btnsParam->_pixPathList.clear();
    btnsParam->_width = 51;
    btnsParam->_height = 16;
    btnsParam->_btnBackgroundColor = QColor(QString("#") + (stOrganInfo.customColor.isEmpty() ? "ff0000" : stOrganInfo.customColor));
    cellWidgetParamMap.insert(ColType_Color, btnsParam);
    //Roi类型
    QCustMtComboBox2Param* comboBoxTypeParam = new QCustMtComboBox2Param();
    comboBoxTypeParam->_textList = m_allRoiTypeList;
    index = m_allRoiTypeList.indexOf(stOrganInfo.roiType);
    comboBoxTypeParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxTypeParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Type, comboBoxTypeParam);
    //分组
    ///判断ROI是否为主结构
    auto funcIsMainStructure = [&]() -> bool
    {
        for (int i = 0; i < m_allOrganGroupList.size(); ++i)
        {
            if (m_allOrganGroupList[i].type == 3 && m_allOrganGroupList[i].refOrganId == stOrganInfo.id)
            {
                return true;
            }
        }

        return false;
    };
    ///判断ROI是否为非CT模态
    auto funcIsNotCTStructure = [&](int organID) -> bool
    {
        return (m_modelInfoMap.contains(m_allOrganMap[organID].modelId) && m_modelInfoMap[m_allOrganMap[organID].modelId].modality != "CT");
    };
    ///获取分组列参数
    auto funcGetGroupColumnParam = [&]() -> CustMultiSelectComboBoxParam*
    {
        CustMultiSelectComboBoxParam* comboBoxGroupParam = new CustMultiSelectComboBoxParam();
        int indexEmpty = -1;            //记录空勾画分组
        QList<int> indexAllList;        //所有的分组索引
        QList<int> indexSecGroupList;   //所有的亚组索引

        for (int i = 0; i < m_allOrganGroupList.size(); ++i)
        {
            comboBoxGroupParam->_textList.append(m_allOrganGroupList[i].name);
            indexAllList.append(i);

            if (m_allOrganGroupList[i].type == 3)
            {
                indexSecGroupList.append(i);
            }

            if (m_allOrganGroupList[i].name == tr("空勾画"))
            {
                indexEmpty = i;
            }
        }

        QList<int> indexCheckedList;//记录当前已选的分组
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = stOrganInfo.organGroupInfoMap.constBegin();

        for (; itor != stOrganInfo.organGroupInfoMap.constEnd(); ++itor)
        {
            n_mtautodelineationdialog::ST_OrganGroupInfo v = itor.value();

            for (int index = 0; index < m_allOrganGroupList.size(); ++index)
            {
                if (m_allOrganGroupList[index].id == v.id && m_allOrganGroupList[index].name == v.name)
                {
                    indexCheckedList.append(index);
                    break;
                }
            }
        }

        ///非空勾画类型的roi，不允许添加空勾画分组；空勾画类型roi，分组信息不能编辑
        if (stOrganInfo.modelId == -1)
        {
            indexAllList.removeAt(indexEmpty);
            comboBoxGroupParam->_comboBoxDisableIndexList = indexAllList;

            if (indexCheckedList.isEmpty())
            {
                indexCheckedList.append(indexEmpty);
            }
        }
        else
        {
            comboBoxGroupParam->_comboBoxDisableIndexList.append(indexEmpty);
        }

        //主结构和空勾画不能选择亚组，非CT模态结构也不能选择亚组
        if (funcIsMainStructure() || stOrganInfo.modelId == -1 || funcIsNotCTStructure(stOrganInfo.id))
        {
            comboBoxGroupParam->_comboBoxDisableIndexList.append(indexSecGroupList);
        }

        comboBoxGroupParam->_comboBoxIndexList = indexCheckedList;
        comboBoxGroupParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
        return comboBoxGroupParam;
    };
    CustMultiSelectComboBoxParam* comboBoxGroupParam = funcGetGroupColumnParam();
    cellWidgetParamMap.insert(ColType_Group, comboBoxGroupParam);
    //来源
    QCustMtLabel2Param* sourceParam = new QCustMtLabel2Param();

    if (stOrganInfo.modelId == 0)
    {
        sourceParam->_text = tr("系统内置");
    }
    else if (stOrganInfo.modelId == -1)
    {
        sourceParam->_text = tr("空勾画");
    }
    else
    {
        sourceParam->_text = m_modelInfoMap.contains(stOrganInfo.modelId) ? m_modelInfoMap[stOrganInfo.modelId].modelName : tr("训练模型");
    }

    cellWidgetParamMap.insert(ColType_Source, sourceParam);
    //ROI描述
    QCustMtLineEdit2Param* lineEditDescParam = new QCustMtLineEdit2Param();
    lineEditDescParam->_maxLength = 32;
    lineEditDescParam->_text = stOrganInfo.roiDesc.isEmpty() ? "-" : stOrganInfo.roiDesc;
    //lineEditDescParam->_regExpStr = RegExp_CharNumber2;
    lineEditDescParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Desc, lineEditDescParam);
    //适用模态
    QString modality = "CT";

    if (m_modelInfoMap.contains(stOrganInfo.modelId))
    {
        modality = m_modelInfoMap[stOrganInfo.modelId].modality;
    }

    if (stOrganInfo.modelId == -1)//空勾画模态为CT+MR
    {
        modality = "CT/MR";
    }

    QCustMtLabel2Param* modalityParam = new QCustMtLabel2Param();
    modalityParam->_text = modality;
    cellWidgetParamMap.insert(ColType_Modality, modalityParam);
    //状态
    QCustSwitchButtonParam* stateParam = new QCustSwitchButtonParam();
    stateParam->_state = stOrganInfo.enable;
    stateParam->_width = 42;
    stateParam->_height = 20;
    cellWidgetParamMap.insert(ColType_State, stateParam);
    //后处理参数
    MtUnitPushButtonGroupParam* btnsSettingParam = new MtUnitPushButtonGroupParam();
    btnsSettingParam->_btnTextStrList = QStringList({ tr("后处理参数") });
    btnsSettingParam->_btnIndexMtTypeMap.insert(0, MtPushButton::MtType::pushbutton5);

    if (stOrganInfo.modelId == -1)
    {
        btnsSettingParam->_btnIndexEnabledMap.insert(0, false);
    }

    cellWidgetParamMap.insert(ColType_Operation, btnsSettingParam);
    QString rowValue = QString::number(stOrganInfo.id);

    if (-1 == nRowIndex)
    {
        this->AddRowItem(rowValue, cellWidgetParamMap);
    }
    else
    {
        this->InsertRowItem(nRowIndex, rowValue, cellWidgetParamMap);
    }
}

void RoiLibraryTable::addStaticRow(const n_mtautodelineationdialog::ST_Organ& stOrganInfo, int nRowIndex/* = -1*/)
{
    m_allOrganMap.insert(stOrganInfo.id, stOrganInfo);
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //勾选
    QMTCheckBoxParam* chkboxParam = new QMTCheckBoxParam();
    chkboxParam->_mtType = MtCheckBox::checkbox1;
    cellWidgetParamMap.insert(ColType_CheckBox, chkboxParam);
    //ROI名称
    QCustMtLabel2Param* lineEditParam = new QCustMtLabel2Param();
    lineEditParam->_text = stOrganInfo.customOrganName;
    cellWidgetParamMap.insert(ColType_Name, lineEditParam);
    //标签
    QCustMtLabel2Param* comboBoxLabelParam = new QCustMtLabel2Param();
    comboBoxLabelParam->_text = m_allLabelList.indexOf(stOrganInfo.roiLabel) < 0 ? "NONE" : stOrganInfo.roiLabel;
    cellWidgetParamMap.insert(ColType_Label, comboBoxLabelParam);
    //中文名称
    QCustMtLabel2Param* lineEditParamChName = new QCustMtLabel2Param();
    lineEditParamChName->_text = stOrganInfo.organChineseName.isEmpty() ? "-" : stOrganInfo.organChineseName;
    cellWidgetParamMap.insert(ColType_ChName, lineEditParamChName);
    //Roi颜色
    QMTAbsHorizontalBtns2Param* btnsParam = new QMTAbsHorizontalBtns2Param();
    btnsParam->_pixPathList.clear();
    btnsParam->_width = 51;
    btnsParam->_height = 16;
    //btnsParam->_disableBtnList.append(0);
    btnsParam->_btnBackgroundColor = QColor(QString("#") + (stOrganInfo.customColor.isEmpty() ? "ff0000" : stOrganInfo.customColor));
    cellWidgetParamMap.insert(ColType_Color, btnsParam);
    //Roi类型
    QCustMtLabel2Param* comboBoxTypeParam = new QCustMtLabel2Param();
    comboBoxTypeParam->_text = m_allRoiTypeList.indexOf(stOrganInfo.roiType) < 0 ? "NONE" : stOrganInfo.roiType;
    comboBoxTypeParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Type, comboBoxTypeParam);
    //分组
    ///获取分组列参数
    auto funcGetGroupColumnParam = [&]() -> QCustMtLabel2Param*
    {
        QCustMtLabel2Param* comboBoxGroupParam = new QCustMtLabel2Param();
        QStringList groupNameList;
        int indexEmpty = -1;            //记录空勾画分组

        for (int i = 0; i < m_allOrganGroupList.size(); ++i)
        {
            if (m_allOrganGroupList[i].name == tr("空勾画"))
            {
                indexEmpty = i;
            }
        }

        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = stOrganInfo.organGroupInfoMap.constBegin();

        for (; itor != stOrganInfo.organGroupInfoMap.constEnd(); ++itor)
        {
            n_mtautodelineationdialog::ST_OrganGroupInfo v = itor.value();

            for (int index = 0; index < m_allOrganGroupList.size(); ++index)
            {
                if (m_allOrganGroupList[index].id == v.id && m_allOrganGroupList[index].name == v.name)
                {
                    groupNameList.append(m_allOrganGroupList[index].name);
                    break;
                }
            }
        }

        ///非空勾画类型的roi，不允许添加空勾画分组；空勾画类型roi，分组信息不能编辑
        if (stOrganInfo.modelId == -1)
        {
            if (groupNameList.isEmpty())
            {
                groupNameList.append(m_allOrganGroupList[indexEmpty].name);
            }
        }

        comboBoxGroupParam->_text = groupNameList.join(";");
        return comboBoxGroupParam;
    };
    QCustMtLabel2Param* comboBoxGroupParam = funcGetGroupColumnParam();
    cellWidgetParamMap.insert(ColType_Group, comboBoxGroupParam);
    //来源
    QCustMtLabel2Param* sourceParam = new QCustMtLabel2Param();

    if (stOrganInfo.modelId == 0)
    {
        sourceParam->_text = tr("系统内置");
    }
    else if (stOrganInfo.modelId == -1)
    {
        sourceParam->_text = tr("空勾画");
    }
    else
    {
        sourceParam->_text = m_modelInfoMap.contains(stOrganInfo.modelId) ? m_modelInfoMap[stOrganInfo.modelId].modelName : tr("训练模型");
    }

    cellWidgetParamMap.insert(ColType_Source, sourceParam);
    //ROI描述
    QCustMtLabel2Param* lineEditDescParam = new QCustMtLabel2Param();
    lineEditDescParam->_text = stOrganInfo.roiDesc.isEmpty() ? "-" : stOrganInfo.roiDesc;
    cellWidgetParamMap.insert(ColType_Desc, lineEditDescParam);
    //适用模态
    QString modality = "CT";

    if (m_modelInfoMap.contains(stOrganInfo.modelId))
    {
        modality = m_modelInfoMap[stOrganInfo.modelId].modality;
    }

    if (stOrganInfo.modelId == -1)//空勾画模态为CT+MR
    {
        modality = "CT/MR";
    }

    QCustMtLabel2Param* modalityParam = new QCustMtLabel2Param();
    modalityParam->_text = modality;
    cellWidgetParamMap.insert(ColType_Modality, modalityParam);
    //状态
    QCustSwitchButtonParam* stateParam = new QCustSwitchButtonParam();
    stateParam->_state = stOrganInfo.enable;
    stateParam->_width = 42;
    stateParam->_height = 20;
    cellWidgetParamMap.insert(ColType_State, stateParam);
    //后处理参数
    MtUnitPushButtonGroupParam* btnsSettingParam = new MtUnitPushButtonGroupParam();
    btnsSettingParam->_btnTextStrList = QStringList({ tr("后处理参数") });
    btnsSettingParam->_btnIndexMtTypeMap.insert(0, MtPushButton::MtType::pushbutton5);

    if (stOrganInfo.modelId == -1)
    {
        btnsSettingParam->_btnIndexEnabledMap.insert(0, false);
    }

    cellWidgetParamMap.insert(ColType_Operation, btnsSettingParam);
    QString rowValue = QString::number(stOrganInfo.id);

    if (-1 == nRowIndex)
    {
        this->AddRowItem(rowValue, cellWidgetParamMap);
    }
    else
    {
        this->InsertRowItem(nRowIndex, rowValue, cellWidgetParamMap);
    }
}

bool RoiLibraryTable::delSelectedRow()
{
    int nCount = GetRowCount();
    QMap<QString/*organId*/, bool/*refTemplate*/> selectedOrganRefTemplMap;
    QList<int/*organId*/> organIdRefTemplateList;

    //查找哪些器官是关联了模板
    for (const n_mtautodelineationdialog::ST_SketchModelCollection& modelItemInfo : m_modelCollectionInfoList)
    {
        organIdRefTemplateList.append(modelItemInfo.showGroupIdMap.keys());
    }

    //查找勾选的器官
    for (int i = 0; i < nCount; ++i)
    {
        QString strOrgnaId = GetRowUniqueValue(i);
        int state = GetCheckBoxState(i, ColType_CheckBox);

        if (state == Qt::Checked)
        {
            int id = strOrgnaId.toInt();
            selectedOrganRefTemplMap[strOrgnaId] = organIdRefTemplateList.contains(id);
        }
    }

    if (selectedOrganRefTemplMap.isEmpty())
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要删除的ROI"));
        return false;
    }

    if (QMessageBox::Yes == MtMessageBox::NoIconRedButton::question(this, tr("是否确定删除选中的空勾画ROI？")))
    {
        QList<bool> refStatusList = selectedOrganRefTemplMap.values();

        if (refStatusList.contains(true))
        {
            if (QMessageBox::Yes != MtMessageBox::NoIcon::question_Title(this->window(), QString(tr("若ROI已被添加进勾画模板中，删除ROI将会从所有关联模板中移除被删除的ROI，是否确定继续？")), QString()))
            {
                return false;
            }
        }

        QList<QString>organIdList = selectedOrganRefTemplMap.keys();

        for (const QString& organIdItem : organIdList)
        {
            int organID = organIdItem.toInt();

            if (m_allOrganMap.contains(organID))
            {
                m_allOrganMap[organID].optTypeEnum = n_mtautodelineationdialog::OptType_Del;
                emit sigOrganInfoChanged(m_allOrganMap[organIdItem.toInt()]);
            }

            DeleteRowItem(organIdItem);

            if (organID == m_editingOrganID)
            {
                m_editingOrganID = -2000;
            }
        }

        //清除表头选中状态
        SetHeadCheckBoxState(ColType_CheckBox, Qt::Unchecked);
    }

    return true;
}

bool RoiLibraryTable::rowGroupInfoChanging(const QString& rowValue, const QString& newGroupStr)
{
    bool bGroupDiff = false;    //记录分组是否改变，若改变，则需要刷新分组列表勾选状态
    int organID = rowValue.toInt();
    QStringList newGroupList = newGroupStr.split(';');
    //原有分组被移除，更新模板组信息，同时，若模板中引用了被移除的分组，则要发送消息通知
    auto funcGroupRemovedHandler = [&](const QStringList& curGroupList, bool bJustOneGroupRemoved) -> void
    {
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = m_allOrganMap[organID].organGroupInfoMap.constBegin();

        for (; itor != m_allOrganMap[organID].organGroupInfoMap.constEnd(); ++itor)
        {
            if (curGroupList.contains(itor.value().name))
            {
                continue;
            }

            //检查模板是否引用该分组并记录模板信息
            QMap<int/*modelID*/, QString/*modelName*/> refModelInfoMap;

            for (n_mtautodelineationdialog::ST_SketchModelCollection& modelItemInfo : m_modelCollectionInfoList)
            {
                //取消的分组在模板中被引用
                if (modelItemInfo.showGroupIdMap.contains(organID) && modelItemInfo.showGroupIdMap[organID].contains(itor.value().id))
                {
                    refModelInfoMap[modelItemInfo.id] = modelItemInfo.templateName;
                    //
                    modelItemInfo.showGroupIdMap[organID].remove(itor.value().id);
                    /*if (modelItemInfo.showGroupIdMap[organID].isEmpty())不用移除该器官，改成添加该器官的第一个分组
                    {
                        modelItemInfo.showGroupIdMap.remove(organID);
                    }*/
                }
            }

            if (refModelInfoMap.size() > 0)
            {
                QString roiName = GetColumnText(rowValue, ColType_Name);
                emit sigRemoveRoiRelatedGroup(organID, roiName, itor.value().id, itor.value().name, refModelInfoMap);
            }

            if (bJustOneGroupRemoved)
            {
                break;
            }
        }
    };
    //还原器官分组
    auto funcRecoverROIGroup = [&]() -> n_mtautodelineationdialog::ST_OrganGroupInfo
    {
        n_mtautodelineationdialog::ST_OrganGroupInfo retGroupInfo;
        newGroupList.clear();
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = m_allOrganMap[organID].organGroupInfoMap.constBegin();

        for (; itor != m_allOrganMap[organID].organGroupInfoMap.constEnd(); ++itor)
        {
            newGroupList.append(itor.value().name);
        }

        UpdateCellWidget(rowValue, ColType_Group, QVariant::fromValue(newGroupList));

        if (newGroupList.size() > 0)
        {
            --itor;
            retGroupInfo = itor.value();
        }

        return retGroupInfo;
    };

    if (newGroupStr.isEmpty())
    {
        n_mtautodelineationdialog::ST_OrganGroupInfo groupRemoved = funcRecoverROIGroup();

        if (newGroupList.size() > 0)
        {
            QMap<int/*modelID*/, QString/*modelName*/> refModelInfoMap;
            emit sigRemoveRoiRelatedGroup(organID, GetColumnText(rowValue, ColType_Name), /*groupRemoved.id*/ -2, /*groupRemoved.name*/"", refModelInfoMap);
        }

        return false;
    }

    //检查取消的分组
    if (m_allOrganMap[organID].organGroupInfoMap.size() > newGroupList.size())
    {
        funcGroupRemovedHandler(newGroupList, true);
    }
    else//检查新增分组
    {
        QStringList curMainGroupNameList;   //当前主分组名
        QStringList curSecGroupNameList;    //当前亚结构分组名
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = m_allOrganMap[organID].organGroupInfoMap.constBegin();

        for (; itor != m_allOrganMap[organID].organGroupInfoMap.constEnd(); ++itor)
        {
            if (itor.value().type == 3)
            {
                curSecGroupNameList.append(itor.value().name);
            }
            else
            {
                curMainGroupNameList.append(itor.value().name);
            }
        }

        //取出新增的分组
        QString newGroupItemName;

        for (const QString& groupItem : newGroupList)
        {
            if (curMainGroupNameList.contains(groupItem) || curSecGroupNameList.contains(groupItem))
            {
                continue;
            }
            else
            {
                newGroupItemName = groupItem;
                break;
            }
        }

        //可能是组合框选项调用了设置状态接口触发了该事件
        if (newGroupItemName.isEmpty())
        {
            return false;
        }

        //获取新增分组的信息
        n_mtautodelineationdialog::ST_OrganGroupInfo newGroupInfo;

        for (int i = 0; i < m_allOrganGroupList.size(); ++i)
        {
            if (m_allOrganGroupList[i].name == newGroupItemName)
            {
                newGroupInfo = m_allOrganGroupList[i];
                break;
            }
        }

        //1. 若将某个ROI添加进某个亚组，则自动取消勾选其他分组
        //2. 若将已属于亚组的ROI添加进常规分组，则自动取消勾选原有的亚组
        //3. 一个ROI仅可以勾选一个亚组
        if (newGroupInfo.type == 3)//新增的是亚组
        {
            //非CT模态的器官，不让选亚组，因为亚组都是CT模态ROI
            if (m_modelInfoMap.contains(m_allOrganMap[organID].modelId) && m_modelInfoMap[m_allOrganMap[organID].modelId].modality != "CT")
            {
                n_mtautodelineationdialog::ST_OrganGroupInfo groupRemoved = funcRecoverROIGroup();
                QMap<int/*modelID*/, QString/*modelName*/> refModelInfoMap;
                emit sigRemoveRoiRelatedGroup(organID, GetColumnText(rowValue, ColType_Name), groupRemoved.id, groupRemoved.name, refModelInfoMap);
                return false;
            }

            newGroupList = QStringList() << newGroupItemName;
            funcGroupRemovedHandler(newGroupList, false);
            bGroupDiff = true;
        }
        else//非亚组
        {
            //若原来选择的是亚组，则把原来的亚组取消
            if (curSecGroupNameList.size() > 0)
            {
                newGroupList = QStringList() << newGroupItemName;
                funcGroupRemovedHandler(newGroupList, false);
                bGroupDiff = true;
            }
            else
            {
                //newGroupList.append(newGroupItemName);
            }
        }
    }

    m_allOrganMap[organID].organGroupInfoMap.clear();

    if (m_allOrganMap[organID].optTypeEnum != n_mtautodelineationdialog::OptType_Add)
    {
        m_allOrganMap[organID].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;//记录修改
    }

    for (const auto& groupItem : m_allOrganGroupList)
    {
        if (newGroupList.contains(groupItem.name))
        {
            m_allOrganMap[organID].organGroupInfoMap[groupItem.id] = groupItem;
        }
    }

    if (m_allOrganMap[organID].enable)
    {
        //还原模板引用器官的分组信息（用户可能取消模板引用器官的分组，然后又勾选回去）
        for (int i = 0; i < m_modelCollectionInfoList2.size(); ++i)
        {
            QSet<int> orignalGroupIdSet;
            QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = m_allOrganMap[organID].organGroupInfoMap.constBegin();

            for (; itor != m_allOrganMap[organID].organGroupInfoMap.constEnd(); ++itor)
            {
                n_mtautodelineationdialog::ST_SketchModelCollection& modelItemInfo = m_modelCollectionInfoList2[i];

                //取消的分组在模板中被引用
                if (modelItemInfo.showGroupIdMap.contains(organID) && modelItemInfo.showGroupIdMap[organID].contains(itor.value().id)
                    && !m_modelCollectionInfoList[i].showGroupIdMap[organID].contains(itor.value().id))
                {
                    //m_modelCollectionInfoList[i].showGroupIdMap[organID].insert(itor.value().id);
                    orignalGroupIdSet.insert(itor.value().id);
                }
            }

            if (m_modelCollectionInfoList[i].showGroupIdMap.contains(organID) && !orignalGroupIdSet.isEmpty())
            {
                m_modelCollectionInfoList[i].showGroupIdMap[organID].clear();
                m_modelCollectionInfoList[i].showGroupIdMap[organID] = orignalGroupIdSet;
            }
        }

        //检查模板，若模板中的器官所属分组为空，则使用该器官的第一个分组
        for (int i = 0; i < m_modelCollectionInfoList.size(); ++i)
        {
            n_mtautodelineationdialog::ST_SketchModelCollection& modelItemInfo = m_modelCollectionInfoList[i];

            if (modelItemInfo.showGroupIdMap.contains(organID) && modelItemInfo.showGroupIdMap[organID].isEmpty())
            {
                modelItemInfo.showGroupIdMap[organID].insert(m_allOrganMap[organID].organGroupInfoMap.begin().key());
            }
        }
    }

    //更新下拉列表
    if (bGroupDiff)
    {
        UpdateCellWidget(rowValue, ColType_Group, QVariant::fromValue(newGroupList));
    }

    emit sigOrganInfoChanged(m_allOrganMap[organID]);
    return true;
}

bool RoiLibraryTable::isItemSelected()
{
    //检查是否有勾选roi
    int nRowCount = GetRowCount();

    for (int i = 0; i < nRowCount; ++i)
    {
        if (GetCheckBoxState(i, ColType_CheckBox) == Qt::Checked && GetCellWidget(GetRowUniqueValue(i), ColType_CheckBox)->isVisible())
        {
            return true;
        }
    }

    return false;
}

void RoiLibraryTable::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    int organId = cellItemIndex._uniqueValue.toInt();
    //记录上一次的编辑项
    int lastEditingOrganID = m_editingOrganID;
    //让该行变为编辑状态
    slotCellItemClicked(cellItemIndex);

    if (cellItemIndex._column == ColType_Color) //roi颜色按钮
    {
        QColor color = Qt::red;

        if (m_allOrganMap.contains(organId))
            color = QString("#") + m_allOrganMap[organId].customColor;

        QColor newColor = QColorDialog::getColor(color/*, this*/);//输入框样式会被父窗口影像，不设置父窗口

        if (newColor.isValid() == false || color == newColor)
            return;

        this->UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Color, QVariant::fromValue(newColor));

        if (m_allOrganMap.contains(organId))
        {
            m_allOrganMap[organId].customColor = newColor.name().remove("#");

            //新增的空勾画颜色发生了改变，同步到默认颜色上
            if (GetColumnText(cellItemIndex._uniqueValue, ColType_Source) == tr("空勾画") && m_allOrganMap[organId].optTypeEnum == n_mtautodelineationdialog::OptType_Add)
            {
                m_allOrganMap[organId].defaultColor = m_allOrganMap[organId].customColor;
            }

            if (m_roiLabelInfoMap.contains(m_allOrganMap[organId].roiLabel))
            {
                m_roiLabelInfoMap[m_allOrganMap[organId].roiLabel].roiColor = m_allOrganMap[organId].customColor;
                m_roiLabelInfoMap[m_allOrganMap[organId].roiLabel].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
            }

            emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_Color, m_allOrganMap[organId].customColor);
        }
    }
    else if (cellItemIndex._column == ColType_Operation && m_allOrganMap.contains(organId)) //后处理参数按钮
    {
        AiModelRoiParamEditor dlg(m_allOrganMap[organId].modelId, m_allOrganMap[organId].roiParam, this);

        if (QDialog::Accepted == dlg.exec())
        {
            m_allOrganMap[organId].roiParam = dlg.getParamInfo();
            emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_Operation, m_allOrganMap[organId].roiParam);
        }
    }
    else if (cellItemIndex._column == ColType_State)
    {
        //由于由非编辑状态切换为编辑状态时，会删除并新建原有的item，因此该item的按钮状态也被重置了，这边把按钮状态先设置回非编辑状态时的状态
        if (lastEditingOrganID != m_editingOrganID)
        {
            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_State, QVariant::fromValue(ischecked));
        }

        if (!ischecked
            && QMessageBox::Yes == MtMessageBox::redWarning(this, tr("禁用操作将会使所有关联模板都从模板中移除该ROI，并且这个ROI无法再用于所有自动勾画过程，是否确定继续？"))
            && QMessageBox::Yes == MtMessageBox::redWarning(this, tr("请再次确认是否要禁用该ROI？")))
        {
            m_allOrganMap[organId].enable = ischecked;

            //从模板中移除
            for (int i = 0; i < m_modelCollectionInfoList.size(); ++i)
            {
                m_modelCollectionInfoList[i].showGroupIdMap.remove(organId);
                m_modelCollectionInfoList2[i].showGroupIdMap.remove(organId);
            }

            emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_State, ischecked ? "1" : "0");
        }
        else
        {
            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_State, QVariant::fromValue(true));

            if (m_allOrganMap[organId].enable)
            {
                return;
            }
            else
            {
                m_allOrganMap[organId].enable = ischecked;
                emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_State, "1");
            }
        }
    }

    if (m_allOrganMap[organId].optTypeEnum != n_mtautodelineationdialog::OptType_Add)
    {
        m_allOrganMap[organId].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;//记录修改
    }

    emit sigOrganInfoChanged(m_allOrganMap[organId]);
}

void RoiLibraryTable::slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText)
{
    int organID = cellItemIndex._uniqueValue.toInt();

    if (m_allOrganMap.contains(organID))
    {
        switch (cellItemIndex._column)
        {
            case ColType_Name://ROI名称
                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(m_allOrganMap[organID].customOrganName));
                    MtMessageBox::warning(this, tr("ROI名称不能为空"));
                    return;
                }

                m_allOrganMap[organID].customOrganName = newText;

                //新增的空勾画名称发生了改变，同步到默认名称上
                if (GetColumnText(cellItemIndex._uniqueValue, ColType_Source) == tr("空勾画") && m_allOrganMap[organID].optTypeEnum == n_mtautodelineationdialog::OptType_Add)
                {
                    m_allOrganMap[organID].defaultOrganName = newText;
                }

                if (m_roiLabelInfoMap.contains(m_allOrganMap[organID].roiLabel))
                {
                    m_roiLabelInfoMap[m_allOrganMap[organID].roiLabel].roiName = newText;
                    m_roiLabelInfoMap[m_allOrganMap[organID].roiLabel].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                }

                break;

            case ColType_Label://标签
                if (newText == "NONE")
                {
                    m_allOrganMap[organID].roiLabel = "";
                }
                else
                {
                    SwitchLabelReminderDialog dlg(this);
                    dlg.setMainText(tr("确定将标签切换为 \"%1\" 吗？").arg(newText));

                    if (QDialog::Accepted != dlg.exec())//还原标签
                    {
                        if (m_allOrganMap[organID].roiLabel.isEmpty())
                        {
                            UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(QString("NONE")));
                        }
                        else
                        {
                            UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(m_allOrganMap[organID].roiLabel));
                        }

                        return;
                    }
                    else
                    {
                        bool bName = dlg.isNameChecked();
                        bool bType = dlg.isTypeChecked();
                        bool bColor = dlg.isColorChecked();
                        bool bChName = dlg.isChineseNameChecked();

                        if (bName || bType || bColor || bChName)
                        {
                            //发送信息获取标签库信息
                            /*QList<n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoVec;
                            emit sigGetLabelLibraryInfo(stRoiLabelInfoVec);

                            //更新roi列表
                            if (stRoiLabelInfoVec.size() != 0)*/
                            {
                                for (const auto& labelInfoItem : /*stRoiLabelInfoVec*/m_roiLabelInfoMap.values())
                                {
                                    if (labelInfoItem.manteiaRoiLabel == newText)
                                    {
                                        if (bName)
                                        {
                                            m_allOrganMap[organID].customOrganName = labelInfoItem.roiName;
                                            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Name, QVariant::fromValue(labelInfoItem.roiName));
                                        }

                                        if (bType)
                                        {
                                            m_allOrganMap[organID].roiType = labelInfoItem.roiType;
                                            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Type, QVariant::fromValue(labelInfoItem.roiType));
                                        }

                                        if (bColor)
                                        {
                                            m_allOrganMap[organID].customColor = labelInfoItem.roiColor;
                                            QColor newColor = QString("#") + labelInfoItem.roiColor;

                                            if (newColor.isValid())
                                            {
                                                UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Color, QVariant::fromValue(newColor));
                                            }
                                        }

                                        if (bChName)
                                        {
                                            m_allOrganMap[organID].organChineseName = labelInfoItem.roiChName;
                                            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_ChName, QVariant::fromValue(labelInfoItem.roiChName));
                                        }

                                        break;
                                    }
                                }
                            }
                        }

                        m_allOrganMap[organID].roiLabel = newText;
                    }
                }

                break;

            case ColType_ChName://中文名
                m_allOrganMap[organID].organChineseName = newText == "-" ? "" : newText;

                if (m_roiLabelInfoMap.contains(m_allOrganMap[organID].roiLabel))
                {
                    m_roiLabelInfoMap[m_allOrganMap[organID].roiLabel].roiChName = m_allOrganMap[organID].organChineseName;
                    m_roiLabelInfoMap[m_allOrganMap[organID].roiLabel].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                }

                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(QString("-")));
                }

                break;

            case ColType_Type://Roi类型
                m_allOrganMap[organID].roiType = newText == "NONE" ? "" : newText;

                if (m_roiLabelInfoMap.contains(m_allOrganMap[organID].roiLabel))
                {
                    m_roiLabelInfoMap[m_allOrganMap[organID].roiLabel].roiType = m_allOrganMap[organID].roiType;
                    m_roiLabelInfoMap[m_allOrganMap[organID].roiLabel].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                }

                break;

            case ColType_Group://分组
                if (!rowGroupInfoChanging(cellItemIndex._uniqueValue, newText))
                {
                    return;
                }

                break;

            case ColType_Desc://ROI描述
                m_allOrganMap[organID].roiDesc = newText == "-" ? "" : newText;

                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(QString("-")));
                }

                break;

            default:
                break;
        }
    }

    if (m_allOrganMap[organID].optTypeEnum != n_mtautodelineationdialog::OptType_Add)
    {
        m_allOrganMap[organID].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;//记录修改
    }

    emit sigOrganInfoChanged(m_allOrganMap[organID]);
    emit sigTableInfoChanged(m_allOrganMap[organID].defaultOrganName, cellItemIndex._column, newText);
}

void RoiLibraryTable::slotGroupButtonClicked(int btnIndex, bool bChecked)
{
    if (!isTableInitialized())
    {
        MtMessageBox::NoIcon::information(this, tr("请等数据加载完成后再进行操作"));
        return;
    }

    QList<n_mtautodelineationdialog::ST_Organ> curOrganInfoList = getAllOrganInfo();
    GroupTableWidget dlg(curOrganInfoList, m_allOrganGroupList, m_modelInfoMap, this);

    if (QDialog::Accepted == dlg.exec())
    {
        //1. 先保存外层窗口确定按钮的使能状态，因为更新组列表会修改该状态，这边保存后，可进行还原状态
        //bool bChanged = false;
        //MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_frameDialog);
        //if (nullptr != parentDialog)
        //{
        //    bChanged = parentDialog->getButton(MtTemplateDialog::BtnRight1)->isEnabled();
        //}
        //2. 解析组信息
        QList<n_mtautodelineationdialog::ST_OrganGroupInfo> oldOrganGroupList;
        QList<n_mtautodelineationdialog::ST_OrganGroupInfo> newOrganGroupList;
        QList<n_mtautodelineationdialog::ST_OrganGroupInfo> delOrganGroupList;
        QList<n_mtautodelineationdialog::ST_OrganGroupInfo> curOrganGroupList = dlg.getTableListInfo();

        for (const auto& item : m_allOrganGroupList)
        {
            bool bExist = false;

            for (const auto& curItem : curOrganGroupList)
            {
                if (curItem.id == item.id)
                {
                    bExist = true;
                    break;
                }
            }

            if (!bExist)
            {
                delOrganGroupList.append(item);
            }
        }

        //3. 抛出信号，进行更新
        emit sigUpdateRoiGroup(curOrganGroupList, delOrganGroupList, newOrganGroupList);
        //4. 更新分组缓存
        oldOrganGroupList = m_allOrganGroupList;
        m_allOrganGroupList = newOrganGroupList;

        //5. 更新器官分组缓存信息
        for (int organIdItem : m_allOrganMap.keys())
        {
            for (int gidItem : m_allOrganMap[organIdItem].organGroupInfoMap.keys())
            {
                for (const auto& newGroupInfo : m_allOrganGroupList)
                {
                    if (m_allOrganMap[organIdItem].organGroupInfoMap[gidItem].id == newGroupInfo.id)
                    {
                        QString oldGroupName = m_allOrganMap[organIdItem].organGroupInfoMap[gidItem].name;
                        m_allOrganMap[organIdItem].organGroupInfoMap[gidItem] = newGroupInfo;

                        if (oldGroupName != newGroupInfo.name)
                        {
                            emit sigOrganInfoChanged(m_allOrganMap[organIdItem]);
                        }
                    }
                }
            }
        }

        //6. 暂停信号，不然更新分组列表时会抛出修改的信号
        blockSignals(true);
        //7. 重新更新列表组信息
        QStringList newGroupList;       //新分组列表
        int indexEmpty = -1;            //记录空勾画分组
        QList<int> indexAllList;        //所有的分组索引
        QList<int> indexSecGroupList;   //所有的亚组索引
        ///判断ROI是否为主结构
        auto funcIsMainStructure = [&](int organId) -> bool
        {
            for (int i = 0; i < m_allOrganGroupList.size(); ++i)
            {
                if (m_allOrganGroupList[i].type == 3 && m_allOrganGroupList[i].refOrganId == organId)
                {
                    return true;
                }
            }

            return false;
        };

        for (int i = 0; i < m_allOrganGroupList.size(); ++i)
        {
            newGroupList.append(m_allOrganGroupList[i].name);

            if (m_allOrganGroupList[i].type == 3)
            {
                indexSecGroupList.append(i);
            }

            if (tr("空勾画") == m_allOrganGroupList[i].name)
            {
                indexEmpty = i;
            }
            else
            {
                indexAllList.append(i);
            }
        }

        int nCount = GetRowCount();
        //若器官太多，则显示更新窗口，否则界面刷新太慢，会显得卡住
        MtProgressDialog* progressDlg = new MtProgressDialog();
        progressDlg->setWindowTitle(tr("正在更新列表组信息..."));
        progressDlg->setLabelText(tr("正在更新列表组信息..."));
        progressDlg->setValue(0);
        progressDlg->setCancelButtonVisible(false);
        progressDlg->setHidden(false);

        for (int i = 0; i < rowCount(); ++i)
        {
            QString strOrganId = GetRowUniqueValue(i);
            QWidget* groupWidget = GetCellWidget(strOrganId, ColType_Group);
            CustMultiSelectComboBox* gComboBox = qobject_cast<CustMultiSelectComboBox*>(groupWidget);

            if (nullptr != gComboBox)
            {
                //先获取保存当前勾选的分组
                QStringList curSelGroupList = gComboBox->currentText();
                //获取当前勾选的分组的组ID
                QList<int> groupIdList;

                for (const auto& groupItemInfo : oldOrganGroupList)
                {
                    if (curSelGroupList.contains(groupItemInfo.name))
                    {
                        groupIdList.append(groupItemInfo.id);
                    }
                }

                //转换为新的组名
                curSelGroupList.clear();

                for (const auto& groupItemInfo : newOrganGroupList)
                {
                    if (groupIdList.contains(groupItemInfo.id))
                    {
                        curSelGroupList.append(groupItemInfo.name);
                    }
                }

                //清空列表
                gComboBox->ClearItems();
                //更新列表
                gComboBox->AddItems(newGroupList);
                //设置当前选中
                gComboBox->setCurrentText(curSelGroupList);

                //设置禁用项
                if (curSelGroupList.size() == 1 && curSelGroupList[0] == tr("空勾画"))
                {
                    gComboBox->SetDisableIndex(QList<int>() << indexAllList << indexSecGroupList);
                }
                else
                {
                    if (funcIsMainStructure(strOrganId.toInt()))
                    {
                        gComboBox->SetDisableIndex(QList<int>() << indexEmpty << indexSecGroupList);
                    }
                    else
                    {
                        gComboBox->SetDisableIndex(QList<int>() << indexEmpty);
                    }
                }
            }
            else
            {
                QCustMtLabel2* groupLabel = qobject_cast<QCustMtLabel2*>(groupWidget);

                if (nullptr != groupLabel)
                {
                    //先获取保存当前勾选的分组
                    QString strOldGroupName = groupLabel->GetCurText();
                    QStringList curSelGroupList = strOldGroupName.split(";", QString::SkipEmptyParts);
                    //获取当前勾选的分组的组ID
                    QList<int> groupIdList;

                    for (const auto& groupItemInfo : oldOrganGroupList)
                    {
                        if (curSelGroupList.contains(groupItemInfo.name))
                        {
                            groupIdList.append(groupItemInfo.id);
                        }
                    }

                    //转换为新的组名
                    curSelGroupList.clear();

                    for (const auto& groupItemInfo : newOrganGroupList)
                    {
                        if (groupIdList.contains(groupItemInfo.id))
                        {
                            curSelGroupList.append(groupItemInfo.name);
                        }
                    }

                    //更新组名到列表项
                    QString strCurGroupName = curSelGroupList.join(";");
                    groupLabel->GetLabel()->setText(strCurGroupName);
                }
            }

            progressDlg->setValue((i + 1) * 100 / nCount);
            QApplication::processEvents();
        }

        //销毁进度窗口
        progressDlg->setValue(100);
        progressDlg->hide();
        delete progressDlg;
        //8. 恢复信号
        blockSignals(false);
        //9. 还原外层窗口确定按钮状态
        //if (nullptr != parentDialog)
        //{
        //    parentDialog->getButton(MtTemplateDialog::BtnRight1)->setEnabled(bChanged);
        //}
    }
}

void RoiLibraryTable::slotLabelButtonSettingClicked(int btnIndex, bool bChecked)
{
    LabelSyncSettingWidget dlg(this);
    dlg.setSync(m_bSyncInfo2Label);

    if (dlg.exec() == QDialog::Accepted)
    {
        m_bSyncInfo2Label = dlg.getSync();
    }
}

void RoiLibraryTable::slotClearTableItems()
{
    ClearDataModel();
    m_editingOrganID = -2000;
}

void RoiLibraryTable::slotCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo, bool bLast)
{
    if (m_bDestroyed)
    {
        return;
    }

    if (!organInfo.defaultOrganName.isEmpty())
    {
        addStaticRow(organInfo);
    }

    qApp->processEvents();

    if (bLast)
    {
        m_bInitialized = true;
        emit sigTableInitialized();
    }

    m_bTableItemCreated = true;
    m_cvCreateTableItem.notify_one(); // 通知Item创建完成，进行创建下一条item
}

void RoiLibraryTable::slotGroupChanged(int nIndex, int state, const QString& itemText)
{
    n_mtautodelineationdialog::MultiSelectComboBox* check_box = qobject_cast<n_mtautodelineationdialog::MultiSelectComboBox*>(sender());

    if (state == Qt::Checked)
    {
        if (tr("空勾画") == itemText || "NONE" == itemText)
        {
            check_box->setCurrentIndex(nIndex);
        }
        else
        {
            check_box->setItemsState(QStringList() << "NONE" << tr("空勾画"), Qt::Unchecked);
        }
    }
}

void RoiLibraryTable::slotCellItemClicked(const mtuiData::TableWidgetItemIndex& tableItemIndex)
{
    QString rowValue = tableItemIndex._uniqueValue;

    if (rowValue.isEmpty())
    {
        return;
    }

    if (tableItemIndex._column == ColType_CheckBox)
    {
        return;
    }

    int organID = rowValue.toInt();

    //点击的是当前编辑的行
    if (m_editingOrganID == organID)
    {
        return;
    }

    if (m_allOrganMap.contains(organID))
    {
        //将上一次编辑的行改为非编辑状态
        if (m_editingOrganID != -2000)
        {
            QString editingRowValue = QString::number(m_editingOrganID);
            int nIndex = GetRowIndex(editingRowValue);
            int state = GetCheckBoxState(nIndex, ColType_CheckBox);
            bool bHide = isRowHidden(nIndex);
            DeleteRowItem(editingRowValue);

            if (m_allOrganMap.contains(m_editingOrganID) && m_allOrganMap[m_editingOrganID].optTypeEnum != n_mtautodelineationdialog::OptType_Del)
            {
                addStaticRow(m_allOrganMap[m_editingOrganID], nIndex);
                SetCheckBoxState(editingRowValue, ColType_CheckBox, state);
                setRowHidden(nIndex, bHide);
            }
        }

        int nIndex = GetRowIndex(rowValue);
        int state = GetCheckBoxState(nIndex, ColType_CheckBox);
        //先设置行id为其他，最后再删除，否则会报错
        UpdateRowMainKey(rowValue, rowValue + "_static_id");
        addRow(m_allOrganMap[organID], nIndex);
        //记录正在编辑的id
        m_editingOrganID = organID;
        //设置当前选中行
        SetCurrentRow(QString::number(m_editingOrganID));
        //删除原有静态行
        DeleteRowItem(rowValue + "_static_id");
        //设置勾选状态
        SetCheckBoxState(rowValue, ColType_CheckBox, state);
    }
}
