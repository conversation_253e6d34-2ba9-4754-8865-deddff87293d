﻿// ************************************************************
// <remarks>
// Author      : LiuHualin
// CreateTime  : 2025-09-27
// Description : 自动勾画管理类
// </remarks>
// ************************************************************
#pragma once

#include <QObject>
#include <QDialog>

#include "IMTAutoDelineateService.h"
#include "IMTContextBase.h"
#include "Series.h"

#include "MTAutoDelineateServiceDataDefine.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include "ModelImportHelper.h"

//算法id
#define AiSketchAlgoUID                         "550e8400-e29b-41d4-a716-************"
//插件id
#define AiSketchPluginUID                       "B7A1F3E2-4C5D-4A8B-9E2F-1C3A5B7D8F9E"
//勾画任务id前缀，用于识别是否为自动勾画
#define AiSketchTaskIdPrefix                    "TASK_AUTO_SKETCH_SUPER_"
//勾画取消任务id前缀，用于识别是否为自动勾画
#define CancelAiSketchTaskIdPrefix              "TASK_AUTO_SKETCH_SUPER_CANCEL_"
//勾画方法名
#define AiSketchTaskMethodName                  "MTAC_SubmitAlgoTask"
//取消勾画方法名
#define CancelAiSketchTaskMethodName            "MTAC_CancelAlgoTask"
/// 集群任务取消报文，调用的算法服务的rpc函数
#define TaskInfoServiceRPCFuncName              "MTAS_ExecuteAlgorithmOperation"
/// 集群任务取消报文，服务类型uuid
#define TaskInfoServiceType                     "32E1CF5B-665A-4754-A9CA-FBE7F7FC52CF"


/// <summary>
/// 自动勾画管理类
/// </summary>
class MTAutoDelineateManager : public QObject, public mtcore::MTEventHandler
{
    Q_OBJECT
public:
    /// <summary>
    /// 构造函数
    /// </summary>
    MTAutoDelineateManager(mtcore::IMTContextBase* context);
    /// <summary>
    /// 析构函数
    /// </summary>
    ~MTAutoDelineateManager();

    /*自动勾画*/

    /// <summary>
    /// 注册自动勾画状态处理接口，所有的勾画状态将返回到该接口处理
    /// </summary>
    /// <param name="moduleId">模块id，用于区别不同模块，不同模块的处理方式可能不一样</param>
    /// <param name="statusCallback">状态回调函数，要使用服务同生命周期函数，调用者在回调中通过参数判断是否进一步处理</param>
    /// <returns>成功返回true</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool RegisterDelineateStatusHandler(const QString& moduleId, std::function<void(const ST_OutputAutoDelineate&)> statusCallback);
    /// <summary>
    /// 获取运行自动勾画参数
    /// </summary>
    /// <param name="stInputParam">输入参数</param>
    /// <param name="outBusinessParam">[OUT]自动勾画信息QJsonObject</param>
    /// <param name="sketchOrganVec">[OUT]选择的勾画器官信息</param>
    /// <param name="trainOrganVec">[OUT]要勾画的训练器官信息</param>
    /// <param name="emptyOrganVec">[OUT]要勾画的空器官信息</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>执行状态</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    mt_algo::EM_AlgoExecRetType GetDelineateParam(const ST_InputAutoDelineate& stInputParam
                                                  , ST_SketchBusinessInfo& outBusinessParam
                                                  , std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec
                                                  , std::vector< ST_REQ_AutoSketchSuper_Organ>& trainOrganVec
                                                  , std::vector< ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec
                                                  , std::string& errMsg);
    /// <summary>
    /// 处理监听事件总入口
    /// </summary>
    /// <param name="event">监听事件数据信息.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    virtual void HandleEvent(const mtcore::MTEvent& event) override;
    /// <summary>
    /// 注册监听的事件
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void RegisterTopics();
    /// <summary>
    /// 获取监听的事件
    /// </summary>
    /// <returns>返回监听事件字符串.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QStringList GetTopicList();
    /// <summary>
    /// 注销监听的事件
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void UnRegisterTopic();

    /*勾画设置*/

    /// <summary>
    /// 获取勾画模板设置窗口
    /// </summary>
    /// <param name="bShowRoiGroupList">是否显示ROI分组列</param>
    /// <param name="bShowUnattendUsedTip">无人值守设置时，是否启用提示</param>
    /// <param name="templateNameListWidth">模板名列宽度</param>
    /// <param name="parentWdt">父窗口</param>
    /// <param name="clientConfigPath">参数设置配置文件路径</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>模板设置窗口</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QWidget* GetDelineateTemplateSettingWidget(bool bShowRoiGroupList
                                               , bool bShowUnattendUsedTip
                                               , int templateNameListWidth
                                               , QWidget* parentWdt
                                               , const QString& clientConfigPath
                                               , QString& errMsg);
    /// <summary>
    /// 初始化勾画模板设置窗口
    /// </summary>
    /// <param name="templateWidget">GetDelineateTemplateSettingWidget接口返回的窗口对象</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void InitDelineateTemplateSettingWidget(QWidget* templateWidget);
    /// <summary>
    /// 处理勾画模板设置窗口结果
    /// </summary>
    /// <param name="templateWidget">GetDelineateTemplateSettingWidget接口返回的窗口对象</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void DealDelineteTemplateWidgetResult(QWidget* templateWidget);
    /// <summary>
    /// 勾画模板设置窗口是否处于编辑状态
    /// </summary>
    /// <param name="templateWidget">GetDelineateTemplateSettingWidget接口返回的窗口对象</param>
    /// <param name="showErrDlg">是否内部显示弹窗提示</param>
    /// <returns>true:处于编辑状态</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool IsDelineateTemplateWidgetEditState(QWidget* templateWidget, bool showErrDlg);

    /*********************ROI库设置窗口**********************/

    /// <summary>
    /// 获取模型ROI设置窗口
    /// </summary>
    /// <param name="parentWdgt">父窗口</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>设置窗口</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QWidget* GetRoiLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg);
    /// <summary>
    /// 窗口数据是否发送了修改
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:数据修改了</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool IsInfoChanged(QWidget* roiLibraryWidget);
    /// <summary>
    /// 初始化设置窗口
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <param name="organDefaultConfigInfoPath">器官默认设置配置文件路径</param>
    /// <returns>true:操作成功</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool InitRoiLibraryWidget(QWidget* roiLibraryWidget, const QString& organDefaultConfigInfoPath);
    /// <summary>
    /// 设置窗口即将销毁（因为销毁窗口时，数据可能还在加载，该接口通知服务停止加载数据）
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetRoiLibraryWidgetDestroying(QWidget* roiLibraryWidget);
    /// <summary>
    /// 保存设置数据
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <param name="progressCallback">保存进度回调，参数(<进度值, 进度信息>)</param>
    /// <returns>true:修改成功</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool SavRoiLibrarySettingData(QWidget* roiLibraryWidget);

    /*********************标签库设置窗口**********************/

    /// <summary>
    /// 获取标签库设置窗口（注意：每次调用都会新建一个窗口）
    /// </summary>
    /// <param name="parentWdgt">父窗口</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>设置窗口</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QWidget* GetLabelLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg);
    /// <summary>
    /// 窗口数据是否发送了修改
    /// </summary>
    /// <param name="labelLibraryWidget">GetLabelLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:数据修改了</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool IsLabelInfoChanged(QWidget* labelLibraryWidget);
    /// <summary>
    /// 初始化设置窗口
    /// </summary>
    /// <param name="labelLibraryWidget">GetLabelLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:操作成功</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool InitLabelLibraryWidget(QWidget* labelLibraryWidget);
    /// <summary>
    /// 保存设置数据
    /// </summary>
    /// <param name="labelLibraryWidget">GetLabelLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:修改成功</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool SavLabelLibrarySettingData(QWidget* labelLibraryWidget);
signals:
    /// <summary>
    /// 处理自动勾画响应
    /// </summary>
    /// <param name="taskId">任务id.</param>
    /// <param name="respInfo">响应信息.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SigHandleDelineateResponse(const QString& taskId, const ST_OutputAutoDelineate& respInfo);

protected slots:
    /// <summary>
    /// 处理自动勾画响应
    /// </summary>
    /// <param name="taskId">任务id.</param>
    /// <param name="respInfo">响应信息.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SlotHandleDelineateResponse(const QString& taskId, const ST_OutputAutoDelineate& respInfo);

protected:
    /// <summary>
    /// 初始化topic对应的处理函数
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void InitTopicFuncMap();
    /// <summary>
    /// 执行算法操作结果处理
    /// </summary>
    /// <param name="event">The event.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void OnExcuteModelImportResultEvent(const mtcore::MTEvent& event);
protected:
    /// <summary>
    /// 创建自动勾画弹窗
    /// </summary>
    /// <param name="sex">[IN]性别</param>
    /// <param name="series">[IN]序列信息</param>
    /// <param name="parentWidget">[IN]父窗口</param>
    /// <param name="bShowBottomFuncWidget">[IN]是否显示底部操作栏</param>
    /// <param name="outBusinessParam">[OUT]自动勾画信息QJsonObject</param>
    /// <param name="sketchOrganVec">[OUT]选择的勾画器官信息</param>
    /// <param name="trainOrganVec">[OUT]要勾画的训练器官信息</param>
    /// <param name="emptyOrganVec">[OUT]要勾画的空器官信息</param>
    /// <returns>窗口关闭方式</returns>
    QDialog::DialogCode CreateAutoDelineateDlg(const QString& sex, const Series& series, QWidget* parentWidget
                                               , bool bShowBottomFuncWidget, bool bShowTemplateBtnWidget
                                               , ST_SketchBusinessInfo& outBusinessParam
                                               , std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec
                                               , std::vector<ST_REQ_AutoSketchSuper_Organ>& trainOrganVec
                                               , std::vector<ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec);
protected:
    /// <summary>
    /// 保存勾画任务
    /// </summary>
    /// <param name="moduleId">模块id</param>
    /// <param name="seriesUid">图像id</param>
    /// <param name="taskId">任务id</param>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks>
    void SaveDelineateTaskInfo(const QString& moduleId, const QString& seriesUid, const QString& taskId);
    /// <summary>
    /// 保存勾画任务
    /// </summary>
    /// <param name="moduleId">模块id</param>
    /// <param name="seriesUid">图像id</param>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks>
    void RemoveDelineateTaskInfo(const QString& moduleId, const QString& seriesUid);
    /// <summary>
    /// 保存勾画任务
    /// </summary>
    /// <param name="moduleId">模块id</param>
    /// <param name="seriesUid">图像id</param>
    /// <retunrs>任务id</retunrs>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks>
    QString GetDelineateTaskInfo(const QString& moduleId, const QString& seriesUid);

protected:
    /// <summary>
    /// 设置roi库设置窗口信号
    /// </summary>
    /// <param name="roiLibraryWidget">设置窗口</param>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks>
    void ConnectRoiSettingWidgetSignal(QWidget* roiLibraryWidget);

protected slots:
    /// <summary>
    /// roi库设置界面导入模型
    /// </summary>
    /// <param name="modelPath">模型路径</param>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks>
    void SlotRoiLibModelImport(const QString& modelPath);
    /// <summary>
    /// ROI库设置界面删除模型
    /// </summary>
    /// <param name="modelId">模型id</param>
    /// <param name="modelName">模型名</param>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks>
    void SlotRoiLibDeleteModel(const QString& modelId, const QString& modelName);
private:
    /// <summary>
    /// 上下文
    /// </summary>
    mtcore::IMTContextBase* m_context = nullptr;
    /// <summary>
    /// topic主题对应的处理函数关系映射
    /// </summary>
    QMap<QString, std::function<void(const mtcore::MTEvent&)>> m_topicFuncMap;
    /// <summary>
    /// 客户端回调接口，key:clientId-moduleId
    /// </summary>
    QMap<QPair<QString, QString>, std::function<void(const ST_OutputAutoDelineate&)>> m_clientModuleCallbackMap;
    /// <summary>
    /// 勾画任务队列，key:<moduleId,SeriesUid>，value:taskId
    /// </summary>
    QMap<QPair<QString, QString>, QString> m_delineateTaskIdMap;
    /// <summary>
    /// 勾画界面选中的页签(1-选择ROI进行勾画  2-选择模板进行勾画)
    /// </summary>
    int m_autoSketchSelectRadioPageType = 1;
    /// <summary>
    /// 虚拟模板,用于*选择ROI进行勾画*页签进行器官提前选中,软件重开后重置
    /// </summary>
    n_mtautodelineationdialog::ST_SketchModelCollection m_stVirtualSketchCollection;
    /// <summary>
    /// 上一次的勾画模板排序
    /// </summary>
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> m_lastTemplateIdMap;
    /// <summary>
    /// roi库设置界面回调函数，key:设置界面对象指针，value:<导入模型回调, 删除模型回调>
    /// </summary>
    bool m_bRoiLibSettingWidgetInit = false;
    /// <summary>
    /// 模型导入助手
    /// </summary>
    ModelImportHelper m_modelImportHelper;
};
