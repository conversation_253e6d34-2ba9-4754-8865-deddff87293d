﻿#include "MTRoiLibraryWidget.h"
#include "ui_MTRoiLibraryWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MTRoiLibraryDialog\RoiLibrarySub\RoiLibraryWidget.h"
#include "CommonUtil.h"
#include "MultiSelectComboBox.h"
#include "MtMessageBox.h"
#include "CMtLanguageUtil.h"

namespace n_mtautodelineationdialog
{

MTRoiLibraryWidget::MTRoiLibraryWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MTRoiLibraryWidgetClass;
    ui->setupUi(this);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTRoiLibraryWidget, " << errMsg.toStdString();
        }
    }
    //
    this->installEventFilter(this);
}
MTRoiLibraryWidget::~MTRoiLibraryWidget()
{
}

void MTRoiLibraryWidget::initRoiLibrarySettingWidget(const QStringList& allRoiTypeList
                                                     , const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList
                                                     , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList
                                                     , const QList<ST_Organ>& stOrganList
                                                     , const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
                                                     , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    //先销毁释放已存在的widget
    widgetDestroying();
    //创建新的widget
    RoiLibraryWidget* roiLibraryWidget = new RoiLibraryWidget(this);
    roiLibraryWidget->setImagePathHash(m_imagePathHash);
    roiLibraryWidget->init(allRoiTypeList.isEmpty() == true ? CommonUtil::getRoiTypeList() : allRoiTypeList, stRoiLabelInfoList, allGroupList, stOrganList, modelInfoMap, modelCollectionInfoList);
    ui->verticalLayout_2->addWidget(roiLibraryWidget);
    doConnect(roiLibraryWidget);
}

void MTRoiLibraryWidget::widgetDestroying()
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* roiLibraryWidget = qobject_cast<RoiLibraryWidget*>(wdg);

    if (roiLibraryWidget)
    {
        roiLibraryWidget->deleteSafe();
        ui->verticalLayout_2->removeWidget(roiLibraryWidget);
    }
}

bool MTRoiLibraryWidget::IsModified()
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* roiLibraryWidget = qobject_cast<RoiLibraryWidget*>(wdg);

    if (nullptr != roiLibraryWidget)
    {
        return roiLibraryWidget->isNeedSave2File();
    }

    return false;
}

void MTRoiLibraryWidget::resetInfoChangeStatus()
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* roiLibraryWidget = qobject_cast<RoiLibraryWidget*>(wdg);

    if (nullptr != roiLibraryWidget)
    {
        roiLibraryWidget->resetNeedSave2FileStatus();
    }
}

void MTRoiLibraryWidget::getWidgetData(QList<ST_Organ>& outStOrganList
                                       , QList<n_mtautodelineationdialog::ST_SketchModelCollection>& outModelCollectionInfoList
                                       , QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& outRoiLabelInfoList
/*, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& outAllGroupList*/)
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* roiLibraryWidget = qobject_cast<RoiLibraryWidget*>(wdg);
    outStOrganList.clear();
    outModelCollectionInfoList.clear();
    outRoiLabelInfoList.clear();

    if (roiLibraryWidget)
    {
        //移除列表焦点，触发窗口修改信息的保存
        roiLibraryWidget->removeFocusFromTable();
        //获取缓存数据
        outStOrganList = roiLibraryWidget->getAllOrganInfo();
        outModelCollectionInfoList = roiLibraryWidget->getAllModelCollectionInfoList();
        outRoiLabelInfoList = roiLibraryWidget->getAllLabelInfoList();
        //outAllGroupList = roiLibraryWidget->getAllOrganGroupInfo();
    }
}
/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTRoiLibraryWidget::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

bool MTRoiLibraryWidget::eventFilter(QObject* obj, QEvent* event)
{
    //点击保存和取消按钮时，让焦点设置到列表上，使列表输入框失去焦点而触发编辑完成事件
    //if (obj == getButton(MtTemplateDialog::BtnRight1))
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent* mouse_event = static_cast<QMouseEvent*>(event);

            if (mouse_event->button() == Qt::LeftButton)
            {
                for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
                {
                    QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

                    if (item->widget())
                    {
                        RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(item->widget());

                        if (tabWidget)
                        {
                            tabWidget->removeFocusFromTable();
                        }
                    }
                }
            }
        }
    }
    return QWidget::eventFilter(obj, event);
}

void MTRoiLibraryWidget::doConnect(QWidget* widget)
{
    RoiLibraryWidget* roiLibraryWidget = qobject_cast<RoiLibraryWidget*>(widget);
    connect(this, &MTRoiLibraryWidget::sigModelImportProgress, roiLibraryWidget, &RoiLibraryWidget::sigModelImportProgress);
    connect(this, &MTRoiLibraryWidget::sigModelImportFinish, roiLibraryWidget, &RoiLibraryWidget::sigModelImportFinish);
    connect(this, &MTRoiLibraryWidget::sigModelImportResult, roiLibraryWidget, &RoiLibraryWidget::sigModelImportResult);
    connect(this, &MTRoiLibraryWidget::sigModelDeleteResult, roiLibraryWidget, &RoiLibraryWidget::sigModelDeleteResult);
    connect(roiLibraryWidget, &RoiLibraryWidget::sigGetLabelLibraryInfo, this, [&](QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)
    {
        emit sigGetLabelLibraryInfo(stRoiLabelInfoVec);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigGetOrganDefaultInfo, this, [&](QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
    {
        emit sigGetOrganDefaultInfo(stOrganDefaultList);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigModelImport, this, [&](const QString& modelPath)
    {
        emit sigModelImport(modelPath);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigDeleteModel, this, [&](const QString& modelId, const QString& modelName)
    {
        emit sigDeleteModel(modelId, modelName);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigSaveModelInfo, this, [&](const QString& modelId, const QString& modelName, const QString& desc, int& result)
    {
        emit sigSaveModelInfo(modelId, modelName, desc, result);
    });
    //连接列表组取消信号
    connect(roiLibraryWidget->getTableWidget(), &RoiLibraryTable::sigRemoveRoiRelatedGroup, this, [&](int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int, QString>& refModelInfoMap)
    {
        emit sigRemoveRoiRelatedGroup(roiID, roiName, groupID, groupName, refModelInfoMap);
    });
    //连接分组信息更新信号
    connect(roiLibraryWidget, &RoiLibraryWidget::sigUpdateRoiGroup, this, [&](const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList, const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList)
    {
        emit sigUpdateRoiGroup(curGroupList, delGroupList, updtedGroupList);
    });
}
QWidget* MTRoiLibraryWidget::getContentWidget()
{
    for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
    {
        QLayoutItem* item = ui->verticalLayout_2->itemAt(i);
        RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(item->widget());

        if (tabWidget)
        {
            return item->widget();
        }
    }

    return nullptr;
}
}
