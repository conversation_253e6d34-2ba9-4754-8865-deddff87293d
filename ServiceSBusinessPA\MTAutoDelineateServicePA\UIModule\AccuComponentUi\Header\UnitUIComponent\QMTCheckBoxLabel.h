﻿#pragma once

#include <QWidget>
#include "MtCheckBox.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"



//QMTCheckBoxLabel 参数
class  QMTCheckBoxLabelParam : public ICellWidgetParam
{
public:
    Qt::CheckState _state = Qt::Unchecked;
    bool _bRead = false;                    //是否已读

    QMTCheckBoxLabelParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTCheckBoxLabelParam)

namespace Ui
{
class QMTCheckBoxLabel;
}

/*checkbox 和 label组合widget
1.支持小红点显示
*/
class  QMTCheckBoxLabel : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QMTCheckBoxLabel(QWidget* parent = Q_NULLPTR);
    ~QMTCheckBoxLabel();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口

    /************checkbox相关接口******************/
    MtCheckBox* GetCheckBox();
    void SetCheckBoxStyle(QString);         //设置样式
    void SetCheckBoxText(QString);          //设置文案
    void SetCheckBoxState(Qt::CheckState);  //设置状态
    int GetCheckBoxState();                 //获取状态

    /************clabel相关接口******************/
    void SetLabelStyle(QString);         //设置样式
    void SetLabelPix(QString pixPath);   //设置图片路径
    void SetLabelText(QString text);     //设置文案
    void SetRead(bool bRead);            //设置是否已读,未读有小红点，已读没有小红点
signals:
    void sigStateChanged(int);      //checkbox状态变化了

private slots:
    void slotCheckBoxStateChange(int);

protected:
    void InitTemplateStyle();
private:
    Ui::QMTCheckBoxLabel* ui;
};
