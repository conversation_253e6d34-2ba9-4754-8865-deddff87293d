﻿#include "RoiLabelSettingDialog.h"
#include "MtRangeLineEdit.h"
#include "MtToolButton.h"
#include "DataDefine/InnerStruct.h"
#include "MtMessageBox.h"

/// <summary>
/// 构造函数
/// </summary>
/// <param name="roiTypeList">[IN]roiType集合(为空则采用内置类型集合)</param>
/// <param name="allRoiLibraryMap">[IN]已存在的manteiaRoiLabel集合(key-manteiaRoiLabel)</param>
RoiLabelSettingDialog::RoiLabelSettingDialog(const QStringList& roiTypeList, const QMap<QString, n_mtautodelineationdialog::ST_RoiLabelInfo>& allRoiLibraryMap, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setTitle(tr("新增标签"));                     //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    this->setDialogWidthAndContentHeight(466, 156); //弹窗的宽度 内容的高度
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //初始化数据
    m_allRoiLibraryMap = allRoiLibraryMap;
    //初始化UI
    {
        ui.lineEdit_label->setRegExpression(RegExp_CharNumber4);
        //ui.lineEdit_roiName->setRegExpression(RegExp_CharNumber4);
        ui.lineEdit_roiAlias->setRegExpression(RegExp_CharNumber4);
        ui.mtComboBox_roiType->addItems(roiTypeList);
        ui.mtComboBox_roiType->setCurrentText("ORGAN");
        ui.mtColorButton->setIndicatorColor(QColor(255, 0, 0));
        ui.mtColorButton->setIndicatorHeight(16);
        ui.mtColorButton->setIndicatorWidth(36);
    }
}

RoiLabelSettingDialog::~RoiLabelSettingDialog()
{
}

/// <summary>
/// 获取最新的manteiaRoiLabel信息
/// </summary>
/// <param name="isPreDelete">[OUT]新增的这个之前是否是被删除的</param>
void RoiLabelSettingDialog::getNewRoiLabelInfo(QString& manteiaRoiLabel, QString& roiName, QString& roiAlias, QString& roiColor, QString& roiType, bool& isPreDelete)
{
    manteiaRoiLabel = ui.lineEdit_label->text();
    //roiName = ui.lineEdit_roiName->text();
    roiAlias = ui.lineEdit_roiAlias->text();
    roiColor = ui.mtColorButton->indicatorColor().name().remove("#");
    roiType = ui.mtComboBox_roiType->currentText();
    isPreDelete = false;

    if (m_allRoiLibraryMap.contains(manteiaRoiLabel.toLower()) == true &&
        m_allRoiLibraryMap[manteiaRoiLabel.toLower()].optTypeEnum == n_mtautodelineationdialog::OptType_Del)
    {
        isPreDelete = true;
        return;
    }
}

void RoiLabelSettingDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void RoiLabelSettingDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void RoiLabelSettingDialog::onBtnRight1Clicked()
{
    if (ui.lineEdit_label->text().isEmpty())
    {
        //ui.lineEdit_label->setWarningBorderStatus(tr("未填写标签"));
        MtMessageBox::NoIcon::information_Title(this, tr("标签不能为空"));
        return;
    }

    /*if (ui.lineEdit_roiName->text().isEmpty())
    {
        ui.lineEdit_roiName->setWarningBorderStatus(tr("未填写ROI名称"));
        return;
    }*/

    if (m_allRoiLibraryMap.contains(ui.lineEdit_label->text().toLower()) == true &&
        m_allRoiLibraryMap[ui.lineEdit_label->text().toLower()].optTypeEnum != n_mtautodelineationdialog::OptType_Del)
    {
        //ui.lineEdit_label->setWarningBorderStatus(tr("标签名已存在"));
        MtMessageBox::NoIcon::information_Title(this, tr("标签名已存在"));
        return;
    }

    this->accept();
}