﻿#include "QCustMtComboBox2.h"
#include "ui_QCustMtComboBox2.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include <QMouseEvent>
#include <QPainter>
#include "CMtCoreDefine.h"
#include "MtComboBox.h"



/*********************单元组件的Param*********************************/
QCustMtComboBox2Param::QCustMtComboBox2Param()
{
    _cellWidgetType = DELEAGATE_QCustMtComboBox;
}

QCustMtComboBox2Param::~QCustMtComboBox2Param()
{
}
QWidget* QCustMtComboBox2Param::CreateUIModule(QWidget* parent)
{
    QCustMtComboBox2* comboBox = new QCustMtComboBox2(parent, _bEnabaleDrawSquare, m_newStyle);
    comboBox->SetupCellWidget(*this);
    return comboBox;
}
/*****************************************************************/


/********************单元组件****************************/
QCustMtComboBox2::QCustMtComboBox2(QWidget* parent, bool bEnabaleDrawSquare/* = false*/, bool bNewStyle/* = false*/)
    : QWidget(parent)
{
    ui = new Ui::QCustMtComboBox2;
    ui->setupUi(this);

    if (false == bEnabaleDrawSquare)
    {
        m_comboBox = new MtComboBox(this);
    }
    else
    {
        m_comboBox = new MtComboBoxDrawSquareColor2(this);
    }

    if (bNewStyle)
    {
        //delete m_comboBox;
        //m_comboBox = new NoIconComboBox(this);
        //m_comboBox->setMtType(MtComboBox::combobox2_1);
        m_delegate = new QCustMtComboBox2Delegate(m_comboBox);
        m_comboBox->setItemDelegate(m_delegate);
    }

    if (nullptr != m_delegate)
    {
        connect(m_comboBox, &MtComboBox::SigPopup, [&]()
        {
            QStringList itemStrList = GetAllItemStrList();
            int index = itemStrList.indexOf(GetCurText());

            if (nullptr != m_delegate)
            {
                m_delegate->setLastSelectedIndex(index);
            }
        });
    }

    ui->horizontalLayout->addWidget(m_comboBox);
    m_comboBox->setMtType(MtComboBox::MtType::combobox1);
    m_comboBox->setElideMode(Qt::ElideRight);
    m_comboBox->setViewTextElideMode(Qt::ElideRight);
    m_comboBox->setEnabledWheelEvent(false);
    m_comboBox->installEventFilter(this);

    if (bNewStyle)
    {
        m_comboBox->setMtType(MtComboBox::MtType::combobox2_1);
    }
}

QCustMtComboBox2::~QCustMtComboBox2()
{
    disconnect(m_comboBox, &MtComboBox::currentTextChanged, this, &QCustMtComboBox2::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
    disconnect(this, &QCustMtComboBox2::sigUIUpdated, m_delegate, &QCustMtComboBox2Delegate::SlotCheckIndexChanged);
    MT_DELETE(m_comboBox);
    MT_DELETE(ui);
}

void QCustMtComboBox2::SetupCellWidget(QCustMtComboBox2Param& cellWidgetParam)
{
    AddItems(cellWidgetParam._textList, cellWidgetParam._userDataList);

    //为Max和Min选项添加蓝绿icon
    for (int i = 0; i < cellWidgetParam.listMaxMin.size(); ++i)
    {
        if (cellWidgetParam.listMaxMin[i] == 0)
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/icon_min.png"), Qt::DecorationRole);
        }
        else if (cellWidgetParam.listMaxMin[i] == 1)
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/icon_max.png"), Qt::DecorationRole);
        }
        else
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/icon_blank.png"), Qt::DecorationRole);
        }
    }

    disconnect(m_comboBox, &MtComboBox::currentTextChanged, this, &QCustMtComboBox2::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));

    if (cellWidgetParam._comboBoxIndex >= 0)
    {
        m_comboBox->setCurrentIndex(cellWidgetParam._comboBoxIndex);
    }
    else if (cellWidgetParam._text.size() >= 0 && cellWidgetParam._textList.indexOf(cellWidgetParam._text) >= 0)
    {
        m_comboBox->setCurrentText(cellWidgetParam._text);
    }
    else
    {
        m_comboBox->setCurrentIndex(-1);
    }

    connect(m_comboBox, &MtComboBox::currentTextChanged, this, &QCustMtComboBox2::slotCurrentTextChanged);
    connect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));

    if (cellWidgetParam.m_newStyle)
    {
        QString styleStr = "QComboBox QAbstractItemView::item:hover { background-color: rgba(@color2,1); }";
        CMtCoreWidgetUtil::formatStyleSheet(styleStr);
        m_comboBox->setStyleSheet(styleStr);

        if (nullptr != m_delegate)
        {
            m_delegate->setLastSelectedIndex(m_comboBox->currentIndex());
        }
    }
}

bool QCustMtComboBox2::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        QString preText = m_comboBox->currentText();
        m_comboBox->blockSignals(true);

        if (text != preText)    //防止多次抛出信号
        {
            QStringList itemStrList = GetAllItemStrList();

            if (itemStrList.indexOf(text) < 0)
            {
                m_comboBox->setCurrentIndex(-1);
            }
            else
            {
                m_comboBox->setCurrentText(text);

                if (nullptr != m_delegate)
                {
                    int index = itemStrList.indexOf(text);
                    m_delegate->setLastSelectedIndex(index);
                    emit sigUIUpdated(index);
                }
            }
        }

        m_comboBox->blockSignals(false);
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        m_comboBox->setEnabled(bEditEnable);
        return true;
    }
    else if (QMetaType::Int == userType)
    {
        bool index = updateData.toInt();
        m_comboBox->setCurrentIndex(index);

        if (nullptr != m_delegate)
        {
            m_delegate->setLastSelectedIndex(index);
        }

        return true;
    }

    return false;
}

QString QCustMtComboBox2::GetCurText()
{
    return m_comboBox->currentText();
}

QString QCustMtComboBox2::currentText()
{
    return m_comboBox->currentText();
}

void QCustMtComboBox2::SetEnableEdit(bool bEdit)
{
    m_comboBox->setEnabled(bEdit);
}

void QCustMtComboBox2::setCurrentIndex(int index)
{
    m_comboBox->setCurrentIndex(index);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(index);
    }
}

void QCustMtComboBox2::setCurrentText(const QString& text)
{
    m_comboBox->setCurrentText(text);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(m_comboBox->currentIndex());
    }
}

QStringList QCustMtComboBox2::GetAllItemStrList()
{
    QStringList itemStrList;

    for (int idx = 0; idx < m_comboBox->count(); idx++)
    {
        QString itemName = m_comboBox->itemText(idx);
        itemStrList << itemName;
    }

    return itemStrList;
}

void QCustMtComboBox2::AddItem(const QString& itemStr, const QVariant& auserData)
{
    m_comboBox->addItem(itemStr, auserData);
}

void QCustMtComboBox2::AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList/* = QList<QVariant>()*/)
{
    if (0 == userDataList.size() || itemStrList.size() != userDataList.size())
    {
        m_comboBox->addItems(itemStrList);
    }
    else if (itemStrList.size() == userDataList.size())
    {
        for (int i = 0; i < itemStrList.size(); ++i)
        {
            m_comboBox->addItem(itemStrList[i], userDataList[i]);
        }
    }
}

void QCustMtComboBox2::RemoveItem(const QString& itemStr)
{
    QStringList itemStrList = GetAllItemStrList();
    int index = itemStrList.indexOf(itemStr);
    m_comboBox->removeItem(index);
}

void QCustMtComboBox2::ClearItems()
{
    m_comboBox->clear();
}

void QCustMtComboBox2::RegisterSquareColor(int index, const QColor& color)
{
    MtComboBoxDrawSquareColor2* drawComboBox = qobject_cast<MtComboBoxDrawSquareColor2*>(m_comboBox);

    if (nullptr == drawComboBox)
    {
        return;
    }

    drawComboBox->RegisterSquareColor(index, color);
}

void QCustMtComboBox2::UnRegisterSquareColor(int index)
{
    MtComboBoxDrawSquareColor2* drawComboBox = qobject_cast<MtComboBoxDrawSquareColor2*>(m_comboBox);

    if (nullptr == drawComboBox)
    {
        return;
    }

    drawComboBox->UnRegisterSquareColor(index);
}

MtComboBox* QCustMtComboBox2::GetMtComboBox()
{
    return m_comboBox;
}

bool QCustMtComboBox2::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (m_comboBox == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
        }
    }

    return QWidget::eventFilter(obj, evt);
}

void QCustMtComboBox2::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void QCustMtComboBox2::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit sigClicked(1);
    }

    QWidget::mousePressEvent(event);
}

void QCustMtComboBox2::slotCurrentTextChanged(const QString& text)
{
    emit currentTextChanged(text);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(GetAllItemStrList().indexOf(text));
    }
}

void QCustMtComboBox2::slotCurrentIndexChanged(int index)
{
    emit currentIndexChanged(index);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(index);
    }
}

MtComboBoxDrawSquareColor2::MtComboBoxDrawSquareColor2(QWidget* parent /*= Q_NULLPTR*/)
    : MtComboBox(parent)
{
}

MtComboBoxDrawSquareColor2::~MtComboBoxDrawSquareColor2()
{
}

void MtComboBoxDrawSquareColor2::RegisterSquareColor(int index, const QColor& color)
{
    m_indexSquareColorMap.insert(index, color);
}

void MtComboBoxDrawSquareColor2::UnRegisterSquareColor(int index)
{
    m_indexSquareColorMap.remove(index);
}

void MtComboBoxDrawSquareColor2::paintEvent(QPaintEvent* event)
{
    int index = this->currentIndex();

    if (index < 0 || false == m_indexSquareColorMap.contains(index))
    {
        MtComboBox::paintEvent(event);
        return;
    }

    MtComboBox::paintEvent(event);
    DrawSquareAppendText(event);
}

void MtComboBoxDrawSquareColor2::DrawSquareAppendText(QPaintEvent* event)
{
    int index = this->currentIndex();

    if (false == m_indexSquareColorMap.contains(index))
    {
        return;
    }

    QFontMetrics fm(qApp->font());
    int itemWidth = 10;

    //查找所有下拉框的长度
    do
    {
        for (int idx = 0; idx < this->count(); idx++)
        {
            QString itemName = this->itemText(idx);
            int tmpItemWidth = fm.width(itemName);

            if (tmpItemWidth > itemWidth)
            {
                itemWidth = tmpItemWidth;
            }
        }
    }
    while (0);

    QSize size = this->size();
    QPainter painter(this);
    painter.save();
    QColor color = m_indexSquareColorMap.value(index);
    painter.setPen(Qt::NoPen);

    if (false == this->isEnabled())
    {
        color.setAlpha(153);
    }
    else
    {
    }

    painter.setBrush(color);
    int width = 10;
    int height = 10;
    int x = itemWidth + 24;

    if (x > (size.width() - width))
    {
        x = size.width() / 2 - width / 2;
    }

    int y = size.height() / 2 - height / 2;
    QRectF rectangle(x, y, width, height);
    painter.drawRoundedRect(rectangle, 2, 2);
    painter.restore();
}





