﻿// *********************************************************************************
// <remarks>
// FileName    : MTUnattendedDialog
// Author      : zlw
// CreateTime  : 2023-12-15
// Description : 无人值守设置弹窗
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTUnattendedWidgetClass;
}

namespace n_mtautodelineationdialog
{

class MTUnattendedWidget : public QWidget, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    MTUnattendedWidget(QWidget* parent = nullptr);
    ~MTUnattendedWidget();

    /// <summary>
    /// 设置默认导出地址
    /// </summary>
    /// <param name="stExportAddr">[IN]默认导出地址</param>
    void setDefaultExportAddr(const ST_AddrSimple& stExportAddr);

    /// <summary>
    /// 设置右侧规则列表最小高度
    /// </summary>
    /// <param name="minHeightNum">[IN]最小高度</param>
    void setMinHeight_widget_table_sketch(const int minHeightNum);

    /// <summary>
    /// 显示无人值守设置弹窗
    /// </summary>
    /// <param name="unattendedConfigMap">[IN]无人值守信息集合(key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息)</param>
    /// <param name="allTemplateNameMap">[IN]所有无人值守勾画模板id-模板名称集合(key-模板id value-模板名称)</param>
    /// <param name="allRemoteScpInfoList">[IN]所有远程服务器信息</param>
    /// <param name="allLocalServerNameMap">[IN]所有本地服务器名称集合(key-serverType value-serverName)</param>
    /// <param name="stCallBackUnattended">[OUT]数据回调</param>
    void init(
        const QMap<QString, ST_UnattendedConfig>& unattendedConfigMap,
        const QMap<EM_OptDcmType, QMap<int, QString>>& allUnattendTemplateNameMap,
        const QList<ST_AddrSimple> allRemoteScpInfoList,
        const QMap<int, QStringList>& allLocalServerNameMap,
        ST_CallBack_Unattended& stCallBackUnattended
    );

    /// <summary>
    /// 是否处于编辑状态
    /// </summary>
    /// <param name="showErrDlg">[IN]是否内部弹出请保存模板的弹窗</param>
    /// <returns>true是</returns>
    bool isEditState(bool showErrDlg = false);

    /// <summary>
    /// 获取默认导出地址
    /// </summary>
    ST_AddrSimple getDefaultExportAddr();

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

private:
    Ui::MTUnattendedWidgetClass* ui;
    void* m_ptrOptUnattendData = nullptr;       //无人值守信息
};

}
