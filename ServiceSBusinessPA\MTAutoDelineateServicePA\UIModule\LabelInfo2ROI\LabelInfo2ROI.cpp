﻿#include "LabelInfo2ROI.h"
#include "CMtLanguageUtil.h"

LabelInfo2ROI::LabelInfo2ROI(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //
    this->setMainLayout(ui.verticalLayout);
    this->setTitle(tr("提示"));
    this->setFixedSize(CMtLanguageUtil::type == english ? 392 : 487, 200);
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(false);
    this->setAllowDrag(true);
    ///
    connect(ui.mtCheckBox_name, &MtCheckBox::stateChanged, this, &LabelInfo2ROI::SlotOperationStateChanged);
    connect(ui.mtCheckBox_chName, &MtCheckBox::stateChanged, this, &LabelInfo2ROI::SlotOperationStateChanged);
    connect(ui.mtCheckBox_type, &MtCheckBox::stateChanged, this, &LabelInfo2ROI::SlotOperationStateChanged);
    connect(ui.mtCheckBox_color, &MtCheckBox::stateChanged, this, &LabelInfo2ROI::SlotOperationStateChanged);
    //去掉了roi名称，先隐藏
    ui.mtCheckBox_name->hide();

    if (CMtLanguageUtil::type == english)
    {
        ui.mtCheckBox_chName->hide();
    }
}

LabelInfo2ROI::~LabelInfo2ROI()
{
}

unsigned LabelInfo2ROI::GetSyncType()
{
    unsigned ret = Type_None;

    if (ui.mtCheckBox_name->isChecked())
    {
        ret |= Type_Name;
    }

    if (ui.mtCheckBox_type->isChecked())
    {
        ret |= Type_Type;
    }

    if (ui.mtCheckBox_color->isChecked())
    {
        ret |= Type_Color;
    }

    if (ui.mtCheckBox_chName->isChecked())
    {
        ret |= Type_ChName;
    }

    return ret;
}

void LabelInfo2ROI::onBtnCloseClicked()
{
    onBtnRight2Clicked();
}

void LabelInfo2ROI::onBtnRight2Clicked()
{
    this->reject();
}

void LabelInfo2ROI::onBtnRight1Clicked()
{
    //关闭文件
    this->accept();
}

void LabelInfo2ROI::SlotOperationStateChanged(int state)
{
    if (Type_None != GetSyncType())
    {
        this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(true);
    }
    else
    {
        this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(false);
    }
}
