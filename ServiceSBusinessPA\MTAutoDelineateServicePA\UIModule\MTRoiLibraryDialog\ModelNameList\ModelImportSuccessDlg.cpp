﻿#include "ModelImportSuccessDlg.h"
#include <QAbstractButton>

ModelImportSuccessDlg::ModelImportSuccessDlg(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::ModelImportSuccessDlg;
    ui->setupUi(this);
    //
    this->setMainLayout(ui->verticalLayout);
    this->setTitle(tr("提示"));
    this->setFixedSize(522, 130);
    //this->setMargin(24, 18, 24, 20);
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("关闭"));
    this->getButton(MtTemplateDialog::BtnRight2)->setVisible(false);
    this->setAllowDrag(true);
}

ModelImportSuccessDlg::~ModelImportSuccessDlg()
{
    if (ui != nullptr)
    {
        delete ui;
    }
}

void ModelImportSuccessDlg::setIconPath(const QString& pixmapPath)
{
    ui->mtLabel_successIcon->setPixmap(pixmapPath);
}

void ModelImportSuccessDlg::onBtnRight1Clicked()
{
    this->accept();
}

void ModelImportSuccessDlg::onBtnRight2Clicked()
{
    this->reject();
}

void ModelImportSuccessDlg::onBtnCloseClicked()
{
    this->reject();
}

