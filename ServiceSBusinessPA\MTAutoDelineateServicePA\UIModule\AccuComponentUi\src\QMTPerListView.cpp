﻿#include "AccuComponentUi\Header\QMTPerListView.h"
#include "ui_QMTPerListView.h"
#include "CMtCoreDefine.h"
#include <QLabel>
#include <QJsonObject>
#include <QScrollBar>
//#include "MExcel.h"
#include "MtMessageBox.h"
#include "AccuComponentUi\Header\Language.h"
#include "AccuComponentUi\Header\QMTAbstractTableView.h"

//void NoFocusDelegate::paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const
//{
//    QStyleOptionViewItem itemOption(option);
//
//    if (itemOption.state & QStyle::State_HasFocus)
//    {
//        itemOption.state = itemOption.state ^ QStyle::State_HasFocus;
//    }
//
//    QStyledItemDelegate::paint(painter, itemOption, index);
//}


QMTPerListView::QMTPerListView(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTPerListView;
    ui->setupUi(this);
    _tableWidget = ui->tableWidget;
    ///
    //InitTableWidget();
    /////
    connect(_tableWidget, SIGNAL(cellClicked(int, int)), this, SLOT(slotCellClicked(int, int)));
    connect(_tableWidget, SIGNAL(currentCellChanged(int, int, int, int)), this, SLOT(slotCurrentCellChanged(int, int, int, int)));
    connect(_tableWidget, SIGNAL(cellDoubleClicked(int, int)), this, SLOT(slotCellDoubleClicked(int, int)));
}

QMTPerListView::~QMTPerListView()
{
    MT_DELETE(ui);
}

void QMTPerListView::InitTableWidget()
{
    _tableWidget->setStyleSheet("background-color: rgb(37, 41, 48);");
    _tableWidget->setColumnCount(_defaultColumn); //设置列数
    _tableWidget->horizontalHeader()->setDefaultSectionSize(150);
    _tableWidget->horizontalHeader()->setDefaultAlignment(Qt::AlignLeft | Qt::AlignVCenter);//表头文案对齐方式
    _tableWidget->horizontalHeader()->setSectionsClickable(false); //设置表头不可点击（默认点击后进行排序）
    //设置表头内容
    _tableWidget->setHorizontalHeaderLabels(_headersList);
    //设置表头字体加粗
    //QFont font = _tableWidget->horizontalHeader()->font();
    //font.setBold(true);
    //_tableWidget->horizontalHeader()->setFont(font);
    _tableWidget->horizontalHeader()->setStretchLastSection(true); //设置充满表宽度
    _tableWidget->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);
    _tableWidget->verticalHeader()->setDefaultSectionSize(260); //设置行高
    _tableWidget->verticalHeader()->setMinimumSectionSize(_perRowHeight); //设置行高
    //_tableWidget->setFrameShape(QFrame::NoFrame); //设置无边框
    _tableWidget->setShowGrid(false); //设置不显示格子线
    _tableWidget->verticalHeader()->setVisible(false); //设置垂直头不可见
    _tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);  //可多选（Ctrl、Shift、  Ctrl+A都能够）
    _tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);  //设置选择行为时每次选择一行
    _tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers); //设置不可编辑

    for (int i = 0; i < _defaultColumn; ++i)
    {
        int width = _columnWidthMap.value(i);

        if (width > 0)
        {
            //_tableWidget->horizontalHeader()->resizeSection(i, width); //设置表头的宽度
            ResizeHorizontalSection(i, width);
        }
    }

    _tableWidget->horizontalHeader()->setFixedHeight(30); //设置表头的高度
    //_tableWidget->setStyleSheet("selection-background-color:lightblue;"); //设置选中背景色
    //_tableWidget->setStyleSheet("QTableWidget:hover { border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:0px;border-color:rgb(83,140,182) }");
    _tableWidget->setStyleSheet(
        //"QTableWidget::Item{border:0px solid rgb(255,0,0);border-bottom:1px solid rgb(255,0,0);}"
        "QTableWidget{color: #fff;font:12px;background-color: rgb(44,50,61);selection-background-color:rgb(55, 64, 80);}"
    );
    this->setStyleSheet("QLabel{padding-left:10px;}");
    _tableWidget->horizontalHeader()->setStyleSheet("QHeaderView::section{background:rgb(44,50,61); color:white;font:12px;padding-left:10px;border:1px solid rgba(146, 155, 170, 0.16);}"); //设置表头背景色
    //设置水平、垂直滚动栏样式
    QScrollBar* horScrollBar = _tableWidget->horizontalScrollBar();
    horScrollBar->setProperty("mtType", "scrollbar1");
    this->style()->unpolish(horScrollBar);
    this->style()->polish(horScrollBar);
    QScrollBar* verScrollBar = _tableWidget->verticalScrollBar();
    verScrollBar->setProperty("mtType", "scrollbar1");
    this->style()->unpolish(verScrollBar);
    this->style()->polish(verScrollBar);
#if 0
    _tableWidget->horizontalScrollBar()->setStyleSheet("QScrollBar{width:20px;background:rgb(37, 41, 48);margin:0px, 0px, 0px, 0px;padding-top:0px;padding-bottom:0px;height:6px;}"
                                                       "QScrollBar::handle{width:20px;background:rgb(91, 100, 116);border-radius:3px;height:6px;}"
                                                       "QScrollBar::handle:hover{width:20px;background:rgba(91, 100, 116, 0.8);border-radius:1px;height:6px;}"
                                                       "QScrollBar::add-page{background:rgb(37,41,48);border-radius:3px;}"
                                                       "QScrollBar::sub-page{background:rgb(37,41,48);border-radius:3px;}"
                                                       "QScrollBar::sub-line{background:rgb(37,41,48);border-radius:3px;}"
                                                       "QScrollBar::add-line{background:rgb(37,41,48);border-radius:3px;}");
    _tableWidget->verticalScrollBar()->setStyleSheet("QScrollBar{width:6px;min-height:20px;background:rgb(37, 41, 48);margin:0px, 0px, 0px, 0px;padding-top:0px;padding-bottom:0px;}"
                                                     "QScrollBar::handle{width:6px;background:rgb(91, 100, 116);border-radius:3px;min-height:20px;}"
                                                     "QScrollBar::handle:hover{width:6px;background:rgba(91, 100, 116, 0.8);border-radius:1px;min-height:20px;}"
                                                     "QScrollBar::add-page{background:rgb(37,41,48);border-radius:3px;}"
                                                     "QScrollBar::sub-page{background:rgb(37,41,48);border-radius:3px;}"
                                                     "QScrollBar::sub-line{background:rgb(37,41,48);border-radius:3px;}"
                                                     "QScrollBar::add-line{background:rgb(37,41,48);border-radius:3px;}");
#endif
    //_tableWidget->setItemDelegate(new NoFocusDelegate());//不显示虚线
    //点击表时不正确表头行光亮（获取焦点）
    _tableWidget->horizontalHeader()->setHighlightSections(false);
}

void QMTPerListView::ResizeHorizontalSection(int index, int size)
{
    _tableWidget->horizontalHeader()->resizeSection(index, size); //设置表头的宽度
}

void QMTPerListView::InitAdjustTableWidget()
{
    /////
    _headersList << tr("模态") << tr("图象数") << tr("拍摄日期") << tr("序列") << tr("序列描述") << tr("检查描述");
    ////
    _mainKey = TOString(seriesUID);
    ////
    _columnKeyMap.insert(0, TOString(Modality));
    _columnKeyMap.insert(1, TOString(numberOfImages));
    _columnKeyMap.insert(2, TOString(seriesDate));
    _columnKeyMap.insert(3, TOString(seriesNumber));
    _columnKeyMap.insert(4, TOString(seriesDescription));
    _columnKeyMap.insert(5, TOString(studyDescription));
    ////
#if 0
    _columnWidthMap.insert(0, 70);
    _columnWidthMap.insert(1, 100);
    _columnWidthMap.insert(2, 100);
    _columnWidthMap.insert(3, 95);
    _columnWidthMap.insert(4, 240);
#endif
    ////
    _colDelegateTypeMap.insert(0, 1);
}

void QMTPerListView::SetDataModel(QJsonArray dataJsonArray)
{
    //QMTTools::getInstance()->WaitForTryLock(_mutex);
    //QTableWidgetItem* item;
    for (int i = 0; i < dataJsonArray.size(); ++i)
    {
        QJsonObject jsonObj = dataJsonArray.at(i).toObject();
        QVariant value = jsonObj.value(_mainKey).toVariant();
        QVariantHash hash;
        hash.insert(_mainKey, value);
        _tableWidgetModelList.append(hash);
        AddRowItemWidget(jsonObj);
    }

    // _mutex.unlock();
}

void QMTPerListView::SetColumnCount(int columns)
{
    _defaultColumn = columns;
}

void QMTPerListView::SetKeyMap(QMap<int, QString> map)
{
    _columnKeyMap = map;
}

void QMTPerListView::SetColumnWidth(int column, int width)
{
    _columnWidthMap.insert(column, width);
}

bool QMTPerListView::SetHorizontalHeaderList(QStringList value)
{
    if (value.size() != _defaultColumn)
        return false;

    _headersList = value;
    return true;
}

void QMTPerListView::SetHorizontalHeaderVisible(bool isvisible)
{
    _tableWidget->horizontalHeader()->setVisible(isvisible); //设置垂直头不可见
}

void QMTPerListView::SetPerRowHeight(int value)
{
    _perRowHeight = value;
}

void QMTPerListView::SetMainKey(QString key)
{
    _mainKey = key;
}

QVariantHash QMTPerListView::FromJsonObject(QJsonObject recordObj)
{
    return recordObj.toVariantHash();
}

QString QMTPerListView::GetModalityPixPath(QString modality, bool isReaded)
{
    QString ret;
    QString normalPix;
    QString readedPix;

    if (modality == "CT")
    {
        normalPix = ":/AccuUIComponentImage/images/ct2.png";
        readedPix = ":/AccuUIComponentImage/images/ct.png";
    }
    else if (modality == "MR")
    {
        normalPix = ":/AccuUIComponentImage/images/mr2.png";
        readedPix = ":/AccuUIComponentImage/images/mr.png";
    }
    else if (modality == "PT")
    {
        normalPix = ":/AccuUIComponentImage/images/pt2.png";
        readedPix = ":/AccuUIComponentImage/images/pt.png";
    }
    else
    {
        normalPix = ":/AccuUIComponentImage/images/unknown2.png";
        readedPix = ":/AccuUIComponentImage/images/unknown.png";
    }

    if (_enableReaded)
    {
        if (isReaded)
        {
            ret = readedPix;
        }
        else
        {
            ret = normalPix;
        }
    }
    else
    {
        ret = readedPix;
    }

    return ret;
}

void QMTPerListView::AddRowItemWidget(QJsonObject jsonObj)
{
    int row_count = _tableWidget->rowCount(); //获取表单行数
    _tableWidget->insertRow(row_count); //插入新行

    for (int col = 0; col < _defaultColumn; ++col)
    {
        int type = _colDelegateTypeMap.value(col);

        if (0 == type)
        {
            QString key = _columnKeyMap.value(col);
            QString value = jsonObj.value(key).toString();
            QLabel* label = new QLabel(_tableWidget);
            label->setAttribute(Qt::WA_TranslucentBackground);
            label->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
            label->setText(value);
            _tableWidget->setCellWidget(row_count, col, label);
        }
        else if (1 == type)
        {
            QString key = _columnKeyMap.value(col);
            QString value = jsonObj.value(key).toString();
            QString pixPath = GetModalityPixPath(value, true);
            QLabel* label = new QLabel(_tableWidget);
            label->setAttribute(Qt::WA_TranslucentBackground);
            label->setAlignment(Qt::AlignHCenter | Qt::AlignVCenter);
            label->setPixmap(QPixmap(pixPath));
            _tableWidget->setCellWidget(row_count, col, label);
        }
    }
}

void QMTPerListView::AddRowItem(QJsonObject jsonObj)
{
    QVariantHash hash = jsonObj.toVariantHash();
    bool isExist = false;

    for (int i = 0; i < _tableWidgetModelList.size(); ++i)
    {
        QVariantHash tmpHash = _tableWidgetModelList.at(i);
        QString value = hash.value(_mainKey).toString();
        QString tmpvalue = tmpHash.value(_mainKey).toString();

        if (value == tmpvalue)
        {
            isExist = true;
        }
    }

    if (!isExist)
    {
        _tableWidgetModelList.append(hash);
        AddRowItemWidget(jsonObj);
    }
}

void QMTPerListView::RemoveRowItem(int row)
{
    if (!_tableWidget)
        return;

    int column = _tableWidget->columnCount();
    _tableWidget->removeRow(row);//auto delete tablewidgetitem

    //then delete cell widget
    for (int col = 0; col < column; ++col)
    {
        QTableWidgetItem* tableWidgetItem = _tableWidget->takeItem(row, col);

        if (tableWidgetItem)
        {
            //delete tableWidgetItem;
            //tableWidgetItem = nullptr;
        }
        else
        {
            QWidget* widgetItem = _tableWidget->cellWidget(row, col);

            if (widgetItem)
            {
                delete widgetItem;
                widgetItem = nullptr;
            }
        }
    }

    _tableWidgetModelList.removeAt(row);
}

void QMTPerListView::ClearDataModel()
{
    if (!_tableWidget)
        return;

    int rowCnt = _tableWidget->rowCount();
    int column = _tableWidget->columnCount();

    for (int row = rowCnt - 1; row >= 0; --row)
    {
        _tableWidget->removeRow(row);//auto delete tablewidgetitem

        //then delete cell widget
        for (int col = 0; col < column; ++col)
        {
            QTableWidgetItem* tableWidgetItem = _tableWidget->takeItem(row, col);

            if (tableWidgetItem)
            {
                //delete tableWidgetItem;
                //tableWidgetItem = nullptr;
            }
            else
            {
                QWidget* widgetItem = _tableWidget->cellWidget(row, col);

                if (widgetItem)
                {
                    delete widgetItem;
                    widgetItem = nullptr;
                }
            }
        }
    }

    //_tableWidget->clear();
}

/// <summary>
/// 将tablewidget中的内容导出到excel中
/// </summary>
/// <param name="filename"></param>
void QMTPerListView::ExportExcel(QString filename, QString sheetname)
{
    try
    {
        //MExcel::QTableWidgetToExcel(_tableWidget, filename, sheetname);
        //MtMessageBox::information(nullptr, tr("导出成功"));
    }
    catch (QString msg)
    {
        //MtMessageBox::information(nullptr, tr("导出失败"));
    }
}

QTableWidget* QMTPerListView::GetTableWidget()
{
    return ui->tableWidget;
}

void QMTPerListView::slotCurrentCellChanged(int row, int column, int preRow, int preColumn)
{
    if (row < 0 || _tableWidgetModelList.size() <= 0 || _tableWidgetModelList.size() <= row)
    {
        return;
    }

    QVariantHash hash = _tableWidgetModelList.at(row);
    QString value = hash.value(_mainKey).toString();
    QMTListViewModelIndex modelIndex(row);
    modelIndex._firstValue = value;
    QMTListViewModelIndex preModelIndex(preRow);

    if (preRow >= 0 && preRow < _tableWidgetModelList.size())
    {
        hash = _tableWidgetModelList.at(preRow);
        value = hash.value(_mainKey).toString();
        preModelIndex._firstValue = value;
    }

    emit sigCurrentCellChanged(modelIndex, preModelIndex);
}

void QMTPerListView::slotCellClicked(int row, int column)
{
    if (row < 0 || _tableWidgetModelList.size() <= 0 || _tableWidgetModelList.size() <= row)
    {
        return;
    }

    QVariantHash hash = _tableWidgetModelList.at(row);
    QString value = hash.value(_mainKey).toString();
    QMTListViewModelIndex modelIndex(row);
    modelIndex._firstValue = value;
    emit sigCellClicked(modelIndex);
}

void QMTPerListView::slotCellDoubleClicked(int row, int column)
{
    QMTListViewModelIndex modelIndex(row);
    emit sigItemDoubleClicked(modelIndex);
}