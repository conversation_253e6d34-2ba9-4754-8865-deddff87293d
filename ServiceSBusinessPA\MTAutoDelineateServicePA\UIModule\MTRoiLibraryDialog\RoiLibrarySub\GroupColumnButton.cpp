﻿#include "GroupColumnButton.h"
#include "ui_GroupColumnButton.h"
#include "MtPushButton.h"
#include "CMtCoreDefine.h"
#include <qDebug>


GroupColumnButtonParam::GroupColumnButtonParam()
{
    _cellWidgetType = DELEAGATE_TYPE_User + 2;
}

GroupColumnButtonParam::~GroupColumnButtonParam()
{
}

QWidget* GroupColumnButtonParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    GroupColumnButton* btn = new GroupColumnButton(parent);
    btn->SetupCellWidget(*this);
    return btn;
}

GroupColumnButton::GroupColumnButton(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::GroupColumnButton;
    ui->setupUi(this);
}

GroupColumnButton::~GroupColumnButton()
{
    MT_DELETE(ui);
}

bool GroupColumnButton::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();
    return false;
}

void GroupColumnButton::SetEnableEdit(bool bEdit)
{
    ui->mtToolButton->setEnabled(bEdit);
}

void GroupColumnButton::SetupCellWidget(GroupColumnButtonParam& param)
{
    _column = param._column;
    ui->mtLabel->setText(param._text);

    if (param._prefixText.isEmpty())
    {
        ui->mtLabel_prefix->setVisible(false);
    }
    else
    {
        ui->mtLabel_prefix->setText(param._prefixText);
    }

    if (param._btnPixelPath.isEmpty())
    {
        ui->mtToolButton->setVisible(false);
    }
    else
    {
        ui->mtToolButton->setPixmapFilename(param._btnPixelPath);
        connect(ui->mtToolButton, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
    }

    if (!param._btnTipStr.isEmpty())
    {
        ui->mtToolButton->setToolTipText(param._btnTipStr);
    }
}

void GroupColumnButton::SetButtonText(int index, QString& text)
{
    ui->mtToolButton->setText(text);
}

void GroupColumnButton::SetButtonEnable(int index, bool enable)
{
    ui->mtToolButton->setEnabled(enable);
}

int GroupColumnButton::GetColumnNumber()
{
    return _column;
}


bool GroupColumnButton::GetCellChecked(int index)
{
    return ui->mtToolButton->isChecked();
}


void GroupColumnButton::slotButtonClicked(bool isChecked)
{
    emit sigClicked(0);
    emit sigButtonClicked(0, isChecked);
}