﻿#include "AutoSketchRoiWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "CommonUtil.h"
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"
#include "Business/DBDataUtils.h"
#include "MTAutoDelineationDialog/TableStyle/GroupNameTable.h"
#include "MTAutoDelineationDialog/ToolDialog/SketchTemplateSaveAs/SketchTemplateSaveAsDialog.h"
#include "MTAutoDelineationDialog/ToolDialog/EclipseImportResult/EclipseImportResultDialog.h"
#include "MTAutoDelineationDialog/ToolDialog/ModUnattendUsed/ModUnattendUsedDialog.h"

#define Def_EveryItemCount                  33              //每个item中的子控件数
#define Def_TreeClose                       "treeClose"     //收起
#define Def_TreeExpand                      "treeExpand"    //展开


AutoSketchRoiWidget::AutoSketchRoiWidget(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    setWidgetLeftEnable(true);
    ui.mtStackedWidget->setHidden(true);
    ui.widget_rightBottom->setHidden(true);
    //模板名称输入框设置输入条件
    ui.lineEdit_templateName->setRegExpression(RegExp_NotSingleQuote);
    //初始化表格
    ui.table_widget_left->init();
    //UI样式
    ui.mtLabel->setStyleSheet("font-weight:bold;");
    ui.mtLabel_templateName->setStyleSheet("font-weight:bold;");
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,AutoSketchRoiWidget, " << errMsg.toStdString();
        }
    }
    //信号槽
    connnectSignal(true);
}

/// <summary>
/// 设置右侧roi-item的宽度
/// </summary>
/// <param name="widthNum">[IN]右侧roi-item的宽度</param>
AutoSketchRoiWidget::~AutoSketchRoiWidget()
{
}

void AutoSketchRoiWidget::setRoiItemWidth(const int widthNum)
{
    m_roiItemWidth = widthNum;
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void AutoSketchRoiWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
    ui.widget_roiListWidget->setImagePathHash(imagePathHash);
    ui.table_widget_left->setImagePathHash(imagePathHash);

    //清除图标
    if (m_clearSearchAction == nullptr)
    {
        m_clearSearchAction = new QAction(QIcon::QIcon(m_imagePathHash["icon_clean"]), "");
        ui.mtLineEdit_roiSearch->addAction(m_clearSearchAction, QLineEdit::TrailingPosition);
        connect(m_clearSearchAction, &QAction::triggered, this, &AutoSketchRoiWidget::onRoiLineEditCleanAciontTriggered);
    }

    if (m_clearSearchAction2 == nullptr)
    {
        m_clearSearchAction2 = new QAction(QIcon::QIcon(m_imagePathHash["icon_clean"]), "");
        ui.mtLineEdit_roiSearch_RoiSelect->addAction(m_clearSearchAction2, QLineEdit::TrailingPosition);
        connect(m_clearSearchAction2, &QAction::triggered, this, &AutoSketchRoiWidget::onRoiLineEditCleanAciontTriggered2);
    }
}

/// <summary>
/// 设置边距
/// </summary>
void AutoSketchRoiWidget::setMargin(int left, int top, int right, int bottom)
{
    ui.verticalLayout->setContentsMargins(left, top, right, bottom);
}

/// <summary>
/// 设置提前选中的页签类型
/// \n 不设置: 默认选中1-选择ROI进行勾画
/// </summary>
/// <param name="pageType">页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
void AutoSketchRoiWidget::setSelectRadioPageType(const int pageType)
{
    m_selectRadioPageType = pageType;
}

/// <summary>
/// 设置需提前选中的模板id
/// </summary>
/// <param name="templateId">模板id</param>
void AutoSketchRoiWidget::setSelectTemplateId(const int templateId)
{
    m_selectTemplateId = templateId;
}

/// <summary>
/// 设置虚拟模板
/// \n用于=选择ROI进行勾画=页签进行器官提前选中
/// </summary>
void AutoSketchRoiWidget::setVirtualSketchCollection(n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection)
{
    m_stVirtualSketchTemplate = stSketchCollection;
}

/// <summary>
/// 初始化数据
/// </summary>
/// <param name="isConnectOptSketchCollection">[IN]设置连接OptSketchCollection信号,只有当同时存在全部模板和无人值守模板时才设置为true</param>
/// <param name="ptrOptSketchCollection">[IN]自动勾画模板信息</param>
/// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
void AutoSketchRoiWidget::initData(OptSketchCollection* ptrOptSketchCollection, n_mtautodelineationdialog::ST_CallBack_AutoSketch& stCallBackAutoSketch)
{
    m_ptrOptSketchCollection = ptrOptSketchCollection;
    m_stCallBackAutoSketch = stCallBackAutoSketch;
    //重置展开/收起按钮
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));
    //清空右侧
    clearRightWidget(true, true);
    reInitLeftTableRow();
    ui.frame_left->setEnabled(true);
    ui.table_widget_left->scrollToTop();
}

/// <summary>
/// 重新初始化显示数据，将清空页面
/// </summary>
void AutoSketchRoiWidget::reInitData()
{
    ShowAllRoi();
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));
    //清空右侧
    clearRightWidget();
    //重新初始化左侧列表item
    reInitLeftTableRow();
    ui.frame_left->setEnabled(true);
    ui.table_widget_left->scrollToTop();
}


/// <summary>
/// 选择ROI进行勾画选中
/// </summary>
void AutoSketchRoiWidget::ShowAllRoi()
{
    ui.mtLineEdit_roiSearch_RoiSelect->clear();
    ui.stackedWidget_save->setCurrentWidget(ui.page_edit2);
    //清空右侧
    clearRightWidget();
    //清空左侧
    ui.table_widget_left->clearSelect();
    //重建右侧
    QApplication::processEvents();
    updateRightWidgetEdit_RadioSelectRoi();
    //重设展开/收起按钮状态
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    m_from_clickRadioTemplate = true;
}

/// <summary>
/// 展开按钮
/// </summary>
void AutoSketchRoiWidget::onMtToolButton_allTreeOpt()
{
    disconnect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchRoiWidget::slotAllItemExpandFromGroupItemListWidget);

    //当前需求只有编辑状态下有全部展开/收起功能
    //判断前一个状态
    if (ui.mtPushButton_expand_RoiSelect->whatsThis() == Def_TreeClose)
    {
        ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeExpand);
        ui.mtPushButton_expand_RoiSelect->setText(tr("全部收起"));
        ui.widget_roiListWidget->expandTreeAll(GroupHTitle::Page_Check, true);
    }
    else if (ui.mtPushButton_expand_RoiSelect->whatsThis() == Def_TreeExpand)
    {
        ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
        ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
        ui.widget_roiListWidget->expandTreeAll(GroupHTitle::Page_Check, false);
    }

    connect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchRoiWidget::slotAllItemExpandFromGroupItemListWidget);
}

/// <summary>
/// 清空选中按钮
/// </summary>
void AutoSketchRoiWidget::onMtToolButton_cleanAllCheck()
{
    //当前需求只有选择ROI进行勾画页签才有清空选中功能
    ui.widget_roiListWidget->checkTreeAll(GroupHTitle::Page_Check, false);
}

/// <summary>
/// 搜索框文本变化
/// </summary>
void AutoSketchRoiWidget::onMtLineEditTextChanged(const QString& text)
{
    //记录要显示的组
    QMap<int/*groupId*/, DBOrganGroupInfo> showGroupMap = m_groupInfoByGroupIdMap;

    for (int i = 0; i < ui.table_widget_left->GetRowCount(); ++i)
    {
        QString rowValue = ui.table_widget_left->GetRowUniqueValue(i);
        QString groupName = ui.table_widget_left->GetColumnText(rowValue, 1);//获取分组名

        if (groupName.contains(text, Qt::CaseInsensitive))
        {
            ui.table_widget_left->HideRowItem(rowValue, false);
            continue;
        }

        ui.table_widget_left->HideRowItem(rowValue, true);

        for (QMap<int, DBOrganGroupInfo>::iterator it = showGroupMap.begin(); it != showGroupMap.end(); ++it)
        {
            if (it.value().GetDefaultGroupName().c_str() == rowValue)
            {
                showGroupMap.remove(it.key());
                break;
            }
        }
    }

    //重新刷ROI列表
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap;
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin();

    for (it; it != m_cacheOrganByGroupIdMap.end(); it++)
    {
        int groupId = it.key();

        if (!showGroupMap.contains(groupId))
        {
            continue;
        }

        QList<n_mtautodelineationdialog::ST_Organ>& organList = it.value();

        for (int i = 0; i < organList.size(); i++)
        {
            newOrganByGroupIdMap[groupId].push_back(organList[i]);
        }
    }

    ui.widget_roiListWidget->clearAllItem();
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    newOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(newOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, newOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 右侧roi搜索框文本变化
/// </summary>
void AutoSketchRoiWidget::onMtLineEditTextChanged_ROI(const QString& text)
{
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap;

    //如果为空，还原回原来状态
    if (text.isEmpty() == true)
    {
        newOrganByGroupIdMap = m_cacheOrganByGroupIdMap;
    }
    else //如果不为空，实时检索，不区分大小写
    {
        QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin();

        for (it; it != m_cacheOrganByGroupIdMap.end(); it++)
        {
            int groupId = it.key();
            QList<n_mtautodelineationdialog::ST_Organ>& organList = it.value();

            for (int i = 0; i < organList.size(); i++)
            {
                n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

                if (stOrgan.customOrganName.contains(text, Qt::CaseInsensitive) == true ||
                    stOrgan.organChineseName.contains(text, Qt::CaseInsensitive) == true ||
                    stOrgan.roiDesc.contains(text, Qt::CaseInsensitive) == true)
                {
                    newOrganByGroupIdMap[groupId].push_back(stOrgan);
                }
            }
        }
    }

    int groupId = ui.widget_roiListWidget->getUniqueKey(); //重新获取一下原有的唯一id，否则会被clearAllItem清掉
    ui.widget_roiListWidget->clearAllItem();
    ui.widget_roiListWidget->setUniqueKey(groupId);
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    newOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(newOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, newOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 右侧roi搜索框清空
/// </summary>
void AutoSketchRoiWidget::onRoiLineEditCleanAciontTriggered(bool checked)
{
    if (ui.mtLineEdit_roiSearch->text().isEmpty() == false)
        ui.mtLineEdit_roiSearch->clear();
}

/// <summary>
/// 右侧roi搜索框清空
/// </summary>
void AutoSketchRoiWidget::onRoiLineEditCleanAciontTriggered2(bool checked)
{
    if (ui.mtLineEdit_roiSearch_RoiSelect->text().isEmpty() == false)
        ui.mtLineEdit_roiSearch_RoiSelect->clear();
}

/// <summary>
/// 取消编辑
/// </summary>
void AutoSketchRoiWidget::onMPushButton_cancelEdit()
{
    //重设展开/收起按钮状态
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));
    int templateId = ui.widget_roiListWidget->getUniqueKey();

    if (templateId == Def_NewCreateId) //新建
    {
        clearRightWidget();
        setWidgetLeftEnable(true);
        delLeftTableRow(QString::number(templateId));
    }
    else //编辑
    {
        updateRightWidgetNoEdit(templateId);
    }

    //发送不处于编辑模式
    emit this->sigEditTemplateStart(false);
}

/// <summary>
/// 另存
/// </summary>
void AutoSketchRoiWidget::onMtmtPushButton_saveas()
{
    n_mtautodelineationdialog::ST_SketchModelCollection newSketchCollection;
    newSketchCollection.isUnattended = false;
    newSketchCollection.templateName = ui.lineEdit_templateName->text();
    newSketchCollection.modality = "CT";
    //
    //整理选中的器官
    int templateId = ui.widget_roiListWidget->getUniqueKey(); //模板id

    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin(); it != m_cacheOrganByGroupIdMap.end(); it++)
    {
        int groupId = it.key();
        QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();
        int mianOrganNum = 0;

        for (int i = 0; i < organList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

            if (stOrgan.isVisiable == false)
            {
                continue;
            }

            int organId = stOrgan.id;

            //如果是亚结构器官的情况下，需把主分组id重新设置为亚分组
            if (stOrgan.organGroupInfoMap.size() == 1 && stOrgan.organGroupInfoMap.begin().value().type == 3)
            {
                int subGroupId = stOrgan.organGroupInfoMap.begin().value().id;
                newSketchCollection.showGroupIdMap[organId].insert(subGroupId);
                newSketchCollection.groupIdSet.insert(subGroupId);
                newSketchCollection.subInMainGroupIdMap[organId].insert(groupId); //亚组在哪些主分组下显示
                continue;
            }

            //主结构器官
            newSketchCollection.showGroupIdMap[organId].insert(groupId);
            mianOrganNum++;
        }

        if (mianOrganNum > 0)
            newSketchCollection.groupIdSet.insert(groupId);
    }

    //判断是否有选择器官
    if (newSketchCollection.showGroupIdMap.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择ROI"));
        return;
    }

    //判断是否只选择空勾画
    {
        bool onlyEmptyRoi = true;
        QSet<int> emptyOrganIdSet = ((OptSketchCollection*)m_ptrOptSketchCollection)->getEmptyOrganIdSet();

        for (QMap<int/*organId*/, QSet<int>>::iterator it = newSketchCollection.showGroupIdMap.begin(); it != newSketchCollection.showGroupIdMap.end(); it++)
        {
            if (emptyOrganIdSet.contains(it.key()) == false)
            {
                onlyEmptyRoi = false;
                break;
            }
        }

        if (onlyEmptyRoi == true)
        {
            MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许只选择空勾画，请再选择其他ROI"));
            return;
        }
    }
    //
    //弹出模板名称重命名窗口
    SketchTemplateSaveAsDialog dlg(m_ptrOptSketchCollection->getSketchCollectionListNotSort(), this);

    if (dlg.exec() != QDialog::Accepted)
        return;

    //数据回调
    int newTemplateId = -1;
    newSketchCollection.templateName = dlg.getNewTemplateName();

    if (m_stCallBackAutoSketch.updateSketchCollectionCallBack != nullptr)
    {
        QString errMsg;

        if (m_stCallBackAutoSketch.updateSketchCollectionCallBack(n_mtautodelineationdialog::EM_OptType::OptType_Add, newSketchCollection, newTemplateId, errMsg) == false)
        {
            MtMessageBox::yellowWarning(this, tr("模板保存失败"), errMsg);
            return;
        }

        newSketchCollection.id = newTemplateId; //更新模板id
        m_ptrOptSketchCollection->addCollection(newSketchCollection);
    }

    //通知新增模板
    emit SigAddOneNewTemplate(newTemplateId, newSketchCollection.templateName, false);
}

/// <summary>
/// 保存为模板
/// </summary>
void AutoSketchRoiWidget::onMtPushButton_saveRoiSelect()
{
    onMtmtPushButton_saveas();
}

/// <summary>
/// 鼠标右键菜单
/// </summary>
void AutoSketchRoiWidget::slotRightMenu()
{
    QAction* action = (QAction*)sender();
    QString actionText = action->text();
}

/// <summary>
/// 左侧列表item选中
/// </summary>
void AutoSketchRoiWidget::slotLeftTableItemSelect(const QString rowValue, Qt::MouseButton button, QPoint point)
{
    //防止重复点击左键
    if (button == Qt::LeftButton && ui.widget_roiListWidget->getUniqueKey() == rowValue)
        return;

    for (QMap<int, DBOrganGroupInfo>::iterator it = m_groupInfoByGroupIdMap.begin(); it != m_groupInfoByGroupIdMap.end(); ++it)
    {
        if (rowValue == it.value().GetDefaultGroupName().c_str())
        {
            ui.widget_roiListWidget->scrollToGroup(it.key());
            break;
        }
    }
}

/// <summary>
/// 左侧列表发生排序
/// </summary>
void AutoSketchRoiWidget::slotLeftTableSortOccurs()
{
    QList<QString> groupDefNameSortList = ui.table_widget_left->getCurGroupSortList();
    QJsonArray groupIdArr;

    for (const QString& defName : groupDefNameSortList)
    {
        for (auto it : m_groupInfoByGroupIdMap)
        {
            if (it.GetDefaultGroupName().c_str() == defName)
            {
                groupIdArr.append(QString::number(it.GetId()));
                break;
            }
        }
    }

    QJsonDocument document(groupIdArr);
    DBFeaturesConfig groupOrderFeature;
    groupOrderFeature.SetEnable(1);
    groupOrderFeature.SetFeatureKey("organ_group_order");
    groupOrderFeature.SetFeatureValue(QString(document.toJson(QJsonDocument::Compact)).toStdString());

    std::vector<DBFeaturesConfig>& featureConfigVec = DBDataUtils::FindFeaturesConfigByKey("organ_group_order");

    if (!featureConfigVec.empty())
    {
        groupOrderFeature.SetId(featureConfigVec[0].GetId());
        DBDataUtils::UpdateFeatureConfigInfo(groupOrderFeature);
    }
    else
    {
        DBDataUtils::AddFeatureConfigInfo(groupOrderFeature);
    }

    //对分组信息进行排序
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoOrderList;
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoVec = m_ptrOptSketchCollection->getAllMainGroupInfoList();

    for (int i = 0; i < groupIdArr.size(); ++i)
    {
        int gID = groupIdArr[i].toString().toInt();

        for (int r = 0; r < groupInfoVec.size(); ++r)
        {
            if (gID == groupInfoVec[r].id)
            {
                groupInfoOrderList.push_back(groupInfoVec[r]);
                groupInfoVec.erase(groupInfoVec.begin() + r);
                break;
            }
        }
    }

    for (const n_mtautodelineationdialog::ST_OrganGroupInfo& groupItem : groupInfoVec)
    {
        groupInfoOrderList.push_back(groupItem);
    }

    m_ptrOptSketchCollection->setAllMainGroupInfoList(groupInfoOrderList);
    updateRightWidgetEdit_RadioSelectRoi();
}

/// <summary>
/// 响应OptSketchCollection拖拽使能信号
/// </summary>
/// <param name="enable">true使能</param>
void AutoSketchRoiWidget::slotTableNameDropEnable(const bool enable)
{
    ui.table_widget_left->setIsDropEnable(enable);
}

/// <summary>
/// item点击(itemId-器官id),来源于GroupItemListWidget
/// </summary>
/// <param name="groupId">[IN]器官分组id</param>
/// <param name="itemIdSet">[IN]器官organId集合(key-organId)</param>
/// <param name="checked">true使能</param>
void AutoSketchRoiWidget::slotOneItemCheckFromGroupItemListWidget(const int groupId, const QSet<int> itemIdSet, const bool checked)
{
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin();

    if (m_cacheOrganByGroupIdMap.contains(groupId) == true)
    {
        QList<n_mtautodelineationdialog::ST_Organ>& organList = m_cacheOrganByGroupIdMap[groupId];

        for (int i = 0; i < organList.size(); i++)
        {
            if (itemIdSet.contains(organList[i].id) == true)
            {
                organList[i].isVisiable = checked;
            }
        }
    }
}

/// <summary>
/// 是否全部展开/收起
/// </summary>
/// <param name="pageTypeEnum">[IN]页面类型</param>
/// <param name="isExpand">[IN]是否全部展开</param>
void AutoSketchRoiWidget::slotAllItemExpandFromGroupItemListWidget(const GroupHTitle::EM_PageType pageTypeEnum, const bool isExpand)
{
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(isExpand == true ? Def_TreeExpand : Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(isExpand == true ? tr("全部收起") : tr("全部展开"));
}

/// <summary>
/// 输入是否正常
/// </summary>
/// <returns>true正常</returns>
bool AutoSketchRoiWidget::isInputNormal()
{
    //编辑模式下，检查模板输入
    if (ui.mtStackedWidget->currentWidget() == ui.page_modelEdit)
    {
        int templateId = ui.widget_roiListWidget->getUniqueKey();

        if (ui.lineEdit_templateName->text().isEmpty())
        {
            ui.lineEdit_templateName->setWarningBorderStatus(tr("未填写模板名称"));
            return false;
        }

        if (m_ptrOptSketchCollection->isExistTemplateName(ui.lineEdit_templateName->text(), templateId) == true)
        {
            ui.lineEdit_templateName->setWarningBorderStatus(tr("模板名称已存在"));
            return false;
        }
    }

    return true;
}

/// <summary>
/// 是否存在该模板id
/// </summary>
/// <param name="rowValue">[IN]模板id</param>
/// <returns>true存在</returns>
bool AutoSketchRoiWidget::isExistTemplateId(const QString& rowValue)
{
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;

    if (rowValue.isEmpty() == true || m_ptrOptSketchCollection->isExistTemplateId(dcmTypeEnum, rowValue.toInt()) == false)
    {
        return false;
    }

    return true;
}

/// <summary>
/// 是否可操作模板
/// </summary>
/// <param name="optTypeEnum">[IN]操作类型</param>
/// <param name="templateId">[IN]模板id</param>
/// <returns>true可操作</returns>
bool AutoSketchRoiWidget::isCanOptTemplate(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const int templateId)
{
    if (m_stCallBackAutoSketch.canOptSketchCollectionCallBack != nullptr)
    {
        QString outMsg, rightBtnText;
        int dlgType = m_stCallBackAutoSketch.canOptSketchCollectionCallBack(optTypeEnum, templateId, outMsg, rightBtnText);

        if (dlgType == 1) //蓝色提示
        {
            MtMessageBox::NoIcon::information_Title(this->window(), outMsg);
            return false;
        }
        else if (dlgType == 2) //蓝色选择
        {
            /*int ret = MtMessageBox::NoIcon::question_Title(this, outMsg, QString());

            if (QMessageBox::Yes != ret)
                return false;*/
            if (m_ptrOptSketchCollection->getIsShowTipOfModUnattendUsed() == true)
            {
                ModUnattendUsedDialog dlg(outMsg, false, this);

                if (dlg.exec() == QDialog::Accepted)
                {
                    m_ptrOptSketchCollection->setIsShowTipOfModUnattendUsed(!dlg.getIsChecked());
                    return true;
                }

                return false;
            }

            return true;
        }
        else if (dlgType == 3) //红色警告选择
        {
            int ret = MtMessageBox::redWarning(this, outMsg, QString(), rightBtnText);

            if (QMessageBox::Yes != ret)
                return false;
        }
    }

    return true;
}

/// <summary>
/// 设置左侧栏使能
/// </summary>
/// <param name="isEnable">[IN]true使能</param>
void AutoSketchRoiWidget::setWidgetLeftEnable(const bool isEnable)
{
    ui.frame_left->setEnabled(isEnable);
}

/// <summary>
/// 删除左侧列表item
/// </summary>
/// <param name="rowValue">[IN]模板id</param>
void AutoSketchRoiWidget::delLeftTableRow(const QString rowValue)
{
    ui.table_widget_left->delRow(rowValue);
    slotLeftTableSortOccurs();
}

/// <summary>
/// 重新初始化左侧列表item
/// </summary>
void AutoSketchRoiWidget::reInitLeftTableRow()
{
    ui.table_widget_left->delAllRow();
    //获取分组信息
    std::vector<DBOrganGroupInfo> groupInfoOrderVec;
    std::vector<DBOrganGroupInfo> groupInfoVec = DBDataUtils::FindAllOrganGroupInfo();
    std::vector<DBFeaturesConfig>& featureConfigVec = DBDataUtils::FindFeaturesConfigByKey("organ_group_order");

    if (featureConfigVec.size() > 0)
    {
        QJsonArray orderArr = CommonUtil::qStringToqJsonArray(QString::fromStdString(featureConfigVec[0].GetFeatureValue()));

        //对分组信息进行排序
        for (int i = 0; i < orderArr.size(); ++i)
        {
            int gID = orderArr[i].toString().toInt();

            for (int r = 0; r < groupInfoVec.size(); ++r)
            {
                if (gID == groupInfoVec[r].GetId())
                {
                    groupInfoOrderVec.push_back(groupInfoVec[r]);
                    groupInfoVec.erase(groupInfoVec.begin() + r);
                    break;
                }
            }
        }
    }

    for (const DBOrganGroupInfo& groupItem : groupInfoVec)
    {
        groupInfoOrderVec.push_back(groupItem);
    }

    for (const DBOrganGroupInfo& groupItem : groupInfoOrderVec)
    {
        m_groupInfoByGroupIdMap[groupItem.GetId()] = groupItem;
        //
        GroupNameTableInfo info;
        info.groupDefName = groupItem.GetDefaultGroupName().c_str();
        info.groupName = groupItem.GetName().c_str();
        ui.table_widget_left->addRow(info);
    }

    //显示所有器官
    updateRightWidgetEdit_RadioSelectRoi();
}

/// <summary>
/// 清空右侧界面
/// </summary>
void AutoSketchRoiWidget::clearRightWidget(bool stackedWidgetHidden, bool isReConnect)
{
    ui.lineEdit_templateName->clear();
    ui.mtStackedWidget->setHidden(stackedWidgetHidden);
    ui.stackedWidget_save->setHidden(stackedWidgetHidden);
    ui.widget_roiListWidget->clearAllItem();

    //
    if (isReConnect == true)
    {
        disconnect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);
        disconnect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);
    }

    ui.mtLineEdit_roiSearch->clear();
    ui.mtLineEdit_roiSearch_RoiSelect->clear();

    if (isReConnect == true)
    {
        connect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);
        connect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);
    }
}

/// <summary>
/// 获取要展示的器官集合
/// </summary>
/// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
/// <param name="dcmTypeEnum">[IN]DICOM类型</param>
/// <returns>要展示的器官集合(key-mainGroupId 亚器官已经被映射到主分组)</returns>
QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> AutoSketchRoiWidget::getOrganToShow(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection,
                                                                                          n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum)
{
    //全部分组器官信息
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = m_ptrOptSketchCollection->getOrganByGroupIdMap(dcmTypeEnum);
    //将亚组映射到主分组中
    QSet<int> oldMainGroupIdInTemplateSet; //模板中的原始主分组集合
    //亚组映射到主分组中
    QSet<int> newGroupInTemplateSet = m_ptrOptSketchCollection->changeSubGroupIdToMainGroupId(curSketchCollection.groupIdSet, oldMainGroupIdInTemplateSet);
    //获取内存中亚分组和主分组关系
    QMap<int/*亚结构groupId*/, QSet<int/*主结构groupId*/>> mainGroupIdBySubGroupIdMap = m_ptrOptSketchCollection->getMainGroupIdBySubGroupIdMap();
    QSet<int/*亚结构organId*/> subOrganNotFindMainSet; //存储未找到主分组的亚器官，挂载到相关联的第一个主分组上
    //QSet<int/*亚结构organId*/> subOrganNormalFindMainSet; //存储正常找到主分组的亚器官

    //更新勾选状态
    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = tempOrganByGroupIdMap.begin(); it != tempOrganByGroupIdMap.end(); it++)
    {
        int mainGroupId = it.key(); //此时亚分组已经被映射到了主分组

        //不在模板里的分组,直接跳过保持未勾选状态
        //if (newGroupInTemplateSet.contains(mainGroupId) == false)
        //    continue;

        //勾选模板里有的器官
        for (int i = 0; i < it.value().size(); i++)
        {
            int organId = it.value()[i].id;
            QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupInfoMap = it.value()[i].organGroupInfoMap; //实际器官中的分组情况

            //没归组的器官忽略
            if (organGroupInfoMap.isEmpty() == true)
                continue;

            //没在模板里的器官忽略
            if (curSketchCollection.showGroupIdMap.contains(organId) == false)
                continue;

            //当前模板内器官对应的分组id
            QSet<int> curTempalteGroupIdSet = curSketchCollection.showGroupIdMap[organId];

            //判断是否是亚组器官,因为亚结构只可能有一个分组
            if (mainGroupIdBySubGroupIdMap.contains(organGroupInfoMap.begin().key()) == true) //亚组器官
            {
                int subGroupId = organGroupInfoMap.begin().key(); //亚组id
                QSet<int> mainGroupIdBySubGroupIdSet = mainGroupIdBySubGroupIdMap[subGroupId];//实际器官中的亚组对应的主分组情况

                //已经挂载到涉及到该亚组的主分组第一个的忽略
                if (subOrganNotFindMainSet.contains(organId) == true)
                {
                    continue;
                }

                //查看是否在当前的主分组下显示亚分组
                if (curSketchCollection.subInMainGroupIdMap.contains(organId) == true)
                {
                    if (curSketchCollection.subInMainGroupIdMap[organId].contains(mainGroupId) == true)
                    {
                        it.value()[i].isVisiable = true;
                        subOrganNotFindMainSet.remove(organId);
                        continue;
                    }
                    else
                    {
                        //判断subInMainGroupIdMap是否是实际的子集,这样就一定会找到亚器官的挂载点
                        QSet<int> remainSet = curSketchCollection.subInMainGroupIdMap[organId] - mainGroupIdBySubGroupIdSet;

                        if (remainSet.size() < curSketchCollection.subInMainGroupIdMap[organId].size())
                        {
                            continue;
                        }
                    }
                }

                //模板中subInMainGroupIdMap没找到设置这个map就是空的时候(老版本升级上来的)
                //该模板里的原主分组有涉及到该亚器官对应的主器官分组
                if (oldMainGroupIdInTemplateSet.contains(mainGroupId) == true)
                {
                    it.value()[i].isVisiable = true;
                    subOrganNotFindMainSet.insert(organId);
                    continue;
                }
                else
                {
                    //根本没有主分组时，直接把当前的主分组加上去
                    if (oldMainGroupIdInTemplateSet.isEmpty() == true)
                    {
                        it.value()[i].isVisiable = true;
                        subOrganNotFindMainSet.insert(organId);
                        oldMainGroupIdInTemplateSet.insert(mainGroupId); //加上一个分组
                        continue;
                    }

                    //判断该模板里的原主分组有涉及到该亚器官对应的主器官分组
                    QSet<int> remainSet = oldMainGroupIdInTemplateSet - mainGroupIdBySubGroupIdSet;

                    //该模板里的原主分组有涉及到该亚器官对应的主器官分组
                    if (remainSet.size() < oldMainGroupIdInTemplateSet.size())
                    {
                        continue;
                    }
                    else //该模板里的原主分组没有有涉及到该亚器官对应的主器官分组
                    {
                        it.value()[i].isVisiable = true;
                        subOrganNotFindMainSet.insert(organId);
                        oldMainGroupIdInTemplateSet.insert(mainGroupId); //加上一个分组
                    }
                }
            }
            else //主分组器官
            {
                //查看是否在主分组里
                if (curTempalteGroupIdSet.contains(mainGroupId) == true)
                {
                    it.value()[i].isVisiable = true;
                    continue;
                }
            }
        }
    }

    return tempOrganByGroupIdMap;
}

/// <summary>
/// 获取选中的模板
/// 如果是选择模板进行勾画页签-编辑模式下直接点击确定，则返回一个id为 -99 的临时模板
/// 如果是选择ROI进行勾画页签-编辑模式下直接点击确定， 则返回一个id为 -98 的临时模板
/// </summary>
/// <returns>选中的模板</returns>
n_mtautodelineationdialog::ST_SketchModelCollection AutoSketchRoiWidget::getSelectSketchCollection()
{
    auto setSketchModelCollection = [this](n_mtautodelineationdialog::ST_SketchModelCollection& sketchCollection)->void
    {
        //整理选中的器官
        for (QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin(); it != m_cacheOrganByGroupIdMap.end(); it++)
        {
            int groupId = it.key(); //亚组已经被映射到主分组
            QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();
            int mianOrganNum = 0;

            for (int i = 0; i < organList.size(); i++)
            {
                n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

                if (stOrgan.isVisiable == false)
                {
                    continue;
                }

                int organId = stOrgan.id;

                //如果是亚结构器官的情况下，需把主分组id重新设置为亚分组
                if (stOrgan.organGroupInfoMap.size() == 1 && stOrgan.organGroupInfoMap.begin().value().type == 3)
                {
                    int subGroupId = stOrgan.organGroupInfoMap.begin().value().id;
                    sketchCollection.showGroupIdMap[organId].insert(subGroupId);
                    sketchCollection.groupIdSet.insert(subGroupId);
                    sketchCollection.subInMainGroupIdMap[organId].insert(groupId); //亚组在哪些主分组下显示
                    continue;
                }

                //主结构器官
                sketchCollection.showGroupIdMap[organId].insert(groupId);
                mianOrganNum++;
            }

            if (mianOrganNum > 0)
                sketchCollection.groupIdSet.insert(groupId);
        }
    };

    //
    //模板设置界面模式
    if (m_showSelectRadioSelectBtn == false)
    {
        QString rowValue = ui.table_widget_left->getCurSelectRow();
        n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
        n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());
        return curSketchCollection;
    }

    //如果是-选择ROI进行勾画页签-选中
    n_mtautodelineationdialog::ST_SketchModelCollection tempSketchCollection;
    tempSketchCollection.id = Def_TempIdOfSelectRoi;
    tempSketchCollection.isUnattended = false;
    tempSketchCollection.templateName = "Not-Template-From-SelectRoi";
    tempSketchCollection.modality = "CT";
    setSketchModelCollection(tempSketchCollection);
    return tempSketchCollection;
}

/// <summary>
/// 获取最新的虚拟模板
/// \n用于*选择ROI进行勾画*页签进行器官提前选中
/// </summary>
/// <returns>最新的虚拟模板</returns>
n_mtautodelineationdialog::ST_SketchModelCollection AutoSketchRoiWidget::getVirtualSketchCollection()
{
    return getSelectSketchCollection();
}

/// <summary>
/// 是否处于编辑状态
/// </summary>
/// <returns>true是</returns>
bool AutoSketchRoiWidget::isEditState()
{
    if (ui.mtStackedWidget->isHidden() == false && ui.mtStackedWidget->currentWidget() == ui.page_modelEdit)
    {
        return true;
    }

    return false;
}

/// <summary>
/// 添加数据到widget_roiListWidget控件
/// </summary>
/// <param name="isEdit">[IN]是否是编辑模式</param>
/// <param name="organByGroupIdMap">[IN]器官按照groupId分组(key-groupId)</param>
/// <param name="allGroupInfoList">[IN]排序后的所有分组信息</param>
/// <param name="subOrganTypeMap">[OUT]亚结构的信息(key-亚结构organId value-1:找到主结构 2:未找到主结构)</param>
void AutoSketchRoiWidget::addDataToRightRoiWidget(const bool isEdit, const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap,
                                                  const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList, const QMap<int, int>& subOrganTypeMap)
{
    for (int i = 0; i < allGroupInfoList.size(); i++)
    {
        int groupId = allGroupInfoList[i].id;

        if (organByGroupIdMap.contains(groupId) == true)
        {
            GroupHTitleData titleData;
            titleData.groupId = groupId;
            titleData.value = allGroupInfoList[i].name;
            ui.widget_roiListWidget->addTitle(isEdit == true ? GroupHTitle::Page_Check : GroupHTitle::Page_Label, titleData);
            //平分成10组
            QList<n_mtautodelineationdialog::ST_Organ> organList = organByGroupIdMap[groupId];
            int totalSize = organList.size();
            int startIndex = 0;

            while (startIndex < totalSize)
            {
                QList<GroupItemData> dataList;
                dataList.reserve(Def_EveryItemCount);

                for (int i = startIndex; i < startIndex + Def_EveryItemCount && i < totalSize; ++i)
                {
                    GroupItemData data;
                    data.groupId = groupId;
                    data.valId = organList[i].id;
                    data.isCheck = organList[i].isVisiable;
                    data.value1 = organList[i].customOrganName;
                    data.value2 = organList[i].organChineseName;
                    data.value3 = organList[i].roiDesc;

                    //设置亚结构缩进
                    if (subOrganTypeMap.contains(data.valId) == true)
                    {
                        data.organType = subOrganTypeMap[data.valId];
                    }

                    dataList.push_back(data);
                }

                ui.widget_roiListWidget->addItems(isEdit == true ? GroupItem::Page_CheckLabel : GroupItem::Page_LabelLabel, dataList, m_roiItemWidth);
                startIndex += Def_EveryItemCount;
            }
        }
    }

    //刷新一下标题勾选状态
    ui.widget_roiListWidget->flushAllTitleCheckStatus();
    //滚动到最上面
    ui.widget_roiListWidget->scrollTop(true);
}

/// <summary>
/// 更新右侧界面-不可编辑状态
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void AutoSketchRoiWidget::updateRightWidgetNoEdit(const int templateId)
{
    //左侧使能
    setWidgetLeftEnable(true);
    //清空并重新初始化
    clearRightWidget();
    ui.widget_roiListWidget->setUniqueKey(templateId);
    //获取当前操作的模板
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, templateId);

    if (m_ptrOptSketchCollection->isExistTemplateId(dcmTypeEnum, templateId) == false)
        return;

    //切换编辑框
    setRightStackWidgetShow(true, false);
    ui.mtLabel_templateName->setText(curSketchCollection.templateName);
    //实际分组器官信息
    QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap;
    //全部分组器官信息
    QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = getOrganToShow(curSketchCollection, dcmTypeEnum);

    for (QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = tempOrganByGroupIdMap.begin(); it != tempOrganByGroupIdMap.end(); it++)
    {
        int mainGroupId = it.key(); //此时亚分组已经被映射到了主分组
        QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();

        for (int i = 0; i < organList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

            if (stOrgan.isVisiable == true)
            {
                newOrganByGroupIdMap[mainGroupId].push_back(stOrgan);
            }
        }
    }

    //填充数据
    newOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow_NoSortSub(newOrganByGroupIdMap);
    addDataToRightRoiWidget(false, newOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), QMap<int, int>());
}

/// <summary>
/// 更新右侧界面-编辑状态
/// </summary>
/// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
void AutoSketchRoiWidget::updateRightWidgetEdit(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection)
{
    //左侧使能
    setWidgetLeftEnable(false);
    //清空并重新初始化
    clearRightWidget();
    ui.widget_roiListWidget->setUniqueKey(curSketchCollection.id);
    //切换编辑框
    setRightStackWidgetShow(true, true);
    ui.lineEdit_templateName->setText(curSketchCollection.templateName);
    //全部分组器官信息
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
    //全部分组器官信息
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = getOrganToShow(curSketchCollection, dcmTypeEnum);
    //填充数据
    m_cacheOrganByGroupIdMap = tempOrganByGroupIdMap;
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    tempOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(tempOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, tempOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 更新右侧界面-编辑状态
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void AutoSketchRoiWidget::updateRightWidgetEdit(const int templateId)
{
    //当前操作的模板
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, templateId);
    updateRightWidgetEdit(curSketchCollection);
}

/// <summary>
/// 更新右侧界面-选择ROI进行自动勾画页签
/// </summary>
/// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
void AutoSketchRoiWidget::updateRightWidgetEdit_RadioSelectRoi(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection)
{
    //清空并重新初始化
    clearRightWidget();
    ui.widget_roiListWidget->setUniqueKey(Def_TempIdOfSelectRoi);
    //切换编辑框
    setRightStackWidgetShow(false, true);
    ui.lineEdit_templateName->setText(curSketchCollection.templateName);
    //全部分组器官信息
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
    //全部分组器官信息
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = getOrganToShow(curSketchCollection, dcmTypeEnum);
    //填充数据
    m_cacheOrganByGroupIdMap = tempOrganByGroupIdMap;
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    tempOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(tempOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, tempOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 更新右侧界面-选择ROI进行自动勾画页签
/// </summary>
void AutoSketchRoiWidget::updateRightWidgetEdit_RadioSelectRoi()
{
    updateRightWidgetEdit_RadioSelectRoi(m_stVirtualSketchTemplate.showGroupIdMap.isEmpty() == false ? m_stVirtualSketchTemplate : n_mtautodelineationdialog::ST_SketchModelCollection());
    //全部收起
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    ui.widget_roiListWidget->expandTreeAll(GroupHTitle::Page_Check, false);
}

/// <summary>
/// 设置右侧上下StackWidget展示模式
/// </summary>
/// <param name="isRadioTemplateSelect">[IN]是否是选择模板进行勾画Radio选中</param>
/// <param name="isEdit">[IN]是否处于编辑状态</param>
void AutoSketchRoiWidget::setRightStackWidgetShow(const bool isRadioTemplateSelect, const bool isEdit)
{
    if (isRadioTemplateSelect == false)
    {
        ui.mtStackedWidget->setHidden(false);
        ui.stackedWidget_save->setHidden(false);
        ui.widget_rightBottom->setHidden(false);
        ui.mtStackedWidget->setCurrentWidget(ui.page_roiSelect);
        ui.stackedWidget_save->setCurrentWidget(ui.page_edit2);
        return;
    }

    if (isEdit == true)
    {
        ui.mtStackedWidget->setHidden(false);
        ui.stackedWidget_save->setHidden(false);
        ui.widget_rightBottom->setHidden(false);
        ui.mtStackedWidget->setCurrentWidget(ui.page_modelEdit);
        ui.stackedWidget_save->setCurrentWidget(ui.page_edit1);
        return;
    }

    ui.mtStackedWidget->setHidden(false);
    ui.stackedWidget_save->setHidden(true);
    ui.widget_rightBottom->setHidden(true);
    ui.mtStackedWidget->setCurrentWidget(ui.page_modelShow);
}

/// <summary>
/// 信号槽
/// </summary>
void AutoSketchRoiWidget::connnectSignal(const bool isConnect)
{
    if (isConnect == true)
    {
        connect(ui.mtPushButton_expand, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtToolButton_allTreeOpt);     //展开按钮
        connect(ui.mtPushButton_expand_RoiSelect, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtToolButton_allTreeOpt);//展开按钮
        connect(ui.mtPushButton_cancelSelect_RoiSelect, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtToolButton_cleanAllCheck);//悬浮工具-清空选中按钮
        connect(ui.mtLineEdit_search, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged);       //搜索框文本变化
        connect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        connect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        connect(ui.table_widget_left, &GroupNameTable::sigItemSelect, this, &AutoSketchRoiWidget::slotLeftTableItemSelect);//左侧列表点击
        connect(ui.table_widget_left, &GroupNameTable::sigSortOccurs, this, &AutoSketchRoiWidget::slotLeftTableSortOccurs);//左侧列表发生排序
        connect(ui.mtPushButton_cancelEdit, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMPushButton_cancelEdit);  //取消编辑按钮
        connect(ui.mtPushButton_saveas, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtmtPushButton_saveas);       //另存按钮
        connect(ui.mtPushButton_saveRoiSelect, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtPushButton_saveRoiSelect); //保存为模板按钮
        connect(ui.widget_roiListWidget, &GroupItemListWidget::sigOneItemCheckFromGroupItemListWidget, this, &AutoSketchRoiWidget::slotOneItemCheckFromGroupItemListWidget); //GroupItemListWidget-item点击
        connect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchRoiWidget::slotAllItemExpandFromGroupItemListWidget); //GroupItemListWidget-是否全部展开/收起
        connect(m_ptrOptSketchCollection, &OptSketchCollection::sigTableNameDropEnable, this, &AutoSketchRoiWidget::slotTableNameDropEnable); //左侧列表拖拽使能
    }
    else
    {
        disconnect(ui.mtPushButton_expand, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtToolButton_allTreeOpt);     //展开按钮
        disconnect(ui.mtPushButton_expand_RoiSelect, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtToolButton_allTreeOpt);//展开按钮
        disconnect(ui.mtPushButton_cancelSelect_RoiSelect, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtToolButton_cleanAllCheck);//悬浮工具-清空选中按钮
        disconnect(ui.mtLineEdit_search, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged);       //搜索框文本变化
        disconnect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        disconnect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchRoiWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        disconnect(ui.table_widget_left, &GroupNameTable::sigItemSelect, this, &AutoSketchRoiWidget::slotLeftTableItemSelect);//左侧列表点击
        disconnect(ui.table_widget_left, &GroupNameTable::sigSortOccurs, this, &AutoSketchRoiWidget::slotLeftTableSortOccurs);//左侧列表发生排序
        disconnect(ui.mtPushButton_cancelEdit, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMPushButton_cancelEdit);  //取消编辑按钮
        disconnect(ui.mtPushButton_saveas, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtmtPushButton_saveas);       //另存按钮
        disconnect(ui.mtPushButton_saveRoiSelect, &QPushButton::clicked, this, &AutoSketchRoiWidget::onMtPushButton_saveRoiSelect); //保存为模板按钮
        disconnect(ui.widget_roiListWidget, &GroupItemListWidget::sigOneItemCheckFromGroupItemListWidget, this, &AutoSketchRoiWidget::slotOneItemCheckFromGroupItemListWidget); //GroupItemListWidget-item点击
        disconnect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchRoiWidget::slotAllItemExpandFromGroupItemListWidget); //GroupItemListWidget-是否全部展开/收起
        disconnect(m_ptrOptSketchCollection, &OptSketchCollection::sigTableNameDropEnable, this, &AutoSketchRoiWidget::slotTableNameDropEnable); //左侧列表拖拽使能
    }
}
