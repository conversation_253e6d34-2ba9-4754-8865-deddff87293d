﻿// *********************************************************************************
// <remarks>
// FileName    : ModelOrganWidget
// Author      : zlw
// CreateTime  : 2023-12-13
// Description : 模型器官展示界面(内嵌于: ModelSettingWidget/TemplateSettingWidget 中)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include <QListWidgetItem>
#include "ui_ModelOrganWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class ModelOrganWidget : public QWidget
{
    Q_OBJECT

public:
    ModelOrganWidget(QWidget* parent = nullptr);
    ~ModelOrganWidget();

    /// <summary>
    /// 设置模型标题
    /// </summary>
    /// <param name="str">[IN]文本</param>
    void setModelTitle(const QString& str);

    /// <summary>
    /// 清空所有item
    /// </summary>
    void clearAllItem();

    /// <summary>
    /// 添加roiItem集合
    /// </summary>
    /// <param name="uniqueKey">[IN]唯一标识(模型:modelName.id 模板:templateName.id)</param>
    /// <param name="titleStr">[IN]小标题(为空时不会有中心小标题出现)</param>
    /// <param name="sketchModel">[IN]模型信息</param>
    void addRoiItem(const QString& uniqueKey, const QString& titleStr, const n_mtautodelineationdialog::ST_SketchModel& sketchModel);

private:
    Ui::ModelOrganWidgetClass ui;
};
