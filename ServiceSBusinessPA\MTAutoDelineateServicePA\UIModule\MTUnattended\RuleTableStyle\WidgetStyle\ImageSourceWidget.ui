<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ImageSourceWidgetClass</class>
 <widget class="QWidget" name="ImageSourceWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>251</width>
    <height>56</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ImageSourceWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>4</number>
   </property>
   <property name="leftMargin">
    <number>12</number>
   </property>
   <property name="topMargin">
    <number>15</number>
   </property>
   <property name="rightMargin">
    <number>12</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="MtLabel" name="mtLabel_enable">
       <property name="minimumSize">
        <size>
         <width>8</width>
         <height>8</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>8</width>
         <height>8</height>
        </size>
       </property>
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item>
      <widget class="MtLabel" name="mtLabel_serverName">
       <property name="text">
        <string>MtTextLabel</string>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel2</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="leftMargin">
      <number>14</number>
     </property>
     <item>
      <widget class="MtLabel" name="mtLabel_serverType">
       <property name="text">
        <string>MtTextLabel</string>
       </property>
       <property name="elideMode">
        <enum>Qt::ElideRight</enum>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel1_1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
