﻿#pragma once

#include <QDialog>
#include "ui_CWidgetButton.h"

class CWidgetButton : public QWidget
{
    Q_OBJECT

public:
    enum ButtonType
    {
        btnType_All,
        btnType_Default,
        btnType_Empty,
        btnType_Model
    };

    CWidgetButton(QWidget* parent = Q_NULLPTR);
    ~CWidgetButton();

public:
    bool isChecked();
    void setChecked(bool bChecked = true);
    void setType(ButtonType type);
    void setImportModelBtnPixmap(const QString& pixmap);

public:
    void setEnabled(bool bEnable);
    void setDisabled(bool bDisable);

    QString text();

signals:
    void sigButtonClicked(int type);
    void sigImportModelClicked();

protected slots:
    void on_listBtnImportModel_clicked();

protected:
    virtual void mouseReleaseEvent(QMouseEvent* event);
    virtual void enterEvent(QEvent* event);
    virtual void leaveEvent(QEvent* event);
    void updateStatus(bool bHover = false);

private:
    Ui::CWidgetButton ui;
    bool m_bChecked;        // 是否选中状态
    ButtonType  m_type;
};
