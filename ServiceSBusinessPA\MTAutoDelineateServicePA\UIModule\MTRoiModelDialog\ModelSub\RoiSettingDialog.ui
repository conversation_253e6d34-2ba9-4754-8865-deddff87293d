<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RoiSettingDialog</class>
 <widget class="QWidget" name="RoiSettingDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>401</width>
    <height>229</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RoiSettingDialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>8</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtCheckBox" name="chkBox_enableROIName">
        <property name="text">
         <string>按ROI名称，自动匹配系统标签库对应信息</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtCheckBox::checkbox1</enum>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <property name="leftMargin">
         <number>24</number>
        </property>
        <property name="bottomMargin">
         <number>8</number>
        </property>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="spacing">
            <number>24</number>
           </property>
           <property name="leftMargin">
            <number>21</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtCheckBox" name="chkBox_label">
             <property name="text">
              <string>标签</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtCheckBox" name="chkBox_roiType">
             <property name="text">
              <string>ROI类型</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtCheckBox" name="chkBox_color">
             <property name="text">
              <string>颜色</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtCheckBox" name="chkBox_chName">
             <property name="text">
              <string>器官名称</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="MtCheckBox" name="chkBox_enableGroup">
        <property name="text">
         <string>选择分组，应用于所有ROI</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtCheckBox::checkbox1</enum>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <property name="leftMargin">
         <number>24</number>
        </property>
        <property name="bottomMargin">
         <number>8</number>
        </property>
        <item>
         <widget class="n_mtautodelineationdialog::MultiSelectComboBox" name="comboBox_group">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtComboBox::combobox1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="MtCheckBox" name="chkBox_enableDesc">
        <property name="text">
         <string>填写ROI描述，应用于所有ROI</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtCheckBox::checkbox1</enum>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="leftMargin">
         <number>24</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtLineEdit" name="edit_desc">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="maxLength">
           <number>32</number>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>n_mtautodelineationdialog::MultiSelectComboBox</class>
   <extends>MtComboBox</extends>
   <header>MTRoiLibraryDialog\MultiSelectComboBox\MultiSelectComboBox.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
