﻿// *********************************************************************************
// <remarks>
// FileName    : MTRoiLibraryWidget
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : Roi库设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include <QWidget>

namespace Ui
{
class MTRoiLibraryWidgetClass;
}

namespace n_mtautodelineationdialog
{

class MTRoiLibraryWidget : public QWidget, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    MTRoiLibraryWidget(QWidget* parent = nullptr);
    ~MTRoiLibraryWidget();

    /// <summary>
    /// 创建ROI库设置界面窗口
    /// </summary>
    /// <param name="allRoiTypeList">[IN]Roi类型集合(如果为空将采用内置的类型集合)</param>
    /// <param name="stRoiLabelInfoList">[IN]label标签集合</param>
    /// <param name="allGroupList">[IN]所有的roi分组信息</param>
    /// <param name="stOrganList">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    /// <param name="modelCollectionInfoList">[IN]所有的模板信息</param>
    void initRoiLibrarySettingWidget(const QStringList& allRoiTypeList
                                     , const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList
                                     , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList
                                     , const QList<ST_Organ>& stOrganList
                                     , const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
                                     , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 窗口关闭前调用，否则若列表正在创建时关闭窗口，会出现卡死问题.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void widgetDestroying();

    /// <summary>
    /// 重置数据修改状态，将修改状态设置为false.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void resetInfoChangeStatus();

    /// <summary>
    /// 获取修改后的数据.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="outStOrganList">[OUT]输出修改后的器官信息</param>
    /// <param name="outModelCollectionInfoList">[OUT]输出修改后的模板信息</param>
    /// <param name="outRoiLabelInfoList">[OUT]输出修改后的标签信息，注意：保存标签信息的时候需要将roiName添加到标签的别名中</param>
    /// <param name="outAllGroupList">[OUT]输出修改后分组信息</param>
    void getWidgetData(QList<ST_Organ>& outStOrganList
                       , QList<n_mtautodelineationdialog::ST_SketchModelCollection>& outModelCollectionInfoList
                       , QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& outRoiLabelInfoList
    /*, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& outAllGroupList*/);

public slots:
    /// <summary>
    /// 检查数据是否发生了修改.(该方法要放到槽函数中，不然外部无法通过QMetaObject::indexOfMethod调用到)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>bool.</returns>
    bool IsModified();

signals:
    /***********************************调用者处理信号，也可以使用回调方式，二选一即可*************************************/
    /// <summary>
    /// 模板关联的roi取消了分组
    /// </summary>
    /// <param name="roiID">[OUT]roiID</param>
    /// <param name="roiName">[OUT]roi名称</param>
    /// <param name="groupID">[OUT]取消的组ID</param>
    /// <param name="groupName">[OUT]取消的组名</param>
    /// <param name="refModelInfoMap">[OUT]关联roi分组的模板信息，当该参数为空时，表示该roi所有分组都被移除</param>
    /// <returns></returns>
    void sigRemoveRoiRelatedGroup(int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap);

    /// <summary>
    /// 更新ROI分组信息到数据库，并重新读取组信息
    /// </summary>
    /// <param name="curGroupList">[OUT]当前所有ROI分组，包括更新了组名和新增（组id小于0）的分组</param>
    /// <param name="delGroupList">[OUT]删除的ROI分组</param>
    /// <param name="updtedGroupList">[OUT]传回的分组信息，因为有新增和删除分组，组id会不合法。所以在入库后需要重新读取组信息</param>
    /// <returns></returns>
    void sigUpdateRoiGroup(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList
                           , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList
                           , QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList);

    /// <summary>
    /// 发送信号，以获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN]返回从标签库中获取的所有记录信息</param>
    void sigGetLabelLibraryInfo(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 发送信号，以获取器官ROI默认设置(根据默认名进行匹配)
    /// </summary>
    /// <param name="stOrganDefaultList">[IN]返回从获取的器官默认设置信息</param>
    void sigGetOrganDefaultInfo(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 发送信号，通知外部进行导入模型，需要进行模型导入处理
    /// </summary>
    /// <param name="modelPath">[OUT][模型文件路径</param>
    void sigModelImport(const QString& modelPath);

    /// <summary>
    /// 模型删除信号
    /// </summary>
    /// <param name="modelId">[OUT]要删除的模型ID</param>
    /// <param name="modelName">[OUT]要删除的模型名</param>
    void sigDeleteModel(const QString& modelId, const QString& modelName);

    /// <summary>
    /// 保存模型信息.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">The model identifier.</param>
    /// <param name="modelName">Name of the model.</param>
    /// <param name="modelName">description of the model.</param>
    /// <param name="result">The result，0：success.</param>
    void sigSaveModelInfo(const QString& modelId, const QString& modelName, const QString& desc, int& result);

    /**************************************内部接收处理信号，由调用者发送*******************************************/
    /// <summary>
    /// 模型导入进度信号，用于更新模型导入进度条
    /// </summary>
    /// <param name="value">[IN]模型导入进度</param>
    void sigModelImportProgress(int value);

    /// <summary>
    /// 模型导入完成信号，用于更新导入进度
    /// </summary>
    /// <param name="bSuccess">[IN]导入是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelImportFinish(bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 模型导入入库完成信号，用于更新模型列表，只添加新导入的模型信息
    /// </summary>
    /// <param name="stOrganInfoVec">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    void sigModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 模型删除是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="modelId">[IN]删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    /// <param name="bSuccess">[IN]删除是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg);

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

    /// <summary>
    /// 调用连接
    /// </summary>
    void doConnect(QWidget* roiLibraryWidget);

    QWidget* getContentWidget();

protected:
    virtual bool eventFilter(QObject* obj, QEvent* event) override;

private:
    Ui::MTRoiLibraryWidgetClass* ui;
    QHash<QString, QString> m_imagePathHash;     //图标map(key-name value-图片相对路径)
};

}
