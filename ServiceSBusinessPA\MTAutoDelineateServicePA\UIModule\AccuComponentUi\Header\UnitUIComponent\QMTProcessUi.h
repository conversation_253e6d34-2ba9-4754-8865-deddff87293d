﻿#pragma once
#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif

#include <QWidget>
#include <QJsonObject>
#include "AccuComponentUi/Header/Language.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"


/*病人页面的勾画和导出进度条控件*/

class  QMTProcessUiParam : public ICellWidgetParam
{
public:
    int _fontSize = 12;           //字体大小
    QColor _textColor = QColor(219, 226, 241, 0.6 * 255);       //字体颜色

    int delineateValue = -1;      //自动勾画进度条的值
    int exportValue = -1;         //导出进度条的值
    QString statusMsg;            //状态栏的值
    QMTProcessUiParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTProcessUiParam);

namespace Ui
{
class QMTProcessUi;
}

class  QMTProcessUi : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    //更新结构体
    typedef struct ST_ProcessStatusUpdateInfo
    {
        int delineateValue = -1;      //自动勾画进度条的值
        int exportValue = -1;         //导出进度条的值
        QString statusMsg;            //状态栏的值
    } ProcessStatusUpdateInfo;

    QMTProcessUi(QWidget* parent = Q_NULLPTR);
    ~QMTProcessUi();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口


    void SetupCellWidget(QMTProcessUiParam& cellWidgetParam); //初始化单元格界面

    void SetCustSheet(const QString& styleSheetStr);                //设置整个widget的样式
    void SetStatusStyle(const QString& styleSheetStr);              //设置状态文案label样式
    void SetSetStatusExportStyle(const QString& styleSheetStr);     //设置RT导出状态栏的样式
    void SetPercentStyle(const QString& styleSheetStr);             //设置进度条的样式

    void HideProcessBar(bool);                                      //隐藏进度条
    void SetValue(int value);                                       //设置进度条的值
    void SetAutoDelineateValue(int value, QString msg = "");        //设置自动勾画进度,自动设置了样式
    void SetExportValue(int value, QString msg = "");               //设置导出进度，自动设置了样式

    void SetStatusString(QString msg, QString style = QString());       //设置勾画进行、导出进行、勾画或者导出结果文案或者RT的生成时间
    void SetStatusExportString(QString msg, QString style = QString()); //RT的导出状态label

    void SetMode(QJsonObject&);
    void SetEnglishSize();
signals:
    void sigRead();
protected:
    void InitTemplateStyle();
private:
    Ui::QMTProcessUi* ui;
};

Q_DECLARE_METATYPE(QMTProcessUi::ProcessStatusUpdateInfo);