﻿// *********************************************************************************
// <remarks>
// FileName    : UnattendSubWidget
// Author      : zlw
// CreateTime  : 2023-12-15
// Description : 无人值守信息界面(内嵌于: MTUnattendedDialog 无人值守设置弹窗)
//               注：所有涉及到的唯一标识uniqueKey指的是unattendedfeature/unattendedrule表customId
// </remarks>
// **********************************************************************************
#pragma once


#include <QWidget>
#include "ui_UnattendSubWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include "MTUnattended/RuleTableStyle/UnattendSubTableShowItem.h"
#include "MTUnattended/RuleTableStyle/UnattendSubTableEditItem.h"
#include "MTUnattended/RuleTableStyle/WidgetStyle/ImageSourceWidget.h"
#include "OptUnattendDataNew.h"


/// <summary>
/// 存储标识数据,存储于ui.mtLabel_title中
/// </summary>
struct ST_UnattendSubWidgetModule
{
    void setValue(const bool val_isAdd, const QString val_imageSourceUniqueKey)
    {
        isAdd = val_isAdd;
        imageSourceUniqueKey = val_imageSourceUniqueKey;
    };

    bool isAdd = false;
    QString imageSourceUniqueKey;
};
Q_DECLARE_METATYPE(ST_UnattendSubWidgetModule);


class UnattendSubWidget : public QWidget
{
    Q_OBJECT

public:
    UnattendSubWidget(QWidget* parent = nullptr);
    ~UnattendSubWidget();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="ptrOptUnattendData">[IN]无人值守信息</param>
    /// <param name="stCallBackUnattended">[OUT]数据回调</param>
    void init(OptUnattendDataNew* ptrOptUnattendData, n_mtautodelineationdialog::ST_CallBack_Unattended& stCallBackUnattended);

    /// <summary>
    /// 是否处于编辑状态
    /// </summary>
    /// <returns>true是</returns>
    bool isEditState();

    /// <summary>
    /// 设置右侧规则列表最小高度
    /// </summary>
    /// <param name="minHeightNum">[IN]最小高度</param>
    void setMinHeight_widget_table_sketch(const int minHeightNum);

protected slots:
    /// <summary>
    /// 影像来源-新增按钮
    /// </summary>
    void onMtToolButton_add();

    /// <summary>
    /// 影像来源-拷贝按钮
    /// </summary>
    void onMtToolButton_copy();

    /// <summary>
    /// 影像来源-删除按钮
    /// </summary>
    void onMtToolButton_del();

    /// <summary>
    /// 影像来源-开关按钮
    /// </summary>
    void onMtSwitchButton_title(bool ischecked);

    /// <summary>
    /// 影像来源-编辑规则按钮
    /// </summary>
    void onMtPushButton_edit_title();

    /// <summary>
    /// 影像来源-保存规则按钮
    /// </summary>
    void onMtPushButton_save_title();

    /// <summary>
    /// 影像来源-取消编辑按钮
    /// </summary>
    void onMtPushButton_cancel_title();

    /// <summary>
    /// 影像接收-新增本地节点按钮
    /// </summary>
    void onMtToolButton_search_source();

    /// <summary>
    /// 关键字过滤复选框
    /// </summary>
    void onMtCheckBox_word_filterChanged(int state);

    /// <summary>
    /// 勾画规则-新增按钮
    /// </summary>
    void onMtPushButton_add_sketch();

    /// <summary>
    /// 勾画规则-删除按钮
    /// </summary>
    void onMtPushButton_del_sketch();

    /// <summary>
    /// 勾画规则-全选按钮
    /// </summary>
    void onMtCheckBox_all_sketch(int state);

    /// <summary>
    /// 左侧影像来源Item选中变化
    /// </summary>
    void slotmtListWidgetLeft_currentItemChanged(QListWidgetItem* current, QListWidgetItem* previous);

protected:
    /// <summary>
    /// 获取当前标识数据,存储于ui.mtLabel_title中
    /// </summary>
    ST_UnattendSubWidgetModule getCurFlagModule();

    /// <summary>
    /// 设置当前标识数据,存储于ui.mtLabel_title中
    /// </summary>
    void setCurFlagModule(const ST_UnattendSubWidgetModule stModule);

    /// <summary>
    /// 是否可以新增一条规则(可用模板)
    /// </summary>
    /// <returns>true可以</returns>
    bool isCanAddOneRule();

    /// <summary>
    /// 初始化影像来源下拉框
    /// </summary>
    /// <param name="allLocalServerNameMap">所有本地服务器名集合(包括空闲和不空闲)</param>
    void initMtComboBox_server_source(const QMap<int, QStringList>& allLocalServerNameMap);

    /// <summary>
    /// 获取ImageSourceWidget指针
    /// 为nullptr时代表没找到
    /// </summary>
    ImageSourceWidget* getItemLeftImageSourceTable(QListWidgetItem* listWidgetItem);

    /// <summary>
    /// 添加左侧影像来源
    /// </summary>
    /// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
    /// <param name="enable">[IN]true开启</param>
    /// <param name="serverName">[IN]本地服务器名</param>
    /// <param name="serverType">[IN]本地服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log 4:SCP服务器)</param>
    /// <returns>插入的行号</returns>
    int addRowLeftImageSourceTable(const QString& imageSourceUniqueKey, const bool enable, const QString& serverName, const int serverType);

    /// <summary>
    /// 删除左侧影像来源
    /// </summary>
    /// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
    void delRowLeftImageSourceTable(const QString& imageSourceUniqueKey);

    /// <summary>
    /// 选中左侧影像来源
    /// </summary>
    /// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
    void selectRowLeftImageSourceTable(const QString& imageSourceUniqueKey);

    /// <summary>
    /// 清空左侧影像来源选中状态
    /// </summary>
    void clearSelectRowLeftImageSourceTable();

    /// <summary>
    /// 更新左侧影像来源
    /// </summary>
    /// <param name="imageSourceUniqueKey">[IN]影像来源唯一标识</param>
    /// <param name="enable">[IN]true开启</param>
    /// <param name="serverName">[IN]本地服务器名</param>
    /// <param name="serverType">[IN]本地服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log 4:SCP服务器)</param>
    void updateLeftImageSourceTable(const QString& imageSourceUniqueKey, const bool enable, const QString& serverName, const int serverType);

    /// <summary>
    /// 添加右侧规则信息(展示模式)
    /// </summary>
    /// <param name="sketchIdentifyUniqueKey">[IN]勾画规则唯一标识</param>
    /// <param name="stSketchIdentify">[IN]勾画规则信息</param>
    void addRowRightRuleTableShow(const QString& sketchIdentifyUniqueKey, const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify);

    /// <summary>
    /// 刷新右侧界面(展示模式)
    /// </summary>
    /// <param name="stUnattendedConfig">无人值守信息</param>
    void updateRightWidgetShow(const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig);

    /// <summary>
    /// 获取UnattendSubTableEditItem指针(编辑模式)
    /// 为nullptr时代表没找到
    /// </summary>
    UnattendSubTableEditItem* getItemRightRuleTableEdit(QListWidgetItem* listWidgetItem);

    /// <summary>
    /// 添加右侧规则信息(编辑模式)
    /// </summary>
    /// <param name="isAdd">[IN]true新增</param>
    /// <param name="sketchIdentifyUniqueKey">[IN]当前勾画规则唯一标识</param>
    /// <param name="stSketchIdentify">[IN]当前勾画规则信息</param>
    void addRowRightRuleTableEdit(const bool isAdd, const QString& sketchIdentifyUniqueKey, const n_mtautodelineationdialog::ST_SketchIdentify& curStSketchIdentify);

    /// <summary>
    /// 刷新右侧界面(编辑模式)
    /// </summary>
    /// <param name="isAdd">[IN]true新增</param>
    /// <param name="stUnattendedConfig">无人值守信息</param>
    void updateRightWidgetEdit(const bool isAdd, const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig);

    /// <summary>
    /// 进入编辑页面模式
    /// </summary>
    /// <param name="isEnter">true进入</param>
    /// <param name="isAdd">true新增模式</param>
    void enterEditPage(const bool isEnter, bool isAdd = false);

    /// <summary>
    /// 设置右侧规则信息全选打勾情况
    /// </summary>
    void setCheckState_mtCheckBox_all_sketch(Qt::CheckState state);

    /// <summary>
    /// 右侧规则信息打勾情况
    /// </summary>
    Qt::CheckState getCheckStateRightRuleTableEdit();

    /// <summary>
    /// 创建默认的18条勾画规则
    /// </summary>
    /// <returns>默认的18条勾画规则</returns>
    n_mtautodelineationdialog::ST_SketchRule createDefaultAISketchRule();

    /// <summary>
    /// 信号槽
    /// </summary>
    void connnectSignal(const bool isConnect);

private:
    Ui::UnattendSubWidgetClass ui;
    OptUnattendDataNew* m_ptrOptUnattendData = nullptr; //内存数据
    n_mtautodelineationdialog::ST_AddrSimple m_defExportAddr; //默认导出地址
    n_mtautodelineationdialog::ST_CallBack_Unattended m_stCallBackUnattended; //数据回调
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)
};
