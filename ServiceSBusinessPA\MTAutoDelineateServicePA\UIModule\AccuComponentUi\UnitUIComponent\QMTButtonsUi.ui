<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTButtonsUi</class>
 <widget class="QWidget" name="QMTButtonsUi">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>96</width>
    <height>152</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>96</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QMTButtonsUi</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_ButtonUiBK" native="true">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>10</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>1</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>1</number>
      </property>
      <item>
       <widget class="MtPushButton" name="pushButton_delinete">
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="text">
         <string>自动勾画</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtPushButton::pushbutton1</enum>
        </property>
        <property name="class" stdset="0">
         <string>blueButton</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtPushButton" name="pushButton_open">
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="text">
         <string>打开图像</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtPushButton::pushbutton1</enum>
        </property>
        <property name="class" stdset="0">
         <string>blueButton</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtPushButton" name="pushButton_redo">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="text">
         <string>重新勾画</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtPushButton::pushbutton1</enum>
        </property>
        <property name="class" stdset="0">
         <string notr="true">labelButton</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtPushButton" name="pushButton_cancel">
        <property name="minimumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>80</width>
          <height>30</height>
         </size>
        </property>
        <property name="cursor">
         <cursorShape>ArrowCursor</cursorShape>
        </property>
        <property name="text">
         <string>取消</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtPushButton::pushbutton2</enum>
        </property>
        <property name="class" stdset="0">
         <string>blueButton</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
