<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AutoSketchTemplateWidgetClass</class>
 <widget class="QWidget" name="AutoSketchTemplateWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>991</width>
    <height>553</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>AutoSketchTemplateWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="leftMargin">
    <number>16</number>
   </property>
   <property name="topMargin">
    <number>16</number>
   </property>
   <property name="rightMargin">
    <number>16</number>
   </property>
   <property name="bottomMargin">
    <number>8</number>
   </property>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>6</number>
     </property>
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="rightMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="MtFrameEx" name="frame_left">
       <property name="minimumSize">
        <size>
         <width>316</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>316</width>
         <height>16777215</height>
        </size>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::frameEx3_30</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_7">
        <property name="spacing">
         <number>4</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtFrame" name="mtFrame_roiLeft">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>38</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>38</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <property name="leftMargin">
            <number>12</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>8</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtComboBox" name="mtComboBox">
             <property name="minimumSize">
              <size>
               <width>90</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>90</width>
               <height>26</height>
              </size>
             </property>
             <property name="elideMode">
              <enum>Qt::ElideRight</enum>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtComboBox::combobox1</enum>
             </property>
             <item>
              <property name="text">
               <string>CT</string>
              </property>
             </item>
             <item>
              <property name="text">
               <string>MR</string>
              </property>
             </item>
            </widget>
           </item>
           <item>
            <widget class="MtLineEdit" name="mtLineEdit_search">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>26</height>
              </size>
             </property>
             <property name="maxLength">
              <number>64</number>
             </property>
             <property name="placeholderText">
              <string>请搜索</string>
             </property>
             <property name="elideMode">
              <enum>Qt::ElideRight</enum>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLineEdit::lineedit1</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="ModelNameTable" name="table_widget_left" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtFrameEx" name="frame_left_line">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>1</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>1</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::frameEx3</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtFrameEx" name="frame_btn">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>34</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>34</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>6</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtToolButton" name="mtToolButton_add">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>新建模板</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_edit">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>编辑模板</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_copy">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>复制模板</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_del">
             <property name="minimumSize">
              <size>
               <width>24</width>
               <height>24</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>24</width>
               <height>24</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>删除模板</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_unattend">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>无人值守</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_top">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>置顶</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_eclipse">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>导入eclipse模板</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <widget class="MtFrameEx" name="frame_right">
       <property name="minimumSize">
        <size>
         <width>20</width>
         <height>0</height>
        </size>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtFrameEx::frameEx3_30</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>16</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtStackedWidget" name="mtStackedWidget">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>50</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>50</height>
           </size>
          </property>
          <property name="currentIndex">
           <number>0</number>
          </property>
          <widget class="QWidget" name="page_modelShow">
           <layout class="QHBoxLayout" name="horizontalLayout_5">
            <property name="spacing">
             <number>6</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>16</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="MtLabel" name="mtLabel">
              <property name="text">
               <string>模板名称：</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtLabel::myLabel3</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtLabel" name="mtLabel_templateName">
              <property name="text">
               <string>MtTextLabel</string>
              </property>
              <property name="elideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtLabel::myLabel3</enum>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_3">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_modelEdit">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="spacing">
             <number>12</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>16</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_8">
              <property name="rightMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="MtLabel" name="mtLabel_2">
                <property name="text">
                 <string>*</string>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtLabel::myLabel1_5</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="MtRangeLineEdit" name="lineEdit_templateName" native="true">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maxLength" stdset="0">
                 <number>64</number>
                </property>
                <property name="placeholderText" stdset="0">
                 <string>未命名模板</string>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>110</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="MtLineEdit" name="mtLineEdit_roiSearch">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>26</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>26</height>
               </size>
              </property>
              <property name="maxLength">
               <number>64</number>
              </property>
              <property name="placeholderText">
               <string>搜索ROI</string>
              </property>
              <property name="elideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtLineEdit::lineedit1</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtPushButton" name="mtPushButton_expand">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>26</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>26</height>
               </size>
              </property>
              <property name="text">
               <string>全部收起</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtPushButton::pushbutton2</enum>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
          <widget class="QWidget" name="page_roiSelect">
           <layout class="QHBoxLayout" name="horizontalLayout_9">
            <property name="spacing">
             <number>16</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>16</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="MtLineEdit" name="mtLineEdit_roiSearch_RoiSelect">
              <property name="minimumSize">
               <size>
                <width>200</width>
                <height>26</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>26</height>
               </size>
              </property>
              <property name="maxLength">
               <number>64</number>
              </property>
              <property name="placeholderText">
               <string>搜索ROI</string>
              </property>
              <property name="elideMode">
               <enum>Qt::ElideRight</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtLineEdit::lineedit1</enum>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_4">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>327</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="MtPushButton" name="mtPushButton_expand_RoiSelect">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>26</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>26</height>
               </size>
              </property>
              <property name="text">
               <string>全部收起</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtPushButton::pushbutton2</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtPushButton" name="mtPushButton_cancelSelect_RoiSelect">
              <property name="minimumSize">
               <size>
                <width>80</width>
                <height>26</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>16777215</width>
                <height>26</height>
               </size>
              </property>
              <property name="text">
               <string>取消选中</string>
              </property>
              <property name="mtMenuType">
               <enum>MtMenu::default_type</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtPushButton::pushbutton2</enum>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </widget>
        </item>
        <item>
         <widget class="GroupItemListWidget" name="widget_roiListWidget" native="true"/>
        </item>
        <item>
         <widget class="QWidget" name="widget_rightBottom" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>35</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>35</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_6">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtFrameEx" name="mtFrameEx_6">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>1</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>1</height>
              </size>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtFrameEx::frameEx3</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtFrameEx" name="mtFrameEx_7">
             <property name="_mtType" stdset="0">
              <enum>MtFrameEx::default_type</enum>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_10">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="QStackedWidget" name="stackedWidget_save">
                <property name="currentIndex">
                 <number>2</number>
                </property>
                <widget class="QWidget" name="page_edit1">
                 <layout class="QHBoxLayout" name="horizontalLayout_11">
                  <property name="spacing">
                   <number>12</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>16</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <spacer name="horizontalSpacer_5">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>391</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_cancelEdit">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>取消编辑</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton2</enum>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_saveas">
                    <property name="minimumSize">
                     <size>
                      <width>56</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>另存</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton2</enum>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_saveEdit">
                    <property name="minimumSize">
                     <size>
                      <width>56</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>保存</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton10</enum>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="page_edit2">
                 <layout class="QHBoxLayout" name="horizontalLayout_12">
                  <property name="spacing">
                   <number>12</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>16</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <spacer name="horizontalSpacer_6">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>545</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_saveRoiSelect">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>保存为模版</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton10</enum>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
                <widget class="QWidget" name="page_modelShowBottom">
                 <layout class="QHBoxLayout" name="horizontalLayout_6">
                  <property name="spacing">
                   <number>12</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>16</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <spacer name="horizontalSpacer_7">
                    <property name="orientation">
                     <enum>Qt::Horizontal</enum>
                    </property>
                    <property name="sizeHint" stdset="0">
                     <size>
                      <width>40</width>
                      <height>20</height>
                     </size>
                    </property>
                   </spacer>
                  </item>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_edit">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>80</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>编辑模板</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton2</enum>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtStackedWidget</class>
   <extends>QStackedWidget</extends>
   <header>MtStackedWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrame</class>
   <extends>QFrame</extends>
   <header>MtFrame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtRangeLineEdit</class>
   <extends>QWidget</extends>
   <header>mtrangelineedit.h</header>
  </customwidget>
  <customwidget>
   <class>ModelNameTable</class>
   <extends>QWidget</extends>
   <header>MTAutoDelineationDialog/TableStyle/modelnametable.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>GroupItemListWidget</class>
   <extends>QWidget</extends>
   <header>AToolWidget/ItemGroupWidget/groupitemlistwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
