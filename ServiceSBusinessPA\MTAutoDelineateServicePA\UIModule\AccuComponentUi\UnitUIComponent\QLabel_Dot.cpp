﻿#include "AccuComponentUi\Header\QLabel_Dot.h"

#include <QDebug>

QLabelParam::QLabelParam()
{
    _cellWidgetType = DELEAGATE_QLabel;
}

QLabelParam::~QLabelParam()
{
}

QWidget* QLabelParam::CreateUIModule(QWidget* parent)
{
    QLabel_Dot* labelDot = new QLabel_Dot(parent);

    if (_mtType > 0)
    {
        labelDot->setMtType((MtLabel::MtType)_mtType);
    }

    labelDot->SetEnableDot(_enableDot);

    if (false == _enableDot)
    {
        labelDot->setWordWrap(_bWordWrap);
    }

    if (_showPix)
    {
        labelDot->setPixmap(QPixmap(_pixPath));
    }
    else
    {
        labelDot->setTextElided(_text);
    }

    return labelDot;
}


QLabel_Dot::QLabel_Dot(QWidget* parent, Qt::WindowFlags f)
    : MtLabel(parent, f)
    //: QLabel(parent, f)
{
    this->setMtType(MtLabel::myLabel1);
    this->setElideMode(Qt::ElideRight);
}

QLabel_Dot::QLabel_Dot(const QString& text, QWidget* parent, Qt::WindowFlags f)
    : MtLabel(text, parent, f)
{
#if 0
    QLabel_Dot(parent, f);
    this->setTextElided(text);
#endif
    this->setElideMode(Qt::ElideRight);
}

#if 1
QLabel_Dot::~QLabel_Dot()
{
}
#endif

bool QLabel_Dot::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->setTextElided(text);
        return true;
    }

    return false;
}

QString QLabel_Dot::GetCurText()
{
    return text();
#if 0
    return _fullStr;
#endif
}

QString QLabel_Dot::GetFullString()
{
    return text();
#if 0
    return _fullStr;
#endif
}

void QLabel_Dot::SetEnableDot(bool bEnable)
{
    if (bEnable)
    {
        this->setElideMode(Qt::ElideRight);
    }
    else
    {
        this->setElideMode(Qt::ElideNone);
    }

#if 0
    _bEnableDot = bEnable;
#endif
}

/// <summary>
/// 设置显示的text，
/// 1. 未设置wordWrap时，如果超出label允许最大宽度，将用…代替
/// 2. 设置了wordWrap，直接调用setText(text)，将在paintEvent对末尾替换为 …
/// 3. 末尾…的情况下自动设置setWhatsThis。并在event时显示，避免设置全局fontsize时导致tooltip会显示不全。
/// 4. 如果要获取设置时的完全字符串，可以用whatsThis()获取。
/// </summary>
/// <param name="text"></param>
/// <remark>tested</remark>
void QLabel_Dot::setTextElided(const QString& text)
{
    this->setText(text);
#if 0
    _fullStr = text;
    QString plainText = EnsurePlainText(text);

    if (plainText != text || false == _bEnableDot)  // 1. 富文本直接赋值，不能转为省略号
    {
        QLabel::setText(text);
    }
    else if (wordWrap())
    {
        //QFontMetrics metrics(font());
        //QRect rect = metrics.boundingRect(geometry(), alignment() | Qt::TextWordWrap, text);
        //// 1. 避免自适应的时候可能达不到最大化限制，如MMessageBox中的label，直接设置时能显示的宽度不达标
        //if (rect.width() != width())
        //{
        //    QRect geo = this->geometry();
        //    this->setGeometry(geo.x(), geo.y(), rect.width() - rect.y(), geo.height());
        //    qDebug() << "rect:" << rect << " geo;" << this->geometry();
        //}
        QLabel::setText(text);
    }
    else
    {
        QLabel::setText(this->GetElidedText(this->font(), text, this->width()));
        this->setWhatsThis("");     // 必须先清空，否则会残留tooltip

        if (!text.isEmpty() &&
            this->text().right(1) != text.right(1))
        {
            //this->setToolTip(text);
            this->setWhatsThis(text);
        }
    }

#endif
}

#if 0

/// <summary>
/// 重绘部件，末尾… ，注意尽量不要这里面setGeometry
/// </summary>
/// <param name="event"></param>
void QLabel_Dot::paintEvent(QPaintEvent* event)
{
    if (wordWrap())
    {
        // 1. 获取text实际的高度，如果超出label的显示高度，那么需要重新绘制label
        QFontMetrics metrics(font());
        QRect rect = metrics.boundingRect(geometry(), alignment() | Qt::TextWordWrap, text());
        //qDebug() << "rect:" << rect << " paintEvent";

        if (rect.height() <= height() && rect.width() <= width())
        {
            this->setWhatsThis("");     // 可以正常显示的情况下不需要tooltip显示
            QLabel::paintEvent(event);
        }
        else
        {
            //QString tmpText;
            //bool didElide = false;
            QPainter painter(this);
            QFontMetrics fontMetrics = painter.fontMetrics();
            int lineSpacing = fontMetrics.lineSpacing();        // 行间距
            int y = 0, offsety = 2;     // y为高度，每绘制一行增加行间距。offsety为避免上下少2个像素导致显示行数出现问题。
            QTextDocument document(text(), this);
            //document.setDefaultFont(font());// 无效
            QTextBlock block = document.begin();
            QRect er = geometry();//event->rect();

            while (block.isValid())
            {
                //qDebug() << block.text() << "geo:" << er;
                QTextLayout* textLayout = block.layout();
                textLayout->setFont(painter.font());

                if (!block.isVisible())
                {
                    y += lineSpacing;
                    block = block.next();
                    continue;
                }

                if (height() < y + lineSpacing - offsety)
                    break;

                textLayout->beginLayout();

                forever
                {
                    QTextLine line = textLayout->createLine();

                    if (!line.isValid())
                        break;

                    line.setLineWidth(width());
                    int nextLineY = y + lineSpacing;

                    if (height() >= nextLineY + lineSpacing - offsety)
                    {
                        line.draw(&painter, QPoint(0, y));
                        y = nextLineY;
                    }
                    else
                    {
                        QString lastLine = block.text().mid(line.textStart());
                        QString elidedLastLine = fontMetrics.elidedText(lastLine, Qt::ElideRight, width());
                        //tmpText += block.text().left(line.textStart()) + elidedLastLine + "\n";
                        painter.drawText(QPoint(0, y + fontMetrics.ascent()), elidedLastLine);
                        y = nextLineY;
                        line = textLayout->createLine();
                        //didElide = line.isValid();
                        break;
                    }
                }

                textLayout->endLayout();
                block = block.next();
            }

            this->setWhatsThis(text());
        }

        //if (didElide != elided) {
        //    elided = didElide;
        //    emit elisionChanged(didElide);
        //}
    }
    else
    {
        QLabel::paintEvent(event);
    }
}



void QLabel_Dot::resizeEvent(QResizeEvent* event)
{
    this->setTextElided(_fullStr);
}

/// <summary>
/// 解决超长字符toolTip显示不全问题
/// </summary>
/// <param name="watched"></param>
/// <param name="event"></param>
/// <returns></returns>
bool QLabel_Dot::event(QEvent* event)
{
    if (event->type() == QEvent::ToolTip)
    {
        if (!this->whatsThis().isEmpty())
        {
            QHelpEvent* helpEvent = (QHelpEvent*)(event);
            int x = helpEvent->globalPos().x();
            int y = helpEvent->globalPos().y();
            QToolTip::showText(helpEvent->globalPos(), this->whatsThis());
        }

        return true;
    }

    //qDebug() << "event";
    return QLabel::event(event);
}

#endif
