﻿#include "GroupTableView.h"
#include <QApplication>
#include <QDrag>
#include <QMimeData>
#include <QPainter>
#include <QHeaderView>
#include <QScrollBar>
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"

GroupTableView::GroupTableView(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    setAcceptDrops(true);
    QString dragLineStyle = "border: 1px solid rgb(@color-gradient1-2);";
    CMtCoreWidgetUtil::formatStyleSheet(dragLineStyle);
    m_dragLine = new QLabel(this);
    m_dragLine->setFixedHeight(2);
    m_dragLine->setGeometry(1, 0, width(), 2);
    m_dragLine->setStyleSheet(dragLineStyle);
    m_dragLine->hide();
    m_bValidPress = false;
    m_rowFrom = 0;
    m_rowTo = 0;
    //
    initTableView({ tr(""), tr("分组名称"), tr("类型"), tr("操作") }, { 36, 226, 172, 80 });
    //连接信号
    connect(this, &GroupTableView::sigCellWidgetButtonClicked, this, &GroupTableView::slotCellWidgetButtonClicked); //某个按键点击了
}

void GroupTableView::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    QCustMtLabelParam* orderWidgetParam = new QCustMtLabelParam();
    orderWidgetParam->_pixPath = ":/images/images/drag.png";
    firstViewParam._headParam._headCellParamMap.insert(ColType_Move, orderWidgetParam);

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    m_rowHeight = 36;
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

void GroupTableView::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    for (const auto& organInfoItem : m_organInfoList)
    {
        int groupId = cellItemIndex._uniqueValue.toInt();

        if (organInfoItem.organGroupInfoMap.contains(groupId))
        {
            QString groupName = organInfoItem.organGroupInfoMap[groupId].name;
            MtMessageBox::NoIcon::information_Title(this, groupName + tr("该分组已被用于关联ROI，解除关联后才可以删除。"));
            return;
        }
    }

    DeleteRowItem(cellItemIndex._uniqueValue);
}

void GroupTableView::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
    if (ColType_Move == column)
    {
        QCustMtLabel* label = qobject_cast<QCustMtLabel*>(cellWidget);

        if (label)
        {
            label->GetLabel()->setPixmap(QPixmap(":/images/images/drag.png"));
        }
    }
    else if (ColType_Name == column)
    {
    }
}

void GroupTableView::initTableList(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList
                                   , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allOrganGroupList)
{
    m_organInfoList = organInfoList;

    for (const auto& groupItem : allOrganGroupList)
    {
        if (groupItem.type == 3)
        {
            continue;
        }

        addRow(groupItem);
    }
}

void GroupTableView::addNew(int newGroupId)
{
    n_mtautodelineationdialog::ST_OrganGroupInfo stGroupInfo = { newGroupId, 2, -1, "" };
    addRow(stGroupInfo);
    //滚动到最下方
    verticalScrollBar()->setValue(verticalScrollBar()->maximum());
}

QList <n_mtautodelineationdialog::ST_OrganGroupInfo> GroupTableView::getTableList()
{
    QList <n_mtautodelineationdialog::ST_OrganGroupInfo> retList;
    QStringList rowValueList = GetAllRowUniqueValueList();

    for (const QString& rowValue : rowValueList)
    {
        n_mtautodelineationdialog::ST_OrganGroupInfo groupItem;
        groupItem.id = rowValue.toInt();
        groupItem.name = GetColumnText(rowValue, ColType_Name);
        groupItem.type = GetColumnText(rowValue, ColType_Type) == tr("自定义") ? 2 : 1;
        retList.append(groupItem);
    }

    return retList;
}

void GroupTableView::addRow(const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo)
{
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //拖动
    QCustMtLabelParam* dragParam = new QCustMtLabelParam();
    dragParam->_pixPath = ":/images/images/drag.png";
    cellWidgetParamMap.insert(ColType_Move, dragParam);
    //组名
    QCustMtLineEditParam* lineEditParam = new QCustMtLineEditParam();
    lineEditParam->_maxLength = 64;
    lineEditParam->_text = stGroupInfo.name;
    lineEditParam->_regExpStr = RegExp_NotSemicolon;
    lineEditParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Name, lineEditParam);
    //类型
    QCustMtLabelParam* typeParam = new QCustMtLabelParam();
    typeParam->_text = stGroupInfo.type == 1 ? tr("系统内置") : tr("自定义");
    cellWidgetParamMap.insert(ColType_Type, typeParam);
    //操作
    QMTAbsHorizontalBtnsParam* btnsOperation = new QMTAbsHorizontalBtnsParam();
    btnsOperation->_pixPathList.append(":/images/images/icon_del2_green.png");
    btnsOperation->_disablePixPathList.append(":/images/images/icon_del2_disable.png");

    if (stGroupInfo.type == 1)
    {
        btnsOperation->_disableBtnList.append(0);
    }

    cellWidgetParamMap.insert(ColType_Operation, btnsOperation);
    this->AddRowItem(QString::number(stGroupInfo.id), cellWidgetParamMap);

    if (stGroupInfo.type == 1)
    {
        QCustMtLineEdit* editName = qobject_cast<QCustMtLineEdit*>(GetCellWidget(QString::number(stGroupInfo.id), ColType_Name));

        if (nullptr != editName)
        {
            editName->SetEnableEdit(false);
        }
    }
}

void GroupTableView::insertRow(int rowIndex, const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo)
{
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //拖动
    QCustMtLabelParam* dragParam = new QCustMtLabelParam();
    dragParam->_pixPath = ":/images/images/drag.png";
    cellWidgetParamMap.insert(ColType_Move, dragParam);
    //组名
    QCustMtLineEditParam* lineEditParam = new QCustMtLineEditParam();
    lineEditParam->_maxLength = 64;
    lineEditParam->_text = stGroupInfo.name;
    //lineEditParam->_regExpStr = RegExp_CharNumber2;
    lineEditParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Name, lineEditParam);
    //类型
    QCustMtLabelParam* typeParam = new QCustMtLabelParam();
    typeParam->_text = stGroupInfo.type == 1 ? tr("系统内置") : tr("自定义");
    cellWidgetParamMap.insert(ColType_Type, typeParam);
    //操作
    QMTAbsHorizontalBtnsParam* btnsOperation = new QMTAbsHorizontalBtnsParam();
    btnsOperation->_pixPathList.append(":/images/images/icon_del2_green.png");
    btnsOperation->_disablePixPathList.append(":/images/images/icon_del2_disable.png");

    if (stGroupInfo.type == 1)
    {
        btnsOperation->_disableBtnList.append(0);
    }

    cellWidgetParamMap.insert(ColType_Operation, btnsOperation);
    this->InsertRowItem(rowIndex, QString::number(stGroupInfo.id), cellWidgetParamMap);

    if (stGroupInfo.type == 1)
    {
        QCustMtLineEdit* editName = qobject_cast<QCustMtLineEdit*>(GetCellWidget(QString::number(stGroupInfo.id), ColType_Name));

        if (nullptr != editName)
        {
            editName->SetEnableEdit(false);
        }
    }
}

void GroupTableView::mousePressEvent(QMouseEvent* e)
{
    if (e->button() == Qt::LeftButton)
    {
        QModelIndex index = indexAt(e->pos());

        if (index.isValid())
        {
            int first_visible_index = rowAt(verticalScrollBar()->value());
            m_bValidPress = true;
            m_rowFrom = index.row();
            m_dragPoint = e->pos();
            m_dragText = GetColumnText(GetRowUniqueValue(m_rowFrom), ColType_Name);
            m_dragPointAtItem = m_dragPoint - QPoint(0, (index.row() - first_visible_index) * m_rowHeight);
        }
    }

    QMTAbstractTableView::mousePressEvent(e);
}

void GroupTableView::mouseMoveEvent(QMouseEvent* e)
{
    if (!m_bValidPress)
    {
        return;
    }

    if (!(e->buttons() & Qt::LeftButton))
        return;

    if ((e->pos() - m_dragPoint).manhattanLength() < QApplication::startDragDistance())
        return;

    m_dragLine->show();
    DoDrag();           //开始拖拽，完成拖拽后才会继续往下走
    m_dragLine->hide();
    m_bValidPress = false;
}

void  GroupTableView::dragEnterEvent(QDragEnterEvent* e)
{
    if (e->mimeData()->hasText())
    {
        e->acceptProposedAction();
    }
    else
    {
        e->ignore();
        QMTAbstractTableView::dragEnterEvent(e);
    }
}

void  GroupTableView::dragMoveEvent(QDragMoveEvent* e)
{
    if (e->mimeData()->hasText())
    {
        int nCurRow = 0;
        int first_visible_index = rowAt(verticalScrollBar()->value());
        int ptY = e->pos().y();

        if (ptY < m_rowHeight / 2 && first_visible_index > 0)//移到了顶部，上滚
        {
            verticalScrollBar()->setValue(--first_visible_index);
        }
        else if ((ptY > height() - horizontalHeader()->height() - m_rowHeight / 2)
                 && (rowCount() > (height() - horizontalHeader()->height()) / m_rowHeight + first_visible_index)) //移到了底部，下滚
        {
            verticalScrollBar()->setValue(++first_visible_index);
        }

        QModelIndex index = indexAt(e->pos());

        if (index.isValid())
        {
            if (ptY - (index.row() - first_visible_index) * m_rowHeight >= m_rowHeight / 2)
            {
                nCurRow = index.row() + 1;
            }
            else
            {
                nCurRow = index.row();
            }
        }
        else
        {
            nCurRow = this->GetRowCount();
        }

        m_rowTo = nCurRow;
        m_dragLine->setGeometry(1, (m_rowTo - first_visible_index) * m_rowHeight + 30, width() - 2, 2);
        e->acceptProposedAction();
        return;
    }

    e->ignore();
    QMTAbstractTableView::dragMoveEvent(e);
}

void  GroupTableView::dropEvent(QDropEvent* e)
{
    if (e->mimeData()->hasText())
    {
        if (m_rowTo != m_rowFrom)
        {
            MoveRow(m_rowFrom, m_rowTo);
        }

        e->acceptProposedAction();
        return;
    }

    e->ignore();
    QMTAbstractTableView::dropEvent(e);
}

void GroupTableView::DoDrag()
{
    QDrag* drag = new QDrag(this);
    QMimeData* mimeData = new QMimeData;
    mimeData->setText(m_dragText);
    drag->setMimeData(mimeData);
    // 设置拖拽图片
    QPixmap drag_img(width(), m_rowHeight);
    drag_img.fill(CMtCoreWidgetUtil::formatColor("rgba(@color0,0.5)"));
    QPainter painter(&drag_img);
    painter.setPen(CMtCoreWidgetUtil::formatColor("rgba(@color4,1)"));
    painter.drawText(QRectF(40, 0, width(), m_rowHeight), m_dragText, QTextOption(Qt::AlignVCenter));
    drag->setPixmap(drag_img);
    drag->setHotSpot(m_dragPointAtItem);

    //***注意：此句会阻塞，进入drag的拖拽消息循环，会依次触发dragEnterEvent、dragMoveEvent、dropEvent消息
    if (drag->exec(Qt::MoveAction) == Qt::MoveAction)
    {
        // 确认移动操作
    }
}

void GroupTableView::MoveRow(int from, int to)
{
    if (from == to)
    {
        return;
    }

    n_mtautodelineationdialog::ST_OrganGroupInfo groupInfo;
    QString rowValue = GetRowUniqueValue(from);
    groupInfo.id = rowValue.toInt();
    groupInfo.name = GetColumnText(rowValue, ColType_Name);
    groupInfo.type = GetColumnText(rowValue, ColType_Type) == tr("自定义") ? 2 : 1;
    //删除拖动行
    DeleteRowItem(rowValue);

    //重新插入拖动行
    if (from > to)
    {
        insertRow(to, groupInfo);
    }
    else
    {
        insertRow(to - 1, groupInfo);
    }

    SetCurrentRow(rowValue);
}