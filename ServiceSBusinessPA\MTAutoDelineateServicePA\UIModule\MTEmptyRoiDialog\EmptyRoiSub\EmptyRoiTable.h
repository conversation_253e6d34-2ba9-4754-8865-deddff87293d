﻿#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class EmptyRoiTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum
    {
        ColType_Name = 0,
        ColType_Label,
        ColType_Color,
        ColType_Type,
        ColType_Desc,
        ColType_TemplateRelated
    };
    /// <summary>
    /// 构造函数
    /// </summary>
    EmptyRoiTable(QWidget* parent = nullptr);
    ~EmptyRoiTable();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="allRoiTypeList">[IN]全部Roi类型集合</param>
    /// <param name="allLabelList">[IN]全部标签</param>
    /// <param name="stOrganList">[IN]Organ信息集合</param>
    /// <param name="modelCollectionInfoList">[IN]模板信息集合</param>
    void init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
              const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="stEmptyOrgan">[IN]空勾画信息</param>
    void addRow(const n_mtautodelineationdialog::ST_Organ stEmptyOrgan);

    /// <summary>
    /// 删除当前行
    /// </summary>
    /// <param name="organId">[OUT]返回删除的勾画ID</param>
    /// <param name="organName">[OUT]返回删除的勾画名称</param>
    /// <returns>若为true，表示成功执行删除操作</returns>
    bool delCurrentRow(QString& organId, QString& organName);

    /// <summary>
    /// 获取所有空勾画信息
    /// </summary>
    /// <returns>所有空勾画信息</returns>
    QList<n_mtautodelineationdialog::ST_Organ> getAllEmptyOrganInfo();

signals:
    /// <summary>
    /// 列表信息发生了改变
    /// </summary>
    void sigTableInfoChanged(const QString& defOrganName, int col, const QString& newText);

    /// <summary>
    /// 创建列表Item信号
    /// </summary>
    void sigCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo);

protected slots:
    /// <summary>
    /// 某个按键点击了
    /// </summary>
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);

    /// <summary>
    /// 单元格文本发送了改变
    /// </summary>
    void slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText);

    /// <summary>
    /// 添加一条记录
    /// </summary>
    void slotCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo);

protected:
    /// <summary>
    /// 重载单元格创建完成后回调
    /// </summary>
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget);

    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <param name="headList">[IN]表头文本集合</param>
    /// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);

private:
    QStringList m_allRoiTypeList;       //全部Roi类型集合
    QStringList m_allLabelList;         //全部标签
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> m_modelCollectionInfoList;   //所有模板信息
    QMap<int, n_mtautodelineationdialog::ST_Organ> m_allOrganMap;                           //全部Organ信息集合(key-ST_Organ-id value-ST_Organ)

    bool m_bDestroyed;                  //列表是否被销毁
};