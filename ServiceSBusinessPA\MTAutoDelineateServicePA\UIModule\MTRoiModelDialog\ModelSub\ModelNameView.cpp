﻿#include "ModelNameView.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLineEdit.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtComboBox.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTAbsHorizontalBtns.h"
#include "MtMessageBox.h"

/// <summary>
/// 构造函数
/// </summary>
ModelNameView::ModelNameView(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    connect(this, SIGNAL(sigRowItemClicked(const mtuiData::TableWidgetItemIndex&)), this, SLOT(slotRowItemChanged(const mtuiData::TableWidgetItemIndex&)));
}

ModelNameView::~ModelNameView()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void ModelNameView::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 初始化
/// </summary>
void ModelNameView::init(const QList<n_mtautodelineationdialog::ST_SketchModel>& modelInfoList)
{
    initTableView();
    //默认模型排在最前
    QList<n_mtautodelineationdialog::ST_SketchModel> orderModelInfoList;
    n_mtautodelineationdialog::ST_SketchModel emptyModel;

    for (const auto& item : modelInfoList)
    {
        if (item.id == -1)
        {
            emptyModel = item;
            continue;
        }

        if (item.id == 0)
        {
            orderModelInfoList.push_front(item);
        }
        else
        {
            orderModelInfoList.push_back(item);
        }
    }

    if (!emptyModel.modelName.isEmpty())
    {
        orderModelInfoList.push_back(emptyModel);
    }

    for (const auto& item : orderModelInfoList)
    {
        addRow(item);
    }

    SetCurrentRow(QString::number(orderModelInfoList.back().id), true);
}

void ModelNameView::addRow(const n_mtautodelineationdialog::ST_SketchModel& modelInfo)
{
    QMap<int/*column*/, ICellWidgetParam*> cellWidgetParamMap;
    getNewCellParamMap(modelInfo, cellWidgetParamMap);
    QString rowValue = QString::number(modelInfo.id);
    m_allModelInfoMap.insert(modelInfo.id, modelInfo);
    this->AddRowItem(rowValue, cellWidgetParamMap);
}

/// <summary>
/// 删除一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
void ModelNameView::delRow(const QString& rowValue)
{
    m_allModelInfoMap.remove(rowValue.toInt());
    this->DeleteRowItem(rowValue);
}

/// <summary>
/// 获取当前选中的行
/// </summary>
/// <returns>rowValue</returns>
QString ModelNameView::getCurSelectRow()
{
    return this->GetCurUniqueValue();
}

/// <summary>
/// 删除当前行
/// </summary>
void ModelNameView::delCurrentRow()
{
    QString templateId = this->GetCurUniqueValue();

    if (templateId.isEmpty())
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要删除的模型"));
        return;
    }

    QString msg = QString(tr("您确认要删除该模型吗？"));
    int ret = MtMessageBox::redWarning(this, msg, QString(), tr("删除"));

    if (QMessageBox::Yes == ret)
    {
        this->DeleteCurRow();
        m_allModelInfoMap.remove(templateId.toInt());
        return;
    }

    return;
}

void ModelNameView::setModelName(const QString& rowValue, const QString& templateName)
{
    //this->UpdateCellParamText(rowValue, COL_ModelName, templateName);
    QWidget* widget = this->GetCellWidget(rowValue, COL_ModelName);

    if (widget != nullptr)
    {
        ((QCustMtLabel*)widget)->GetLabel()->setText(templateName);
    }
}

/// <summary>
/// 初始化表格
/// </summary>
void ModelNameView::initTableView()
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._defaultColumn = 2;
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 0;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    firstViewParam._headParam._columnWidthMap.insert(COL_ModelName, 200);
    firstViewParam._headParam._columnWidthMap.insert(COL_Modality, 40);
    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._gridColor = CMtCoreWidgetUtil::formatColor("rgb(@color2)");
    firstViewParam._canvasBackColor = CMtCoreWidgetUtil::formatColor("rgba(@color0,0.5)");
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = true;
    firstViewParam._showGrid = false;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

void ModelNameView::getNewCellParamMap(const n_mtautodelineationdialog::ST_SketchModel& modelInfo, QMap<int/*column*/, ICellWidgetParam*>& outCellParamMap)
{
    //模型名称
    QCustMtLabelParam* modelNameLabelParam = new QCustMtLabelParam();
    modelNameLabelParam->_text = modelInfo.modelName;
    outCellParamMap.insert(COL_ModelName, modelNameLabelParam);
    //模型模态
    QCustMtLabelParam* modalityLabelParam = new QCustMtLabelParam();
    modalityLabelParam->_text = modelInfo.modality;
    outCellParamMap.insert(COL_Modality, modalityLabelParam);
}

void ModelNameView::slotRowItemChanged(const mtuiData::TableWidgetItemIndex& indexInfo)
{
    emit sigModelTableChanged(indexInfo._uniqueValue);
}