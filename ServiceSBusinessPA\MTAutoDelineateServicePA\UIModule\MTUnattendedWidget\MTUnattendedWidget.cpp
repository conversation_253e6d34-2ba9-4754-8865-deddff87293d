﻿#include "MTUnattendedWidget.h"
#include "ui_MTUnattendedWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtMessageBox.h"
#include "MTUnattended/UnattendedSub/OptUnattendDataNew.h"


namespace n_mtautodelineationdialog
{

MTUnattendedWidget::MTUnattendedWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MTUnattendedWidgetClass;
    ui->setupUi(this);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTUnattendedWidget, " << errMsg.toStdString();
        }
    }
}

MTUnattendedWidget::~MTUnattendedWidget()
{
}

/// <summary>
/// 设置默认导出地址
/// </summary>
/// <param name="stExportAddr">[IN]默认导出地址</param>
void MTUnattendedWidget::setDefaultExportAddr(const ST_AddrSimple& stExportAddr)
{
    OptUnattendDataNew::setDefaultExportAddr(stExportAddr);
}

/// <summary>
/// 设置右侧规则列表最小高度
/// </summary>
/// <param name="minHeightNum">[IN]最小高度</param>
void MTUnattendedWidget::setMinHeight_widget_table_sketch(const int minHeightNum)
{
    ui->widget->setMinHeight_widget_table_sketch(minHeightNum);
}

/// <summary>
/// 显示无人值守设置弹窗
/// </summary>
/// <param name="unattendedConfigMap">[IN]无人值守信息集合(key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息)</param>
/// <param name="allTemplateNameMap">[IN]所有无人值守勾画模板id-模板名称集合(key-模板id value-模板名称)</param>
/// <param name="allRemoteScpInfoList">[IN]所有远程服务器信息</param>
/// <param name="allLocalServerNameMap">[IN]所有本地服务器名称集合(key-serverType value-serverName)</param>
/// <param name="stCallBackUnattended">[OUT]数据回调</param>
void MTUnattendedWidget::init(const QMap<QString, ST_UnattendedConfig>& unattendedConfigMap, const QMap<EM_OptDcmType, QMap<int, QString>>& allUnattendTemplateNameMap, const QList<ST_AddrSimple> allRemoteScpInfoList, const QMap<int, QStringList>& allLocalServerNameMap, ST_CallBack_Unattended& stCallBackUnattended)
{
    //初始化数据
    if (m_ptrOptUnattendData != nullptr)
    {
        delete m_ptrOptUnattendData;
        m_ptrOptUnattendData = nullptr;
    }

    m_ptrOptUnattendData = new OptUnattendDataNew(unattendedConfigMap, allUnattendTemplateNameMap, allRemoteScpInfoList, allLocalServerNameMap);
    ui->widget->init((OptUnattendDataNew*)m_ptrOptUnattendData, stCallBackUnattended);
}

/// <summary>
/// 是否处于编辑状态
/// </summary>
/// <returns>true是</returns>
bool MTUnattendedWidget::isEditState(bool showErrDlg)
{
    bool ret = ui->widget->isEditState();

    if (ret == true && showErrDlg == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请先保存规则"));
    }

    return ret;
}

/// <summary>
/// 获取默认导出地址
/// </summary>
ST_AddrSimple MTUnattendedWidget::getDefaultExportAddr()
{
    return OptUnattendDataNew::getDefaultExportAddr();
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTUnattendedWidget::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    QHash<QString, QString> imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
    ui->widget->setImagePathHash(imagePathHash);
}

}
