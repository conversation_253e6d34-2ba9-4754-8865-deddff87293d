﻿// *********************************************************************************
// <remarks>
// FileName    : GroupItemListWidget
// Author      : zlw
// CreateTime  : 2024-01-20
// Description : listWidget窗体(由标题GroupHTitle和子项groupItem组成)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_GroupItemListWidget.h"
#include "GroupHTitle.h"
#include "GroupItem.h"


class GroupItemListWidget : public QWidget
{
    Q_OBJECT

public:
    GroupItemListWidget(QWidget* parent = nullptr);
    ~GroupItemListWidget();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 设置唯一标识
    /// </summary>
    /// <param name="uniqueKey">[IN]唯一标识</param>
    void setUniqueKey(const int uniqueKey);

    /// <summary>
    /// 获取唯一标识
    /// </summary>
    int getUniqueKey();

    /// <summary>
    /// 添加标题
    /// </summary>
    /// <param name="pageTypeEnum">[IN]item类型</param>
    /// <param name="data">[IN]数据</param>
    /// <param name="topMargin">[IN]上边距</param>
    /// <param name="bottomMargin">[IN]下边距</param>
    void addTitle(const GroupHTitle::EM_PageType pageTypeEnum, const GroupHTitleData& data, int topMargin = 10, int bottomMargin = 6);

    /// <summary>
    /// 添加item
    /// </summary>
    /// <param name="pageTypeEnum">[IN]item类型</param>
    /// <param name="dataList">[IN]数据集合</param>
    /// <param name="itemWidthNum">[IN]item宽度</param>
    /// <param name="maxColumn">[IN]最大列数</param>
    /// <param name="horizontalSpace">[IN]水平间距</param>
    void addItems(const GroupItem::EM_PageType pageTypeEnum, const QList<GroupItemData>& dataList, int itemWidthNum, int maxColumn = 2, int horizontalSpace = 24);

    /// <summary>
    /// 清空
    /// </summary>
    void clearAllItem();

    /// <summary>
    /// 刷新所有标题勾选状态
    /// </summary>
    void flushAllTitleCheckStatus();

    /// <summary>
    /// 滚动到最上方
    /// </summary>
    /// <param name="isTop">[IN]true最上方 false最下方</param>
    void scrollTop(const bool isTop);

    /// <summary>
    /// 滚动到组
    /// </summary>
    void scrollToGroup(int groupId);

    /// <summary>
    /// 展开全部
    /// </summary>
    /// <param name="expand">true全部</param>
    void expandTreeAll(const GroupHTitle::EM_PageType pageTypeEnum, const bool expand);

    /// <summary>
    /// 勾选全部
    /// </summary>
    /// <param name="expand">true全部</param>
    void checkTreeAll(const GroupHTitle::EM_PageType pageTypeEnum, const bool isCheck);

    /// <summary>
    /// 获取勾选的item集合
    /// </summary>
    /// <returns>key-groupId value-itemId</returns>
    QMap<int, QSet<int>> getCheckedItemMap();

signals: //发送到父控件
    void sigOneItemCheckFromGroupItemListWidget(const int groupId, const QSet<int> itemIdSet, const bool checked); //item点击(itemId-器官id),发送到AutoSketchTemplateWidget
    void sigAllItemExpandFromGroupItemListWidget(const GroupHTitle::EM_PageType pageTypeEnum, const bool isExpand);//是否全部展开/收起

signals: //发送到子控件
    void sigCheckAllToChildGroupItem_MtLabelCheck(const int groupId, const bool checked);    //勾选分组下的全部MtLabelCheck，发送到GroupItem

protected slots:
    /// <summary>
    /// 标题复选框是否勾选，接收子控件的信号
    /// </summary>
    void slotChildHTitleCheckToGroupItemListWidget(const int groupId, const bool checked);

    /// <summary>
    /// 标题展开/收起，接收子控件的信号
    /// </summary>
    void slotChildHTitleExpandToGroupItemListWidget(const GroupHTitle::EM_PageType pageTypeEnum, const int groupId, const bool expanded/*true展开*/);

private:
    Ui::GroupItemListWidgetClass ui;
    int m_uniqueKey;                           //唯一标识
    QHash<QString, QString> m_imagePathHash;        //图标map(key-name value-图片相对路径)
    QMap<int, GroupHTitle*> m_groupHTitleMap;       //标题对象(key-groupId)
    QMap<int, QListWidgetItem*> m_groupItemWidgetMap;//组item对应的ListWidget，用于滚动定位
    QMap<int, QMap<int, bool>> m_itemStateMap;      //item状态(key-groupId value-itemId value-checked)
    QMap<int, QList<QListWidgetItem*>> m_labelLabel_listWidgetTitleMap; //非编辑页下Title的MtListWidget每一行对象(key-groupId)
    QMap<int, QList<QListWidgetItem*>> m_labelCheck_listWidgetTitleMap; //编辑页下Tilte的MtListWidget每一行对象(key-groupId)
    QMap<int, QList<QListWidgetItem*>> m_labelLabel_listWidgetItemMap;  //非编辑页下(MtLabelLable)的MtListWidget每一行对象(key-groupId)
    QMap<int, QList<QListWidgetItem*>> m_labelCheck_listWidgetItemMap;  //编辑页下(MtLabelCheck)的MtListWidget每一行对象(key-groupId)
};
