﻿#include "AccuComponentUi/Header/UnitUIComponent\MtUnitLabelWithColor.h"
#include "ui_MtUnitLabelWithColor.h"
#include "MtToolButton.h"
#include "CMtCoreDefine.h"
#include <qDebug>
#include "CMtCoreWidgetUtil.h"


MtUnitLabelWithColorParam::MtUnitLabelWithColorParam()
{
    _cellWidgetType = DELEAGATE_MtUnitLabelWithColor;
}

MtUnitLabelWithColorParam::~MtUnitLabelWithColorParam()
{
}

QWidget* MtUnitLabelWithColorParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    MtUnitLabelWithColor* btns = new MtUnitLabelWithColor(parent);
    btns->SetupCellWidget(*this);
    return btns;
}

MtUnitLabelWithColor::MtUnitLabelWithColor(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MtUnitLabelWithColor;
    ui->setupUi(this);
    ui->mtLabel_color->setFixedWidth(2);
    ui->mtLabel_text->setMtType(MtLabel::myLabel1);
    ui->mtLabel_text->setElideMode(Qt::ElideRight);
}

MtUnitLabelWithColor::~MtUnitLabelWithColor()
{
    MT_DELETE(ui);
}

bool MtUnitLabelWithColor::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        ui->mtLabel_text->setText(text);
        return true;
    }
    else if (QMetaType::QColor == userType)
    {
        QColor color = updateData.value<QColor>();
        SetPreBackgroundColor(color);
        return true;
    }

    return false;
}


QString MtUnitLabelWithColor::GetCurText()
{
    return  ui->mtLabel_text->text();
}

void MtUnitLabelWithColor::SetupCellWidget(MtUnitLabelWithColorParam& param)
{
    //color
    if (param._preColorWidth > 0)
    {
        ui->mtLabel_color->setFixedWidth(param._preColorWidth);
    }

    SetPreBackgroundColor(param._preColor);
    //text
    ui->mtLabel_text->setText(param._text);

    if (param._mtType > 0)
    {
        ui->mtLabel_text->setMtType((MtLabel::MtType)param._mtType);
    }
}

void MtUnitLabelWithColor::SetPreBackgroundColor(const QColor& bkColor)
{
    QString tmpColorStr = CMtCoreWidgetUtil::formatColorStr(bkColor, false);
    QString styleSheetStr = QString("#mtLabel_color{background-color:%1;}").arg(tmpColorStr);
    ui->mtLabel_color->setStyleSheet(styleSheetStr);
}
