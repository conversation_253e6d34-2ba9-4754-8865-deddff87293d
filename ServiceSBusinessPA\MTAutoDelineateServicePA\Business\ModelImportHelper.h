#pragma once

#include <QObject>
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include <QTimer>
#include <string>

#include "MtProgressDialog.h"

class ModelImportHelper : public QObject
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    ModelImportHelper();
    /// <summary>
    /// 析构函数
    /// </summary>
    ~ModelImportHelper();

    /// <summary>
    /// 上传文件到服务器
    /// </summary>
    /// <param name="filePath">文件路径</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>true：成功；false：失败</returns>
    bool UploadFile(const QString& filePath, QString& errMsg);

signals:
    /// <summary>
    /// 上传结束信号
    /// </summary>
    void SigUploadFinished();

private slots:
    /// <summary>
    /// 处理网络上传结束
    /// </summary>
    void OnUploadFinished();
    /// <summary>
    /// 处理上传进度
    /// </summary>
    /// <param name="bytesSent">当前发送的字节数</param>
    /// <param name="bytesTotal">文件总大小</param>
    void OnUploadProgress(qint64 bytesSent, qint64 bytesTotal);
    /// <summary>
    /// 处理上传进度
    /// </summary>
    /// <param name="error">错误信息</param>
    void OnNetworkError(QNetworkReply::NetworkError error);

private:
    /// <summary>
    /// 网络对象
    /// </summary>
    QNetworkAccessManager* m_networkManager = nullptr;
    /// <summary>
    /// 网络响应对象
    /// </summary>
    QNetworkReply* m_currentReply = nullptr;
    /// <summary>
    /// 上传是否成功
    /// </summary>
    bool m_uploadSuccess = false;
    /// <summary>
    /// 错误信息
    /// </summary>
    QString m_errorMessage;
    /// <summary>
    /// 上传进度窗口
    /// </summary>
    MtProgressDialog m_progressDlg;
};