﻿// ************************************************************
// <remarks>
// Author      : Qiuchh
// CreateTime  : 2024-11-12
// Description : 拖曳交互的结构体信息
// </remarks>
// ************************************************************
#pragma once

#include <QObject>
#include <QMetaType>
#include <QVariant>
#include <QDataStream>

//拖曳的类型宏定义
#define DragItemUiInfo_MimeType     "ST_DragItemUiInfo_Type"
//布局拖曳信息
struct ST_DragItemUiInfo
{
    /// <summary>
    /// 接收的布局组件UID
    /// </summary>
    QString acceptLCptUID;
    /// <summary>
    /// 接收的布局组件的对象Id，用于区别同个组件UID，不同对象
    /// </summary>
    int acceptLCptObjId = 0;
    /// <summary>
    /// 单元组件UID
    /// </summary>
    QString unitCptUID;
    /// <summary>
    /// 单元组件名称
    /// </summary>
    QString unitCptName;
    /// <summary>
    /// 单元组件对象Id。用于区别同个组件UID，不同对象
    /// </summary>
    int unitCptObjId = 0;
};
Q_DECLARE_METATYPE(ST_DragItemUiInfo)

//重载QDataStream
inline QDataStream& operator<<(QDataStream& in, ST_DragItemUiInfo& buttonInfo)
{
    in << buttonInfo.acceptLCptUID << buttonInfo.acceptLCptObjId << buttonInfo.unitCptUID << buttonInfo.unitCptName << buttonInfo.unitCptObjId;
    return in;
}

inline QDataStream& operator>>(QDataStream& out, ST_DragItemUiInfo& buttonInfo)
{
    out >> buttonInfo.acceptLCptUID >> buttonInfo.acceptLCptObjId >> buttonInfo.unitCptUID >> buttonInfo.unitCptName >> buttonInfo.unitCptObjId;
    return out;
}