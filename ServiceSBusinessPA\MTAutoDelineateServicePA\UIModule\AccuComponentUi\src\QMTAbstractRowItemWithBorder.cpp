﻿#include "AccuComponentUi\Header\QMTAbstractRowItemWithBorder.h"
#include "ui_QMTAbstractRowItemWithBorder.h"
#include "CMtCoreDefine.h"

QMTAbstractRowItemWithBorder::QMTAbstractRowItemWithBorder(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTAbstractRowItemWithBorder;
    ui->setupUi(this);
    //    ui->widget_leftMargin->setFixedWidth(12);
    _columnWidgetParentList.append(ui->widget);
    _columnWidgetParentList.append(ui->widget_2);
    _columnWidgetParentList.append(ui->widget_3);
    _columnWidgetParentList.append(ui->widget_4);
    _columnWidgetParentList.append(ui->widget_5);
    _columnWidgetParentList.append(ui->widget_6);

    for (int i = 0; i < _columnWidgetParentList.size(); ++i)
    {
        QWidget* widget = _columnWidgetParentList.at(i);
        widget->hide();
    }
}

QMTAbstractRowItemWithBorder::~QMTAbstractRowItemWithBorder()
{
    for (int i = 0; i < _columnWidgetList.size(); ++i)
    {
        QWidget* widget = _columnWidgetList.at(i);
        delete widget;
    }

    _columnWidgetList.clear();
    MT_DELETE(ui);
}

void QMTAbstractRowItemWithBorder::InitPerRowWidgetParam(QMTPerRowItemWidgetParam& param)
{
    _perRowItemParam = &param;
    SetColumnCount(_perRowItemParam->_defaultColumn);

    for (int i = 0; i < _perRowItemParam->_defaultColumn; ++i)
    {
        int width = _perRowItemParam->_columnWidthMap.value(i);
        int type = _perRowItemParam->_colDelegateTypeMap.value(i);
        SetColumnWidth(i, width);
        //SetColumnType(i, type);
    }

    if (_perRowItemParam->_rowWidgetheight > 0)
    {
        this->setFixedHeight(_perRowItemParam->_rowWidgetheight);
    }
}

void QMTAbstractRowItemWithBorder::SetLabelText(QString text)
{
    //    ui->label_first->setText(text);
}

void QMTAbstractRowItemWithBorder::SetColumnCount(int columns)
{
    if (columns < 0 || columns >= _columnWidgetParentList.size())
        return;

    for (int i = 0; i < columns; ++i)
    {
        QWidget* widget = _columnWidgetParentList.at(i);
        widget->show();
    }
}

void QMTAbstractRowItemWithBorder::SetColumnWidth(int column, int width)
{
    if (column < 0 || column >= _columnWidgetParentList.size())
        return;

    if (width > 0)
    {
        QWidget* widget = _columnWidgetParentList.at(column);
        widget->setFixedWidth(width);
    }
}

void QMTAbstractRowItemWithBorder::SetComboBoxSignals(QComboBox* comboBox)
{
    //connect(comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
    //connect(comboBox, SIGNAL(currentIndexChanged(const QString&)), this, SLOT(slotCurrentIndexChanged(const QString&)));
    connect(comboBox, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCurrentTextChanged(const QString&)));
}

QWidget* QMTAbstractRowItemWithBorder::CreateColumnWidget(int type, QWidget* parent, QString str)
{
    QWidget* ret = nullptr;

    if (Type_Label == type)
    {
        QLabel* label = new QLabel(parent);
        parent->layout()->addWidget(label);
        label->setText(str);
        ret = label;
    }
    else if (Type_LineEidt == type)
    {
    }
    else if (Type_NewLineEdit == type)
    {
    }
    else if (Type_ToolButton == type)
    {
    }
    else if (Type_ComboBox == type)
    {
        QComboBox* comboBox = new QMTComboBox(parent);
        comboBox->setMaxVisibleItems(6);
        SetComboBoxSignals(comboBox);
        parent->layout()->addWidget(comboBox);
        QStringList strList = str.split(",");
        comboBox->addItems(strList);
        ret = comboBox;
    }

    _columnWidgetList.append(ret);
    return ret;
}

void QMTAbstractRowItemWithBorder::CreateWidgetItem(QJsonObject& obj)
{
    QString key = _perRowItemParam->_mainKey;
    _uniqueValue = obj.value(key).toString();
    QMap<int, QString> columnKeyMap = _perRowItemParam->_columnKeyMap;
    int column = _perRowItemParam->_defaultColumn;
    QMap<int, int> colDelegateTypeMap = _perRowItemParam->_colDelegateTypeMap;

    for (int i = 0; i < column; ++i)
    {
        int type = colDelegateTypeMap.value(i);
        QString key = columnKeyMap.value(i);
        QString value = obj.value(key).toString();
        QWidget* parentWidget = _columnWidgetParentList.at(i);
        CreateColumnWidget(type, parentWidget, value);
    }
}

void QMTAbstractRowItemWithBorder::SetColumnType(int column, int type)
{
    if (column < 0 || column >= _columnWidgetParentList.size())
        return;

    //_columnWidgetTypeMap.insert(column, type);
    QWidget* parentWidget = _columnWidgetParentList.at(column);
    CreateColumnWidget(type, parentWidget);
}

void QMTAbstractRowItemWithBorder::AddComboBoxItem(int column, QString text)
{
    int type = _perRowItemParam->_colDelegateTypeMap.value(column);

    if (Type_ComboBox != type)
        return;

    if (column < 0 || column >= _columnWidgetList.size())
        return;

    QWidget* columnWidget = _columnWidgetList.at(column);
    QComboBox* comboBoxWidget = qobject_cast<QComboBox*>(columnWidget);

    if (comboBoxWidget)
        comboBoxWidget->addItem(text);
}

void QMTAbstractRowItemWithBorder::AddComboBoxItems(int column, QStringList strList)
{
    int type = _perRowItemParam->_colDelegateTypeMap.value(column);

    if (Type_ComboBox != type)
        return;

    if (column < 0 || column >= _columnWidgetList.size())
        return;

    QWidget* columnWidget = _columnWidgetList.at(column);
    QComboBox* comboBoxWidget = qobject_cast<QComboBox*>(columnWidget);

    if (comboBoxWidget)
        comboBoxWidget->addItems(strList);
}

QString QMTAbstractRowItemWithBorder::CurrentText(int column)
{
    QString ret;

    if (column < 0 || column >= _columnWidgetList.size())
        return QString();

    QWidget* columnWidget = _columnWidgetList.at(column);
    QComboBox* comboBoxWidget = qobject_cast<QComboBox*>(columnWidget);

    if (comboBoxWidget)
        ret = comboBoxWidget->currentText();

    return ret;
}


void QMTAbstractRowItemWithBorder::slotCurrentIndexChanged(int index)
{
}

void QMTAbstractRowItemWithBorder::slotCurrentIndexChanged(const QString& text)
{
}

int QMTAbstractRowItemWithBorder::GetColumnWidgetIndex(QWidget* widget)
{
    if (widget == NULL)
        return -1;

    for (int i = 0; i < _columnWidgetList.size(); ++i)
    {
        QWidget* widgetTmp = _columnWidgetList.at(i);

        if (widgetTmp == widget)
            return i;
    }

    return -1;
}

QWidget* QMTAbstractRowItemWithBorder::GetColumnWidget(int index)
{
    if (index < 0 || index >= _columnWidgetList.size())
        return NULL;

    return _columnWidgetList[index];
}

void QMTAbstractRowItemWithBorder::slotCurrentTextChanged(const QString& text)
{
    QObject* sendObj = this->sender();
    QComboBox* sendWidget = qobject_cast<QComboBox*>(sendObj);
    int index = GetColumnWidgetIndex(sendWidget);

    if (index >= 0)
    {
        emit sigCurrentIndexChanged(index, text);
    }
}