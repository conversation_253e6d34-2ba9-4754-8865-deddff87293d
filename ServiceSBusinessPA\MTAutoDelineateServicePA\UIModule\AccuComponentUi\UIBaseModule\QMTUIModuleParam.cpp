﻿#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QComboBox>
#include "AccuComponentUi/Header/UnitUIComponent\QMTAbsHorizontalBtns.h"
#include "AccuComponentUi/Header/UnitUIComponent\QMTAbsComboBox.h"
#include "AccuComponentUi/Header/UnitUIComponent\QMTAbsLineEdit.h"
#include "AccuComponentUi/Header/QMTLineEdit.h"
#include "AccuComponentUi/Header/UnitUIComponent\QMTCheckBox.h"
#include "AccuComponentUi/Header/UnitUIComponent\QMTCheckBoxLabel.h"
#include "AccuComponentUi/Header/QMTButtonMenuWidget.h"


QMTAbstractCellWidget::QMTAbstractCellWidget()
{
}


QMTAbstractCellWidget::~QMTAbstractCellWidget()
{
}

QString QMTAbstractCellWidget::GetCurText()
{
    QString text;
    return text;
}

int QMTAbstractCellWidget::GetCellState()
{
    return Qt::CheckState::Unchecked;
}

bool QMTAbstractCellWidget::GetCellChecked(int index)
{
    return false;
}

void QMTAbstractCellWidget::SetEnableEdit(bool bEdit)
{
}

void QMTAbstractCellWidget::SetButtonEnable(int btnIndex/**/, bool bEnable)
{
}

void QMTAbstractCellWidget::SetCellData(int role, const QString& data)
{
    _custDataMap.insert(role, data);
}


QString QMTAbstractCellWidget::GetCellData(int role)
{
    return _custDataMap.value(role);
}


QMTAbsTableHeadParam::QMTAbsTableHeadParam()
{
    _backGroundColor = CMtCoreWidgetUtil::formatColor("rgba(@colorA2,0.5)");     //表头背景色颜色
    _textColor = CMtCoreWidgetUtil::formatColor("rgb(@colorA3)");                //字体颜色
}


QMTAbsRowWidgetItemParam::QMTAbsRowWidgetItemParam()
{
    //表头
    this->_headParam._textColor = CMtCoreWidgetUtil::formatColor("rgba(@colorA3,0.8)");
    //表体
    _textColor = CMtCoreWidgetUtil::formatColor("rgb(@colorA3,0.8)");              //字体颜色
    _gridColor = CMtCoreWidgetUtil::formatColor("rgba(@colorA0,1)");               //边框颜色
    _canvasBackColor = CMtCoreWidgetUtil::formatColor("rgba(@colorA0,0.3)");       //画布背景色
    //_canvasBackColor = CMtCoreWidgetUtil::formatColor("rgba(-1, -1, -1,0.3)");  //画布背景色
    _unselectBackColor = CMtCoreWidgetUtil::formatColor("rgba(@colorA0,0.3)");     //未选中背景色
    _selectBackColor = CMtCoreWidgetUtil::formatColor("rgba(@colorB1,0.26)");      //选中背景色
    _selectBorderColor = CMtCoreWidgetUtil::formatColor("rgba(@colorB1,1)");       //选中border色
    _borderColor = CMtCoreWidgetUtil::formatColor("rgb(@colorB1)");                //悬浮边框色
}

int QMTAbsRowWidgetItemParam::GetCurrentType()
{
    return ++s_type;
}

int QMTAbsRowWidgetItemParam::s_type = Template_User;

void QMTAbsRowWidgetItemParam::Init()
{
}













