<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModelSettingWidgetClass</class>
 <widget class="QWidget" name="ModelSettingWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>982</width>
    <height>462</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ModelSettingWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>1</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>26</number>
      </property>
      <item>
       <widget class="MtFrameEx" name="mtFrameEx_2">
        <property name="minimumSize">
         <size>
          <width>247</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>247</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtFrameEx::default_type</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_4">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <property name="leftMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="ModelNameView" name="widget_modelTable" native="true">
             <property name="minimumSize">
              <size>
               <width>247</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>247</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <widget class="MtFrameEx" name="mtFrameEx_line">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>1</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>1</height>
            </size>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtFrameEx::frameEx3</enum>
           </property>
          </widget>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_9">
           <property name="leftMargin">
            <number>13</number>
           </property>
           <property name="topMargin">
            <number>6</number>
           </property>
           <property name="bottomMargin">
            <number>6</number>
           </property>
           <item>
            <widget class="MtToolButton" name="mtToolButton_import">
             <property name="minimumSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton4</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_del">
             <property name="minimumSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="MtFrameEx" name="mtFrameEx_3">
        <property name="_mtType" stdset="0">
         <enum>MtFrameEx::default_type</enum>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="spacing">
          <number>12</number>
         </property>
         <property name="leftMargin">
          <number>16</number>
         </property>
         <property name="topMargin">
          <number>10</number>
         </property>
         <property name="rightMargin">
          <number>16</number>
         </property>
         <property name="bottomMargin">
          <number>16</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="spacing">
            <number>16</number>
           </property>
           <item>
            <widget class="MtLabel" name="mtLabel">
             <property name="text">
              <string>模型名称</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtLineEdit" name="mtLineEdit_name">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>419</width>
               <height>30</height>
              </size>
             </property>
             <property name="maxLength">
              <number>64</number>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLineEdit::lineedit1</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_2">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="MtPushButton" name="mtPushButton_save">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>保存</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtPushButton::pushbutton10</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtPushButton" name="mtPushButton_cancel">
             <property name="minimumSize">
              <size>
               <width>80</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>取消编辑</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtPushButton::pushbutton2</enum>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_3">
             <item>
              <widget class="ModelRoiTable" name="widget_roiTable" native="true"/>
             </item>
            </layout>
           </item>
           <item>
            <widget class="MtFrameEx" name="mtFrameEx_4">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>1</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>1</height>
              </size>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtFrameEx::frameEx3</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtFrameEx" name="mtFrameEx_5">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>33</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>33</height>
              </size>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtFrameEx::default_type</enum>
             </property>
             <layout class="QHBoxLayout" name="horizontalLayout_7" stretch="0,1,0">
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>16</number>
              </property>
              <item>
               <widget class="QWidget" name="widget" native="true">
                <layout class="QHBoxLayout" name="horizontalLayout_5">
                 <property name="spacing">
                  <number>24</number>
                 </property>
                 <property name="leftMargin">
                  <number>16</number>
                 </property>
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="rightMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <widget class="MtPushButton" name="mtPushButton_settingROIInfo">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="text">
                    <string>批量设置ROI信息</string>
                   </property>
                   <property name="pixmapFilename">
                    <string notr="true">:/images/images/icon_setting_blue.png</string>
                   </property>
                   <property name="_mtType" stdset="0">
                    <enum>MtPushButton::pushbutton5</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="MtPushButton" name="mtPushButton_reset">
                   <property name="sizePolicy">
                    <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                     <horstretch>0</horstretch>
                     <verstretch>0</verstretch>
                    </sizepolicy>
                   </property>
                   <property name="text">
                    <string>恢复默认设置</string>
                   </property>
                   <property name="toolTipText">
                    <string>恢复默认的器官名称、ROI名称、标签、颜色、ROI类型、描述</string>
                   </property>
                   <property name="_mtType" stdset="0">
                    <enum>MtPushButton::pushbutton5</enum>
                   </property>
                  </widget>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <widget class="MtFrameEx" name="mtFrameEx_6">
                <layout class="QHBoxLayout" name="horizontalLayout_4">
                 <property name="spacing">
                  <number>4</number>
                 </property>
                 <property name="topMargin">
                  <number>0</number>
                 </property>
                 <property name="bottomMargin">
                  <number>0</number>
                 </property>
                 <item>
                  <spacer name="horizontalSpacer_7">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                 <item>
                  <widget class="MtLabel" name="label_data_loading">
                   <property name="text">
                    <string>数据加载中</string>
                   </property>
                   <property name="_mtType" stdset="0">
                    <enum>MtLabel::myLabel1_1</enum>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <widget class="MRotate" name="label_rotate">
                   <property name="text">
                    <string/>
                   </property>
                  </widget>
                 </item>
                 <item>
                  <spacer name="horizontalSpacer_6">
                   <property name="orientation">
                    <enum>Qt::Horizontal</enum>
                   </property>
                   <property name="sizeHint" stdset="0">
                    <size>
                     <width>40</width>
                     <height>20</height>
                    </size>
                   </property>
                  </spacer>
                 </item>
                </layout>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>40</width>
                  <height>20</height>
                 </size>
                </property>
               </spacer>
              </item>
             </layout>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QFormLayout" name="formLayout">
           <property name="horizontalSpacing">
            <number>16</number>
           </property>
           <property name="verticalSpacing">
            <number>12</number>
           </property>
           <item row="0" column="0">
            <widget class="MtLabel" name="mtLabel_2">
             <property name="text">
              <string>导入时间</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1</enum>
             </property>
            </widget>
           </item>
           <item row="0" column="1">
            <widget class="MtLineEdit" name="mtLineEdit_exportTime">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="readOnly">
              <bool>true</bool>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLineEdit::lineedit1</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="MtLabel" name="mtLabel_3">
             <property name="text">
              <string>模型简介</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="MtLineEdit" name="mtLineEdit_desc">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>92</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>92</height>
              </size>
             </property>
             <property name="maxLength">
              <number>64</number>
             </property>
             <property name="alignment">
              <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLineEdit::lineedit1</enum>
             </property>
            </widget>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MRotate</class>
   <extends>QLabel</extends>
   <header>AccuComponentUi\Header\mrotate.h</header>
  </customwidget>
  <customwidget>
   <class>ModelRoiTable</class>
   <extends>QWidget</extends>
   <header>MTRoiModelDialog\ModelSub\modelroitable.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>ModelNameView</class>
   <extends>QWidget</extends>
   <header>MTRoiModelDialog\ModelSub\modelnameview.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
