<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTPerListView</class>
 <widget class="QWidget" name="QMTPerListView">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>467</width>
    <height>362</height>
   </rect>
  </property>
  <property name="cursor">
   <cursorShape>ArrowCursor</cursorShape>
  </property>
  <property name="windowTitle">
   <string>QMTPerListView</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QTableWidget" name="tableWidget">
     <property name="styleSheet">
      <string notr="true">QHeaderView::section{
background:rgb(44,50,61); 
color:white;
font:12px;
padding-left:10px;
border:1px solid rgba(146, 155, 170, 0.16);
}</string>
     </property>
     <property name="horizontalScrollBarPolicy">
      <enum>Qt::ScrollBarAsNeeded</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
