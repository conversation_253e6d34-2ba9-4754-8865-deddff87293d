﻿#pragma once

#include <QWidget>
#include <QLabel>
#include <QPushButton>


enum ExpandItemType
{
    Expand_BLWithClose,//buttton label
    Expand_LabelButton,
};
namespace Ui
{
class QMTExpandItemWidget;
}

class  QMTExpandItemWidget : public QWidget
{
    Q_OBJECT

public:
    QMTExpandItemWidget(QWidget* parent = Q_NULLPTR);
    ~QMTExpandItemWidget();
    //ui
    void InitExpandItem(ExpandItemType);
    //add
    void SetTextStr(QString);
    void SetWidgetList(QList<QWidget*>);
    void AddWidgetToList(QWidget*);
    void AddWidgetToList(int index, QWidget*);
    //get
    QList<QWidget*> GetWidgetList()
    {
        return _widgetList;
    }
    QString GetKeyValue()
    {
        return _keyValue;
    }
    bool IsWidgetExist(QWidget*);
    //remove
    void RemoveExpandItem(QWidget*);
    void ClearWidgetData();
    //update
    void ChangeState(int);
    void ChangeStateByHand(int);
    void HideSelf();
    void ShowSelf();
    void HadWidgetList(bool had)
    {
        _hadWidgetList = had;
    }
    bool HadWidgetList()
    {
        return _hadWidgetList;
    }
    int CurrentState()
    {
        return _state;
    }
    int Count()
    {
        return _widgetList.count();
    }
protected:
    virtual void mousePressEvent(QMouseEvent* event);
signals:
    void sigExpandBtnCloseClicked();
    void sigExpandClicked(int);
private slots:
    void slotBtnExpandClicked();
    void slotBtnCloseClicked();
private:
    Ui::QMTExpandItemWidget* ui;
    //ui
    QLabel* _label = nullptr;
    QPushButton* _btnExpand = nullptr;
    QPushButton* _btnClose = nullptr;
    //
    int _state = 0;
    QList<QWidget*> _widgetList;
    QString _keyValue;
    bool _hadWidgetList = false;
};
