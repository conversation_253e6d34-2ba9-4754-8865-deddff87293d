﻿#pragma once

#include "ui_ModelInfoSetting.h"
#include "MtTemplateDialog.h"

class ModelInfoSetting : public MtTemplateDialog
{
    Q_OBJECT

public:
    ModelInfoSetting(QWidget* parent = nullptr);
    ~ModelInfoSetting();

    void setInfo(const QString& name, const QString& time, const QString& description);
    void info(QString& name, QString& description);

signals:

protected slots:

protected:
    virtual void onBtnCloseClicked() override;  //关闭按钮
    virtual void onBtnRight2Clicked() override; //取消按钮
    virtual void onBtnRight1Clicked() override; //确认按钮

    QString formatDateTimeStr(QString dateTimeStr);

private:
    Ui::ModelInfoSetting ui;
};
