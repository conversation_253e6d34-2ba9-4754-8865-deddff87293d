﻿#pragma once

#include <QWidget>
#include <QComboBox>
#include <QLabel>
#include <QString>
#include <QJsonObject>
#include "QMTEnumDef.h"
#include "QMTComboBox.h"


enum ItemType
{
    Type_None,
    Type_Label,
    Type_LineEidt,
    Type_NewLineEdit,
    Type_ToolButton,
    Type_ComboBox,
};

namespace Ui
{
class QMTAbstractRowItemWithBorder;
}

//带有基础控件的item widget
class  QMTAbstractRowItemWithBorder : public QWidget
{
    Q_OBJECT

public:
    QMTAbstractRowItemWithBorder(QWidget* parent = Q_NULLPTR);
    ~QMTAbstractRowItemWithBorder();
    //ui
    void InitPerRowWidgetParam(QMTPerRowItemWidgetParam&);
    void SetLabelText(QString);
    void SetColumnCount(int columns);
    void SetColumnWidth(int column, int width);
    void SetColumnType(int, int);
    QWidget* CreateColumnWidget(int, QWidget*, QString str = QString());
    void CreateWidgetItem(QJsonObject& obj);

    //get
    int GetColumnWidgetIndex(QWidget*);
    QWidget* GetColumnWidget(int);
    /******Type_ComboBox*******/
    void AddComboBoxItem(int, QString);
    void AddComboBoxItems(int, QStringList);
    QString CurrentText(int);
    //Type_Label
    //void SetText(int, QString);
    //QString GetText(int);

signals:
    /******Type_ComboBox*******/
    void sigCurrentIndexChanged(int, QString);//column,index

private slots:
    /******Type_ComboBox*******/
    void slotCurrentIndexChanged(int);
    void slotCurrentIndexChanged(const QString&);
    void slotCurrentTextChanged(const QString&);
private:
    void SetComboBoxSignals(QComboBox*);
private:
    Ui::QMTAbstractRowItemWithBorder* ui;
    QMTPerRowItemWidgetParam* _perRowItemParam = nullptr;
    QList<QWidget*> _columnWidgetParentList;
    QList<QWidget*> _columnWidgetList;//new 出来的每列widget
    QString _uniqueValue;
};
