﻿#include "LabelLibraryTable.h"

#include <QColorDialog>
#include <thread>
#include <Windows.h>

#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MTRoiLibraryDialog\CustWidget\QCustMtLabel2.h"
#include "MTRoiLibraryDialog\CustWidget\QCustMtLineEdit2.h"
#include "MTRoiLibraryDialog\CustWidget\QCustMtComboBox2.h"
#include "MTRoiLibraryDialog\CustWidget\QMTAbsHorizontalBtns2.h"
#include "MTRoiLibraryDialog\CustWidget\MtUnitPushButtonGroup2.h"
#include "MtMessageBox.h"
#include "MtProgressDialog.h"
#include "DataDefine/InnerStruct.h"
#include "RoiCodeSettingDialog.h"
#include "RoiLabelSettingDialog.h"

/// <summary>
/// 构造函数
/// </summary>
LabelLibraryTable::LabelLibraryTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    connect(this, &LabelLibraryTable::sigCellWidgetButtonClicked, this, &LabelLibraryTable::slotCellWidgetButtonClicked); //某个按键点击了
    connect(this, &LabelLibraryTable::sigCellWidgetTextChange, this, &LabelLibraryTable::slotCellWidgetTextChange); //表格中的编辑框文本发生了改变
    connect(this, &LabelLibraryTable::sigCreateTableRowItem, this, &LabelLibraryTable::slotCreateTableRowItem, Qt::BlockingQueuedConnection);
}

LabelLibraryTable::~LabelLibraryTable()
{
    m_bDestroyed = true;
}

bool LabelLibraryTable::isTableInitialized()
{
    return m_bInitialized;
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="roiTypeList">[IN]Roi类型集合</param>
/// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
void LabelLibraryTable::init(const QStringList& roiTypeList, const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)
{
    m_roiTypeList = roiTypeList;
    initTableView(
        {
            tr("标签"), tr("ROI名称"), tr("器官名称"), tr("ROI类型"), tr("ROI颜色"), tr("匹配字段"), tr("编码关系")
        },
        /*{ 172, 221, 130, 251, 84, 132, 68 }*/
        { 300, 198, 198, 251, 102, 132, 68 });
    //暂时不需要ROI名称，先隐藏
    HideColumn(Col_ROIName);

    if (CMtLanguageUtil::type == english)
    {
        HideColumn(Col_ROIChName);
    }

    //使用线程发消息方式创建，不然创建列表要很久
    m_bInitialized = false;
    m_bDestroyed = false;
    //
    std::thread t([&](QList<n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoVec)
    {
        for (int i = 0; i < stRoiLabelInfoVec.size() && !m_bDestroyed; ++i)
        {
            emit sigCreateTableRowItem(stRoiLabelInfoVec[i], i == stRoiLabelInfoVec.size() - 1);
            //Sleep(36);
        }
    }, stRoiLabelInfoVec);
    t.detach();
}

/// <summary>
/// 添加新的manteiaRoiLabel
/// </summary>
void LabelLibraryTable::addNewManteiaRoiLabel()
{
    RoiLabelSettingDialog dlg(m_roiTypeList, m_allRoiLibraryMap);

    if (dlg.exec() == QDialog::Accepted)
    {
        n_mtautodelineationdialog::ST_RoiLabelInfo stRoiLabelInfo;
        stRoiLabelInfo.isbuiltIn = false;
        bool isPreDelete = false;
        dlg.getNewRoiLabelInfo(stRoiLabelInfo.manteiaRoiLabel, stRoiLabelInfo.roiName, stRoiLabelInfo.roiAlias, stRoiLabelInfo.roiColor, stRoiLabelInfo.roiType, isPreDelete);

        if (stRoiLabelInfo.roiName.isEmpty())
        {
            stRoiLabelInfo.roiName = stRoiLabelInfo.manteiaRoiLabel;
        }

        stRoiLabelInfo.optTypeEnum = (isPreDelete == true ? n_mtautodelineationdialog::OptType_Mod : n_mtautodelineationdialog::OptType_Add);
        addRow(stRoiLabelInfo);
    }
}

/// <summary>
/// 更新/新增manteiaRoiLabel
/// </summary>
/// <param name="info">[IN]待更新数据</param>
/// <param name="outUpdateNum">[IN]更新了几个</param>
/// <param name="outNewNum">[IN]新增了几个</param>
void LabelLibraryTable::updateManteiaRoiLabel(const n_mtautodelineationdialog::ST_RoiLabelInfo& info, int& outUpdateNum, int& outNewNum)
{
    n_mtautodelineationdialog::ST_RoiLabelInfo tempInfo = info;
    QString lowerManteiaRoiLabel = info.manteiaRoiLabel.toLower();

    if (m_allRoiLibraryMap.contains(lowerManteiaRoiLabel) == true) //更新
    {
        //roiName
        QWidget* widget = this->GetCellWidget(tempInfo.manteiaRoiLabel, Col_ROIName);

        if (widget != nullptr)
            ((QCustMtLineEdit2*)widget)->setText(tempInfo.roiName);

        //roi chinese name
        widget = this->GetCellWidget(tempInfo.manteiaRoiLabel, Col_ROIChName);

        if (nullptr != widget)
            ((QCustMtLineEdit2*)widget)->setText(tempInfo.roiChName);

        //roi alias
        widget = this->GetCellWidget(tempInfo.manteiaRoiLabel, Col_AliasName);

        if (widget != nullptr)
            ((QCustMtLineEdit2*)widget)->setText(tempInfo.roiAlias);

        //roiType
        widget = this->GetCellWidget(tempInfo.manteiaRoiLabel, Col_ROIType);

        if (widget != nullptr)
            ((QCustMtComboBox2*)widget)->setCurrentText(info.roiType);

        //更新内存数据
        if (m_allRoiLibraryMap[lowerManteiaRoiLabel].optTypeEnum != n_mtautodelineationdialog::OptType_Add)
            m_allRoiLibraryMap[lowerManteiaRoiLabel].optTypeEnum = n_mtautodelineationdialog::OptType_Mod;

        m_allRoiLibraryMap[lowerManteiaRoiLabel].roiName = tempInfo.roiName;
        m_allRoiLibraryMap[lowerManteiaRoiLabel].roiChName = tempInfo.roiChName;
        m_allRoiLibraryMap[lowerManteiaRoiLabel].roiAlias = tempInfo.roiAlias;
        m_allRoiLibraryMap[lowerManteiaRoiLabel].roiType = tempInfo.roiType;
        m_allRoiLibraryMap[lowerManteiaRoiLabel].roiCodeMap = tempInfo.roiCodeMap;
        outUpdateNum += 1;
    }
    else //新增
    {
        tempInfo.optTypeEnum = n_mtautodelineationdialog::OptType_Add;
        addRow(tempInfo);
        outNewNum += 1;
    }
}

/// <summary>
/// 删除当前行
/// </summary>
void LabelLibraryTable::delCurrentRow()
{
    QString manteiaRoiLabel = this->GetCurUniqueValue();

    if (manteiaRoiLabel.isEmpty())
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要删除的标签"));
        return;
    }

    if (m_allRoiLibraryMap.contains(manteiaRoiLabel.toLower()) == true && m_allRoiLibraryMap[manteiaRoiLabel.toLower()].isbuiltIn == true)
    {
        MtMessageBox::yellowWarning(this->window(), QString(tr("系统内置的标签不允许删除")), QString());
        return;
    }

    QString msg = QString(tr("您确认要删除该标签吗？"));
    int ret = MtMessageBox::NoIcon::question_Title(this, msg);

    if (QMessageBox::Yes == ret)
    {
        if (m_allRoiLibraryMap.contains(manteiaRoiLabel.toLower()) == true)
        {
            m_allRoiLibraryMap[manteiaRoiLabel.toLower()].optTypeEnum = n_mtautodelineationdialog::OptType_Del;
        }

        this->DeleteCurRow();
        return;
    }

    return;
}

/// <summary>
/// 隐藏或展示行
/// </summary>
/// <param name="labelOrName">[IN]manteiaRoiLabel或roiName(如果传空全部显示)</param>
void LabelLibraryTable::hideShowRow(const QString& labelOrName)
{
    int rowNum = this->GetRowCount();

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        //manteiaRoiLabel
        QString manteiaRoiLabel = this->GetColumnText(rowValue, Col_Lable);
        //roiName
        QString roiName = this->GetColumnText(rowValue, Col_ROIName);
        //chName
        QString roiChName = this->GetColumnText(rowValue, Col_ROIChName);
        //aliasName
        QString aliasName = this->GetColumnText(rowValue, Col_AliasName);

        if (labelOrName.isEmpty())
        {
            this->setRowHidden(i, false);
            continue;
        }

        if (manteiaRoiLabel.contains(labelOrName, Qt::CaseInsensitive)
            || roiName.contains(labelOrName, Qt::CaseInsensitive)
            || roiChName.contains(labelOrName, Qt::CaseInsensitive)
            || aliasName.contains(labelOrName, Qt::CaseInsensitive))
        {
            this->setRowHidden(i, false);
        }
        else
        {
            this->setRowHidden(i, true);
        }
    }
}

void LabelLibraryTable::hideLabelListColumn(const QVector<int>& columnIndexVec, bool bHide /* = true */)
{
    for (int col : columnIndexVec)
    {
        this->setColumnHidden(col, bHide);
    }
}

void LabelLibraryTable::enableLabelListColumn(const QVector<int> columnIndexVec, bool bEnable /* = true */)
{
    QStringList rowValueList = this->GetAllRowUniqueValueList();

    for (const QString& rowValue : rowValueList)
    {
        for (int index : columnIndexVec)
        {
            this->SetCellEditEnable(rowValue, index, false);
        }
    }
}

/// <summary>
/// 获取所有manteiaRoiLabel信息
/// </summary>
/// <returns>所有manteiaRoiLabel信息</returns>
QList<n_mtautodelineationdialog::ST_RoiLabelInfo> LabelLibraryTable::getAllRoiLabelInfo()
{
    int rowNum = this->GetRowCount();
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> stNewRoiLabelVec;
    stNewRoiLabelVec.reserve(rowNum + 1);

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        //manteiaRoiLabel
        QWidget* widget = this->GetCellWidget(rowValue, Col_Lable);
        QString manteiaRoiLabel, roiName, roiChName, roiAlias, roiType;

        if (nullptr == widget)
        {
            manteiaRoiLabel = this->GetColumnText(rowValue, Col_Lable);
            roiName = this->GetColumnText(rowValue, Col_ROIName);
            roiChName = this->GetColumnText(rowValue, Col_ROIChName);
            roiAlias = this->GetColumnText(rowValue, Col_AliasName);
            roiType = this->GetColumnText(rowValue, Col_ROIType);
            QCustMtComboBox2Param* roiTypeParam = (QCustMtComboBox2Param*)this->GetCellWidgetParam(rowValue, 4);

            if (nullptr != roiTypeParam && -1 < roiTypeParam->_comboBoxIndex && roiTypeParam->_comboBoxIndex < roiTypeParam->_textList.size())
            {
                roiType = roiTypeParam->_textList[roiTypeParam->_comboBoxIndex];
            }
        }
        else
        {
            manteiaRoiLabel = ((QCustMtLabel2*)widget)->GetCurText();
            //roiName
            widget = this->GetCellWidget(rowValue, Col_ROIName);
            roiName = ((QCustMtLineEdit2*)widget)->getText();
            //roiChName
            widget = this->GetCellWidget(rowValue, Col_ROIChName);
            roiChName = ((QCustMtLineEdit2*)widget)->getText();
            //roi alias
            widget = this->GetCellWidget(rowValue, Col_AliasName);
            roiAlias = ((QCustMtLineEdit2*)widget)->getText();
            //roiColor在slotCellWidgetButtonClicked中
            //roiType
            widget = this->GetCellWidget(rowValue, Col_ROIType);
            roiType = ((QCustMtComboBox2*)widget)->GetCurText();
        }

        if (m_allRoiLibraryMap.contains(manteiaRoiLabel.toLower()) == true)
        {
            QString lowerName = manteiaRoiLabel.toLower();
            m_allRoiLibraryMap[lowerName].roiName = roiName;
            m_allRoiLibraryMap[lowerName].roiChName = roiChName == "-" ? "" : roiChName;
            m_allRoiLibraryMap[lowerName].roiAlias = roiAlias;
            m_allRoiLibraryMap[lowerName].roiType = roiType;
        }
    }

    //更新manteiaRoiLabel记录
    for (int i = 0; i < m_manteiaRoiLabelListSort.size(); i++)
    {
        QString lowerRoiName = m_manteiaRoiLabelListSort[i].toLower();

        if (m_allRoiLibraryMap.contains(lowerRoiName) == true)
        {
            stNewRoiLabelVec.push_back(m_allRoiLibraryMap[lowerRoiName]);
        }
    }

    return stNewRoiLabelVec;
}

void LabelLibraryTable::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
    QMTAbstractTableView::CreateCellWidgtFinishCallBack(rowValue, column, cellType, cellWidget);
}

/// <summary>
/// 初始化表格
/// </summary>
/// <param name="headList">[IN]表头文本集合</param>
/// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
void LabelLibraryTable::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="stEmptyOrgan">[IN]manteiaRoiLabel信息</param>
void LabelLibraryTable::addRow(const n_mtautodelineationdialog::ST_RoiLabelInfo& stRoiLabelInfo)
{
    m_manteiaRoiLabelListSort.push_back(stRoiLabelInfo.manteiaRoiLabel);
    m_allRoiLibraryMap.insert(stRoiLabelInfo.manteiaRoiLabel.toLower(), stRoiLabelInfo);
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //manteia标签
    QCustMtLabel2Param* labelParam = new QCustMtLabel2Param();
    labelParam->_text = stRoiLabelInfo.manteiaRoiLabel;
    cellWidgetParamMap.insert(Col_Lable, labelParam);
    //ROI名称
    QCustMtLineEdit2Param* lineEditParam = new QCustMtLineEdit2Param();
    lineEditParam->_maxLength = 64;
    lineEditParam->_text = stRoiLabelInfo.roiName;
    lineEditParam->_regExpStr = RegExp_CharNumber4;
    lineEditParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(Col_ROIName, lineEditParam);
    //ROI中文名
    QCustMtLineEdit2Param* lineEditParam3 = new QCustMtLineEdit2Param();
    lineEditParam3->_maxLength = 64;
    lineEditParam3->_text = stRoiLabelInfo.roiChName.isEmpty() ? "-" : stRoiLabelInfo.roiChName;
    lineEditParam3->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(Col_ROIChName, lineEditParam3);
    //ROI别名
    QCustMtLineEdit2Param* lineEditParam2 = new QCustMtLineEdit2Param();
    lineEditParam2->_maxLength = 64;
    lineEditParam2->_text = stRoiLabelInfo.roiAlias;
    lineEditParam2->_regExpStr = RegExp_CharNumber4;
    lineEditParam2->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(Col_AliasName, lineEditParam2);
    //Roi颜色
    QMTAbsHorizontalBtns2Param* btnsParam = new QMTAbsHorizontalBtns2Param();
    btnsParam->_pixPathList.clear();
    btnsParam->_width = 58;
    btnsParam->_height = 16;
    btnsParam->_btnBackgroundColor = QColor(QString("#") + (stRoiLabelInfo.roiColor.isEmpty() ? "ff0000" : stRoiLabelInfo.roiColor));
    cellWidgetParamMap.insert(Col_Color, btnsParam);
    //Roi类型
    QCustMtComboBox2Param* comboBoxParam = new QCustMtComboBox2Param();
    comboBoxParam->_textList = m_roiTypeList;
    int index = m_roiTypeList.indexOf(stRoiLabelInfo.roiType);
    comboBoxParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(Col_ROIType, comboBoxParam);
    //编码关系
    MtUnitPushButtonGroup2Param* btnsSettingParam = new MtUnitPushButtonGroup2Param();
    btnsSettingParam->_btnTextStrList = QStringList({ tr("设置") });
    btnsSettingParam->_btnIndexMtTypeMap.insert(0, 5);
    cellWidgetParamMap.insert(Col_Operate, btnsSettingParam);
    this->AddRowItem(stRoiLabelInfo.manteiaRoiLabel, cellWidgetParamMap);
}

/// <summary>
/// 某个按键点击了
/// </summary>
void LabelLibraryTable::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    QColor color = Qt::red;
    QString lowermManteiaRoiLabel = cellItemIndex._uniqueValue.toLower();

    if (cellItemIndex._column == Col_Color) //roi颜色按钮
    {
        if (m_allRoiLibraryMap.contains(lowermManteiaRoiLabel))
            color = QString("#") + m_allRoiLibraryMap[lowermManteiaRoiLabel].roiColor;

        QColor newColor = QColorDialog::getColor(color/*, this*/);//输入框样式会被父窗口影像，不设置父窗口

        if (newColor.isValid() == false || color == newColor)
            return;

        this->UpdateCellWidget(cellItemIndex._uniqueValue, Col_Color, QVariant::fromValue(newColor));

        if (m_allRoiLibraryMap.contains(lowermManteiaRoiLabel) == true)
        {
            m_allRoiLibraryMap[lowermManteiaRoiLabel].roiColor = newColor.name().remove("#");
            emit sigTableInfoChanged();
        }
    }
    else if (cellItemIndex._column == Col_Operate) //编码关系按钮
    {
        if (m_allRoiLibraryMap.contains(lowermManteiaRoiLabel) == true)
        {
            RoiCodeSettingDialog dlg(cellItemIndex._uniqueValue, m_allRoiLibraryMap[lowermManteiaRoiLabel].roiCodeMap, this);

            if (dlg.exec() == QDialog::Accepted)
            {
                QString outManteiaRoiLabel;
                QMap<n_mtautodelineationdialog::EM_Manufacturer, QMap<QString, QString>> outRoiCodeMap;
                dlg.getNewRoiCode(outManteiaRoiLabel, outRoiCodeMap);

                if (m_allRoiLibraryMap.contains(outManteiaRoiLabel.toLower()) == true)
                {
                    m_allRoiLibraryMap[outManteiaRoiLabel.toLower()].roiCodeMap = outRoiCodeMap;
                    emit sigTableInfoChanged();
                }
            }
        }
    }
}

void LabelLibraryTable::slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText)
{
    emit sigTableInfoChanged();
}

void LabelLibraryTable::slotCreateTableRowItem(const n_mtautodelineationdialog::ST_RoiLabelInfo& labelInfo, bool bLast)
{
    addRow(labelInfo);
    qApp->processEvents();

    if (bLast)
    {
        m_bInitialized = true;
        emit sigTableInitialized();
    }
}