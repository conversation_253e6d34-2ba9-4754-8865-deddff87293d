﻿#include "MultiSelectComboBox.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtScrollBar.h"
#include "MtCheckBox.h"

namespace n_mtautodelineationdialog
{
MultiSelectComboBox::MultiSelectComboBox(QWidget* parent)
    : MtComboBox(parent)
    , m_hidden_flag(true)
{
    setMtType(MtComboBox::combobox1);
    m_list_widget = new QListWidget();
    m_lineEdit = new MtLineEdit();
#if ShowComboBoxSearchBar
    m_search_bar = new MtLineEdit();
#endif
    m_lineEdit->setMtType(MtLineEdit::lineedit3);
#if ShowComboBoxSearchBar
    m_search_bar->setMtType(MtLineEdit::lineedit3);
#endif
    QString strStyle = "border: none; background: rgba(@colorA0,0); padding-left: 0px; padding-right: 0px;";
    CMtCoreWidgetUtil::formatStyleSheet(strStyle);
    m_lineEdit->setStyleSheet(strStyle);
#if ShowComboBoxSearchBar
    strStyle = "QLineEdit{border: none; background: rgb(@colorA0); padding-left: 0px; padding-right: 0px} QLineEdit:hover { background: rgb(@colorA1); }";
    CMtCoreWidgetUtil::formatStyleSheet(strStyle);
    m_search_bar->setStyleSheet(strStyle);
#endif
    //
    MtScrollBar* bar = new MtScrollBar(this);
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    m_list_widget->setHorizontalScrollBar(bar);
    bar = new MtScrollBar(this);
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    m_list_widget->setVerticalScrollBar(bar);
#if ShowComboBoxSearchBar
    /*设置搜索框*/
    QListWidgetItem* currentItem = new QListWidgetItem(m_list_widget);
    //设置搜索框提示信息
    m_search_bar->setPlaceholderText("Search......");
    //显示清除按钮
    m_search_bar->setClearButtonEnabled(true);
    m_list_widget->addItem(currentItem);
    m_list_widget->setItemWidget(currentItem, m_search_bar);
    SetSearchBarHidden(true);
#endif
    /*设置文本框*/
    //设为只读，因为该输入框只用来显示选中的选项，称为文本框更合适些
    m_lineEdit->setReadOnly(true);
    //把当前对象安装(或注册)为事件过滤器，当前也称为过滤器对象。事件过滤器通常在构造函数中进行注册。
    m_lineEdit->installEventFilter(this);
    this->setModel(m_list_widget->model());
    this->setView(m_list_widget);
    this->setLineEdit(m_lineEdit);
#if ShowComboBoxSearchBar
    connect(m_search_bar, SIGNAL(textChanged(const QString&)), this, SLOT(onSearch(const QString&)));
#endif
    connect(this, static_cast<void (MtComboBox::*)(int)>(&MtComboBox::activated), this, &MultiSelectComboBox::itemClicked);
}

MultiSelectComboBox::~MultiSelectComboBox()
{
}

void MultiSelectComboBox::addItem(const QString& _text, const QVariant& _variant /*= QVariant()*/)
{
    Q_UNUSED(_variant);
    QListWidgetItem* item = new QListWidgetItem(m_list_widget);
    MtCheckBox* checkbox = new MtCheckBox(this);
    QString textTmp = _text;
    checkbox->setMtType(MtCheckBox::checkbox1);
    checkbox->setText(textTmp.replace("&", "&&"));
    checkbox->setElideMode(Qt::ElideRight);
    m_list_widget->addItem(item);
    m_list_widget->setItemWidget(item, checkbox);
    connect(checkbox, &MtCheckBox::stateChanged, this, &MultiSelectComboBox::stateChange);
}

void MultiSelectComboBox::addItems(const QStringList& _text_list)
{
    for (const auto& text_one : _text_list)
    {
        addItem(text_one);
    }
}

QStringList MultiSelectComboBox::currentText()
{
    QStringList text_list;

    if (!m_lineEdit->text().isEmpty())
    {
        //以;为分隔符分割字符串
        text_list = m_lineEdit->text().split(';');
    }

    return text_list;
}

QList<int> MultiSelectComboBox::currentIndex()
{
    QList<int> retList;
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (check_box->isChecked())
        {
            retList.append(i - 1);
        }
    }

#else

    for (int i = 0; i < count; i++)
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (check_box->isChecked())
        {
            retList.append(i);
        }
    }

#endif
    return retList;
}

int MultiSelectComboBox::count() const
{
#if ShowComboBoxSearchBar
    int count = m_list_widget->count() - 1;
#else
    int count = m_list_widget->count();
#endif

    if (count < 0)
    {
        count = 0;
    }

    return count;
}

void MultiSelectComboBox::SetSearchBarPlaceHolderText(const QString _text)
{
#if ShowComboBoxSearchBar
    m_search_bar->setPlaceholderText(_text);
#endif
}

void MultiSelectComboBox::SetPlaceHolderText(const QString& _text)
{
    m_lineEdit->setPlaceholderText(_text);
}

void MultiSelectComboBox::ResetSelection()
{
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);
        //check_box->setChecked(false);
        check_box->setCheckState(Qt::Unchecked);
    }
}

void MultiSelectComboBox::clear()
{
    m_lineEdit->clear();
    //
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = count - 1; i > 0; i--)
#else
    for (int i = count - 1; i >= 0; i--)
#endif
    {
        QListWidgetItem* item = m_list_widget->takeItem(i);
        delete item;
    }

    return;
    //
    m_list_widget->clear();
#if ShowComboBoxSearchBar
    QListWidgetItem* currentItem = new QListWidgetItem(m_list_widget);
    m_search_bar->setPlaceholderText("Search......");
    m_search_bar->setClearButtonEnabled(true);
    m_list_widget->addItem(currentItem);
    m_list_widget->setItemWidget(currentItem, m_search_bar);
    SetSearchBarHidden(m_hidden_flag);
    connect(m_search_bar, SIGNAL(textChanged(const QString&)), this, SLOT(onSearch(const QString&)));
#endif
}

void MultiSelectComboBox::TextClear()
{
    m_lineEdit->clear();
    ResetSelection();
}

void MultiSelectComboBox::setCurrentText(const QString& _text)
{
    QString tempText = _text;
    tempText.replace("&", "&&");
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (tempText.compare(check_box->text()) == 0)
        {
            //check_box->setChecked(true);
            check_box->setCheckState(Qt::Checked);
            SetLineEditText(_text);
        }
        else
        {
            check_box->setCheckState(Qt::Unchecked);
        }
    }
}

void MultiSelectComboBox::setCurrentText(const QStringList& _text_list)
{
    int count = m_list_widget->count();
    QString editText;
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);
        QString strCheckboxText = check_box->text().replace("&&", "&");

        if (_text_list.contains(strCheckboxText))
        {
            //check_box->setChecked(true);
            check_box->setCheckState(Qt::Checked);
            editText += strCheckboxText + ";";
        }
        else
        {
            check_box->setCheckState(Qt::Unchecked);
        }
    }

    if (!editText.isEmpty())
    {
        editText.chop(1);
        SetLineEditText(editText);
    }
}

void MultiSelectComboBox::setCurrentIndex(int index)
{
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    if (index < count - 1)
    {
        for (int i = 1; i < count; i++)
#else
    if (index < count)
    {
        for (int i = 0; i < count; i++)
#endif
        {
            //获取对应位置的QWidget对象
            QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
            //将QWidget对象转换成对应的类型
            MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

            if (nullptr != check_box)
            {
                //check_box->setChecked(true);
#if ShowComboBoxSearchBar
                if (i == index + 1)
#else
                if (i == index)
#endif
                {
                    check_box->setCheckState(Qt::Checked);
                    SetLineEditText(check_box->text().replace("&&", "&"));
                }
                else
                {
                    check_box->setCheckState(Qt::Unchecked);
                }
            }
        }
    }
}

void MultiSelectComboBox::setCurrentIndex(const QList<int>&indexList)
{
    QString editText;
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (nullptr != check_box)
        {
            //check_box->setChecked(true);
#if ShowComboBoxSearchBar
            if (indexList.contains(i - 1))
#else
            if (indexList.contains(i))
#endif
            {
                check_box->setCheckState(Qt::Checked);
                editText += check_box->text().replace("&&", "&") + ";";
            }
            else
            {
                check_box->setCheckState(Qt::Unchecked);
            }
        }
    }

    if (!editText.isEmpty())
    {
        editText.chop(1);
        SetLineEditText(editText);
    }
}

void MultiSelectComboBox::setDisabledIndex(const QList<int>&indexList)
{
    for (int index : indexList)
    {
        //获取对应位置的QWidget对象
#if ShowComboBoxSearchBar
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(index + 1));
#else
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(index));
#endif
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (nullptr != check_box)
        {
            check_box->setEnabled(false);
        }
    }
}

void MultiSelectComboBox::setItemState(int index, Qt::CheckState state)
{
    QString editText;
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (nullptr != check_box)
        {
            //check_box->setChecked(true);
#if ShowComboBoxSearchBar
            if (index == i - 1)
#else
            if (index == i)
#endif
            {
                check_box->setCheckState(state);
            }

            if (check_box->isChecked())
            {
                editText += check_box->text().replace("&&", "&") + ";";
            }
        }
    }

    if (!editText.isEmpty())
    {
        editText.chop(1);
        SetLineEditText(editText);
    }
}

void MultiSelectComboBox::setItemsState(const QList<int>&indexList, Qt::CheckState state)
{
    QString editText;
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (nullptr != check_box)
        {
            //check_box->setChecked(true);
#if ShowComboBoxSearchBar
            if (indexList.contains(i - 1))
#else
            if (indexList.contains(i))
#endif
            {
                check_box->setCheckState(state);
            }

            if (check_box->isChecked())
            {
                editText += check_box->text().replace("&&", "&") + ";";
            }
        }
    }

    if (!editText.isEmpty())
    {
        editText.chop(1);
        SetLineEditText(editText);
    }
}

void MultiSelectComboBox::setItemState(const QString & text, Qt::CheckState state)
{
    QString editText;
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (nullptr != check_box)
        {
            //check_box->setChecked(true);
            QString str = check_box->text().replace("&&", "&");

            if (text == str)
            {
                check_box->setCheckState(state);
            }

            if (check_box->isChecked())
            {
                editText += str + ";";
            }
        }
    }

    if (!editText.isEmpty())
    {
        editText.chop(1);
        SetLineEditText(editText);
    }
}

void MultiSelectComboBox::setItemsState(const QList<QString>&textList, Qt::CheckState state)
{
    QString editText;
    int count = m_list_widget->count();
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        //获取对应位置的QWidget对象
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        //将QWidget对象转换成对应的类型
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (nullptr != check_box)
        {
            //check_box->setChecked(true);
            QString str = check_box->text().replace("&&", "&");

            if (textList.contains(str))
            {
                check_box->setCheckState(state);
            }

            if (check_box->isChecked())
            {
                editText += str + ";";
            }
        }
    }

    if (!editText.isEmpty())
    {
        editText.chop(1);
        SetLineEditText(editText);
    }
}

void MultiSelectComboBox::SetSearchBarHidden(bool _flag)
{
#if ShowComboBoxSearchBar
    m_hidden_flag = _flag;
    m_list_widget->item(0)->setHidden(m_hidden_flag);
#endif
}

bool MultiSelectComboBox::eventFilter(QObject * watched, QEvent * event)
{
    //设置点击输入框也可以弹出下拉框
    if (watched == m_lineEdit && event->type() == QEvent::MouseButtonPress && this->isEnabled())
    {
        if (!m_timer->isActive())
        {
            if (!_isPopupShow)
                MtComboBox::showPopup();
            else if (_isPopupShow)
                MtComboBox::hidePopup();

            return true;
        }
    }

    return false;
}

void MultiSelectComboBox::wheelEvent(QWheelEvent * event)
{
    //禁用QComboBox默认的滚轮事件
    Q_UNUSED(event);
}

void MultiSelectComboBox::keyPressEvent(QKeyEvent * event)
{
    MtComboBox::keyPressEvent(event);
}

void MultiSelectComboBox::SetLineEditText(const QString & text)
{
    m_lineEdit->setText(text);
    m_lineEdit->setCursorPosition(0);
}

void MultiSelectComboBox::stateChange(int state)
{
    QString selected_data("");
    int count = m_list_widget->count();
    int nIndex = 0;
    QString itemText;
#if ShowComboBoxSearchBar

    for (int i = 1; i < count; i++)
#else
    for (int i = 0; i < count; i++)
#endif
    {
        QWidget* widget = m_list_widget->itemWidget(m_list_widget->item(i));
        MtCheckBox* check_box = static_cast<MtCheckBox*>(widget);

        if (sender() == check_box)
        {
#if ShowComboBoxSearchBar
            nIndex = i - 1;
#else
            nIndex = i;
#endif
            itemText = check_box->text().replace("&&", "&");
        }

        if (check_box->isChecked())
        {
            selected_data.append(check_box->text().replace("&&", "&")).append(";");
        }
    }

    selected_data.chop(1);

    if (!selected_data.isEmpty())
    {
        SetLineEditText(selected_data);
    }
    else
    {
        m_lineEdit->clear();
    }

    //m_lineEdit->setToolTip(selected_data);//使用默认提示信息风格
    emit sigSelectedTextChange(selected_data);
    emit sigSelectionChange(nIndex, state, itemText);
}

void MultiSelectComboBox::onSearch(const QString & _text)
{
#if ShowComboBoxSearchBar

    for (int i = 1; i < m_list_widget->count(); i++)
#else
    for (int i = 0; i < m_list_widget->count(); i++)
#endif
    {
        MtCheckBox* check_box = static_cast<MtCheckBox*>(m_list_widget->itemWidget(m_list_widget->item(i)));

        //文本匹配则显示，反之隐藏
        //Qt::CaseInsensitive模糊查询
        if (check_box->text().contains(QString(_text).replace("&", "&&"), Qt::CaseInsensitive))
            m_list_widget->item(i)->setHidden(false);
        else
            m_list_widget->item(i)->setHidden(true);
    }
}

void MultiSelectComboBox::itemClicked(int _index)
{
#if ShowComboBoxSearchBar

    if (_index != 0)
#endif
    {
        MtCheckBox* check_box = static_cast<MtCheckBox*>(m_list_widget->itemWidget(m_list_widget->item(_index)));
        //check_box->setChecked(!check_box->isChecked());
        check_box->setCheckState(check_box->isChecked() ? Qt::Unchecked : Qt::Checked);
    }
}

}
