﻿#include "AutoDelineationCallBack.h"
#include "MtMessageBox.h"

AutoDelineationCallBack::AutoDelineationCallBack(QObject* parent)
    : QObject(parent)
{
}

AutoDelineationCallBack::~AutoDelineationCallBack()
{
}

/// <summary>
/// 创建自动勾画回调对象
/// </summary>
/// <returns>自动勾画回调对象</returns>
n_mtautodelineationdialog::ST_CallBack_AutoSketch AutoDelineationCallBack::createAutoSketchCallBack()
{
    n_mtautodelineationdialog::ST_CallBack_AutoSketch autoSketchCallBack;
    //
    //更新模板信息
    autoSketchCallBack.updateSketchCollectionCallBack = [&](
        const n_mtautodelineationdialog::EM_OptType optTypeEnum,
        const n_mtautodelineationdialog::ST_SketchModelCollection stSketchCollection,
        int& newTemplateId, QString& outErrMsg)
    {
        QSet<int> usedTemplateIdSet = AutoDelineationDataOpt::getUsedTemplateIdOfUnattended();

        if (optTypeEnum == n_mtautodelineationdialog::OptType_Del) //删除
        {
            if (usedTemplateIdSet.contains(stSketchCollection.id) == true)
            {
                outErrMsg = tr("当前模板已被添加进无人值守规则，无法直接删除，请先从无人值守规则中移除该模板");
                return false;
            }
        }

        return AutoDelineationDataOpt::updateSketchCollection(optTypeEnum, stSketchCollection, newTemplateId);
    };
    //
    //模板是否允许操作
    autoSketchCallBack.canOptSketchCollectionCallBack = [&](const n_mtautodelineationdialog::EM_OptType optTypeEnum, const int templateId, QString& outMsg, QString& rightBtnText)
    {
        QSet<int> usedTemplateIdSet = AutoDelineationDataOpt::getUsedTemplateIdOfUnattended();

        if (usedTemplateIdSet.contains(templateId) == false)
            return 0;

        if (optTypeEnum == n_mtautodelineationdialog::OptType_Del)
        {
            outMsg = tr("当前模板已被添加进无人值守规则，无法直接删除，请先从无人值守规则中移除该模板");
            return 1;
        }
        else if (optTypeEnum == n_mtautodelineationdialog::OptType_Mod)
        {
            outMsg = tr("当前模板已被添加进无人值守规则，修改保存后将直接生效，是否确定修改？");
            return 2;
        }
        else if (optTypeEnum == n_mtautodelineationdialog::OptType_OutUnattend)
        {
            if (usedTemplateIdSet.contains(templateId) == true)
            {
                outMsg = tr("当前模板已被添加进无人值守规则，无法设置为常规模板");
                return 1;
            }
        }

        return 0;
    };
    //
    //打开模型和ROI设置，返回是否更新了数据
    autoSketchCallBack.modelAndRoiSettingCallBack = [&]()
    {
        //Todo: 通知打开模型和ROI设置，并返回是否更新了数据
        bool bInfoChanged = false;//是否更新了模型和roi设置
        return bInfoChanged;
    };
    //
    //更新数据库ModelCollection表是否是无人值守字段isUnattended
    autoSketchCallBack.setSketchCollectionUnattendCallBack = [&](const int templateId, const bool isUnattended, QString& outErrMsg)
    {
        if (isUnattended == false)
        {
            QSet<int> usedTemplateIdSet = AutoDelineationDataOpt::getUsedTemplateIdOfUnattended();

            if (usedTemplateIdSet.contains(templateId) == true)
            {
                outErrMsg = tr("当前模板已被添加进无人值守规则，无法设置为常规模板");
                return false;
            }
        }

        return AutoDelineationDataOpt::updateSketchCollectionUnattended(templateId, isUnattended);
    };
    autoSketchCallBack.editRemoteScpCallBack = [&]()
    {
        //Todo: 修改远程导出地址
        //获取新的远程导出地址
        return AutoDelineationDataOpt::getAllRemoteScpAddrList();
    };
    //获取所有的器官信息
    autoSketchCallBack.getAllOrganInfoCallback = [&]()
    {
        QList<n_mtautodelineationdialog::ST_Organ> allOrganInfoList;
        QMap<int, n_mtautodelineationdialog::ST_Organ> outOrganMap;
        AutoDelineationDataOpt::getAllOrganUnique(true, allOrganInfoList, outOrganMap, true);
        return allOrganInfoList;
    };
    //获取标签列表信息
    autoSketchCallBack.getAllRoiLabelInfoCallback = [&]()
    {
        QList<n_mtautodelineationdialog::ST_RoiLabelInfo> allRoiLabelInfoList;
        AutoDelineationDataOpt::getAllRoiLabelInfoList(allRoiLabelInfoList);
        return allRoiLabelInfoList;
    };
    //更新保存器官列表
    autoSketchCallBack.updateOrganInfoCallback = [&](const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList)
    {
        AutoDelineationDataOpt::updateOrganInfo2DB(organInfoList);
        return true;
    };
    //更新保存ROI标签列表
    autoSketchCallBack.updateRoiLabelInfoCallback = [&](const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& roiLabelInfoList)
    {
        //更新数据信息到数据库
        AutoDelineationDataOpt::updateRoiLabelInfo2DB(roiLabelInfoList);
        //Todo: 更新标签列表信息
        return true;
    };
    //
    return autoSketchCallBack;
}

/// <summary>
/// 创建无人值守回调对象
/// </summary>
/// <returns>无人值守回调对象</returns>
n_mtautodelineationdialog::ST_CallBack_Unattended AutoDelineationCallBack::creteUnattendedCallBack()
{
    n_mtautodelineationdialog::ST_CallBack_Unattended stCallBackUnattended;
    //
    //更新无人值守信息
    stCallBackUnattended.updateUnattendedCallBack = [&](
        const n_mtautodelineationdialog::EM_OptType optTypeEnum,
        const QString customId,
        const n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig, QString& outErrMsg)
    {
        return AutoDelineationDataOpt::updateUnattemdedInfoToDB(optTypeEnum, customId, stUnattendedConfig);
    };
    //
    //更新数据库unattendedfeature表enable
    stCallBackUnattended.ruleEnableCallBack = [&](const bool ruleEnable, const QString customId, QString& outErrMsg)
    {
        AutoDelineationDataOpt::updateEnableOfUnattendedfeatureDB(customId, ruleEnable);
        return true;
    };
    //
    //增删改本地节点
    stCallBackUnattended.editLocalServerCallBack = [&]()
    {
        //修改本地服务节点
        return AutoDelineationDataOpt::getAllLocalServerName();
    };
    //
    return stCallBackUnattended;
}

/// <summary>
/// 创建模型和ROI设置回调对象
/// </summary>
/// <returns>模型和ROI设置回调对象</returns>
n_mtautodelineationdialog::ST_CallBack_ROILibrarySetting AutoDelineationCallBack::createModelRoiSettingCallback()
{
    n_mtautodelineationdialog::ST_CallBack_ROILibrarySetting roiSettingCb;
    return roiSettingCb;
}
