﻿#pragma once

#include <QWidget>
#include <QCheckBox>
#include <QMouseEvent>
#include <QResizeEvent>
#include <QJsonObject>
#include <QJsonValue>
#include <QJsonArray>
#include <QJsonDocument>
#include "Language.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "QMTUIDataStruct.h"


//单元格的类
class QMTAbsComboBox;
class QMTLineEdit;
class QMTCheckBox;
class QMTButtonMenuWidget;

namespace Ui
{
class QMTAbsTableWidgetItem;
}

//自定义一级列表QMTAbstractTableWidget每一行界面
class  QMTAbsTableWidgetItem : public QWidget
{
    Q_OBJECT
public:
    QMTAbsTableWidgetItem(QWidget* parent = Q_NULLPTR);
    virtual~QMTAbsTableWidgetItem();

    //静态函数
    static QMap<int, QMTAbsTableWidgetItem*> m_pPrevious; //记录上次选择的item
    static QMTAbsTableWidgetItem* PreviousRowItemWidget(RowWidgetTemplateType type);
    static void ClearPreItem(int type = Template_None);

    /***********************ui**********************/
    void CreateRowWidgetItem(QMTAbsRowWidgetItemParam&);
    void SetLeftMarginWidth(int);                       //设置左侧margin的宽度
    void SetRightMarginWidth(int);                      //设置右侧margin的宽度
    void SetContentWidgetMargins(int, int, int, int);   //设置content区域margins
    void SetContentSpacing(int);                        //设置content区域spacing
    void ResizeWidthMap(QMap<int, int>& columnWidthMap);                //重置每一列widget的宽度
    void SetAllColumnHeight(int height);                //设置每一列widget的高度

    /***********************add**********************/
    void SetCellWidget(int, QWidget* widget);
    void AddCellWidget(QWidget* cellWidget);
    void AddStretch();
    /**********************delete*************************/
    virtual void ResetWidgetViewAndData();              //清除widget的数据，包含选中状态,以及绑定的信号

    /***************************update***************************************/
    /*set info*/
    void SetRowWidgetItemParam(QMTAbsRowWidgetItemParam* param);    //设置参数
    void SetRowItemWidgetType(int type);                            //设置类型
    void SetUniqueValue(const QString& value);                      //设置唯一值
    void SetParentValue(const QString& value);                      //设置父类唯一值

    /// <summary>
    /// 设置下边的分割线高度。行高=itemHeight。分割线是每行布局里的其中一员。
    /// </summary>
    /// <param name="gridHeight">分割线高度</param>
    /// <param name="itemHeight">item的行高，如果使用默认-1，那么使用QMTAbsRowWidgetItemParam参数里的行高</param>
    void SetBottomGridWidgetHeight(int gridHeight, int itemHeight = -1);

    //是否选中
    void SetSelectState(bool isSelect = false);
    void SetSelect(bool);
    void ResetSelect();                     //重置选中

    //设置合并单元格
    void SetSpan(const QString& text, const QString& styleSheetStr);
    void SetSelectEnable(bool bEnable, QString styleSheetStr = "");         //是否允许被选中

    //隐藏某列
    void HideColumn(int column, bool bHide);

    /***********************get******************************/
    QMTAbstractCellWidget* GetCellWidget(int column);   //获取单元格对象
    QString GetRowItemUniqueValue();            //获取唯一值
    bool GetSelect();                           //是否选中
    QWidget* GetColumnWidget(int column);       //某一列widget
    int GetColumnWidgetCount();                 //总共多少列
    int GetCoumntIndex(QObject*);               //某一个widget在第几列
    QString GetColumnText(int column);          //获取某一列文案
    QStringList GetColumnTextList();            //获取当前文案
    int GetRowItemWidgetType();                 //获取类型
    QString GetUniqueValue();                   //获取唯一值
    QString GetParentValue();                   //获取父类唯一值
    int GetCheckBoxState(int column);           //获取checkbox状态

signals:
    void sigItemEnter(const QString&, bool bEnter);    //鼠标

    void sigItemClicked(const QString&, Qt::KeyboardModifiers);
    void sigItemDoubleClicked(const QString&, Qt::KeyboardModifiers);

    void sigItemClicked(const mtuiData::TableWidgetItemIndex&, Qt::KeyboardModifiers);
    void sigItemDoubleClicked(const mtuiData::TableWidgetItemIndex&, Qt::KeyboardModifiers);

    void sigItemClicked(const QString&);
    void sigItemDoubleClicked(const QString&);

    void sigItemClicked(const mtuiData::TableWidgetItemIndex&);
    void sigItemDoubleClicked(const mtuiData::TableWidgetItemIndex&);

protected:
    /*****************create*************************/
    //void ConstructColumnItems(QJsonObject& obj);            //创建每一列的widget
    QWidget* CreateGridWidget(QWidget* parent);
    bool eventFilter(QObject* watched, QEvent* event);

    /********************delete***************************/
    void DeleteWidgetList(QList<QWidget*>& widgetList);

public:
    //virtual void paintEvent(QPaintEvent*);
    virtual void mousePressEvent(QMouseEvent* event);
    virtual void mouseDoubleClickEvent(QMouseEvent* event);
    virtual void enterEvent(QEvent* e);
    virtual void leaveEvent(QEvent* e);

private slots:

public:
    /**************UI层************************/
    QWidget* _contentParent = nullptr;      //每一列widget父类
    QList<QWidget*> _columnWidgets;         //column item

    /**************数据层************************/
    QMTAbsRowWidgetItemParam* _perRowWidgetItemParam = nullptr;     //指向一级列表参数
    bool _isSelect = false;     //是否选中
    bool _isHover = false;      //鼠标是否悬浮在上方

protected:
    Ui::QMTAbsTableWidgetItem* ui = nullptr;

    /**************数据层************************/
    //ui 样式字符串
    QString _selectSheet;
    QString _unselectSheet;
    QString _hoverSheet;

    bool _enableSelect = true;  //是否允许被选中抛出信号

    int _rowItemType = 0;               //rowItem的类型，提供给外部设置和使用
    QString _uniqueValue;               //唯一值
    QString _parentValue;               //父类唯一值
};
