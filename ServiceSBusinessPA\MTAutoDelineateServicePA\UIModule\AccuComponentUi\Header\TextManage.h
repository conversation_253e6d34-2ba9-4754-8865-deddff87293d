﻿#pragma once
#include <QFont>
#include <QString>
#include <QFontMetrics>


/// <summary>
/// 界面显示的文本扩展管理
/// 如QLabel,QLineEdit都可以继承TextManage
/// </summary>
class  TextManage
{
public:
    TextManage();
    ~TextManage();

    /// <summary>
    /// 文本超出MaxWidth的右部显示省略号
    /// </summary>
    /// <param name="font"></param>
    /// <param name="str"></param>
    /// <param name="MaxWidth"></param>
    /// <returns></returns>
    QString GetElidedText(QFont font, QString str, int MaxWidth);
    /// <summary>
    /// 获取文本str在font字体的显示宽度
    /// </summary>
    /// <param name="font"></param>
    /// <param name="str"></param>
    /// <returns></returns>
    int GetTextWidth(QFont font, QString str);

    /// <summary>
    /// 将富文本转为普通文本
    /// <font color=red>aaa 转为 aaa
    /// </summary>
    /// <param name="text"></param>
    /// <returns></returns>
    QString EnsurePlainText(const QString& text);

};

