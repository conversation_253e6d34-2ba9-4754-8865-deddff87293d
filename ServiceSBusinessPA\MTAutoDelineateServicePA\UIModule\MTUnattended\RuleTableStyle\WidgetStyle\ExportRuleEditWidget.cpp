﻿#include "ExportRuleEditWidget.h"
#include "MtMessageBox.h"
#include "CMtCoreWidgetUtil.h"
#include "CommonUtil.h"
#include "MTUnattended/UnattendedSub/OptUnattendDataNew.h"
#include "MTUnattended/RuleExportDialog/UnattendRuleExportDialog.h"
#include <QDir>

/// <summary>
/// 构造函数
/// </summary>
/// <param name="pageType">[IN]页签类型(0按钮 1文本)</param>
/// <param name="addrUniqueKey">[IN]导出规则唯一标识</param>
/// <param name="stAddr">[IN]当前导出地址</param>
/// <param name="allRemoteServerList">[IN]所有导出地址</param>
/// <param name="parantLayout">[IN]父控件</param>
ExportRuleEditWidget::ExportRuleEditWidget(const int pageType,
                                           const QString& addrUniqueKey,
                                           const n_mtautodelineationdialog::ST_AddrSimple& stAddr,
                                           QList<n_mtautodelineationdialog::ST_AddrSimple>& allRemoteServerList,
                                           QVBoxLayout* parantLayout, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    m_pageType = pageType;
    m_addrUniqueKey = addrUniqueKey;
    m_curStAddr = stAddr;
    m_parantLayout = parantLayout;
    m_allRemoteServerList = allRemoteServerList;
    ui.mtStackedWidget->setCurrentIndex(m_pageType);

    if (m_pageType == 1)
    {
        ui.widget_opt->setHidden(true);
        ui.mtLabel_range->setText(tr("内容：") + CommonUtil::getTextFromExportRange(stAddr.exportRange));
        ui.mtLabel_format->setText(tr("格式：") + CommonUtil::getTextFromExportFormat(stAddr.exportFormat));

        if (stAddr.addrType == 1)
            ui.mtLabel_addr->setText(tr("地址：") + QString(tr("共享文件夹")) + " - " + stAddr.stDirInfo.dirPath);
        else if (stAddr.addrType == 4)
            ui.mtLabel_addr->setText(tr("地址：") + QString(tr("远程节点")) + " - " + stAddr.stScpInfo.serverName);
    }

    //判断是否是默认导出地址
    if (isDefaultRule() == false)
    {
        ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setnodef"]);
    }
    else
    {
        ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setdef"]);
    }

    //信号槽
    connect(ui.mtPushButton_add, &QPushButton::clicked, this, &ExportRuleEditWidget::onMtPushButton_add);
    connect(ui.mtToolButton_mod, &QPushButton::clicked, this, &ExportRuleEditWidget::onMtPushButton_mod);
    connect(ui.mtToolButton_del, &QPushButton::clicked, this, &ExportRuleEditWidget::onMtPushButton_del);
    connect(ui.mtToolButton_def, &QPushButton::clicked, this, &ExportRuleEditWidget::onMtPushButton_def);
}

ExportRuleEditWidget::~ExportRuleEditWidget()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void ExportRuleEditWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
    ui.mtToolButton_mod->setPixmapFilename(imagePathHash["icon_edit"]);
    ui.mtToolButton_del->setPixmapFilename(imagePathHash["icon_del2"]);
    ui.mtToolButton_def->setPixmapFilename(imagePathHash["icon_setnodef"]);
}

/// <summary>
/// 更新状态-设置为默认按钮
/// </summary>
/// <param name="isDefault">[IN]设置为默认</param>
void ExportRuleEditWidget::updateState_mtToolButton_def(const bool isDefault)
{
    if (isDefault == true)
    {
        ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setdef"]);
        ui.mtToolButton_def->setToolTipText(tr("移除默认导出规则"));
    }
    else
    {
        ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setnodef"]);
        ui.mtToolButton_def->setToolTipText(tr("设置为默认导出规则"));
    }
}

/// <summary>
/// 获取数据
/// </summary>
void ExportRuleEditWidget::getNewAddrInfo(n_mtautodelineationdialog::ST_AddrSimple& outStAddr, QString& outAddrUniqueKey)
{
    outStAddr = m_curStAddr;
    outAddrUniqueKey = m_addrUniqueKey;
}

/// <summary>
/// 获取导出规则唯一标识
/// </summary>
QString ExportRuleEditWidget::getAddrUniqueKey()
{
    return m_addrUniqueKey;
}

/// <summary>
/// 获取页签类型(0按钮 1文本)
/// </summary>
int ExportRuleEditWidget::getPageType()
{
    return m_pageType;
}

/// <summary>
/// 新增规则按钮
/// </summary>
void ExportRuleEditWidget::onMtPushButton_add()
{
    //判断是否超过了5条
    if (m_parantLayout->count() > 5)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("最多只能添加5个导出规则"));
        return;
    }

    //
    QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> usedAddrMap = getOtherUsedAddr();
    //
    UnattendRuleExportDialog dlg(this);
    dlg.init(true, n_mtautodelineationdialog::ST_AddrSimple(), usedAddrMap, m_allRemoteServerList);

    if (dlg.exec() == QDialog::Accepted)
    {
        bool isMod = false;
        n_mtautodelineationdialog::ST_AddrSimple newAddr;
        dlg.getNewAddrInfo(isMod, newAddr);
        emit this->sigCreateNewAddr(newAddr);
    }
}

/// <summary>
/// 编辑规则按钮
/// </summary>
void ExportRuleEditWidget::onMtPushButton_mod()
{
    QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> usedAddrMap = getOtherUsedAddr();
    //
    UnattendRuleExportDialog dlg(this);
    dlg.init(false, m_curStAddr, usedAddrMap, m_allRemoteServerList);

    if (dlg.exec() == QDialog::Accepted)
    {
        bool isMod = false;
        dlg.getNewAddrInfo(isMod, m_curStAddr);
        //更新界面
        ui.mtLabel_range->setText(tr("内容：") + CommonUtil::getTextFromExportRange(m_curStAddr.exportRange));
        ui.mtLabel_format->setText(tr("格式：") + CommonUtil::getTextFromExportFormat(m_curStAddr.exportFormat));

        if (m_curStAddr.addrType == 1)
            ui.mtLabel_addr->setText(tr("地址：") + QString(tr("共享文件夹")) + " - " + m_curStAddr.stDirInfo.dirPath);
        else if (m_curStAddr.addrType == 4)
            ui.mtLabel_addr->setText(tr("地址：") + QString(tr("远程节点")) + " - " + m_curStAddr.stScpInfo.serverName);
    }
}

/// <summary>
/// 删除规则按钮
/// </summary>
void ExportRuleEditWidget::onMtPushButton_del()
{
    QString msg = QString(tr("您确定要删除该导出规则吗？"));
    int ret = MtMessageBox::NoIcon::question_Title(this->window(), msg);

    if (QMessageBox::Yes == ret)
    {
        emit this->sigDelOneAddr(m_addrUniqueKey);
    }
}

/// <summary>
/// 设置为默认按钮
/// </summary>
void ExportRuleEditWidget::onMtPushButton_def()
{
    if (isDefaultRule() == false)
    {
        int ret = MtMessageBox::NoIcon::question_Title(this->window(), tr("是否确定设置为默认导出规则?"), tr("设置默认导出规则后，可便捷将默认导出规则应用于其他规则中。"));

        if (QMessageBox::Yes == ret)
        {
            OptUnattendDataNew::setDefaultExportAddr(m_curStAddr);
            ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setdef"]);
        }
    }
    else
    {
        OptUnattendDataNew::setDefaultExportAddr(n_mtautodelineationdialog::ST_AddrSimple());
        ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setnodef"]);
    }
}

/// <summary>
/// 获取已经被其他导出规则使用的地址集合
/// </summary>
/// <returns>已经被其他导出规则使用的地址集合(key-导出规则唯一标识)</returns>
QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> ExportRuleEditWidget::getOtherUsedAddr()
{
    QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> addrMap;

    for (int i = 0; i < m_parantLayout->count(); i++)
    {
        QLayoutItem* item = m_parantLayout->itemAt(i);

        if (item->widget() == nullptr)
            continue;

        QWidget* widget = item->widget();
        ExportRuleEditWidget* exportRuleEditWidget = qobject_cast<ExportRuleEditWidget*>(widget);

        if (exportRuleEditWidget == nullptr)
            continue;

        if (exportRuleEditWidget->getPageType() == 1) //文本页
        {
            QString otherAddrUniqueKey;
            n_mtautodelineationdialog::ST_AddrSimple otherStAddr;
            exportRuleEditWidget->getNewAddrInfo(otherStAddr, otherAddrUniqueKey);

            if (otherAddrUniqueKey == m_addrUniqueKey)
                continue;

            addrMap.insert(otherAddrUniqueKey, otherStAddr);
        }
    }

    return addrMap;
}

/// <summary>
/// 是否是默认规则
/// </summary>
/// <returns>true是</returns>
bool ExportRuleEditWidget::isDefaultRule()
{
    //判断设置默认按钮状态
    n_mtautodelineationdialog::ST_AddrSimple defaultAddr = OptUnattendDataNew::getDefaultExportAddr();

    if (defaultAddr.addrType != m_curStAddr.addrType)
    {
        return false;
    }

    if ((defaultAddr.addrType == 1 || defaultAddr.addrType == 4) &&
        (defaultAddr.exportFormat != m_curStAddr.exportFormat || defaultAddr.exportRange != m_curStAddr.exportRange))
    {
        return false;
    }

    if (defaultAddr.addrType == 1 && QDir(defaultAddr.stDirInfo.dirPath) != QDir(m_curStAddr.stDirInfo.dirPath))
    {
        return false;
    }

    if (defaultAddr.addrType == 1 && defaultAddr.stDirInfo.mkSubType != m_curStAddr.stDirInfo.mkSubType)
    {
        return false;
    }

    if (defaultAddr.addrType == 4 && defaultAddr.stScpInfo.serverName != m_curStAddr.stScpInfo.serverName)
    {
        return false;;
    }

    return true;
}

/// <summary>
/// 鼠标移入事件
/// </summary>
void ExportRuleEditWidget::enterEvent(QEvent* event)
{
    if (m_pageType == 1)
    {
        QString styleStr = "#page_2{background-color:rgba(@colorA0,0.3);}";
        CMtCoreWidgetUtil::formatStyleSheet(styleStr);
        this->setStyleSheet(styleStr);
        ui.widget_opt->setHidden(false);

        //判断设置默认按钮状态
        if (isDefaultRule() == false)
        {
            ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setnodef"]);
            ui.mtToolButton_def->setToolTipText(tr("设置为默认导出规则"));
            return;
        }

        ui.mtToolButton_def->setPixmapFilename(m_imagePathHash["icon_setdef"]);
        ui.mtToolButton_def->setToolTipText(tr("设置为普通导出规则"));
    }
}

/// <summary>
/// 鼠标移出事件
/// </summary>
void ExportRuleEditWidget::leaveEvent(QEvent* event)
{
    if (m_pageType == 1)
    {
        this->setStyleSheet("#page_2{background: transparent;}");
        ui.widget_opt->setHidden(true);
    }
}
