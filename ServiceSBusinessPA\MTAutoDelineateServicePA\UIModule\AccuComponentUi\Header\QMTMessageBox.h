﻿#pragma once

#include <QDialog>
#include <QMessageBox>
#include <QGraphicsDropShadowEffect>
#include "Language.h"
#include "DarkeningWidget.h"
#include "QLabel_Dot.h"

/// <summary>
/// 操作提示框
/// 从左往右，默认第一个按键是隐藏的
/// 第一个按键点击返回QMessageBox::Save，
/// 第二个按键QMessageBox::No，
/// 第三个按键QMessageBox::Yes，
/// 右上角按键QMessageBox::Cancel，
/// 通过getResultButton接口获取点击的是哪个按键
/// </summary>

/*
已废弃此类，请使用MtMessageBox
*/
namespace Ui
{
class QMTMessageBox;
};

class  QMTMessageBox : public QDialog
{
    Q_OBJECT


public:
    enum ButtonType//从左往右，按键顺序
    {
        BtnType_First = QMessageBox::Save,
        BtnType_Second = QMessageBox::No,
        BtnType_Third = QMessageBox::Yes,
        BtnType_Close = QMessageBox::Cancel,
    };
    QMTMessageBox(QWidget* parent = Q_NULLPTR);
    ~QMTMessageBox();
    QMessageBox::StandardButton getResultButton();              //获取点击的是哪个按键
    /******************************常用的静态*************************************************/
    //只显示一个按键,按键文案可自定义
    static void About(QString message, QString buttonText = tr("确认"), bool cancel  = true, bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);
    static void About(QList<QString> messageList, QString buttonText = tr("确认"), bool cancel = true, bool hideLogo = false, QString logoPath = "", QWidget* parent = Q_NULLPTR);

    //显示取消和确定按键，取消和确定的样式可以自定义
    static QMessageBox::StandardButton Information(QString message, bool cancel = true, bool hideLogo = true, QString logoPath = "", QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton Information(bool cancel, QList<QString> messageList, bool hideLogo = true, QString logoPath = "", QWidget* parent = Q_NULLPTR);
    static QMessageBox::StandardButton Information(bool cancel, QList<QString> messageList, QString noStyle = "", QString OkStyle = "", bool hideLogo = true, QString logoPath = "", QWidget* parent = Q_NULLPTR);

    //显示取消和确定按键，并且logo是警告图标
    static QMessageBox::StandardButton ShowWarning(bool cancel, QString message, QString message2 = "", QString noStyle = "", QString yesStyle = "", bool hideDarken = true, QWidget* parent = Q_NULLPTR);

    //删除警示样式
    static QMessageBox::StandardButton ShowDeleteWarning(bool cancel, QString message, QString message2 = "", bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);

    //显示不保存和保存两个按键
    static QMessageBox::StandardButton ShowSaveAndNo(bool cancel, QString message, bool hideLogo = true, bool hideDarken = true, QWidget* parent = Q_NULLPTR);

    //两个或者三个选项选择
    static QMessageBox::StandardButton ShowSelectBox(QString message, QStringList buttonTextList, bool hideLogo = true);
    /******************************ui样式*******************************************************/
    /// <summary>
    /// 设置显示的button
    /// </summary>
    /// <param name="buttons">
    /// Yes: 显示yes按键
    /// No: 显示no按键
    /// Save: 显示save按键
    /// Cancel: 显示cancel按键
    /// </param>
    virtual void SetShowButtons(QMessageBox::StandardButtons buttons = QMessageBox::Yes);

    virtual void HideSaveButton(bool value = true);             //是否隐藏第一个按键
    virtual void HideNoButton(bool value = true);               //是否隐藏第二个按键
    virtual void HideYesButton(bool value = true);              //是否隐藏第三个按键
    virtual void HideCancelButton(bool value = true);           //是否隐藏右上角取消按键
    virtual void HideLogo(bool value = true);                   //是否隐藏logo
    virtual void HideDarken(bool value = true);                 //是否隐藏黑色背景

    //设置样式
    virtual void SetSaveBtnStyle(QString);                      //设置第一个按键样式
    virtual void SetNoBtnStyle(QString);                        //设置第二个按键样式
    virtual void SetYesBtnStyle(QString);                       //设置第三个按键样式
    virtual void SetMessage1Style(QString styleStr);
    virtual void SetMessage2Style(QString styleStr);
    //设置显示的文案信息
    virtual void SetMessage(QList<QString> messages);
    virtual void SetMessage1(QString message, bool hideLabel2 = true);
    virtual void SetMessage2(QString message);

    virtual void SetIcon(QString urls);                         //设置logo的图案

    //设置按键的文案
    virtual void SetButtonText(QList<QString> texts);           //按键顺序，save,no,yes
    virtual void SetSaveBtnText(QString text);
    virtual void SetNoBtnText(QString text);
    virtual void SetYesBtnText(QString text);
    /*************************************************************************************/
protected:
    void SetMessageWidth(QString message, QLabel_Dot* label);   //设置messsage 的显示宽度,不足用...表示
    void SetMessageWidth(QString message, QLabel* label);       //设置messsage,显示不足换行
    void InitTemplateStyle();
/// <summary>
/// 属性
/// </summary>
protected:
    QMessageBox::StandardButton resultButton;   //< 返回结果，不能用setResult和result返回，始终是0
    DarkeningWidget* darkenWidget = nullptr;

private:
    Ui::QMTMessageBox* ui;
    QGraphicsDropShadowEffect* shadow_effect = nullptr;
};
