﻿// *********************************************************************************
// <remarks>
// FileName    : InnerStruct
// Author      : zlw
// CreateTime  : 2023-23-01
// Description : 内部数据
// </remarks>
// **********************************************************************************
#pragma once

#include <QObject>
#include <QMetaType>
#include "MTAutoDelineationDialogData.h"
#include "AccuComponentUi\Header\QMTEnumDef.h"


/************************正则表达式*************************/
//输入数字
#define RegExp_Number "(^[0-9]*"
//输入数字和.
#define RegExp_Number2 "(^[0-9-.]*"
//输入正整数
#define RegExp_Number3 "[1-9]\\d*"
//输入正数及小数点后两位
#define RegExp_Number4 "^([1-9]\\d*|0)(\\.\\d{1,2})?$"
//输入正整数1~99
#define RegExp_Number5 "^[1-9]|[1-9][0-9]$"
//输入字符和数字
#define RegExp_CharNumber "[A-Za-z0-9-_]*"
#define RegExp_CharNumber2 "[A-Za-z0-9-`~!#^&*()_-+=|;:""'',.]*"
#define RegExp_CharNumber3 "[A-Za-z0-9- _]*"
#define RegExp_CharNumber4 "[A-Za-z0-9-`~!#^&*() _-+=|;:""'',.]*"
//不允许输入反斜杠
#define RegExp_NotBackslashAnd "^[^\\]*$"
//不允许输入单引号
#define RegExp_NotSingleQuote "[^']*$"
//不允许输入分号
#define RegExp_NotSemicolon "[^;]*$"


/************************模态类型标签***********************/
#define Def_CT              "CT"                 //CT
#define Def_MR              "MR"                 //MR


/************************其他类型标签***********************/
#define Def_Separator       "."                 //分隔符


/*************************模板id标签************************/
#define Def_NewCreateId                     -100            //新建时的临时id
#define Def_TempIdOfSelectTemplate          -99             //选择模板进行勾画页签-临时id
#define Def_TempIdOfSelectRoi               -98             //选择ROI进行勾画页签-临时id



/********************内置的固定模板英文名*******************/
#define Def_English_HeadNeck                    "Head&Neck"
#define Def_English_Thorax_M                    "Thorax-M"
#define Def_English_Thorax_F                    "Thorax-F"
#define Def_English_Abdomen                     "Abdomen"
#define Def_English_Pelvis_M                    "Pelvis-M"
#define Def_English_Pelvis_F                    "Pelvis-F"
#define Def_English_HeadNeck_Thorax_M           "Head&Neck + Thorax-M"
#define Def_English_HeadNeck_Thorax_F           "Head&Neck + Thorax-F"
#define Def_English_Thorax_Abdomen_M            "Thorax + Abdomen-M"
#define Def_English_Thorax_Abdomen_F            "Thorax + Abdomen-F"
#define Def_English_Abdomen_Pelvis_M            "Abdomen + Pelvis-M"
#define Def_English_Abdomen_Pelvis_F            "Abdomen + Pelvis-F"
#define Def_English_HeadNeck_Thorax_Abdomen_M   "Head&Neck + Thorax + Abdomen-M"
#define Def_English_HeadNeck_Thorax_Abdomen_F   "Head&Neck + Thorax + Abdomen-F"
#define Def_English_Thorax_Abdomen_Pelvis_M     "Thorax + Abdomen + Pelvis-M"
#define Def_English_Thorax_Abdomen_Pelvis_F     "Thorax + Abdomen + Pelvis-F"
#define Def_English_All_Parts_M                 "All Parts-M"
#define Def_English_All_Parts_F                 "All Parts-F"


/********************内置的固定模板中文名*******************/
#define Def_Chinese_HeadNeck                    QObject::tr("头颈部OARs模板")
#define Def_Chinese_Thorax_M                    QObject::tr("胸部（男）OARs模板")
#define Def_Chinese_Thorax_F                    QObject::tr("胸部（女）OARs模板")
#define Def_Chinese_Abdomen                     QObject::tr("腹部OARs模板")
#define Def_Chinese_Pelvis_M                    QObject::tr("下腹部（男）OARs模板")
#define Def_Chinese_Pelvis_F                    QObject::tr("下腹部（女）OARs模板")
#define Def_Chinese_HeadNeck_Thorax_M           QObject::tr("头颈部+胸部（男）OARs模板")
#define Def_Chinese_HeadNeck_Thorax_F           QObject::tr("头颈部+胸部（女）OARs模板")
#define Def_Chinese_Thorax_Abdomen_M            QObject::tr("胸部+腹部（男）OARs模板")
#define Def_Chinese_Thorax_Abdomen_F            QObject::tr("胸部+腹部（女）OARs模板")
#define Def_Chinese_Abdomen_Pelvis_M            QObject::tr("腹部+下腹部（男）OARs模板")
#define Def_Chinese_Abdomen_Pelvis_F            QObject::tr("腹部+下腹部（女）OARs模板")
#define Def_Chinese_HeadNeck_Thorax_Abdomen_M   QObject::tr("头颈部+胸部+腹部（男）OARs模板")
#define Def_Chinese_HeadNeck_Thorax_Abdomen_F   QObject::tr("头颈部+胸部+腹部（女）OARs模板")
#define Def_Chinese_Thorax_Abdomen_Pelvis_M     QObject::tr("胸部+腹部+下腹部（男）OARs模板")
#define Def_Chinese_Thorax_Abdomen_Pelvis_F     QObject::tr("胸部+腹部+下腹部（女）OARs模板")
#define Def_Chinese_All_Parts_M                 QObject::tr("全身（男）OARs模板")
#define Def_Chinese_All_Parts_F                 QObject::tr("全身（女）OARs模板")


/**************************内部枚举*************************/
/// <summary>
/// 自定义组件的单元格的类型
/// </summary>
enum EM_CustCellWidgetDelegateType
{
    DELEAGATE_QCustcmAnonyWidget = DELEAGATE_TYPE_User + 1,       //DcmAnonyTableWidgetItemParam
    DELEAGATE_QCustcmUnattendInfoWidget,                          //UnattendInfoTableItem
    DELEAGATE_QCustcmUnattendInfoWidget2,                         //UnattendInfoTableItem
    DELEAGATE_QCustcmConfigRuleWidget,                            //ConfigRuleItem
    DELEAGATE_QUnattendSubTableWidget                               //UnattendSubTable
};


/*************************内部结构体************************/
/// <summary>
/// Eclipse器官信息
/// </summary>
struct ST_EclipseRoi
{
    QString structureID;    //ROI-ID
    QString volumeType;     //ROI类型
    QString ColorAndStyle;  //ROI颜色
    QMap<QString, QString> roiCodeMap;
    /// <summary>
    /// "code"          : ROI编码(codeValue)
    /// "label"         : ROI标签(roiObservationLabel)
    /// "codeScheme"    : 编码方案标识(codingSchemeDesignator)，FMA、99VMS_STRUCTCODE...
    /// "codeSchemeVer" : 编码方案版本(codingSchemeVersion), 3.2、1.0...
    /// "codeMean"      : ROI含义(codeMeaning)
    /// </summary>
};
Q_DECLARE_METATYPE(ST_EclipseRoi);

/// <summary>
/// Eclipse模板信息
/// </summary>
struct ST_EclipseTemplate
{
    QString templateID;     //模板ID
    QString approval;       //是否审批(Unapproved)
    QString user;           //用户
    QString desc;           //描述
    QMap<QString/*roiID_roiCode*/, ST_EclipseRoi> roiInfoMap;
};
Q_DECLARE_METATYPE(ST_EclipseTemplate);

/// <summary>
/// 远程共享目录信息
/// </summary>
struct ST_STU_REMOTE_DIR
{
    void setValue(const QString val_export_dir, const QString val_create_sharedir, const QString val_export_format_guid)
    {
        export_dir = val_export_dir;
        create_sharedir = (val_create_sharedir.isEmpty() == true ? "0" : val_create_sharedir);
        export_format_guid = (val_export_format_guid == true ? "0" : val_export_format_guid);
    };

    QString export_dir;         //共享目录完整路径
    QString create_sharedir;    //导出到共享目录时是否根据序列patientID创建相应目录(0:否 1:是)
    QString export_format_guid; //导出格式GUID(指的是插件GUID,适配导出到不同TPS对DICOM特定字段的要求，为空失效)
};
Q_DECLARE_METATYPE(ST_STU_REMOTE_DIR);
