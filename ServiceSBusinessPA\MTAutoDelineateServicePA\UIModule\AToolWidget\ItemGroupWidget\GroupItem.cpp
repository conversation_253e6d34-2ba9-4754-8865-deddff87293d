﻿#include "GroupItem.h"
#include "MtCheckBox.h"
#include "AccuComponentUi\Header\Language.h"
#include "MtLabel.h"

#define Def_VerticalSpace   10  //垂直间距
#define Def_ItemHeight      18  //项高度
#define Def_Indentation     26  //缩进


GroupItem::GroupItem(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
}

/// <summary>
/// 析构函数
/// </summary>
GroupItem::~GroupItem()
{
}

void GroupItem::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 设置item的宽度
/// </summary>
/// <param name="itemWidthNum">[IN]item的宽度</param>
void GroupItem::setItemWidth(const int itemWidthNum)
{
    m_itemWidth = itemWidthNum;
}

/// <summary>
/// 初始化Item
/// </summary>
/// <param name="pageTypeEnum">[IN]页面类型</param>
/// <param name="dataList">[IN]数据集合</param>
/// <param name="maxColumn">[IN]最大列数</param>
/// <param name="horizontalSpace">[IN]水平间距</param>
/// <returns>项高度</returns>
int GroupItem::init(const EM_PageType pageTypeEnum, const QList<GroupItemData>& dataList, int maxColumn, int horizontalSpace)
{
    if (maxColumn <= 0 || dataList.size() <= 0)
        return 0;

    int subWidth = this->width() / maxColumn;
    //实际行数
    int rowCount = dataList.size() / maxColumn + (dataList.size() % maxColumn > 0 ? 1 : 0);

    if (pageTypeEnum == Page_LabelLabel)
    {
        ui.mtStackedWidget->setCurrentWidget(ui.page_label);

        for (int i = 0; i < dataList.size(); i++)
        {
            int row = i / maxColumn;
            int col = i % maxColumn;
            GroupItemData data = dataList[i];
            MtLabelLabel* labelLabel = new MtLabelLabel(this);
            labelLabel->setMtType(MtLabelLabel::labellabel2);
            labelLabel->setLabel1Text(data.value1);
            labelLabel->setLabel2Text("");
            labelLabel->setFixedWidth(m_itemWidth);
            labelLabel->setStyleSheet("font-size:13px;");

            if (data.value2.isEmpty() == false || data.value3.isEmpty() == false)
            {
                labelLabel->setLabel2Text(QString("[%1%2%3]").arg(data.value2).arg(data.value2.isEmpty() == false && data.value3.isEmpty() == false ? "," : "").arg(data.value3));
            }
            else
            {
                labelLabel->label2()->setHidden(true); //不然会显示... fix:#21891
            }

            m_labelLabelList.push_back(labelLabel);
            ui.gridLayout_pageLabel->setHorizontalSpacing(horizontalSpace);
            ui.gridLayout_pageLabel->setVerticalSpacing(Def_VerticalSpace);
            ui.gridLayout_pageLabel->addWidget(labelLabel, row, col);
        }

        //如果实际数据少于maxColumn，加一个空的MtLabelLabel
        if (maxColumn > dataList.size())
        {
            int needFillNum = maxColumn - dataList.size();

            for (int i = 0; i < needFillNum; i++)
            {
                MtLabelLabel* labelLabel = new MtLabelLabel(this);
                labelLabel->setMtType(MtLabelLabel::labellabel2);
                labelLabel->setLabel1Text("");
                labelLabel->setLabel2Text("");
                labelLabel->setFixedWidth(m_itemWidth);
                ui.gridLayout_pageLabel->setHorizontalSpacing(horizontalSpace);
                ui.gridLayout_pageLabel->setVerticalSpacing(Def_VerticalSpace);
                ui.gridLayout_pageLabel->addWidget(labelLabel, 0, dataList.size() + i);
            }
        }
    }
    else if (pageTypeEnum == Page_CheckLabel)
    {
        ui.mtStackedWidget->setCurrentWidget(ui.page_check);

        for (int i = 0; i < dataList.size(); i++)
        {
            int row = i / maxColumn;
            int col = i % maxColumn;
            GroupItemData data = dataList[i];
            MtCheckBoxLabel* checkLabel = new MtCheckBoxLabel(this);
            checkLabel->setMtType(MtCheckBoxLabel::checkboxlabel2);
            checkLabel->setChecked(data.isCheck);
            checkLabel->setCheckBoxText(data.value1);
            checkLabel->setLabelText("");
            checkLabel->setFixedWidth(m_itemWidth);
            checkLabel->setStyleSheet("font-size:13px;");
            checkLabel->setModelData(QVariant::fromValue(data));

            if (data.organType == 1)  //带有主结构的亚结构器官需要缩进+斜体
            {
                checkLabel->setMtType(MtCheckBoxLabel::checkboxlabel2);
                checkLabel->setContentsMargins(Def_Indentation, 0, 0, 0);
            }
            else if (data.organType == 2) //不带有主结构的亚结构器官只要斜体
            {
                checkLabel->setMtType(MtCheckBoxLabel::checkboxlabel2);
            }

            if (data.value2.isEmpty() == false || data.value3.isEmpty() == false)
                checkLabel->setLabelText(QString("[%1%2%3]").arg(data.value2).arg(data.value2.isEmpty() == false && data.value3.isEmpty() == false ? "," : "").arg(data.value3));

            //信号槽
            connect(checkLabel->checkBox(), &QCheckBox::toggled, this, [=](bool checked)
            {
                emit this->sigGroupItemCheckToGroupItemListWidget(data.groupId, data.valId, checked);
                printf("sigGroupItemCheck1\n");
            });
            //
            m_checkBoxLabelList.push_back(checkLabel);
            ui.gridLayout_pageCheck->setHorizontalSpacing(horizontalSpace);
            ui.gridLayout_pageCheck->setVerticalSpacing(Def_VerticalSpace);

            //ui.gridLayout_pageCheck->setRowMinimumHeight(row, checkLabel->sizeHint().height());
            if (data.organType == 1)//带有主结构的亚结构器官需要缩进+斜体+连接折线
            {
                QLabel* labelLinker = new QLabel(this);
                labelLinker->setPixmap(m_imagePathHash["Linker"]);
                labelLinker->setContentsMargins(6, 0, 0, 0);
                ui.gridLayout_pageCheck->addWidget(labelLinker, row, col);
                ui.gridLayout_pageCheck->addWidget(checkLabel, row, col);
            }
            else
            {
                ui.gridLayout_pageCheck->addWidget(checkLabel, row, col);
            }
        }

        //如果实际数据少于maxColumn，加一个空的MtLabelLabel
        if (maxColumn > dataList.size())
        {
            int needFillNum = maxColumn - dataList.size();

            for (int i = 0; i < needFillNum; i++)
            {
                MtLabelLabel* labelLabel = new MtLabelLabel(this);
                labelLabel->setMtType(MtLabelLabel::labellabel2);
                labelLabel->setLabel1Text("");
                labelLabel->setLabel2Text("");
                labelLabel->setFixedWidth(m_itemWidth);
                ui.gridLayout_pageCheck->setHorizontalSpacing(horizontalSpace);
                ui.gridLayout_pageCheck->setVerticalSpacing(Def_VerticalSpace);
                ui.gridLayout_pageCheck->addWidget(labelLabel, 0, dataList.size() + i);
            }
        }
    }

    //返回实际高度
    int topMargin, bottomMargin;
    ui.verticalLayout->getContentsMargins(0, &topMargin, 0, &bottomMargin);
    return Def_ItemHeight * rowCount + Def_VerticalSpace * (rowCount > 0 ? rowCount - 1 : 0) + topMargin + bottomMargin;
}

/// <summary>
/// 勾选全部MtCheckBoxLabel项
/// </summary>
/// <param name="groupId">[IN]分组id</param>
/// <param name="checked">[IN]true勾选 false不勾选</param>
void GroupItem::slotCheckAllItem_MtCheckBoxLabel(const int groupId, const bool checked)
{
    //printf("!!!!! m_checkBoxLabelList.size() = %d\n", m_checkBoxLabelList.size());
    for (int i = 0; i < m_checkBoxLabelList.size(); i++)
    {
        MtCheckBoxLabel* item = m_checkBoxLabelList[i];

        if (item != nullptr)
        {
            GroupItemData itemData = item->modelData().value<GroupItemData>();

            if (itemData.groupId == groupId)
            {
                item->checkBox()->setChecked(checked);
                //printf("###### %s\n", item->checkBox()->text().toStdString().c_str());
            }
        }
    }
}


