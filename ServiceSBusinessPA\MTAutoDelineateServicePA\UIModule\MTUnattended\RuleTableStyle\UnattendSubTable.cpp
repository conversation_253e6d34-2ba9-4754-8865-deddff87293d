﻿#include "UnattendSubTable.h"
#include "AccuComponentUi\Header\UnitUIComponent\QCustMtLabel.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "UnattendSubTableEditItem.h"
#include "UnattendSubTableShowItem.h"


UnattendSubTable::UnattendSubTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
}

UnattendSubTable::~UnattendSubTable()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void UnattendSubTable::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

void UnattendSubTable::init()
{
    initTableView();
}

/// <summary>
/// 初始化表格
/// </summary>
void UnattendSubTable::initTableView()
{
}
