﻿#include <QString>
/*
description:
统一规则：
1.数字前面加L代表lower加了负号：例：L9999= -9999
2.数字后面加P代表point加了点，例：9999P99 =9999.99
3._V(1/2/3...)代表该正则有多个相近的版本，但是实现的效果略有偏差（一般不是指正则表达式略有偏差）
author:luohao
*/
/*--------------------------------------整数------------------------------------------*/
//正则限制1-50
#define RegExp_1_To_50 "^([1-4]([0-9])?|50|[1-9])$"
////正则限制1-99 int整数
#define RegExp_1_To_99  "(^([1-9][0-9]{0,1}))?$"
//正则限制0-100 int整数
#define RegExp_1_To_100 "(^([1-9][0-9]{0,1}|100))?$"
//正则限制0-9999 int整数
#define RegExp_0_To_9999 "(\\d{0,4})?"

/*--------------------------------------浮点数------------------------------------------*/
//正则限制0.00-1
#define RegExp_0P00_To_1 "(^(0((\\.)(\\d{1,2})?)?|1((\\.)(0{1,2})?)?))?$"
//正则限制0.10-0.50，和0（0冲突）
#define RegExp_0P10_To_0P50 "^0\\.([1-4]([0-9])?)?|0(\\.5(0)?)?$"
//正则限制0.00-100
#define RegExp_0P00_To_100 "(^(\\d?\\d((\\.)(\\d{0,2})?)?|100((\\.)(0{1,2})?)?))?$"
//正则限制0.00-100，保留一位小数
#define RegExp_0P00_To_100V_P1 "(^(\\d?\\d((\\.)(\\d{0,1})?)?|100((\\.)(0{0,1})?)?))?$"
//正则限制0.00-1000.00
#define RegExp_0P00_To_1000P00 "^[0-9]{1,3}(\\.([0-9])?([0-9])?)?|1000(\\.(0)?(0)?)?$"
//正则限制0.00-200.00
#define RegExp_0P00_To_200P00 "^\\d{1,2}(\\.([0-9]{1,2})?)?|1\\d{2}(\\.([0-9]{1,2})?)?|200(\\.(0)?(0)?)?$"
//正则限制0.00-20000.00
#define RegExp_0P00_To_20000P00 "^\\d{1,4}(\\.([0-9]{1,2})?)?|1\\d{4}(\\.([0-9]{1,2})?)?|20000(\\.(0)?(0)?)?$"
//正则限制0.00-15000000.00
#define RegExp_0P00_To_15000000P00 "^\\d{1,7}(\\.([0-9]{1,2})?)?|1\\d{5}(\\.([0-9]{1,2})?)?|15000000(\\.(0)?(0)?)?$"
//正则限制1.00-20000.00
#define RegExp_1P00_To_20000P00 "^[1-9][0-9]{0,3}(((\\.)([0-9]{1,2})?)?)?|1([0-9]{4})?((\\.)([0-9]{1,2})?)?|20000((\\.)((0)?(0)?)?)?$"
//正则限制0.00000-64.00000  (10-59.99999+0-9.99999+60-63.99999+64.00000)
#define RegExp_0P00000_To_64P00000 "^[1-5][0-9](\\.([0-9]{1,5})?)?|[0-9](\\.([0-9]{1,5})?)?|6[0-3](\\.([0-9]{1,5})?)?|64(\\.(0{1,5})?)?$"

//圆的角度 -360-360,保留2位小数
#define RegExp_Circle_L360_To_360 "(^(-?(([0-9]|[1-9]\\d|[1-2]\\d\\d|3[0-5]\\d))((\\.)(\\d{1,2})?)?)|(-?360((\\.)(0{1,2})?)?))?$"
//圆的角度 -360-360，保留一位小数
#define RegExp_Circle_L360_To_360V_P1 "(^(-?(([0-9]|[1-9]\\d|[1-2]\\d\\d|3[0-5]\\d))((\\.)(\\d)?)?)|(-?360(\\.0{0,1})?))?$"
//圆的角度 0-360
#define RegExp_Circle_0_To_360 "(^([1-9]|([1-9][0-9])|([1-2][0-9][0-9])|([3][0-5][0-9])|([0]{1}))$|^[3][6][0])?$"

//正则限制0.00-9999.99
#define RegExp_0P00_To_9999P99 "(^\\d{1,4}((\\.)(\\d{1,2})?)?)?$"
//正则限制0.000-9999.999
#define RegExp_0P000_To_9999P999 "(^\\d{1,4}((\\.)(\\d{1,3})?)?)?$"
//正则限制-9999.99-9999.99
#define RegExp_L9999P99_To_9999P99 "(^-?\\d{1,4}((\\.)(\\d{1,2})?)?)?$"
//正则限制-9999.999-9999.999
#define RegExp_L9999P999_To_9999P999 "(^-?\\d{1,4}((\\.)(\\d{1,3})?)?)?$"
//正则限制-9999.9999-9999.9999
#define RegExp_L9999P9999_To_9999P9999 "(^-?\\d{1,4}((\\.)(\\d{1,4})?)?)?$"
//正则限制-99999.9999-99999.9999
#define RegExp_L99999P9999_To_99999P9999 "(^-?\\d{1,5}((\\.)(\\d{1,4})?)?)?$"

/*--------------------------------------混合------------------------------------------*/
//正则字母数字和一些特殊符号,不包括","
#define RegExp_SpecialSymbol_V1 "(^[A-Za-z0-9~_ ()!@#$%Z^&*+-/\\`;:\"'?<>]+)?$"
//包括包括","
#define RegExp_SpecialSymbol_V2 "(^[A-Za-z0-9~_ !@#$%Z^&*+-/\\.`;:\"'?<>,]+)?$"
//正则字母数字和一些特殊符号,不包括","和"!"
#define RegExp_SpecialSymbol_V3 "(^[A-Za-z0-9~_ @#$%Z^&*+-/\\`;:\"'?<>]+)?$"
//正则字母数字和一些特殊符号,不包括","和空格
#define RegExp_SpecialSymbol_V4 "(^[A-Za-z0-9~_!@#$%Z^&*+-/\\`;:\"'?<>]+)?$"
//账号正则
#define RegExp_SpecialSymbol_Account "(^[A-Za-z0-9~!@#$%Z^*+_/\\`;:.{}\\[\\]?]+)?$"
//只能输入字符
#define RegExp_Char "(^[A-Za-z]+)?$"
//只能输入数字
#define RegExp_Number "(^[0-9]+)?$"
//只能输入字符和数字
#define RegExp_CharNumber "(^[A-Za-z0-9]+)?$"
//只能输入字符和数字和"-"和" _"暂时用于AETitle
#define RegExp_CharNumber_SpecialSymbol_V1 "([A-Za-z0-9-_]*)?$"
//只能输入字符和数字和"-"和" _"还有空格，
#define RegExp_CharNumber_SpecialSymbol_V2 "([A-Za-z0-9-_ ]*)?$"
//只能输入字符和数字和"-"和" _"和"()"还有空格，
#define RegExp_CharNumber_SpecialSymbol_V3 "([A-Za-z0-9-_ ()]*)?$"


/**********长度限制************************/
#define  STRINGNAME_MAXLEN32  32    //最大32字节的名称(模板名称)
#define  STRINGNAME_MAXLEN128  128  //最大128字节的名称(描述)

/*--------------------------------------特定用途------------------------------------------*/
//IP地址
#define RegExp_IPAddress "(^(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|[1-9])\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d)\\.(1\\d{2}|2[0-4]\\d|25[0-5]|[1-9]\\d|\\d))?$"
//端口号 1-65535
#define RegExp_Port "(^(1(02[4-9]|0[3-9][0-9]|[1-9][0-9]{2})|[2-9][0-9]{3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5]))?$"