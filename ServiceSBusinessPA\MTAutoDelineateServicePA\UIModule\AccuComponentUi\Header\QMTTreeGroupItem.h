﻿#pragma once

#include <QWidget>


namespace Ui
{
class QMTTreeGroupItem;
}

class  QMTTreeGroupItem : public QWidget
{
    Q_OBJECT

public:
    QMTTreeGroupItem(QString uniqueValue, QWidget* parent = 0);
    ~QMTTreeGroupItem();
    void InitTreeGroupItem();
    void setName(QString name);;
    QString getName();
    QString getUniqueValue();
    bool GetShowed();
    void SetFillAll(bool isFillAll, bool bEmitSig = false);
    bool GetFillAll();
    void SetShowItemNum(bool isShow);
    void SetItemNum(int ItemNum);
    int GetItemNum();
    void SetHideWidget(bool bHide);
    bool GetGroupExpand();
    void ExpandBtnClicked();
    void ShowBtnClicked();
    void SetSelected(bool isSelected);
    void FillAllBtnClicked();
    void SetShowed(bool isShowAll, bool bEmitSig = true);//bEmitSig,是否发送点击信号
    void SetSelectBtnTipString(const QString& tips, const QString& tipsEx); //设置右侧下拉箭头tips
    void SetShowHideBtnTipString(const QString& tips, const QString& tipsEx);   //设置显示隐藏按钮tips
public slots:
    void on_actionExpandChanged();
    void on_actionShowAllChanged();
signals:
    void sigShowAllGroupItem(QString, bool);//显示/隐藏
    void sigIsExpandGroup(QString, bool);//展开/收束
    void sigIsFillAllGroup(QString, bool);//填充

protected:
    void enterEvent(QEvent*);
    void leaveEvent(QEvent*);
private:
    Ui::QMTTreeGroupItem* ui;

    QString _name;
    int _ItemNum;
    bool _isShowAll;
    bool _isExpand;
    bool _isSelected;
    bool _isFillAll;
    bool _isShow;
    QString _uniqueValue;
};



