<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QCustLabelWithButton</class>
 <widget class="QWidget" name="QCustLabelWithButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>258</width>
    <height>55</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QCustLabelWithButton</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtLabel" name="mtLabel">
     <property name="text">
      <string>MtTextLabel</string>
     </property>
     <property name="margin">
      <number>8</number>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtLabel::myLabel1</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="MtToolButton" name="mtToolButton">
     <property name="minimumSize">
      <size>
       <width>30</width>
       <height>20</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>30</width>
       <height>20</height>
      </size>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="iconSize">
      <size>
       <width>20</width>
       <height>20</height>
      </size>
     </property>
     <property name="pixmapFilename">
      <string notr="true">:/ACPlanDesign/images/ViewComponent/VCptOptimizedConstraintList/toRight.png</string>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtToolButton::toolbutton1_2</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../MTCompenent/ACPlanDesign/Resources/ACPlanDesign.qrc"/>
 </resources>
 <connections/>
</ui>
