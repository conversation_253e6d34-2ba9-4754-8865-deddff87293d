﻿#pragma once

#include "MtTemplateDialog.h"
#include "ui_ModelImportSuccessDlg.h"

class ModelImportSuccessDlg : public MtTemplateDialog
{
    Q_OBJECT

public:
    ModelImportSuccessDlg(QWidget* parent = nullptr);
    ~ModelImportSuccessDlg();

    void setIconPath(const QString& pixmapPath);

public slots:

protected:
    virtual void onBtnCloseClicked() override;
    virtual void onBtnRight2Clicked() override;
    virtual void onBtnRight1Clicked() override;

private:

    Ui::ModelImportSuccessDlg* ui = nullptr;
};
