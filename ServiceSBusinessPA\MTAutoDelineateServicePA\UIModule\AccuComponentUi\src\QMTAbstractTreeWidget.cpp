﻿#include "AccuComponentUi\Header\QMTAbstractTreeWidget.h"
#include "CMtCoreWidgetUtil.h"
#include <QScrollBar>
#include <qDebug>
#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif
QMTAbstractTreeWidget::QMTAbstractTreeWidget(QWidget* parent)
    : QTreeWidget(parent)
{
    this->setColumnCount(1);
    this->setAlternatingRowColors(true);
    this->viewport()->setFocusPolicy(Qt::NoFocus);
    this->setAttribute(Qt::WA_TranslucentBackground, true);
    QString styleStr;
    //treewidget样式
    styleStr = "QTreeWidget::item:selected { background:rgba(@colorB1, 0.26); border: 1px solid rgba(@colorB1, 1);border-radius: 1px;}";
    styleStr += "QTreeWidget::item:selected:hover {  background:rgba(@colorB1, 0.26);border: 1px solid rgba(@colorB1, 1);border-radius: 1px;}";
    styleStr += "QTreeWidget::item:hover { border: 1px solid rgba(@colorB1, 1);border-radius: 1px;}";
    styleStr += "QTreeWidget::item { border-top:0px solid rgba(@colorA0,1);border-left:1px solid rgba(@colorA0,1);border-right:1px solid rgba(@colorA0,1);border-bottom:1px solid rgba(@colorA0,1);border-radius: 1px;}";
    styleStr += "QTreeWidget {background:transparent; alternate-background-color:transparent;border:none;}";
    CMtCoreWidgetUtil::formatStyleSheet(styleStr);
    this->setStyleSheet(styleStr);
    QScrollBar* verticalScrollBar = this->verticalScrollBar();
    QScrollBar* horizontalScrollBar = this->horizontalScrollBar();

    if (verticalScrollBar)
    {
        verticalScrollBar->setProperty("mtType", "scrollbar1");
        verticalScrollBar->setStyleSheet(verticalScrollBar->styleSheet() + "padding-left:2px;width:8px;");
    }

    if (horizontalScrollBar)
    {
        horizontalScrollBar->setProperty("mtType", "scrollbar1");
    }

    this->setHeaderHidden(true);
    this->setContextMenuPolicy(Qt::CustomContextMenu);//右键 不可少否则右键无反应
    this->setIndentation(0);//设置左边width,设置成0，不显示左侧按键
    _selectMenu = new QMTAbstractMenu(this);
    connect(_selectMenu, SIGNAL(sigTriggered(int)), this, SLOT(slotSelectMenuTriggered(int)));
    ConnectTreeWidgetSignals();
}

QMTAbstractTreeWidget::~QMTAbstractTreeWidget()
{
    ClearAllItems();
}

void QMTAbstractTreeWidget::SubItemSelectButtonClickedCallBack(QString parentValue, QString uniqueValue, QMTAbstractMenu* menu)
{
#if 0
    static bool isFirst = true;

    if (isFirst)
    {
        isFirst = false;
        QIcon originIcon(":/AccuUIComponentImage/images/icon_originSketch.png");
        QIcon rigidIcon(":/AccuUIComponentImage/images/icon_regidRegister.png");
        QIcon deformableIcon(":/AccuUIComponentImage/images/icon_deformable.png");
        QIcon autoSketchIcon(":/AccuUIComponentImage/images/icon_AI_selected.png");
        QIcon manualIcon(":/AccuUIComponentImage/images/icon_NoAI_selected.png");
        QString iconPath = originIcon.name();
        _selectMenu->AddAction(originIcon, tr("原始勾画"));
        _selectMenu->AddAction(rigidIcon, tr("刚体勾画"));
        _selectMenu->AddAction(deformableIcon, tr("形变勾画"));
        _selectMenu->AddAction(autoSketchIcon, tr("自动勾画"));
        _selectMenu->AddAction(manualIcon, tr("手动勾画"));
    }

#endif
}

void QMTAbstractTreeWidget::AddGroupItem(QString text, QString uniqueValue, bool havePacket /* = false*/)
{
    if (uniqueValue.size() == 0)
        uniqueValue = text;

    if (!CheckGroupItemUnique(uniqueValue, text))
        return;

    QTreeWidgetItem* widgetItemGroup = new QTreeWidgetItem(this);

    if (nullptr == widgetItemGroup)
    {
        qWarning() << "nullptr == widgetItemGroup";
        return;
    }

    QMTTreeGroupItem* groupItem = new QMTTreeGroupItem(uniqueValue, this);
    groupItem->setFixedHeight(_treeWidgetProperty.groupItemHeight);
    groupItem->setName(text);
    this->setItemWidget(widgetItemGroup, 0, groupItem);

    if (nullptr == groupItem)
    {
        qWarning() << "nullptr == groupItem";
        return;
    }

    ConnectTreeGroupSignals(groupItem);
    //widgetItemGroup->setSizeHint(0, QSize(0, 0));
    widgetItemGroup->setExpanded(true);//默认展开

    //groupItem->SetSelected(true);
    if (havePacket)
    {
        QTreeWidgetItem* widgetItemGroup2 = new QTreeWidgetItem(widgetItemGroup);
        QTreeWidgetItem* widgetItemGroup3 = new QTreeWidgetItem(widgetItemGroup);
        QTreeWidgetItem* widgetItemGroup4 = new QTreeWidgetItem(widgetItemGroup);
        QMTTreeGroupPacketItem* groupPackItem = new QMTTreeGroupPacketItem(uniqueValue, this);
        QMTTreeGroupPacketItem* groupPackItem2 = new QMTTreeGroupPacketItem(uniqueValue, this);
        QMTTreeGroupPacketItem* groupPackItem3 = new QMTTreeGroupPacketItem(uniqueValue, this);
        int showwide = groupPackItem->width();
        int showwide2 = groupItem->width();
        //groupPackItem->setFixedSize(showwide2, 25);
        groupPackItem->setFixedHeight(25);
        groupPackItem->setName(tr("靶区"));
        groupPackItem->setGroupType(EM_ROICellFormat::IsPTV);
        groupPackItem2->setFixedHeight(25);
        groupPackItem2->setName(tr("非靶区"));
        groupPackItem2->setGroupType(EM_ROICellFormat::IsNotPTV);
        groupPackItem3->setFixedHeight(25);
        groupPackItem3->setName(tr("晶格靶区"));
        groupPackItem3->setGroupType(EM_ROICellFormat::IsSFRTPTV);
        groupPackItem3->SetBHideWhenItemNumIsEzro(true);
        this->setItemWidget(widgetItemGroup2, 0, groupPackItem);
        this->setItemWidget(widgetItemGroup3, 0, groupPackItem2);
        this->setItemWidget(widgetItemGroup4, 0, groupPackItem3);
        ConnectTreeGroupPacketSignals(groupPackItem);
        ConnectTreeGroupPacketSignals(groupPackItem2);
        ConnectTreeGroupPacketSignals(groupPackItem3);
        widgetItemGroup2->setExpanded(true);
        widgetItemGroup3->setExpanded(true);
        widgetItemGroup4->setExpanded(true);
        widgetItemGroup4->setHidden(true);
        //         widgetItemGroup->addChild(widgetItemGroup2);
        //         widgetItemGroup->addChild(widgetItemGroup3);
    }
}

void QMTAbstractTreeWidget::InsertGroupItem(int index, QString& name, QString uniqueValue)
{
    if (uniqueValue.size() == 0)
        uniqueValue = name;

    if (!CheckGroupItemUnique(uniqueValue, name))
        return;

    QTreeWidgetItem* widgetItemGroup = new QTreeWidgetItem();
    QMTTreeGroupItem* groupItem = new QMTTreeGroupItem(uniqueValue, this);
    groupItem->setFixedHeight(_treeWidgetProperty.groupItemHeight);
    groupItem->setName(name);
    this->insertTopLevelItem(index, widgetItemGroup);
    this->setItemWidget(widgetItemGroup, 0, groupItem);//需要放到insert后面
    ConnectTreeGroupSignals(groupItem);
    //widgetItemGroup->setSizeHint(0, QSize(0, 0));
    widgetItemGroup->setExpanded(true);//默认展开
    //groupItem->SetSelected(true);
}

void QMTAbstractTreeWidget::AddSubItem(const TreeSubItemAddUIInfo& subItemUIInfo, bool havePacket /*=false*/)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
        AddGroupItem(subItemUIInfo._groupKey, subItemUIInfo._groupKey);
    }

    // else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = nullptr;
        QString groupKey = subItemUIInfo._groupKey;

        if (groupKey.size() == 0)
        {
            groupWidgetItem = this->topLevelItem(0);
        }
        else
        {
            groupWidgetItem = GetGroupWidgetItem(groupKey);
        }

        if (groupWidgetItem == nullptr)
        {
            qWarning() << "groupWidgetItem == nullptr";
            return;
        }

        QTreeWidgetItem* groupWidgetItem2 = nullptr;
        QTreeWidgetItem* groupWidgetItem3 = nullptr;
        QTreeWidgetItem* groupWidgetItem4 = nullptr;

        /**/
        if (havePacket)
        {
            groupWidgetItem2 = groupWidgetItem->child(0);
            groupWidgetItem3 = groupWidgetItem->child(1);
            groupWidgetItem4 = groupWidgetItem->child(2);
        }

        /**/
        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setToolTip(subItemUIInfo._itemTips);
        QTreeWidgetItem* widgetItem = nullptr;

        if (havePacket)
            widgetItem = new QTreeWidgetItem(groupWidgetItem->child(subItemUIInfo._packetKey));
        else
            widgetItem = new QTreeWidgetItem(groupWidgetItem);

        if (nullptr == widgetItem)
        {
            qWarning() << "nullptr == widgetItem";
            return;
        }

        this->setItemWidget(widgetItem, 0, subItem);
        //groupWidgetItem2->addChild(widgetItem);
        subItem->setMapString(subItemUIInfo);

        if (_validator)
        {
            subItem->SetItemValidator(_validator);
        }

        ConnectTreeSubItemSignals(subItem);
        //widgetItem->setSizeHint(0, QSize(0, 0));
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

        if (nullptr == groupItem)
        {
            qWarning() << "nullptr == groupItem";
            return;
        }

        if (havePacket)
        {
            groupItem->SetItemNum(groupWidgetItem2->childCount() + groupWidgetItem3->childCount() + groupWidgetItem4->childCount());
            QWidget* widget = this->itemWidget(groupWidgetItem2, 0);
            QMTTreeGroupPacketItem* groupItem2 = qobject_cast<QMTTreeGroupPacketItem*>(widget);

            if (groupItem2)
            {
                groupItem2->SetItemNum(groupWidgetItem2->childCount());

                if (groupItem2->GetBHideWhenItemNumIsEzro() == true)
                {
                    if (groupWidgetItem2->childCount() != 0)
                    {
                        groupWidgetItem2->setHidden(false);
                    }
                    else
                    {
                        groupWidgetItem2->setHidden(true);
                    }
                }

                widget = this->itemWidget(groupWidgetItem3, 0);
                groupItem2 = qobject_cast<QMTTreeGroupPacketItem*>(widget);
                groupItem2->SetItemNum(groupWidgetItem3->childCount());

                if (groupItem2->GetBHideWhenItemNumIsEzro() == true)
                {
                    if (groupWidgetItem3->childCount() != 0)
                    {
                        groupWidgetItem3->setHidden(false);
                    }
                    else
                    {
                        groupWidgetItem3->setHidden(true);
                    }
                }

                //晶格靶区
                widget = this->itemWidget(groupWidgetItem4, 0);
                groupItem2 = qobject_cast<QMTTreeGroupPacketItem*>(widget);
                groupItem2->SetItemNum(groupWidgetItem4->childCount());

                if (groupItem2->GetBHideWhenItemNumIsEzro() == true)
                {
                    if (groupWidgetItem4->childCount() != 0)
                    {
                        groupWidgetItem4->setHidden(false);
                    }
                    else
                    {
                        groupWidgetItem4->setHidden(true);
                    }
                }
            }
        }
        else
        {
            groupItem->SetItemNum(groupWidgetItem->childCount());
        }

        // 更新poi的图标类型
        int oarTypeIndex = 0;
        QString type = subItemUIInfo._userDataMap.value(oarTypeIndex);
        subItem->SetPoiItemIconType(type);
    }
}
void QMTAbstractTreeWidget::AddSubItemList(QList<TreeSubItemAddUIInfo>& subItemUIInfoList)
{
    for (int i = 0; i < subItemUIInfoList.size(); ++i)
    {
        if (subItemUIInfoList.at(i)._packetKey != -1)
            AddSubItem(subItemUIInfoList.at(i), true);
        else
            AddSubItem(subItemUIInfoList.at(i));
    }
}

void QMTAbstractTreeWidget::InsertSubItem(int index, TreeSubItemAddUIInfo& subItemUIInfo)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QString groupKey = subItemUIInfo._groupKey;
        QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(groupKey);

        if (groupWidgetItem == nullptr)
            return;

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(NULL);//insert不能指定父类，否则insertChild无效
        groupWidgetItem->insertChild(index, widgetItem);
        //widgetItem->insertChild(index, groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(subItemUIInfo);
        this->update();
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
        groupItem->SetItemNum(groupWidgetItem->childCount());
        ConnectTreeSubItemSignals(subItem);
    }
}

void QMTAbstractTreeWidget::UpdateSubItem(TreeSubItemUpdateInfo& subItemUpdateInfo)
{
    QString groupKey = subItemUpdateInfo._uiInfo._groupKey;
    QString subUniqueValue = subItemUpdateInfo._uiInfo._uniqueValue;
    QMTTreeSubItem* subItem = GetSubItemWidget(groupKey, subUniqueValue);

    if (subItem != nullptr)
    {
        subItem->UpdateUIInfo(subItemUpdateInfo);
    }
}

void QMTAbstractTreeWidget::AddRowItem(bool& isCheck, QColor& color, QString& text, const QString& gourpText)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = nullptr;

        if (gourpText.size() == 0)
        {
            groupWidgetItem = this->topLevelItem(0);
        }
        else
        {
            groupWidgetItem = GetGroupWidgetItem(gourpText);
        }

        if (groupWidgetItem == nullptr)
            return;

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(gourpText, text, text, color, "");
        subItem->SetAIiconShow(_subItemProperty._isAIShow);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
#if 1
        groupWidgetItem->addChild(widgetItem);
#else
        int childCount = groupWidgetItem->childCount();

        if (childCount > 0)
            childCount--;

        groupWidgetItem->insertChild(childCount, widgetItem);
#endif

        if (_validator)
        {
            subItem->SetItemValidator(_validator);
        }

        ConnectTreeSubItemSignals(subItem);
        //widgetItem->setSizeHint(0, QSize(0, 0));
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
        groupItem->SetItemNum(groupWidgetItem->childCount());
    }
}

void QMTAbstractTreeWidget::AddRowItem(QColor& color, QString& name, QString& type, QString& text, const QString& gourpText)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = nullptr;

        if (gourpText.size() == 0)
        {
            groupWidgetItem = this->topLevelItem(0);
        }
        else
        {
            groupWidgetItem = GetGroupWidgetItem(gourpText);
        }

        if (groupWidgetItem == nullptr)
            return;

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(gourpText, text, name, color, type);
        //subItem->SetEnableChangeName(_subItemProperty._isEnableChangeName);
        //subItem->SetEnableChangeColor(_subItemProperty._isEnableChangeColor);
        subItem->SetAIiconShow(_subItemProperty._isAIShow);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
        groupWidgetItem->addChild(widgetItem);

        if (_validator)
        {
            subItem->SetItemValidator(_validator);
        }

        ConnectTreeSubItemSignals(subItem);
        //widgetItem->setSizeHint(0, QSize(0, 0));
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
        groupItem->SetItemNum(groupWidgetItem->childCount());
    }
}

void QMTAbstractTreeWidget::AddRowItem(QColor& color, QString& name, QString& type, QString& label, QString& text, const QString& gourpText)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = nullptr;

        if (gourpText.size() == 0)
        {
            groupWidgetItem = this->topLevelItem(0);
        }
        else
        {
            groupWidgetItem = GetGroupWidgetItem(gourpText);
        }

        if (groupWidgetItem == nullptr)
            return;

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(gourpText, text, name, color, type, label);
        //subItem->SetEnableChangeName(_subItemProperty._isEnableChangeName);
        //subItem->SetEnableChangeColor(_subItemProperty._isEnableChangeColor);
        subItem->SetAIiconShow(_subItemProperty._isAIShow);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
        groupWidgetItem->addChild(widgetItem);

        if (_validator)
        {
            subItem->SetItemValidator(_validator);
        }

        ConnectTreeSubItemSignals(subItem);
        //widgetItem->setSizeHint(0, QSize(0, 0));
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
        groupItem->SetItemNum(groupWidgetItem->childCount());
    }
}

void QMTAbstractTreeWidget::InsertRowItem(int& index, bool& isCheck, QColor& color,
                                          QString& text/*item key*/, QString& gourpText/*group key*/)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(gourpText);

        if (groupWidgetItem == nullptr)
            return;

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(NULL);//insert不能指定父类，否则insertChild无效
        groupWidgetItem->insertChild(index, widgetItem);
        //widgetItem->insertChild(index, groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(gourpText, text, text, color, "");
        this->update();
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
        groupItem->SetItemNum(groupWidgetItem->childCount());
        ConnectTreeSubItemSignals(subItem);
    }
}

void QMTAbstractTreeWidget::InsertRowItem(int& index, QColor& color, QString& name, QString& type,
                                          QString& text/*item key*/, QString& gourpText/*group key*/, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(gourpText);

        if (nullptr == groupWidgetItem)
        {
            return;
        }

        if (roiCellFormat != EM_ROICellFormat::NotROI)
        {
            groupWidgetItem = groupWidgetItem->child((int)roiCellFormat);
        }

        if (groupWidgetItem == nullptr)
            return;

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(NULL);//insert不能指定父类，否则insertChild无效
        groupWidgetItem->insertChild(index, widgetItem);
        //widgetItem->insertChild(index, groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(gourpText, text, name, color, type);
        //subItem->SetEnableChangeName(_subItemProperty._isEnableChangeName);
        //subItem->SetEnableChangeColor(_subItemProperty._isEnableChangeColor);
        //subItem->SetAIiconShow(_subItemProperty._isAIShow);
        this->update();
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);

        if (roiCellFormat != EM_ROICellFormat::NotROI)
        {
            QMTTreeGroupPacketItem* groupItem = qobject_cast<QMTTreeGroupPacketItem*>(widget);

            if (groupItem)
                groupItem->SetItemNum(groupWidgetItem->childCount());

            if (groupItem->GetBHideWhenItemNumIsEzro() == true)
            {
                if (groupWidgetItem->childCount() != 0)
                {
                    groupWidgetItem->setHidden(false);
                }
                else
                {
                    groupWidgetItem->setHidden(true);
                }
            }
        }
        else
        {
            QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

            if (groupItem)
                groupItem->SetItemNum(groupWidgetItem->childCount());
        }

        ConnectTreeSubItemSignals(subItem);
    }
}

void QMTAbstractTreeWidget::InsertRowItem(int& index, QColor& color, QString& name, QString& type, QString& label,
                                          QString& text/*item key*/, QString& gourpText/*group key*/, int IsPTV /*=-1*/)
{
    int iTopLevel = this->topLevelItemCount();

    if (iTopLevel <= 0) //没有需要创建一个
    {
    }
    else//已经有了就不创建了
    {
        QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(gourpText);

        if (groupWidgetItem == nullptr)
            return;

        if (IsPTV != -1)
        {
            groupWidgetItem = groupWidgetItem->child(IsPTV);
        }

        QMTTreeSubItem* subItem = new QMTTreeSubItem(NULL, (QWidget*)this);
        subItem->setFixedHeight(_treeWidgetProperty.subItemHeight);
        QTreeWidgetItem* widgetItem = new QTreeWidgetItem(NULL);//insert不能指定父类，否则insertChild无效
        groupWidgetItem->insertChild(index, widgetItem);
        //widgetItem->insertChild(index, groupWidgetItem);
        this->setItemWidget(widgetItem, 0, subItem);
        subItem->SetSubItemProperty(_subItemProperty);
        subItem->setMapString(gourpText, text, name, color, type, label);
        //subItem->SetEnableChangeName(_subItemProperty._isEnableChangeName);
        //subItem->SetEnableChangeColor(_subItemProperty._isEnableChangeColor);
        //subItem->SetAIiconShow(_subItemProperty._isAIShow);
        this->update();
        QWidget* widget = this->itemWidget(groupWidgetItem, 0);

        if (IsPTV != -1)
        {
            QMTTreeGroupPacketItem* groupItem = qobject_cast<QMTTreeGroupPacketItem*>(widget);

            if (groupItem)
                groupItem->SetItemNum(groupWidgetItem->childCount());

            if (groupItem->GetBHideWhenItemNumIsEzro() == true)
            {
                if (groupWidgetItem->childCount() != 0)
                {
                    groupWidgetItem->setHidden(false);
                }
                else
                {
                    groupWidgetItem->setHidden(true);
                }
            }
        }
        else
        {
            QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

            if (groupItem)
                groupItem->SetItemNum(groupWidgetItem->childCount());
        }

        ConnectTreeSubItemSignals(subItem);
    }
}

void QMTAbstractTreeWidget::InsertGroupItemData(QString key, QVariant data)
{
    _groupItemDataMap.insert(key, data);
}

void QMTAbstractTreeWidget::InsertSubItemData(QString groupKey, QString itemKey, QVariant value)
{
    if (_subItemDataMap.contains(groupKey))
    {
        QMap<QString, QMap<QString, QVariant>>::iterator it = _subItemDataMap.begin();

        for (; it != _subItemDataMap.end(); ++it)
        {
            QString key = it.key();

            if (groupKey == key)
            {
                QMap<QString, QVariant> valueMap = it.value();
                valueMap.insert(itemKey, value);
                _subItemDataMap.insert(groupKey, valueMap);
            }
        }
    }
    else
    {
        QMap<QString, QVariant> valueMap;
        valueMap.insert(itemKey, value);
        _subItemDataMap.insert(groupKey, valueMap);
    }
}

QStringList QMTAbstractTreeWidget::GetAllGroupKey()
{
    QStringList groupKeyList;
    int iTopLevel = this->topLevelItemCount();

    for (int igroup = 0; igroup < iTopLevel; igroup++)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(igroup);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));

            if (nullptr == groupItemTemp)
                continue;

            QString groupKey = groupItemTemp->getUniqueValue();
            groupKeyList.append(groupKey);
        }
    }

    return groupKeyList;
}

QTreeWidgetItem* QMTAbstractTreeWidget::GetGroupWidgetItem(const QString& text)
{
    QTreeWidgetItem* groupWidgetItem = nullptr;

    for (int i = 0; i < this->topLevelItemCount(); i++)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(i);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));

            if (nullptr == groupItemTemp)
                continue;

            if (groupItemTemp->getUniqueValue() == text)
            {
                groupWidgetItem = curWidgetGroup;
                break;
            }
        }
    }

    return groupWidgetItem;
}

QTreeWidgetItem* QMTAbstractTreeWidget::GetGroupPackWidgetItem(QTreeWidgetItem* groupWidgetItem, const QString& groupPackKey)
{
    QTreeWidgetItem* groupPackWidgetItem = nullptr;

    for (int i = 0; i < groupWidgetItem->childCount(); i++)
    {
        QTreeWidgetItem* curWidgetGroup = groupWidgetItem->child(i);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupPacketItem* groupItemTemp = (QMTTreeGroupPacketItem*)(this->itemWidget(curWidgetGroup, 0));

            if (nullptr == groupItemTemp)
                continue;

            if (groupItemTemp->getName() == groupPackKey)
            {
                groupPackWidgetItem = curWidgetGroup;
                break;
            }
        }
    }

    return groupPackWidgetItem;
}

QTreeWidgetItem* QMTAbstractTreeWidget::GetItemWidgetItem(QString& groupKey, QString& itemKey, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    QTreeWidgetItem* retSubItemWidget = nullptr;
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);

    if (nullptr == groupItemWidget)
        return retSubItemWidget;

    if (roiCellFormat == EM_ROICellFormat::UnClear)
    {
        QList<int> traverseTreeNode = { EM_ROICellFormat::IsPTV, EM_ROICellFormat::IsNotPTV, EM_ROICellFormat::IsSFRTPTV };

        for (int j = 0; j < traverseTreeNode.size(); j++)
        {
            QTreeWidgetItem* midGroupItemWidget = groupItemWidget->child(traverseTreeNode[j]);

            if (nullptr == midGroupItemWidget)
                continue;

            for (int i = 0; i < midGroupItemWidget->childCount(); i++)
            {
                QTreeWidgetItem* subItemWidget = midGroupItemWidget->child(i);

                if (nullptr == subItemWidget)
                    continue;

                QWidget* widget = this->itemWidget(subItemWidget, 0);
                QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

                if (nullptr == subItem)
                    continue;

                if (subItem->getUniqueValue() == itemKey)
                {
                    retSubItemWidget = subItemWidget;
                    return retSubItemWidget;
                }
            }
        }
    }

    if (roiCellFormat != EM_ROICellFormat::NotROI && roiCellFormat != EM_ROICellFormat::UnClear && groupItemWidget->childCount() > roiCellFormat)
    {
        groupItemWidget = groupItemWidget->child((int)roiCellFormat);
    }

    if (nullptr == groupItemWidget)
        return nullptr;

    for (int i = 0; i < groupItemWidget->childCount(); i++)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

        if (nullptr == subItemWidget)
            continue;

        QWidget* widget = this->itemWidget(subItemWidget, 0);
        QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

        if (nullptr == subItem)
            continue;

        if (subItem->getUniqueValue() == itemKey)
        {
            retSubItemWidget = subItemWidget;
            break;
        }
    }

    return retSubItemWidget;
}

QMTTreeGroupItem* QMTAbstractTreeWidget::GetGroupItemWidget(QString& groupKey)
{
    QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(groupKey);

    if (nullptr == groupWidgetItem)
        return nullptr;

    QWidget* widget = itemWidget(groupWidgetItem, 0);
    QMTTreeGroupItem* groupItemWidget = qobject_cast<QMTTreeGroupItem*>(widget);
    return groupItemWidget;
}

QMTTreeSubItem* QMTAbstractTreeWidget::GetSubItemWidget(QString& groupKey, QString& itemKey, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    QMTTreeSubItem* retSubItemWidget = nullptr;
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);

    if (nullptr == groupItemWidget)
        return nullptr;

    if (roiCellFormat != EM_ROICellFormat::NotROI)
    {
        if (roiCellFormat != EM_ROICellFormat::UnClear)
        {
            if (groupItemWidget->childCount() < roiCellFormat)
                return nullptr;

            QTreeWidgetItem* finalGroupItemWidget = groupItemWidget->child((int)roiCellFormat);

            if (nullptr == finalGroupItemWidget)
                return nullptr;

            for (int i = 0; i < finalGroupItemWidget->childCount(); i++)
            {
                QTreeWidgetItem* subItemWidget = finalGroupItemWidget->child(i);

                if (nullptr == subItemWidget)
                    continue;

                QWidget* widget = this->itemWidget(subItemWidget, 0);
                retSubItemWidget = qobject_cast<QMTTreeSubItem*>(widget);

                if (nullptr == retSubItemWidget)
                    continue;

                if (retSubItemWidget->getUniqueValue() == itemKey)
                {
                    return retSubItemWidget;
                }
            }
        }
        else
        {
            int allChilds = groupItemWidget->childCount();

            if (allChilds == 0)
                return nullptr;

            for (int groupPacket = 0; groupPacket < groupItemWidget->childCount(); groupPacket++)
            {
                QTreeWidgetItem* finalGroupItemWidget = groupItemWidget->child(groupPacket);

                if (finalGroupItemWidget == nullptr)
                {
                    continue;
                }

                for (int i = 0; i < finalGroupItemWidget->childCount(); i++)
                {
                    QTreeWidgetItem* subItemWidget = finalGroupItemWidget->child(i);

                    if (nullptr == subItemWidget)
                        continue;

                    QWidget* widget = this->itemWidget(subItemWidget, 0);
                    retSubItemWidget = qobject_cast<QMTTreeSubItem*>(widget);

                    if (nullptr == retSubItemWidget)
                        continue;

                    if (retSubItemWidget->getUniqueValue() == itemKey)
                    {
                        return retSubItemWidget;
                    }
                }

                QTreeWidgetItem* subItemWidget = groupItemWidget->child(groupPacket);

                if (nullptr == subItemWidget)
                    continue;

                QWidget* widget = this->itemWidget(subItemWidget, 0);
                retSubItemWidget = qobject_cast<QMTTreeSubItem*>(widget);

                if (nullptr == retSubItemWidget)
                    continue;

                if (retSubItemWidget->getUniqueValue() == itemKey)
                {
                    return retSubItemWidget;
                }
            }
        }
    }
    else
    {
        for (int i = 0; i < groupItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

            if (nullptr == subItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subItemWidget, 0);
            retSubItemWidget = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == retSubItemWidget)
                continue;

            if (retSubItemWidget->getUniqueValue() == itemKey)
            {
                return retSubItemWidget;
            }
        }
    }

    return nullptr;
}



bool QMTAbstractTreeWidget::GetSubItemWidgetList(QString& groupKey, QList<QMTTreeSubItem*>& subItemWidgetList)
{
    QMTTreeSubItem* retSubItemWidget = nullptr;
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);

    if (nullptr == groupItemWidget)
        return false;

    for (int i = 0; i < groupItemWidget->childCount(); i++)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

        if (nullptr == subItemWidget)
            continue;

        QWidget* widget = this->itemWidget(subItemWidget, 0);
        //如果是roi分组需要往下继续找
        QMTTreeGroupPacketItem* GroupPacketWidget = qobject_cast<QMTTreeGroupPacketItem*>(widget);

        if (nullptr != GroupPacketWidget)
        {
            for (int j = 0; j < subItemWidget->childCount(); j++)
            {
                QTreeWidgetItem* finalSubItemWidget = subItemWidget->child(j);

                if (nullptr == finalSubItemWidget)
                    continue;

                QWidget* finalWidget = this->itemWidget(finalSubItemWidget, 0);
                retSubItemWidget = qobject_cast<QMTTreeSubItem*>(finalWidget);

                if (nullptr == retSubItemWidget)
                    continue;

                subItemWidgetList.append(retSubItemWidget);
            }
        }

        retSubItemWidget = qobject_cast<QMTTreeSubItem*>(widget);

        if (nullptr == retSubItemWidget)
            continue;

        subItemWidgetList.append(retSubItemWidget);
    }

    return true;
}

bool QMTAbstractTreeWidget::GetSubItemUniqueValueList(QString& groupKey, QStringList& strList)
{
    QMTTreeSubItem* retSubItemWidget = nullptr;
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);

    if (nullptr == groupItemWidget)
        return false;

    for (int i = 0; i < groupItemWidget->childCount(); i++)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

        if (nullptr == subItemWidget)
            continue;

        QWidget* widget = this->itemWidget(subItemWidget, 0);
        //如果是roi分组需要往下继续找
        QMTTreeGroupPacketItem* GroupPacketWidget = qobject_cast<QMTTreeGroupPacketItem*>(widget);

        if (nullptr != GroupPacketWidget)
        {
            for (int j = 0; j < subItemWidget->childCount(); j++)
            {
                QTreeWidgetItem* finalSubItemWidget = subItemWidget->child(j);

                if (nullptr == finalSubItemWidget)
                    continue;

                QWidget* finalWidget = this->itemWidget(finalSubItemWidget, 0);
                retSubItemWidget = qobject_cast<QMTTreeSubItem*>(finalWidget);

                if (nullptr == retSubItemWidget)
                    continue;

                strList.append(retSubItemWidget->getUniqueValue());
            }
        }

        retSubItemWidget = qobject_cast<QMTTreeSubItem*>(widget);

        if (nullptr == retSubItemWidget)
            continue;

        strList.append(retSubItemWidget->getUniqueValue());
    }

    return true;
}

QVariant QMTAbstractTreeWidget::GetGroupItemData(QString key)
{
    return _groupItemDataMap.value(key);
}

bool QMTAbstractTreeWidget::GetSubItemData(QString groupKey, QString subKey, QVariant& outValue)
{
    bool ret = false;

    if (!_subItemDataMap.contains((groupKey)))
        return ret;

    QMap<QString, QVariant> valueMap = _subItemDataMap.value(groupKey);

    if (!valueMap.contains((subKey)))
        return ret;

    outValue = valueMap.value(subKey);
    return true;
}

bool QMTAbstractTreeWidget::GetCurItemKey(QString& groupKey, QString& itemKey)
{
    //indexOfTopLevelItem();
    QTreeWidgetItem* curWidgetItem = this->currentItem();

    if (nullptr == curWidgetItem)
        return false;

    int index = indexOfTopLevelItem(curWidgetItem);

    if (index < 0)//sub
    {
        QWidget* widget = this->itemWidget(curWidgetItem, 0);
        QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

        if (nullptr == subItem)
            return false;

        groupKey = subItem->getParentUniqueValue();
        itemKey = subItem->getUniqueValue();
    }
    else//group
    {
        QWidget* widget = this->itemWidget(curWidgetItem, 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

        if (nullptr == groupItem)
            return false;

        groupKey = groupItem->getUniqueValue();
    }

    return true;
}

bool QMTAbstractTreeWidget::GetItemIsShow(QString groupKey, QString itemKey, bool& isShow)
{
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);
    bool ret = false;

    if (nullptr == groupItemWidget)
        return ret;

    if (itemKey.size() > 0)
    {
        QTreeWidgetItem* retSubItemWidget = nullptr;

        for (int i = 0; i < groupItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

            if (nullptr == subItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == subItem)
                continue;

            if (subItem->getUniqueValue() == itemKey)
            {
                retSubItemWidget = subItemWidget;
                break;
            }
        }

        if (retSubItemWidget)
        {
            QWidget* widget = this->itemWidget(retSubItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (subItem)
            {
                ret = true;
                isShow = subItem->getIsShow();
            }
        }
    }
    else
    {
    }

    return ret;
}


bool  QMTAbstractTreeWidget::GetSubItemColor(QString groupKey, QString itemKey, QColor& color)
{
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);
    bool ret = false;

    if (nullptr == groupItemWidget)
        return ret;

    if (itemKey.size() > 0)
    {
        QTreeWidgetItem* retSubItemWidget = nullptr;

        for (int i = 0; i < groupItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

            if (nullptr == subItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == subItem)
                continue;

            if (subItem->getUniqueValue() == itemKey)
            {
                retSubItemWidget = subItemWidget;
                break;
            }
        }

        if (retSubItemWidget)
        {
            QWidget* widget = this->itemWidget(retSubItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (subItem)
            {
                ret = true;
                color = subItem->getColor();
            }
        }
    }
    else
    {
    }

    return ret;
}

bool QMTAbstractTreeWidget::GetGroupSubItemStrList(QString groupKey, QStringList& subStrList, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);
    bool ret = false;

    if (nullptr == groupItemWidget)
        return ret;

    QTreeWidgetItem* retSubItemWidget = nullptr;

    if (roiCellFormat != EM_ROICellFormat::NotROI)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child((int)roiCellFormat);

        for (int i = 0; i < subItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subMidItemWidget = subItemWidget->child(i);

            if (nullptr == subMidItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subMidItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == subItem)
                continue;

            ret = true;
            subStrList.append(subItem->getUniqueValue());
        }
    }
    else
    {
        for (int i = 0; i < groupItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

            if (nullptr == subItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == subItem)
                continue;

            ret = true;
            subStrList.append(subItem->getUniqueValue());
        }
    }

    return ret;
}

bool QMTAbstractTreeWidget::GetGroupSubItemNameList(QString groupKey, QStringList& subStrList, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);
    bool ret = false;

    if (nullptr == groupItemWidget)
        return ret;

    QTreeWidgetItem* retSubItemWidget = nullptr;

    if (roiCellFormat != EM_ROICellFormat::NotROI)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child((int)roiCellFormat);

        for (int i = 0; i < subItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subMidItemWidget = subItemWidget->child(i);

            if (nullptr == subMidItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subMidItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == subItem)
                continue;

            ret = true;
            subStrList.append(subItem->getName());
        }
    }
    else
    {
        for (int i = 0; i < groupItemWidget->childCount(); i++)
        {
            QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

            if (nullptr == subItemWidget)
                continue;

            QWidget* widget = this->itemWidget(subItemWidget, 0);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

            if (nullptr == subItem)
                continue;

            ret = true;
            subStrList.append(subItem->getName());
        }
    }

    return ret;
}

bool QMTAbstractTreeWidget::GetAllGroupIsHide()
{
    _bAllGroupHide = true;
    int iTopLevel = this->topLevelItemCount();

    for (int igroup = 0; igroup < iTopLevel; igroup++)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(igroup);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));

            if (nullptr == groupItemTemp)
                continue;

            if (groupItemTemp->GetShowed())
            {
                _bAllGroupHide = false;
                break;
            }
        }
    }

    return _bAllGroupHide;
}

const TreeWidgetProperty& QMTAbstractTreeWidget::GetTreeWidgetProperty()
{
    return _treeWidgetProperty;
}

const TreeSubItemProperty& QMTAbstractTreeWidget::GetSubItemProperty()
{
    return _subItemProperty;
}

bool QMTAbstractTreeWidget::GetIsHadGroupItemData()
{
    bool bHadGroup = false;
    QStringList groupKeyList = GetAllGroupKey();

    if (groupKeyList.size() > 0)
    {
        bHadGroup = true;
    }

    return bHadGroup;
}

bool QMTAbstractTreeWidget::GetIsHadSubItemData()
{
    bool bHadSubItem = false;
    QStringList groupKeyList = GetAllGroupKey();

    for (int i = 0; i < groupKeyList.size(); ++i)
    {
        QStringList subItemKeyList;

        if (GetSubItemUniqueValueList(groupKeyList[i], subItemKeyList))
        {
            if (subItemKeyList.size() > 0)
            {
                bHadSubItem = true;
                break;
            }
        }
    }

    return bHadSubItem;
}

void QMTAbstractTreeWidget::deleteWidgetItem(QTreeWidgetItem* node)
{
    if (nullptr == node)
    {
        return;
    }

    while (node->childCount() > 0)
    {
        QTreeWidgetItem* tmpQTreeWidgetItem = node->child(0);
        node->removeChild(tmpQTreeWidgetItem);
        deleteWidgetItem(tmpQTreeWidgetItem);
    }

    if (node)
    {
        delete node;
        node = nullptr;
    }
}

void QMTAbstractTreeWidget::DeleteCurItem()
{
    QTreeWidgetItem* curItem = this->currentItem();

    if (curItem == nullptr)
        return;

    QWidget* widget = this->itemWidget(curItem, 0);

    if (widget->inherits("QMTTreeSubItem"))
    {
        QWidget* ParentWidget = this->itemWidget(curItem->parent(), 0);
        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(ParentWidget);

        if (groupItem != nullptr)
        {
            groupItem->SetItemNum(curItem->parent()->childCount() - 1);
        }

        QMTTreeGroupPacketItem* groupItem2 = qobject_cast<QMTTreeGroupPacketItem*>(ParentWidget);

        if (groupItem2 != nullptr)
        {
            groupItem2->SetItemNum(curItem->parent()->childCount() - 1);

            if (groupItem2->GetBHideWhenItemNumIsEzro() == true)
            {
                if ((curItem->parent()->childCount() - 1) != 0)
                {
                    curItem->parent()->setHidden(false);
                }
                else
                {
                    curItem->parent()->setHidden(true);
                }
            }

            QWidget* ParentWidget2 = this->itemWidget(curItem->parent()->parent(), 0);
            QMTTreeGroupItem* finalGroupItem = qobject_cast<QMTTreeGroupItem*>(ParentWidget2);

            if (finalGroupItem != nullptr)
            {
                finalGroupItem->SetItemNum(curItem->parent()->parent()->childCount() - 1);
            }
        }
    }

    deleteWidgetItem(curItem);
    this->setCurrentItem(nullptr);
}

void QMTAbstractTreeWidget::DeleteSubItem(QString groupKey, QString itemKey, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    for (int i = 2; i >= -1; i--)
    {
        QTreeWidgetItem* curItem = this->GetItemWidgetItem(groupKey, itemKey, (EM_ROICellFormat)i);

        if (curItem == nullptr)
            continue;

        QWidget* widget = this->itemWidget(curItem, 0);

        if (widget->inherits("QMTTreeSubItem"))
        {
            QWidget* ParentWidget = this->itemWidget(curItem->parent(), 0);
            QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(ParentWidget);

            if (groupItem != nullptr)
            {
                groupItem->SetItemNum(curItem->parent()->childCount() - 1);
            }

            QMTTreeGroupPacketItem* groupItem2 = qobject_cast<QMTTreeGroupPacketItem*>(ParentWidget);

            if (groupItem2 != nullptr)
            {
                groupItem2->SetItemNum(curItem->parent()->childCount() - 1);

                if (groupItem2->GetBHideWhenItemNumIsEzro() == true)
                {
                    if ((curItem->parent()->childCount() - 1) != 0)
                    {
                        curItem->parent()->setHidden(false);
                    }
                    else
                    {
                        curItem->parent()->setHidden(true);
                    }
                }

                QWidget* ParentWidget2 = this->itemWidget(curItem->parent()->parent(), 0);
                QMTTreeGroupItem* finalGroupItem = qobject_cast<QMTTreeGroupItem*>(ParentWidget2);

                if (finalGroupItem != nullptr)
                {
                    finalGroupItem->SetItemNum(curItem->parent()->parent()->childCount() - 1);
                }
            }
        }

        this->setCurrentItem(nullptr);
        deleteWidgetItem(curItem);
        return;
    }
}

void QMTAbstractTreeWidget::ClearAllItems()
{
    if (this->topLevelItemCount() == 0)
        return;

    while (this->topLevelItemCount() > 0)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(0);

        if (nullptr == curWidgetGroup)
            continue;

        deleteWidgetItem(curWidgetGroup);
    }
}

void QMTAbstractTreeWidget::DeleteGroupItem(QString key)
{
    for (int i = this->topLevelItemCount() - 1; i >= 0; i--)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(i);
        QWidget* widget = this->itemWidget(curWidgetGroup, 0);

        if (widget == nullptr)
            continue;

        QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

        if (nullptr == groupItem)
            continue;

        if (groupItem->getUniqueValue() == key)
        {
            deleteWidgetItem(curWidgetGroup);
            break;
        }
    }
}

void QMTAbstractTreeWidget::SetTreeWidgetProperty(const TreeWidgetProperty& treeWidgetProperty)
{
    _treeWidgetProperty = treeWidgetProperty;
}

void QMTAbstractTreeWidget::SetSubItemProperty(TreeSubItemProperty& subItemProperty)
{
    _subItemProperty = subItemProperty;

    if (_selectMenu)
        _selectMenu->Clear();

    if (0 == subItemProperty._menuUnselectIconPathList.size() ||
        0 == subItemProperty._menuSelectIconPathList.size() ||
        0 == subItemProperty._menuStrList.size())
        return;

    if (subItemProperty._menuUnselectIconPathList.size() != subItemProperty._menuSelectIconPathList.size() &&
        subItemProperty._menuSelectIconPathList.size() != subItemProperty._menuStrList.size())
        return;

    QStringList& menuUnselectIconPathList = subItemProperty._menuUnselectIconPathList;
    QStringList& menuSelectIconPathList = subItemProperty._menuSelectIconPathList;
    QStringList& menuStrList = subItemProperty._menuStrList;

    for (int i = 0; i < menuSelectIconPathList.size(); ++i)
    {
        QString menuSelectIconPath = menuSelectIconPathList.at(i);
        QString menuStr = menuStrList.at(i);

        if (_selectMenu)
            _selectMenu->AddAction(QIcon(menuSelectIconPath), menuStr);
    }

    _selectMenu->SetIconPathList(true, menuSelectIconPathList);
    _selectMenu->SetIconPathList(false, menuUnselectIconPathList);
}

void QMTAbstractTreeWidget::SetCurrentGroup(QString groupKey)
{
    if (groupKey.size() == 0)
        return;

    QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(groupKey);

    if (groupWidgetItem)
    {
        this->setCurrentItem(groupWidgetItem);
    }
}

void QMTAbstractTreeWidget::SetCurrentItem(QString gourpKey, QString itemKey, EM_ROICellFormat roiCellFormat /*=EM_ROICellFormat::IsNotPTV*/)
{
    if (gourpKey.size() == 0 || itemKey.size() == 0)
        return;

    QTreeWidgetItem* subWidgetItem = GetItemWidgetItem(gourpKey, itemKey);

    if (subWidgetItem == nullptr)
    {
        subWidgetItem = GetItemWidgetItem(gourpKey, itemKey, EM_ROICellFormat::UnClear);
    }

    if (subWidgetItem)
    {
        this->setCurrentItem(subWidgetItem);
        QTreeWidgetItem* packetWidgetItem = subWidgetItem->parent();

        if (packetWidgetItem)
        {
            QWidget* widget = this->itemWidget(packetWidgetItem, 0);

            if (widget != nullptr)
            {
                QMTTreeGroupPacketItem* groupPacketItem = qobject_cast<QMTTreeGroupPacketItem*>(widget);

                if (groupPacketItem)
                {
                    groupPacketItem->setExpandState(true);
                }
            }
        }
    }
}

void QMTAbstractTreeWidget::SetGroupHide(int index, bool hide)
{
    QTreeWidgetItem* curWidgetGroup = this->topLevelItem(index);
    static QSize preSize = QSize();

    if (curWidgetGroup != nullptr)
    {
        bool isExpanded = curWidgetGroup->isExpanded();
        curWidgetGroup->setExpanded(!isExpanded);
        QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));
        groupItemTemp->SetHideWidget(hide);
        groupItemTemp->hide();

        if (hide)
        {
            preSize = curWidgetGroup->sizeHint(0);
            curWidgetGroup->setSizeHint(0, QSize(0, 0));
        }
        else
        {
            curWidgetGroup->setSizeHint(0, preSize);
        }

        curWidgetGroup->setExpanded(isExpanded);
    }
}

void QMTAbstractTreeWidget::SetGroupHide(QString key, bool hide)
{
    static QSize preSize = QSize();

    if (key.size() == 0)
        return;

    QTreeWidgetItem* curWidgetGroup = GetGroupWidgetItem(key);

    if (nullptr == curWidgetGroup)
        return;

    // widgetItemGroup->setHidden(hide);
    bool isExpanded = curWidgetGroup->isExpanded();
    curWidgetGroup->setExpanded(!isExpanded);
    QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));
    groupItemTemp->SetHideWidget(hide);
    groupItemTemp->hide();

    if (hide)
    {
        preSize = curWidgetGroup->sizeHint(0);
        curWidgetGroup->setSizeHint(0, QSize(0, 0));
    }
    else
    {
        curWidgetGroup->setSizeHint(0, preSize);
    }

    curWidgetGroup->setExpanded(isExpanded);
#if 0
    QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(widgetItemGroup, 0));

    if (groupItemTemp)
    {
        groupItemTemp->SetHideWidget(hide);
    }

#endif
}

void QMTAbstractTreeWidget::SetEnableChangeName(bool enable)
{
    _subItemProperty._isEnableChangeName = enable;
}
void QMTAbstractTreeWidget::SetEnableChangeColor(bool enable)
{
    _subItemProperty._isEnableChangeColor = enable;
}

void QMTAbstractTreeWidget::SetAIiconShow(bool isShow)
{
    _subItemProperty._isAIShow = isShow;
}

void QMTAbstractTreeWidget::SetGroupAllItemsHide(QString key, bool hide)
{
    if (key.size() == 0)
        return;

    QTreeWidgetItem* widgetItemGroup = GetGroupWidgetItem(key);

    if (nullptr == widgetItemGroup)
        return;

    //widgetItemGroup->setExpanded(!hide);
    widgetItemGroup->setHidden(hide);
#if 0
    QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(widgetItemGroup, 0));

    if (groupItemTemp)
    {
        groupItemTemp->SetHideWidget(hide);
    }

#endif
}

void QMTAbstractTreeWidget::SetGroupIsShow(QString groupKey, bool isShow, bool bEmitSig/* = true*/)
{
    QTreeWidgetItem* widgetItemGroup = GetGroupWidgetItem(groupKey);

    if (nullptr == widgetItemGroup)
        return;

    QWidget* widget = itemWidget(widgetItemGroup, 0);
    QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

    if (groupItem)
    {
        groupItem->SetShowed(isShow, bEmitSig);
    }
}

void QMTAbstractTreeWidget::SetAllGroupIsShow(bool isShow)
{
    int iTopLevel = this->topLevelItemCount();

    for (int igroup = 0; igroup < iTopLevel; igroup++)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(igroup);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(curWidgetGroup, 0));

            if (OARITEM != nullptr)
            {
                continue;
            }

            QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));

            if (nullptr == groupItemTemp)
                continue;

            groupItemTemp->SetShowed(isShow);
        }
    }

    _bAllGroupHide = isShow;
}

void QMTAbstractTreeWidget::SetSubItemIsShow(QString groupKey, QString itemKey, bool isShow)
{
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);

    if (nullptr == groupItemWidget)
        return;

    if (itemKey.size() == 0)
        return;

    QTreeWidgetItem* retSubItemWidget = nullptr;
    bool bAllHide = true;

    for (int i = 0; i < groupItemWidget->childCount(); i++)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

        if (nullptr == subItemWidget)
            continue;

        QWidget* widget = this->itemWidget(subItemWidget, 0);
        QMTTreeGroupPacketItem* subPacketItem = qobject_cast<QMTTreeGroupPacketItem*>(widget);

        if (subPacketItem != nullptr)
        {
            for (int j = 0; j < subItemWidget->childCount(); j++)
            {
                QTreeWidgetItem* finalItemWidget = subItemWidget->child(j);

                if (nullptr == finalItemWidget)
                    continue;

                QWidget* finalWidget = this->itemWidget(finalItemWidget, 0);
                QMTTreeSubItem* finalSubItem = qobject_cast<QMTTreeSubItem*>(finalWidget);

                if (nullptr == finalSubItem)
                    continue;

                if (finalSubItem->getUniqueValue() == itemKey)
                {
                    retSubItemWidget = subItemWidget;
                    finalSubItem->SetShowState(isShow);
                }

                if (finalSubItem->getIsShow())
                {
                    bAllHide = false;
                }

                if (retSubItemWidget != nullptr && bAllHide == false)
                {
                    break;
                }
            }
        }

        if (retSubItemWidget != nullptr && bAllHide == false)
        {
            break;
        }

        QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

        if (nullptr == subItem)
            continue;

        if (subItem->getUniqueValue() == itemKey)
        {
            retSubItemWidget = subItemWidget;
            subItem->SetShowState(isShow);
        }

        if (subItem->getIsShow())
        {
            bAllHide = false;
        }

        if (retSubItemWidget != nullptr && bAllHide == false)
        {
            break;
        }
    }

    //判断是否该组下的所有Item都隐藏了
    QWidget* widget = itemWidget(groupItemWidget, 0);
    QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
    groupItem->SetShowed(!bAllHide, false);
    //判断是否所有的组都隐藏了
    bool bAllGroupHide = true;
    int iTopLevel = this->topLevelItemCount();

    for (int igroup = 0; igroup < iTopLevel; igroup++)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(igroup);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(curWidgetGroup, 0));

            if (OARITEM != nullptr)
            {
                continue;
            }

            QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));

            if (nullptr == groupItemTemp)
                continue;

            if (groupItemTemp->GetShowed())
            {
                bAllGroupHide = false;
                break;
            }
        }
    }

    _bAllGroupHide = bAllGroupHide;
}

void QMTAbstractTreeWidget::SetSubItemIsShow(QString groupKey, QStringList itemKeyList, bool isShow)
{
    for (int i = 0; i < itemKeyList.size(); ++i)
    {
        QString itemKey = itemKeyList.at(i);
        SetSubItemIsShow(groupKey, itemKey, isShow);
    }
}

void QMTAbstractTreeWidget::SetSubItemValidator(QValidator* validator)
{
    if (_validator)
    {
        delete _validator;
        _validator = nullptr;
    }

    _validator = validator;
}

void QMTAbstractTreeWidget::SetComboBoxVisiable(bool isShow)
{
    _subItemProperty._isComboBoxVisiable = isShow;
}

void QMTAbstractTreeWidget::SetEyeBtnVisiable(bool isShow)
{
    _subItemProperty._isEyeBtnVisiable = isShow;
}

bool QMTAbstractTreeWidget::CheckGroupItemUnique(QString groupKey, QString itemname)
{
    bool ret = true;
    QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(groupKey);

    if (nullptr == groupWidgetItem)
        return ret;

    QWidget* widget = this->itemWidget(groupWidgetItem, 0);
    QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);

    if (nullptr == groupItem)
        return ret;

    if (groupItem->getUniqueValue() == groupKey ||
        groupItem->getUniqueValue() == itemname)
    {
        ret = false;
    }

    return ret;
}

bool QMTAbstractTreeWidget::CheckSubItemUnique(QString groupKey, QString subKey)
{
    bool ret = true;
    QTreeWidgetItem* groupWidgetItem = GetGroupWidgetItem(groupKey);

    if (nullptr == groupWidgetItem)
        return ret;

    if (groupWidgetItem->childCount() > 0)
    {
        for (int i = 0; i < groupWidgetItem->childCount(); i++)
        {
            QTreeWidgetItem* subWidgetItem = groupWidgetItem->child(i);
            QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(itemWidget(subWidgetItem, 0));//subWidgetItem;

            if (subWidgetItem == nullptr || nullptr == subItem)
                continue;

            if (subItem->getUniqueValue() == subKey ||
                subItem->getName() == subKey)
            {
                ret = false;
                break;
            }
        }
    }

    return ret;
}

void QMTAbstractTreeWidget::SetClickSignalReplaceChanged()
{
    connect(this, &QTreeWidget::itemClicked, this, &QMTAbstractTreeWidget::slotTreeItemClicked_New);
}

void QMTAbstractTreeWidget::ConnectTreeWidgetSignals()
{
    connect(this, SIGNAL(currentItemChanged(QTreeWidgetItem*, QTreeWidgetItem*)), this, SLOT(slotTreeItemClicked(QTreeWidgetItem*, QTreeWidgetItem*)));
    connect(this, SIGNAL(itemDoubleClicked(QTreeWidgetItem*, int)), this, SLOT(slotItemDoubleClicked(QTreeWidgetItem*, int)));
}

void QMTAbstractTreeWidget::ConnectTreeGroupSignals(QMTTreeGroupItem* widget)
{
    if (nullptr == widget)
    {
        return;
    }

    connect(widget, SIGNAL(sigShowAllGroupItem(QString, bool)), this, SLOT(slotShowAllGroupItem(QString, bool)));
    connect(widget, SIGNAL(sigIsExpandGroup(QString, bool)), this, SLOT(slotExpandGroup(QString, bool)));
    connect(widget, SIGNAL(sigIsFillAllGroup(QString, bool)), this, SLOT(slotFillAllGroupItem(QString, bool)));
}

void QMTAbstractTreeWidget::DisConnectTreeGroupSignals(QMTTreeGroupItem* widget)
{
    if (nullptr == widget)
    {
        return;
    }

    disconnect(widget, SIGNAL(sigShowAllGroupItem(QString, bool)), this, SLOT(slotShowAllGroupItem(QString, bool)));
    disconnect(widget, SIGNAL(sigIsExpandGroup(QString, bool)), this, SLOT(slotExpandGroup(QString, bool)));
    disconnect(widget, SIGNAL(sigIsFillAllGroup(QString, bool)), this, SLOT(slotFillAllGroupItem(QString, bool)));
}

void QMTAbstractTreeWidget::ConnectTreeGroupPacketSignals(QMTTreeGroupPacketItem* widget)
{
    if (nullptr == widget)
    {
        return;
    }

    connect(widget, SIGNAL(sigIsExpandGroup(QString, QString, bool)), this, SLOT(slotExpandPackGroup(QString, QString, bool)));
}

void QMTAbstractTreeWidget::DisConnectTreeGroupPacketSignals(QMTTreeGroupPacketItem* widget)
{
    if (nullptr == widget)
    {
        return;
    }

    disconnect(widget, SIGNAL(sigIsExpandGroup(QString, QString, bool)), this, SLOT(slotExpandPackGroup(QString, QString, bool)));
}

void QMTAbstractTreeWidget::ConnectTreeSubItemSignals(QMTTreeSubItem* widget)
{
    if (nullptr == widget)
    {
        return;
    }

    connect(widget, SIGNAL(sigIsShowItemList(QString, QStringList, bool)), this, SIGNAL(sigIsShowItemList(QString, QStringList, bool)));
    connect(widget, SIGNAL(sigCurrentNameChange(QString, QString, QString)), this, SIGNAL(sigCurrentNameChange(QString, QString, QString)));
    connect(widget, SIGNAL(sigCurrentColorChange(QString, QString, QColor, QColor)), this, SIGNAL(sigCurrentColorChange(QString, QString, QColor, QColor)));
    //下拉框
    connect(widget, SIGNAL(sigComboBoxTextChange(QString, QString, QString)), this, SLOT(slotComboBoxSelectTextChange(QString, QString, QString)));
    connect(widget, SIGNAL(sigComboBoxIndexChange(QString, QString, int)), this, SLOT(slotComboBoxSelectIndexChange(QString, QString, int)));
    //选择按键
    connect(widget, SIGNAL(sigSelectButtonClicked(QString, QString)), this, SLOT(slotSubItemSelectButtonClicked(QString, QString)));
    connect(widget, SIGNAL(sigSelectButtonClickedButNotShowMenu(QString, QString)), this, SLOT(slotSubItemSelectButtonClickedButNotShowMenu(QString, QString)));
    //衍生ROI菜单点击
    connect(widget, SIGNAL(SigClickedDerivedMenu(QString, QString, EM_DerivedROIMenu)), this, SIGNAL(SigClickedDerivedMenu(QString, QString, EM_DerivedROIMenu)));
}

void QMTAbstractTreeWidget::DisConnectTreeSubItemSignals(QMTTreeSubItem* widget)
{
    if (nullptr == widget)
    {
        return;
    }

    disconnect(widget, SIGNAL(sigIsShowItemList(QString, QStringList, bool)), this, SIGNAL(sigIsShowItemList(QString, QStringList, bool)));
    disconnect(widget, SIGNAL(sigCurrentNameChange(QString, QString, QString)), this, SIGNAL(sigCurrentNameChange(QString, QString, QString)));
    disconnect(widget, SIGNAL(sigCurrentColorChange(QString, QString, QColor, QColor)), this, SIGNAL(sigCurrentColorChange(QString, QString, QColor, QColor)));
    //下拉框
    disconnect(widget, SIGNAL(sigComboBoxTextChange(QString, QString, QString)), this, SLOT(slotComboBoxSelectTextChange(QString, QString, QString)));
    disconnect(widget, SIGNAL(sigComboBoxIndexChange(QString, QString, int)), this, SLOT(slotComboBoxSelectIndexChange(QString, QString, int)));
    //选择按键
    disconnect(widget, SIGNAL(sigSelectButtonClicked(QString, QString)), this, SLOT(slotSubItemSelectButtonClicked(QString, QString)));
    disconnect(widget, SIGNAL(sigSelectButtonClickedButNotShowMenu(QString, QString)), this, SLOT(slotSubItemSelectButtonClickedButNotShowMenu(QString, QString)));
    disconnect(widget, SIGNAL(SigClickedDerivedMenu(QString, QString, EM_DerivedROIMenu)), this, SIGNAL(SigClickedDerivedMenu(QString, QString, EM_DerivedROIMenu)));
}

void QMTAbstractTreeWidget::slotTreeItemClicked(QTreeWidgetItem* curItem, QTreeWidgetItem* previous)
{
    if (curItem == nullptr)
    {
        return;
    }

    QMTTreeGroupItem* groupTemp = nullptr;
    QString curGroupKey;
    QString curSub;
    QString preGroupKey;
    QString preSub;

    //重置前一个状态
    if (previous != nullptr)
    {
        int LastTopLevel = this->indexOfTopLevelItem(previous);

        if (LastTopLevel >= 0)
        {
            groupTemp = (QMTTreeGroupItem*)(this->itemWidget(previous, 0));

            if (groupTemp != nullptr)
            {
                preGroupKey = groupTemp->getUniqueValue();
                groupTemp->SetSelected(false);
            }
        }
        else
        {
            QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(previous, 0));

            if (OARITEM == nullptr)
            {
                QMTTreeSubItem* oARsItem = (QMTTreeSubItem*)(this->itemWidget(previous, 0));

                if (oARsItem != nullptr)
                {
                    preGroupKey = oARsItem->getParentUniqueValue();
                    preSub = oARsItem->getUniqueValue();
                    oARsItem->setSelected(false);
                }
            }
        }
    }

    int TopLevel = this->indexOfTopLevelItem(curItem);

    //改变当前item状态
    if (TopLevel >= 0)
    {
        groupTemp = (QMTTreeGroupItem*)(this->itemWidget(curItem, 0));

        if (groupTemp != nullptr)
        {
            curGroupKey = groupTemp->getUniqueValue();
            groupTemp->SetSelected(true);
        }
    }
    else
    {
        QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(curItem, 0));

        if (OARITEM == nullptr)
        {
            QMTTreeSubItem* oARsItem = (QMTTreeSubItem*)(this->itemWidget(curItem, 0));

            if (oARsItem != nullptr)
            {
                curGroupKey = oARsItem->getParentUniqueValue();
                curSub = oARsItem->getUniqueValue();
                oARsItem->setSelected(true);
            }
        }
    }

    // qDebug() << "bbb";
    _isSetClickSignal = true;
    emit sigTreeItemClicked(curGroupKey, curSub, preGroupKey, preSub);
}

void QMTAbstractTreeWidget::slotTreeItemClicked_New(QTreeWidgetItem* curItem, int column)
{
    if (_isSetClickSignal == true)
    {
        //qDebug() << "slotTreeItemClicked_New return ";
        _isSetClickSignal = false;
        return;
    }
    else
    {
        // qDebug() << "slotTreeItemClicked_New click ";
        slotTreeItemClicked(curItem, nullptr);
        _isSetClickSignal = false;
        return;
    }
}

void QMTAbstractTreeWidget::slotItemDoubleClicked(QTreeWidgetItem* curItem, int column)
{
    if (curItem == nullptr)
    {
        return;
    }

    QMTTreeGroupItem* groupTemp = nullptr;
    int TopLevel = this->indexOfTopLevelItem(curItem);

    if (TopLevel >= 0)
    {
        groupTemp = (QMTTreeGroupItem*)(this->itemWidget(curItem, 0));

        if (groupTemp != nullptr)
        {
            groupTemp->ExpandBtnClicked();
        }
    }
    else
    {
        QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(curItem, 0));

        if (OARITEM != nullptr)
        {
            //OARITEM->changeExpandState();
            return;
        }

        QMTTreeSubItem* oARsItem = (QMTTreeSubItem*)(this->itemWidget(curItem, 0));

        if (oARsItem != nullptr)
        {
            QString curGroupKey;
            QString curSub;
            curGroupKey = oARsItem->getParentUniqueValue();
            curSub = oARsItem->getUniqueValue();
            emit sigTreeItemDoubleClicked(curGroupKey, curSub);
        }
    }
}

void QMTAbstractTreeWidget::slotShowAllGroupItem(QString text, bool state)
{
    QTreeWidgetItem* widgetItemGroup = GetGroupWidgetItem(text);

    if (widgetItemGroup == nullptr)
    {
        return;
    }

    int count = widgetItemGroup->childCount();
    QStringList strList;

    if (count > 0)
    {
        for (int i = 0; i < count; i++)
        {
            QTreeWidgetItem* widgetItemTemp = widgetItemGroup->child(i);

            for (int j = 0; j < widgetItemTemp->childCount(); j++)
            {
                QTreeWidgetItem* widgetFinalItemTemp = widgetItemTemp->child(j);

                if (widgetFinalItemTemp == nullptr)
                {
                    continue;
                }

                //靶区/非靶区
                QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(widgetFinalItemTemp, 0));

                if (OARITEM != nullptr)
                {
                    continue;
                }

                //ROI/POI
                QMTTreeSubItem* OARsItem = (QMTTreeSubItem*)(this->itemWidget(widgetFinalItemTemp, 0));

                if (OARsItem != nullptr)
                {
                    //oARsItem->setChecked(state);
                    //oARsItem->setCheckColor();
                    QString value = OARsItem->getUniqueValue();
                    strList.append(value);
                    OARsItem->SetShowState(state, false);
                }
            }

            QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(widgetItemTemp, 0));

            if (OARITEM != nullptr)
            {
                continue;
            }

            QMTTreeSubItem* oARsItem = (QMTTreeSubItem*)(this->itemWidget(widgetItemTemp, 0));

            if (oARsItem != nullptr)
            {
                //oARsItem->setChecked(state);
                //oARsItem->setCheckColor();
                QString value = oARsItem->getUniqueValue();
                strList.append(value);
                oARsItem->SetShowState(state, false);
            }
        }
    }

    //判断是否所有的组都隐藏了
    bool bAllGroupHide = true;
    int iTopLevel = this->topLevelItemCount();

    for (int igroup = 0; igroup < iTopLevel; igroup++)
    {
        QTreeWidgetItem* curWidgetGroup = this->topLevelItem(igroup);

        if (curWidgetGroup != nullptr)
        {
            QMTTreeGroupItem* groupItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup, 0));
            QMTTreeGroupPacketItem* OARITEM = qobject_cast<QMTTreeGroupPacketItem*>(this->itemWidget(curWidgetGroup, 0));

            if (OARITEM != nullptr)
            {
                //continue;
                for (int j = 0; j < OARITEM->GetItemNum(); j++)
                {
                    QMTTreeGroupItem* groupSubItemTemp = (QMTTreeGroupItem*)(this->itemWidget(curWidgetGroup->child(igroup)->child(j), 0));

                    if (nullptr == groupSubItemTemp)
                        continue;

                    if (groupSubItemTemp->GetShowed())
                    {
                        bAllGroupHide = false;
                        break;
                    }
                }
            }

            if (bAllGroupHide == false)
                break;

            if (nullptr == groupItemTemp)
                continue;

            if (groupItemTemp->GetShowed())
            {
                bAllGroupHide = false;
                break;
            }
        }
    }

    _bAllGroupHide = bAllGroupHide;
    emit sigIsShowItemList(text, strList, state);
}

void QMTAbstractTreeWidget::slotExpandGroup(QString text, bool state)
{
    QTreeWidgetItem* widgetItemGroup = GetGroupWidgetItem(text);

    if (widgetItemGroup != nullptr)
    {
        widgetItemGroup->setExpanded(state);
    }
}

void QMTAbstractTreeWidget::slotExpandPackGroup(QString text, QString Packnum, bool state)
{
    QTreeWidgetItem* widgetItemGroup = GetGroupWidgetItem(text);
    QTreeWidgetItem* widgetItemGroup2 = GetGroupPackWidgetItem(widgetItemGroup, Packnum);

    if (widgetItemGroup != nullptr && widgetItemGroup2 != nullptr)
    {
        widgetItemGroup2->setExpanded(state);
    }
}

void QMTAbstractTreeWidget::slotFillAllGroupItem(QString text, bool state)
{
    QTreeWidgetItem* widgetItemGroup = GetGroupWidgetItem(text);

    if (widgetItemGroup == nullptr)
    {
        return;
    }

    int count = widgetItemGroup->childCount();

    if (count == 0)
    {
        return;
    }

    emit sigIsFillAllGroupItem(text, state);
}

void QMTAbstractTreeWidget::slotComboBoxSelectTextChange(QString parentValue, QString uniqueValue, QString text)
{
    emit sigComboBoxSelectTextChange(parentValue, uniqueValue, text);
}

void QMTAbstractTreeWidget::slotComboBoxSelectIndexChange(QString parentValue, QString uniqueValue, int index)
{
    emit sigSelectIndexChange(parentValue, uniqueValue, index);
}

void QMTAbstractTreeWidget::slotSubItemSelectButtonClicked(QString parentValue, QString uniqueValue)
{
    _menuSelectParanValue = parentValue;
    _menuUniqueValue = uniqueValue;
    QMTTreeSubItem* subItemWidget = GetSubItemWidget(_menuSelectParanValue, _menuUniqueValue);
    _selectMenu->SetAllActionEnable(true);

    if (subItemWidget)
    {
        QList<int> indexList = subItemWidget->GetMenuDisableIndexList();

        for (int i = 0; i < indexList.size(); ++i)
        {
            int index = indexList.at(i);
            _selectMenu->SetActionEnable(index, false);
        }

        SubItemSelectButtonClickedCallBack(parentValue, uniqueValue, _selectMenu);
        QPoint pos = subItemWidget->pos();
        int x = pos.x() + (subItemWidget->width() - 120);
        int y = pos.y() + subItemWidget->height();
        pos.setX(x);
        pos.setY(y);
        _selectMenu->PopMenu(this->mapToGlobal(pos));
        //20220519 为适配ROI列表勾画模式可多选展示，
    }
}

void QMTAbstractTreeWidget::slotSubItemSelectButtonClickedButNotShowMenu(QString parentValue, QString uniqueValue)
{
    _menuSelectParanValue = parentValue;
    _menuUniqueValue = uniqueValue;
    QMTTreeSubItem* subItemWidget = GetSubItemWidget(_menuSelectParanValue, _menuUniqueValue, UnClear);
    _selectMenu->SetAllActionEnable(true);

    if (subItemWidget)
    {
        QList<int> indexList = subItemWidget->GetMenuDisableIndexList();

        for (int i = 0; i < indexList.size(); ++i)
        {
            int index = indexList.at(i);
            _selectMenu->SetActionEnable(index, false);
        }
    }

    SubItemSelectButtonClickedCallBack(parentValue, uniqueValue, _selectMenu);
}

void QMTAbstractTreeWidget::slotSelectMenuTriggered(int index)
{
    QMTTreeSubItem* subItemWidget = GetSubItemWidget(_menuSelectParanValue, _menuUniqueValue);

    if (nullptr == subItemWidget)
    {
        return;
    }

    if (index == subItemWidget->GetMenuSelectIndex())
        return;

    if (subItemWidget)
    {
        subItemWidget->SetMenuSelectIndex(index);
        QString unselectIconPath = _subItemProperty._menuUnselectIconPathList[index];
        QString selectIconPath = _subItemProperty._menuSelectIconPathList[index];
        subItemWidget->SetAILabelPixmap(unselectIconPath, selectIconPath);
    }

    emit sigSelectIndexChange(_menuSelectParanValue, _menuUniqueValue, index);
}

bool QMTAbstractTreeWidget::isHideAllGroupItem(QString groupKey)
{
    QTreeWidgetItem* groupItemWidget = GetGroupWidgetItem(groupKey);

    if (nullptr == groupItemWidget)
        return false;

    QTreeWidgetItem* retSubItemWidget = nullptr;
    bool bAllHide = true;

    for (int i = 0; i < groupItemWidget->childCount(); i++)
    {
        QTreeWidgetItem* subItemWidget = groupItemWidget->child(i);

        if (nullptr == subItemWidget)
            continue;

        QWidget* widget = this->itemWidget(subItemWidget, 0);
        QMTTreeGroupPacketItem* subPacketItem = qobject_cast<QMTTreeGroupPacketItem*>(widget);

        if (subPacketItem != nullptr)
        {
            for (int j = 0; j < subItemWidget->childCount(); j++)
            {
                QTreeWidgetItem* dinalItemWidget = subItemWidget->child(j);

                if (nullptr == dinalItemWidget)
                    continue;

                QWidget* widget = this->itemWidget(dinalItemWidget, 0);
                QMTTreeSubItem* finalSubItem = qobject_cast<QMTTreeSubItem*>(widget);

                if (nullptr == finalSubItem)
                    continue;

                if (finalSubItem->getIsShow())
                {
                    bAllHide = false;
                    break;
                }
            }
        }

        if (bAllHide == false)
        {
            break;
        }

        QMTTreeSubItem* subItem = qobject_cast<QMTTreeSubItem*>(widget);

        if (nullptr == subItem)
            continue;

        if (subItem->getIsShow())
        {
            bAllHide = false;
            break;
        }
    }

    //判断是否该组下的所有Item都隐藏了
    QWidget* widget = itemWidget(groupItemWidget, 0);
    QMTTreeGroupItem* groupItem = qobject_cast<QMTTreeGroupItem*>(widget);
    groupItem->SetShowed(!bAllHide, false);
    return bAllHide;
}