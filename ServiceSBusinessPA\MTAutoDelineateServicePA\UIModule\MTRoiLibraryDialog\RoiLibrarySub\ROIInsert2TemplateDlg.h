﻿#pragma once

#include "MtTemplateDialog.h"
#include "ui_ROIInsert2TemplateDlg.h"

class ROIInsert2TemplateDlg : public MtTemplateDialog
{
    Q_OBJECT

public:
    ROIInsert2TemplateDlg(QWidget* parent = nullptr);
    ~ROIInsert2TemplateDlg();

    void initList(QMap<int/*templateID*/, QString/*templateName*/> templateIdNameMap);
    QList<int/*templateID*/> getSelectTemplateId();

public slots:
    void slotSearchTemplateNameChanged(const QString& text);

protected:
    virtual void onBtnCloseClicked() override;
    virtual void onBtnRight2Clicked() override;
    virtual void onBtnRight1Clicked() override;

private:

    Ui::ROIInsert2TemplateDlg ui;
    QList<int> m_selectedTemplateIdList;
};
