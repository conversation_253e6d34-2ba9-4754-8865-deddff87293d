﻿#include "AccuComponentUi\Header\QMTTableDataCache.h"
#include <QDir>
#include <QFile>
#include <qDebug>
#include "MtLabel.h"
#include "MtMessageBox.h"

QMTTableDataCache::QMTTableDataCache()
{
}


QMTTableDataCache::~QMTTableDataCache()
{
    //如果后续有new的数据，那么这边需要delete
    DeleteCellParamMap(_perRowItemParam._headParam._headCellParamMap);
}

void QMTTableDataCache::SetRowItemType(const QString& rowValue, int type)
{
    _rowItemValueTypeMap.insert(rowValue, type);
}

int QMTTableDataCache::GetRowItemType(const QString& rowValue)
{
    int type = -1;

    if (_rowItemValueTypeMap.contains(rowValue))
    {
        type = _rowItemValueTypeMap[rowValue];
    }

    return type;
}

void QMTTableDataCache::InitColumnComboBoxItems(int index, const QStringList& items)
{
    _comboBoxColumnMap.insert(index, items);
}

QStringList QMTTableDataCache::GetColumnComboBoxItems(int column)
{
    return _comboBoxColumnMap.value(column);
}

void QMTTableDataCache::InsertRowCellWidgetParamMap(const QString& rowValue, const QMap<int, ICellWidgetParam*>& cellWidgetParamMap)
{
    if (_rowCellWidgetParamMapMap.contains(rowValue))
    {
        MtMessageBox::yellowWarning(nullptr, "[QMTTableDataCache::InsertRowCellWidgetParamMap]_rowCellWidgetParamMapMap.contains(rowValue)");
        return;
    }

    _rowCellWidgetParamMapMap.insert(rowValue, cellWidgetParamMap);
}

void QMTTableDataCache::DeleteRowItem(const QString& value)
{
    if (value.isEmpty())
        return;

    _rowValueList.removeOne(value);
    _cachRowValueList.removeOne(value);

    if (_rowCellWidgetParamMapMap.contains(value))
    {
        QMap<int/*column*/, ICellWidgetParam*> cellParamMap = _rowCellWidgetParamMapMap.value(value);
        _rowCellWidgetParamMapMap.remove(value);
        DeleteCellParamMap(cellParamMap);
    }

    //
    _rowItemValueTypeMap.remove(value);
    _rowItemWidgetFlagMap.remove(value);
    _searchResultStrList.removeOne(value);
    _rowItemEnableEditMapMap.remove(value);
    RemoveCheckBoxStateModel(value);
    SetReadStateInModel(value, false);

    if (_curRowItemValue == value)
    {
        _curRowItemValue.clear();
    }
}

void QMTTableDataCache::ClearDataModel()
{
    //清空表头
    DeleteCellParamMap(_perRowItemParam._headParam._headCellParamMap);
    //清空表格
    _refreshRowWidgetIndexList.clear();
    _rowValueList.clear();
    QMap<QString/*rowValue*/, QMap<int/*column*/, ICellWidgetParam*>> rowCellWidgetParamMapMap = _rowCellWidgetParamMapMap;
    _rowCellWidgetParamMapMap.clear();
    _cachRowValueList.clear();
    QMap<QString/*rowValue*/, QMap<int/*column*/, ICellWidgetParam*>>::iterator iter;

    for (iter = rowCellWidgetParamMapMap.begin(); iter != rowCellWidgetParamMapMap.end(); ++iter)
    {
        QMap<int/*column*/, ICellWidgetParam*> cellParamMap = iter.value();
        DeleteCellParamMap(cellParamMap);
    }

    rowCellWidgetParamMapMap.clear();
    _curRowItemValue.clear();
    _rowItemValueTypeMap.clear();
    _rowItemWidgetFlagMap.clear();
    _searchResultStrList.clear();
    _rowItemColumnCheckStateMapMap.clear();
    _rowItemReadStateMap.clear();
    _orderData.Init();
    _curSearchText.clear();
    _searchResultStrList.clear();
    //是否允许编辑
    _rowItemEnableEditMapMap.clear();
}

void QMTTableDataCache::SetCurRowValueList(const QStringList& rowValueList)
{
    _rowValueList = rowValueList;
}

void QMTTableDataCache::HideColumn(int column, bool bHide)
{
    _columnHideStateMap.insert(column, bHide);
}

void QMTTableDataCache::UpdateCellParamText(const QString& rowValue, int column, const QString& newText)
{
    ICellWidgetParam* cellParam = GetCellWidgetParam(rowValue, column);

    if (nullptr == cellParam)
    {
        qWarning() << QString("UpdateCellParamText, GetCellWidgetParam(%1, %2) err!").arg(rowValue).arg(column);
        return;
    }

    cellParam->_text = newText;
}

void QMTTableDataCache::UpdateCellParamState(const QString& rowValue, int column, int state)
{
    ICellWidgetParam* cellParam = GetCellWidgetParam(rowValue, column);

    if (nullptr == cellParam)
    {
        qWarning() << QString("UpdateCellParamState, GetCellWidgetParam(%1, %2) err!").arg(rowValue).arg(column);
        return;
    }

    cellParam->_state = state;
}

void QMTTableDataCache::UpdateCellParamChecked(const QString& rowValue, int column, bool bChecked)
{
    ICellWidgetParam* cellParam = GetCellWidgetParam(rowValue, column);

    if (nullptr == cellParam)
    {
        qWarning() << QString("UpdateCellParamChecked,GetCellWidgetParam(%1, %2) err!").arg(rowValue).arg(column);
        return;
    }

    cellParam->_isChecked = bChecked;
}

ICellWidgetParam* QMTTableDataCache::GetCellWidgetParam(const QString& rowValue, int column)
{
    ICellWidgetParam* cellWidgetParam = nullptr;

    if (false == _rowCellWidgetParamMapMap.contains(rowValue))
    {
        return cellWidgetParam;
    }

    QMap<int/*column*/, ICellWidgetParam*> cellParamMap = _rowCellWidgetParamMapMap.value(rowValue);
    cellWidgetParam = cellParamMap.value(column);
    return cellWidgetParam;
}

bool QMTTableDataCache::IsExistRowItem(const QString& rowValue)
{
    bool bExist = _rowCellWidgetParamMapMap.contains(rowValue);
    return bExist;
}

mtuiData::TableWidgetItemIndex QMTTableDataCache::GetRowItemIndexInfo(const QString& rowValue)
{
    mtuiData::TableWidgetItemIndex rowItemIndex;
    ICellWidgetParam* cellParam = GetCellWidgetParam(rowValue, 0);
    rowItemIndex._uniqueValue = rowValue;
    rowItemIndex._type = GetRowItemType(rowValue);

    if (cellParam)
    {
        rowItemIndex._parentValue = cellParam->_parentValue;
    }

    return rowItemIndex;
}

QStringList QMTTableDataCache::GetAllRowUniqueValueList()
{
    return _rowValueList;
}

QStringList QMTTableDataCache::GetCacheRowValueList()
{
    return _cachRowValueList;
}

void QMTTableDataCache::SetSearchResultStrList(const QString& searchText, const QStringList& rowValueList)
{
    _curSearchText = searchText;
    _searchResultStrList = rowValueList;
}

void QMTTableDataCache::GetSearchResultStrList(QString& searchText, QStringList& rowValueList)
{
    searchText = _curSearchText;
    rowValueList = _searchResultStrList;
}

QList<int> QMTTableDataCache::GetRowItemStateList(int column, QStringList rowValueList)
{
    QList<int> stateList;

    for (int i = 0; i < rowValueList.size(); ++i)
    {
        int state = Qt::Unchecked;
        QString rowValue = rowValueList[i];

        if (_rowItemColumnCheckStateMapMap.contains(rowValue))
        {
            QMap<int/*column*/, int/*state*/> columnWidgetCheckStateMap = _rowItemColumnCheckStateMapMap.value(rowValue);

            if (columnWidgetCheckStateMap.contains(column))
            {
                state = columnWidgetCheckStateMap.value(column);
            }
        }

        stateList.append(state);
    }

    return stateList;
}

void QMTTableDataCache::SetCheckBoxStateInModel(QString uniqueValue, int column, int state)
{
    QMap<int/*column*/, int/*state*/> columnWidgetCheckStateMap = _rowItemColumnCheckStateMapMap.value(uniqueValue);
    columnWidgetCheckStateMap.insert(column, state);
    _rowItemColumnCheckStateMapMap.insert(uniqueValue, columnWidgetCheckStateMap);
}

QStringList QMTTableDataCache::GetRowItemWithCheckBoxStateInModel(int column, int state)
{
    QStringList retRowValueList;
    QMap<QString/*uniqueValue*/, QMap<int/*column*/, int/*state*/>>::iterator iter;

    for (iter = _rowItemColumnCheckStateMapMap.begin(); iter != _rowItemColumnCheckStateMapMap.end(); ++iter)
    {
        QString rowValue = iter.key();
        QMap<int/*column*/, int/*state*/>& columnWidgetCheckStateMap = iter.value();

        if (columnWidgetCheckStateMap.contains(column) && columnWidgetCheckStateMap[column] == state)
        {
            retRowValueList.append(rowValue);
        }
    }

    return retRowValueList;
}

int QMTTableDataCache::GetCheckBoxStateInModel(QString rowValue, int column)
{
    int state = Qt::Unchecked;

    if (_rowItemColumnCheckStateMapMap.contains(rowValue))
    {
        QMap<int/*column*/, int/*state*/> columnWidgetCheckStateMap = _rowItemColumnCheckStateMapMap.value(rowValue);

        if (columnWidgetCheckStateMap.contains(column))
        {
            state = columnWidgetCheckStateMap.value(column);
        }
    }

    return state;
}


bool QMTTableDataCache::IsAllRowChecked(int column, QStringList rowValueList /*= QStringList()*/)
{
    bool bAllChecked = true;
    QStringList allRowValueList;

    if (rowValueList.size() > 0)
    {
        allRowValueList = rowValueList;
    }
    else
    {
        allRowValueList = GetAllRowUniqueValueList();
    }

    if (0 == allRowValueList.size())
    {
        bAllChecked = false;
    }

    for (int i = 0; i < allRowValueList.size(); ++i)
    {
        int state = GetCheckBoxStateInModel(allRowValueList[i], column);

        if (Qt::Checked != state)
        {
            bAllChecked = false;
            break;
        }
    }

    return bAllChecked;
}

bool QMTTableDataCache::GetRowItemCheckStateMap(const QString& rowValue, QMap<int/*column*/, int/*state*/>& outColumnStateMap)
{
    bool bExist = false;

    if (_rowItemColumnCheckStateMapMap.contains(rowValue))
    {
        bExist = true;
        outColumnStateMap = _rowItemColumnCheckStateMapMap.value(rowValue);
    }

    return bExist;
}


bool QMTTableDataCache::GetIsCheckBoxCellWidget(const QString& rowUniqueValue, int column)
{
    bool bIsCheckBox = false;
    ICellWidgetParam* cellParam = GetCellWidgetParam(rowUniqueValue, column);

    if (nullptr == cellParam)
        return bIsCheckBox;

    if (DELEAGATE_CheckBoxLabel == cellParam->_cellWidgetType || DELEAGATE_QMTCheckBox == cellParam->_cellWidgetType)
    {
        bIsCheckBox = true;
    }

    return bIsCheckBox;
}


void QMTTableDataCache::SetCheckBoxColumn(int column)
{
    _checkBoxColumn = column;
}

int QMTTableDataCache::GetCheckBoxColumn()
{
    return _checkBoxColumn;
}

void QMTTableDataCache::ClearAllCheckBoxState(int column)
{
    QMap<QString/*uniqueValue*/, QMap<int/*column*/, int/*state*/>>::iterator iter = _rowItemColumnCheckStateMapMap.begin();

    for (iter = _rowItemColumnCheckStateMapMap.begin(); iter != _rowItemColumnCheckStateMapMap.end(); ++iter)
    {
        QString uniqueValue = iter.key();
        QMap<int/*column*/, int/*state*/> rowItemColumnCheckStateMap = iter.value();
        rowItemColumnCheckStateMap.insert(column, Qt::Unchecked);
        _rowItemColumnCheckStateMapMap[uniqueValue] = rowItemColumnCheckStateMap;
    }
}

void QMTTableDataCache::RemoveCheckBoxStateModel(QString rowValue)
{
    if (false == _rowItemColumnCheckStateMapMap.contains(rowValue))
        return;

    _rowItemColumnCheckStateMapMap.remove(rowValue);
}


void QMTTableDataCache::SetReadStateInModel(QString uniqueValue, bool bRead)
{
    bool preReadState = false;
    bool bExist = _rowItemReadStateMap.contains(uniqueValue);

    if (bExist)
    {
        preReadState = _rowItemReadStateMap.value(uniqueValue);
    }

    _rowItemReadStateMap.insert(uniqueValue, bRead);

    if (true == bRead && (true == bExist && preReadState == bRead))
        return;

    if (_readDirPath.isEmpty())
        return;

    if (true == bRead)
    {
        QString pathTempDir = GetReadTempPath();
        QString pathTempSeriesDir = pathTempDir + uniqueValue;
        QDir destDir1 = QDir(pathTempSeriesDir);

        if (!destDir1.exists())
        {
            if (!destDir1.mkdir(pathTempSeriesDir))
            {
            }
        }
    }
    else
    {
        QString pathTempDir = GetReadTempPath();
        QString seriesPath = pathTempDir + uniqueValue;
        QDir seriesDir(seriesPath);

        // Return if the directory does not exist
        if (!seriesDir.exists())
        {
            // reset seriesDir path if filesNames do not empty
            qWarning("QMTTableDataCache::deleteFolderSeries: directory does not exist");
            return;
        }

        // Remove all of the files in the series directory
        bool result = true;
        QFileInfoList infoList = seriesDir.entryInfoList(QDir::Files);

        for (int i = 0; i < infoList.size(); i++)
        {
            QFileInfo info = infoList.at(i);

            if (info.isFile())
            {
                result = QFile::remove(info.absoluteFilePath());

                if (!result)
                {
                    qWarning("QMTTableDataCache::deleteFolderSeries: couldn't remove file");
                    return;
                }
            }
        }

        // Remove the directory
        result = seriesDir.rmdir(seriesDir.absolutePath());

        if (!result)
        {
            qWarning("QMTTableDataCache::deleteFolderSeries: couldn't remove directory");
            return;
        }
    }
}

void QMTTableDataCache::RemoveAllReadStateInModel()
{
    QDir dir;
    dir.setPath(_readDirPath);
    dir.removeRecursively();
    _rowItemReadStateMap.clear();
}


bool QMTTableDataCache::GetRowItemIsRead(QString uniqueValue)
{
    if (_rowItemReadStateMap.contains(uniqueValue))
    {
        bool bRead = _rowItemReadStateMap.value(uniqueValue);
        return bRead;
    }

    bool bRead = false;
    QString path = GetReadTempPath();

    if (path.isEmpty())
    {
        bRead = true;
    }
    else
    {
        path += uniqueValue;
        QDir destDir1 = QDir(path);

        if (true == destDir1.exists())
        {
            bRead = true;
        }
    }

    _rowItemReadStateMap.insert(uniqueValue, bRead);
    return bRead;
}

void QMTTableDataCache::SetReadDirPath(const QString& path)
{
    _readDirPath = path;
}

QString QMTTableDataCache::GetReadTempPath()
{
    QString path = _readDirPath;
    QDir destDir = QDir(path);

    if (false == destDir.exists())
    {
        destDir.mkpath(path);     //上层目录不存在也没关系，自动一起创建。目录已经存在时会返回true。
    }

    return path;
}

void QMTTableDataCache::SetEditEnable(bool isEditEnable)
{
    _editEnable = isEditEnable;
}

bool QMTTableDataCache::GetEditEnable()
{
    return _editEnable;
}

void QMTTableDataCache::SetCanEditColumnList(QList<int>& columnList)
{
    for (int i = 0; i < columnList.size(); ++i)
    {
        if (_canEditColumnList.indexOf(columnList[i]) < 0)
        {
            _canEditColumnList.append(columnList[i]);
        }
    }
}

QList<int> QMTTableDataCache::GetCanEditColumnList()
{
    return _canEditColumnList;
}

bool QMTTableDataCache::IsCanEnableEditType(int column, int cellType)
{
    bool bEditType = false;

    //目前只有这几个单元格，如果其它单元格也有此类功能，那么子类重写此函数即可
    if (DELEAGATE_QMTLineEdit == cellType ||
        DELEAGATE_QMTAbsComboBox == cellType ||
        DELEAGATE_QMTCheckBox == cellType)
    {
        bEditType = true;
    }

    return bEditType;
}

void QMTTableDataCache::SetRowItemEditEnable(const QString& rowValue, bool bEdit)
{
    for (int i = 0; i < _canEditColumnList.size(); ++i)
    {
        SetCellEditEnable(rowValue, _canEditColumnList[i], bEdit);
    }
}

void QMTTableDataCache::SetColumnEditEnable(int column, bool bEnable)
{
    if (true == bEnable)
    {
        if (_canEditColumnList.indexOf(column) < 0)
        {
            _canEditColumnList << column;
        }
    }
    else
    {
        _canEditColumnList.removeAll(column);
    }
}

void QMTTableDataCache::SetCellEditEnable(const QString& rowValue, int column, bool bEdit)
{
    QMap<int/*column*/, bool/*state*/> columnItemEnableEditMap = _rowItemEnableEditMapMap.value(rowValue);
    columnItemEnableEditMap.insert(column, bEdit);
    _rowItemEnableEditMapMap.insert(rowValue, columnItemEnableEditMap);
}

bool QMTTableDataCache::GetCellEditEnable(const QString& rowValue, int column)
{
    bool bEditEnable = true;

    if (_canEditColumnList.indexOf(column) < 0)
    {
        return false;           //如果不在允许编辑列里，那么都不允许编辑
    }

    QMap<int/*column*/, bool/*state*/> columnItemEnableEditMap = _rowItemEnableEditMapMap.value(rowValue);

    if (columnItemEnableEditMap.contains(column))
    {
        bEditEnable = columnItemEnableEditMap.value(column);
    }

    return bEditEnable;
}

void QMTTableDataCache::DeleteCellParamMap(QMap<int, ICellWidgetParam*>& cellParamMap)
{
    if (0 == cellParamMap.size())
        return;

    QMap<int, ICellWidgetParam*>::iterator iter;

    for (iter = cellParamMap.begin(); iter != cellParamMap.end(); ++iter)
    {
        ICellWidgetParam* cellParam = iter.value();

        if (cellParam)
        {
            delete cellParam;
            cellParam = nullptr;
        }
    }

    cellParamMap.clear();
}

int QMTTableDataCache::GetMtLabelTypeWithFontSize(int fontSize)
{
    QMap<int, int> fontSizeMtTypeMap;
    fontSizeMtTypeMap.insert(11, MtLabel::myLabel4);
    fontSizeMtTypeMap.insert(12, MtLabel::myLabel1);
    fontSizeMtTypeMap.insert(14, MtLabel::myLabel2);
    fontSizeMtTypeMap.insert(16, MtLabel::myLabel3);

    if (true == fontSizeMtTypeMap.contains(fontSize))
    {
        int mtType = fontSizeMtTypeMap.value(fontSize);
        return mtType;
    }

    return MtLabel::myLabel1;
}
