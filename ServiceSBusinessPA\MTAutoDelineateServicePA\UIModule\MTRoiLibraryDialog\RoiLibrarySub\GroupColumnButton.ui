<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GroupColumnButton</class>
 <widget class="QWidget" name="GroupColumnButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>218</width>
    <height>41</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>GroupColumnButton</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#mtLabel_prefix {
color: rgba(213, 65, 65, 1);
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>4</number>
   </property>
   <property name="leftMargin">
    <number>9</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtLabel" name="mtLabel_prefix">
     <property name="text">
      <string>MtTextLabel</string>
     </property>
    </widget>
   </item>
   <item>
    <widget class="MtLabel" name="mtLabel">
     <property name="text">
      <string>分组</string>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtLabel::myLabel1</enum>
     </property>
    </widget>
   </item>
   <item>
    <widget class="MtToolButton" name="mtToolButton">
     <property name="text">
      <string/>
     </property>
     <property name="iconSize">
      <size>
       <width>13</width>
       <height>13</height>
      </size>
     </property>
     <property name="pixmapFilename">
      <string notr="true">:/images/images/icon_setting.png</string>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtToolButton::toolbutton1</enum>
     </property>
    </widget>
   </item>
   <item>
    <spacer name="horizontalSpacer">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>40</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
