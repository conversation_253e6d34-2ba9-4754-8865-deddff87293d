﻿#include "OptUnattendDataNew.h"
#include "DataDefine/InnerStruct.h"


n_mtautodelineationdialog::ST_AddrSimple g_stDefExportAddr;

/// <summary>
/// 设置默认导出地址
/// </summary>
/// <param name="stExportAddr">[IN]默认导出地址</param>
void OptUnattendDataNew::setDefaultExportAddr(const n_mtautodelineationdialog::ST_AddrSimple& stExportAddr)
{
    g_stDefExportAddr = stExportAddr;
}

/// <summary>
/// 获取默认导出地址
/// </summary>
n_mtautodelineationdialog::ST_AddrSimple OptUnattendDataNew::getDefaultExportAddr()
{
    return g_stDefExportAddr;
}

/// <summary>
/// 构造函数
/// </summary>
/// <param name="unattendedConfigMap">[IN]无人值守信息集合(key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息)</param>
/// <param name="allTemplateNameMap">[IN]所有勾画模板id-模板名称集合</param>
/// <param name="allRemoteScpInfoList">[IN]所有远程服务器信息</param>
/// <param name="allLocalServerNameMap">[IN]所有本地服务器名称集合(key-serverType value-serverName)</param>
OptUnattendDataNew::OptUnattendDataNew(
    const QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>& unattendedConfigMap,
    const QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*模板id*/, QString>>& allTemplateNameMap,
    const QList<n_mtautodelineationdialog::ST_AddrSimple> allRemoteScpInfoList,
    const QMap<int, QStringList>& allLocalServerNameMap)
{
    m_unattendedConfigMap = unattendedConfigMap;
    m_allTemplateNameMap = allTemplateNameMap;
    m_allRemoteScpInfoList = allRemoteScpInfoList;
    m_allLocalServerNameMap = allLocalServerNameMap;
    m_usedLocalServerNameUpperSet.clear();

    if (m_allTemplateNameMap.contains(n_mtautodelineationdialog::OptDcmType_CT) == false)
    {
        m_allTemplateNameMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    }

    if (m_allTemplateNameMap.contains(n_mtautodelineationdialog::OptDcmType_MR) == false)
    {
        m_allTemplateNameMap[n_mtautodelineationdialog::OptDcmType_MR].clear();
    }

    //整理已经被使用的本地服务器
    for (QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>::iterator it = m_unattendedConfigMap.begin(); it != m_unattendedConfigMap.end(); it++)
    {
        //serverType.serverName
        addUsedLocalServerNameMap(it.value().stLocalServerRule.curServerType, it.value().stLocalServerRule.curServerName);
    }
}

OptUnattendDataNew::~OptUnattendDataNew()
{
}

/// <summary>
/// 添加已经被使用的本地服务器
/// </summary>
/// <param name="serverType">[IN]服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)</param>
/// <param name="serverName">[IN]服务器名</param>
void OptUnattendDataNew::addUsedLocalServerNameMap(const int serverType, const QString& serverName)
{
    if (serverType <= 0 || serverName.isEmpty() == true)
        return;

    m_usedLocalServerNameUpperSet.insert(QString::number(serverType) + "." + serverName.toUpper());
}

/// <summary>
/// 删除被使用的本地服务器
/// </summary>
/// <param name="serverType">[IN]服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)</param>
/// <param name="serverName">[IN]服务器名</param>
void OptUnattendDataNew::delUsedLocalServerNameMap(const int serverType, const QString& serverName)
{
    m_usedLocalServerNameUpperSet.remove(QString::number(serverType) + "." + serverName.toUpper());
}

/// <summary>
/// 替换最新的本地服务器名集合(根据服务器类型有就换)
/// </summary>
/// <param name="newLocalServerNameMap">[IN]key-服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器) value-serverName</param>
void OptUnattendDataNew::replaceLocalServerNameMap(const QMap<int, QStringList>& newLocalServerNameMap)
{
    for (QMap<int, QStringList>::const_iterator it = newLocalServerNameMap.begin(); it != newLocalServerNameMap.end(); it++)
    {
        m_allLocalServerNameMap[it.key()] = it.value();
    }
}

/// <summary>
/// 获取所有本地服务器名集合(包括空闲和不空闲)
/// </summary>
/// <returns>key-serverType(1:共享文件夹 2:FTP服务器 4:SCP服务器) value-serverName</returns>
QMap<int, QStringList> OptUnattendDataNew::getAllLocalServerMap()
{
    return m_allLocalServerNameMap;
}

/// <summary>
/// 获取空闲的本地服务器名集合
/// </summary>
/// <returns>key-serverType(1:共享文件夹 2:FTP服务器 4:SCP服务器) value-serverName</returns>
QMap<int, QStringList> OptUnattendDataNew::getAvailLocalServerMap()
{
    QMap<int/*serverType*/, QStringList> availLocalServerNameMap;

    for (QMap<int/*serverType*/, QStringList>::iterator it = m_allLocalServerNameMap.begin(); it != m_allLocalServerNameMap.end(); it++)
    {
        int serverType = it.key();
        QStringList serverNameList = it.value();

        for (int i = 0; i < serverNameList.size(); i++)
        {
            QString upperKey = QString::number(serverType) + "." + serverNameList[i].toUpper();

            if (m_usedLocalServerNameUpperSet.contains(upperKey) == false)
            {
                availLocalServerNameMap[serverType].push_back(serverNameList[i]);
            }
        }
    }

    return availLocalServerNameMap;
}

/// <summary>
///  获取空闲的本地服务器名集合
/// </summary>
/// <param name="notRemoveKey">[IN]不用去除某个服务器(serverType.serverName)</param>
/// <returns>key-serverType(1:共享文件夹 2:FTP服务器 4:SCP服务器) value-serverName</returns>
QMap<int, QStringList> OptUnattendDataNew::getAvailLocalServerMap(const QString& notRemoveKey)
{
    QMap<int/*serverType*/, QStringList> availLocalServerNameMap;

    for (QMap<int/*serverType*/, QStringList>::iterator it = m_allLocalServerNameMap.begin(); it != m_allLocalServerNameMap.end(); it++)
    {
        int serverType = it.key();
        QStringList serverNameList = it.value();

        for (int i = 0; i < serverNameList.size(); i++)
        {
            QString upperKey = QString::number(serverType) + "." + serverNameList[i].toUpper();

            if (m_usedLocalServerNameUpperSet.contains(upperKey) == false || upperKey == notRemoveKey.toUpper())
            {
                availLocalServerNameMap[serverType].push_back(serverNameList[i]);
            }
        }
    }

    return availLocalServerNameMap;
}

/// <summary>
/// 获取所有远程服务器导出地址集合
/// </summary>
/// <returns>所有远程服务器导出地址集合</returns>
QList<n_mtautodelineationdialog::ST_AddrSimple> OptUnattendDataNew::getAllRemoteServerList()
{
    return m_allRemoteScpInfoList;
}

/// <summary>
/// 获取无人值守信息集合
/// </summary>
/// <returns>key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息</returns>
QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig> OptUnattendDataNew::getUnattendedConfigMap()
{
    return m_unattendedConfigMap;
}

/// <summary>
/// 获取无人值守信息
/// </summary>
/// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
/// <returns>无人值守信息(其实就一条)</returns>
QList<n_mtautodelineationdialog::ST_UnattendedConfig> OptUnattendDataNew::getUnattendedConfig(const QString& customId)
{
    if (m_unattendedConfigMap.contains(customId) == true)
    {
        return { m_unattendedConfigMap[customId] };
    }

    return QList<n_mtautodelineationdialog::ST_UnattendedConfig>();
}

/// <summary>
/// 新增无人值守信息
/// </summary>
/// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
/// <param name="stUnattendedConfig">无人值守信息</param>
void OptUnattendDataNew::addUnattendedConfig(const QString& customId, const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig)
{
    m_unattendedConfigMap[customId] = stUnattendedConfig;
    //
    //新增被使用的本地服务器名
    QString upperKey = QString::number(stUnattendedConfig.stLocalServerRule.curServerType) + "." + stUnattendedConfig.stLocalServerRule.curServerName.toUpper();
    m_usedLocalServerNameUpperSet.insert(upperKey);
}

/// <summary>
/// 删除无人值守信息
/// </summary>
/// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
/// <param name="stUnattendedConfig">无人值守信息</param>
void OptUnattendDataNew::delUnattendedConfig(const QString& customId)
{
    if (m_unattendedConfigMap.contains(customId) == true)
    {
        n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig = m_unattendedConfigMap[customId];
        m_unattendedConfigMap.remove(customId);
        //剔除被使用的本地服务器名
        QString upperKey = QString::number(stUnattendedConfig.stLocalServerRule.curServerType) + "." + stUnattendedConfig.stLocalServerRule.curServerName.toUpper();
        m_usedLocalServerNameUpperSet.remove(upperKey);
    }
}

/// <summary>
/// 更新无人值守信息
/// </summary>
/// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
/// <param name="stUnattendedConfig">无人值守信息</param>
void OptUnattendDataNew::updateUnattendedConfig(const QString& customId, const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig)
{
    if (m_unattendedConfigMap.contains(customId) == true)
    {
        n_mtautodelineationdialog::ST_UnattendedConfig oldUnattendedConfig = m_unattendedConfigMap[customId];
        //先剔除被使用的本地服务器名
        QString upperKey = QString::number(oldUnattendedConfig.stLocalServerRule.curServerType) + "." + oldUnattendedConfig.stLocalServerRule.curServerName.toUpper();
        m_usedLocalServerNameUpperSet.remove(upperKey);
        //再新增被使用的本地服务器名
        upperKey = QString::number(stUnattendedConfig.stLocalServerRule.curServerType) + "." + stUnattendedConfig.stLocalServerRule.curServerName.toUpper();
        m_usedLocalServerNameUpperSet.insert(upperKey);
        //更新无人值守信息
        m_unattendedConfigMap[customId] = stUnattendedConfig;
    }
}

/// <summary>
/// 获取所有模板名称集合
/// </summary>
/// <returns>key-模态 value(key-templateId value-templateName)</returns>
QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>> OptUnattendDataNew::getAllTempateNameMap()
{
    return m_allTemplateNameMap;
}