﻿#pragma once

#include <QWidget>
#include "ui_SecGroupItemEditWidget.h"
#include "MtTemplateDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class SecGroupItemEditWidget : public MtTemplateDialog
{
    Q_OBJECT

public:
    enum
    {
        Add = 0,
        Edit = 1
    };

    SecGroupItemEditWidget(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList
                           , const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
                           , int wndType, QWidget* parent = nullptr);
    ~SecGroupItemEditWidget();

    void setGroupInfo(const QString& groupName, int organId);
    void getGroupInfo(QString& groupName, int& organId);

protected slots:

protected:
    virtual void onBtnRight1Clicked();

    void initRefRoiList();

private:
    Ui::SecGroupItemEditWidget ui;
    QList<n_mtautodelineationdialog::ST_Organ> m_organInfoList;
    QMap<int, n_mtautodelineationdialog::ST_SketchModel> m_modelInfoMap;
};
