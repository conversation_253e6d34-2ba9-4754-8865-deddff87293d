﻿#include "MtUnitPushButtonGroup2.h"
#include "ui_MtUnitPushButtonGroup2.h"
#include "MtPushButton.h"
#include "CMtCoreDefine.h"
#include <qDebug>


MtUnitPushButtonGroup2Param::MtUnitPushButtonGroup2Param()
{
    _cellWidgetType = DELEAGATE_MtUnitPushButtonGroup;
}

MtUnitPushButtonGroup2Param::~MtUnitPushButtonGroup2Param()
{
}

QWidget* MtUnitPushButtonGroup2Param::CreateUIModule(QWidget* parent /*= NULL*/)
{
    MtUnitPushButtonGroup2* btns = new MtUnitPushButtonGroup2(parent);
    btns->SetupCellWidget(*this);
    return btns;
}

MtUnitPushButtonGroup2::MtUnitPushButtonGroup2(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::MtUnitPushButtonGroup2;
    ui->setupUi(this);
}

MtUnitPushButtonGroup2::~MtUnitPushButtonGroup2()
{
    DeleteButtons();
    MT_DELETE(ui);
}

bool MtUnitPushButtonGroup2::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();
    /*if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->SetButtonText(0, text);
        return true;
    }*/
    return false;
}

void MtUnitPushButtonGroup2::SetEnableEdit(bool bEdit)
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        _buttonList[i]->setEnabled(bEdit);
    }
}

void MtUnitPushButtonGroup2::SetupCellWidget(MtUnitPushButtonGroup2Param& param)
{
    _pushBtnGroupParam = param;
    DeleteButtons();

    for (int i = 0; i < param._btnTextStrList.size(); ++i)
    {
        MtPushButton* button = new MtPushButton(this);
        ui->horizontalLayout->addWidget(button);
        QString text = param._btnTextStrList[i];

        if (_pushBtnGroupParam._btnWidth > 0)
        {
            button->setFixedWidth(_pushBtnGroupParam._btnWidth);
        }
        else
        {
            //根据字符串自适应宽度
            /*QFont font = this->font();
            QFontMetrics fm(font);
            int width = fm.width(text) + 16;
            button->setFixedWidth(width);*/
        }

        if (_pushBtnGroupParam._btnHeight > 0)
        {
            button->setFixedHeight(_pushBtnGroupParam._btnHeight);
        }

        button->setText(text);

        if (_pushBtnGroupParam._btnIndexMtTypeMap.contains(i))
        {
            MtPushButton::MtType mtType = (MtPushButton::MtType)_pushBtnGroupParam._btnIndexMtTypeMap.value(i);
            button->setMtType(mtType);
        }

        if (_pushBtnGroupParam._btnIndexEnabledMap.contains(i))
        {
            bool enabled = _pushBtnGroupParam._btnIndexEnabledMap.value(i);
            button->setEnabled(enabled);
        }

        _buttonList.push_back(button);
        connect(button, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
    }
}

void MtUnitPushButtonGroup2::DeleteButtons()
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        QWidget* widget = _buttonList.at(i);
        delete widget;
    }

    _buttonList.clear();
}

void MtUnitPushButtonGroup2::HideButton(int index, bool bHide)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setHidden(bHide);
    }
    else
    {
    }
}

void MtUnitPushButtonGroup2::SetButtonText(int index, QString& text)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setText(text);
    }
    else
    {
    }
}

void MtUnitPushButtonGroup2::SetButtonEnable(int index, bool enable)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setEnabled(enable);
    }
    else
    {
    }
}


bool MtUnitPushButtonGroup2::GetCellChecked(int index)
{
    bool bChecked = false;

    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        bChecked = _buttonList[index]->isChecked();
    }

    return bChecked;
}


void MtUnitPushButtonGroup2::slotButtonClicked(bool isChecked)
{
    QObject* sender = this->sender();
    int index = -1;

    for (int i = 0; i < _buttonList.size(); ++i)
    {
        MtPushButton* tempButton = _buttonList.at(i);

        if (tempButton == sender)
        {
            index = i;
            break;
        }
    }

    if (index >= 0)
    {
        emit sigClicked(index);
        emit sigButtonClicked(index, isChecked);
    }
}