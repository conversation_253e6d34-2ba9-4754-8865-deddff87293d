﻿#include "MTAutoDelineateManager.h"

#include <QApplication>
#include <QScreen>
#include <QDebug>
#include <QTextCodec>
#include <QJsonDocument>
#include <QCoreApplication>
#include <QtConcurrent/QtConcurrent>

#include "MtMessageBox.h"
#include "MtMessageDialog.h"
#include "MtProgressDialog.h"

//服务头文件
#include "MTServiceAccessMgr.h"
#include "MTAlgorithmService/IMTAlgorithmService.h"
#include "MTRPCTcpClientService/IMTRPCTcpClientService.h"
#include "MTRPCTcpClientService\MTRpcTcpClientServiceTopic.h"

//rpc
#include "RpcTcpClient.h"
#include "MTCommonRpc_ClientConnect.h"
#include "MTDicomRpcData_Common.h"

//内部头文件
#include "CommonUtil.h"
#include "DBDataUtils.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include "MTExportRoiTemplateWidget/MTExportRoiTemplateWidget.h"
#include "MTRoiLibraryDialog/MTRoiLibraryWidget.h"
#include "AutoDelineationCallBack.h"
#include "MTAutoDelineationDialog/MTAutoDelineationDialog.h"
#include "MTLabelLibraryDialog/MTLabelLibraryWidget.h"
#include "LabelInfo2ROI/LabelInfo2ROI.h"

#pragma execution_character_set("utf-8")

#define CSRPC_TOPIC_RESULTSTATUS(topic) (QString("manteia/resultStatus/")+topic)
#define TOPIC_MTACS_TASK_BROADCAST_AIMODEL_IMPORT_RESULT_FUNCNAME "MTACS_BroadcastAIModelImportResult"
#define TOPIC_MTACS_TASK_BROADCAST_AIMODEL_IMPORT_RESULT CSRPC_TOPIC_RESULTSTATUS("MTACS_BroadcastAIModelImportResult")


MTAutoDelineateManager::MTAutoDelineateManager(mtcore::IMTContextBase* context)
{
    m_context = context;
    InitTopicFuncMap();
    connect(this, &MTAutoDelineateManager::SigHandleDelineateResponse, this, &MTAutoDelineateManager::SlotHandleDelineateResponse, Qt::BlockingQueuedConnection);
}

MTAutoDelineateManager::~MTAutoDelineateManager()
{
}

bool MTAutoDelineateManager::RegisterDelineateStatusHandler(const QString& moduleId, std::function<void(const ST_OutputAutoDelineate&)> statusCallback)
{
    QString clientId;
    QString svrIP;
    int svrPort;

    if (!CommonUtil::GetServerIpPortClientId(svrIP, svrPort, clientId))
    {
        return false;
    }

    m_clientModuleCallbackMap[QPair<QString, QString>(clientId, moduleId)] = statusCallback;
    return true;
}

mt_algo::EM_AlgoExecRetType MTAutoDelineateManager::GetDelineateParam(const ST_InputAutoDelineate& stInputParam
                                                                      , ST_SketchBusinessInfo& outBusinessParam
                                                                      , std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec
                                                                      , std::vector< ST_REQ_AutoSketchSuper_Organ>& trainOrganVec
                                                                      , std::vector< ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec, std::string& errMsg)
{
    if (stInputParam.patientID.isEmpty() || stInputParam.seriesUID.isEmpty())
    {
        errMsg = "parameters are empty";
        return mt_algo::EM_AlgoExecRetError;
    }

    //设置配置文件路径
    CommonUtil::SetClientConfigPath(stInputParam.clientConfigPath);

    //获取患者信息
    Patient patient = DBDataUtils::GetOnePatientFromDb(stInputParam.patientID);

    if (patient.getPatientID().empty())
    {
        errMsg = "getting patient info failed";
        return mt_algo::EM_AlgoExecRetError;
    }

    //获取序列信息
    Series series = DBDataUtils::GetOneSeriesBySeriesUid(stInputParam.seriesUID);

    if (series.getSeriesUID().empty())
    {
        errMsg = "getting series info failed";
        return mt_algo::EM_AlgoExecRetError;
    }

    //显示器官选择窗口
    if (QDialog::Rejected == CreateAutoDelineateDlg(patient.getPatientSex().c_str()
                                                    , series
                                                    , stInputParam.parentWidget
                                                    , stInputParam.bShowPostFuncWidget
                                                    , stInputParam.bShowTemplateBtnWidget
                                                    , outBusinessParam
                                                    , sketchOrganVec
                                                    , trainOrganVec
                                                    , emptyOrganVec))
    {
        return mt_algo::EM_AlgoExecRetSuccess;
    }

    return mt_algo::EM_AlgoExecRetType::EM_AlgoExecRetSuccess;
}

void MTAutoDelineateManager::HandleEvent(const mtcore::MTEvent& event)
{
    mtcore::MTEvent tmpEvent = event;       // 防止mtcore里释放了，所以这边重新赋值拷贝
    QString topicStr = tmpEvent.getTopic();

    if (m_topicFuncMap.contains(topicStr))
    {
        m_topicFuncMap[topicStr](tmpEvent);  // 注意，这边是在子线程中处理，如果处理函数有关于ui刷新等问题需要emit信号到主线程处理
    }
}

void MTAutoDelineateManager::RegisterTopics()
{
    IMTRPCTcpClientService* pService = MTServiceAccessMgr::GetMTServicePluginObj<IMTRPCTcpClientService>();

    if (pService != nullptr)
    {
        QString errMsg;
        int  retCode = pService->RegisterMapRpcFuncNameToEventType(TOPIC_MTACS_TASK_BROADCAST_AIMODEL_IMPORT_RESULT_FUNCNAME, TOPIC_MTACS_TASK_BROADCAST_AIMODEL_IMPORT_RESULT, errMsg);

        if (retCode != 0)
        {
            qWarning() << QString("RegisterMapRpcFuncNameToEventType failed.topic:%1,errMsg:%2").arg(TOPIC_MTACS_TASK_BROADCAST_AIMODEL_IMPORT_RESULT).arg(errMsg);
        }

        m_context->RegisterEventHandle(this, GetTopicList());
    }
}

QStringList MTAutoDelineateManager::GetTopicList()
{
    return m_topicFuncMap.keys();
}

void MTAutoDelineateManager::UnRegisterTopic()
{
    m_context->UnRegisterEventHandle(this, GetTopicList());
}

QWidget* MTAutoDelineateManager::GetDelineateTemplateSettingWidget(bool bShowRoiGroupList
                                                                   , bool bShowUnattendUsedTip
                                                                   , int templateNameListWidth
                                                                   , QWidget* parentWdt
                                                                   , const QString& clientConfigPath
                                                                   , QString& errMsg)
{
    //实例化对象
    n_mtautodelineationdialog::MTExportRoiTemplateWidget* ptrWidget = new n_mtautodelineationdialog::MTExportRoiTemplateWidget(parentWdt);
    //设置右侧roi-item宽度
    ptrWidget->SetRoiItemWidth(templateNameListWidth);
    //隐藏选择ROI进行勾画/选择模板进行勾画页签
    ptrWidget->SetShowSelectRadioSelectBtn(bShowRoiGroupList);
    //显示模态选择列表
    ptrWidget->SetShowModalityComboBox(true);
    //设置修改模板涉及到无人值守时的提示
    CommonUtil::SetClientConfigPath(clientConfigPath);
    AutoDelineationDataOpt::setIsShowTipOfModUnattendUsed(bShowUnattendUsedTip);
    ptrWidget->SetIsShowTipOfModUnattendUsed(bShowUnattendUsedTip);
    return ptrWidget;
}

void MTAutoDelineateManager::InitDelineateTemplateSettingWidget(QWidget* templateWidget)
{
    n_mtautodelineationdialog::MTExportRoiTemplateWidget* roiTemplateWidget = qobject_cast<n_mtautodelineationdialog::MTExportRoiTemplateWidget*>(templateWidget);

    if (nullptr == roiTemplateWidget)
    {
        return;
    }

    //回调函数
    n_mtautodelineationdialog::ST_CallBack_AutoSketch autoSketchCallBack = AutoDelineationCallBack::createAutoSketchCallBack();
    //所有分组规则
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> allGroupInfoList = AutoDelineationDataOpt::getAllGroupInfoAndSort();
    //所有模型信息
    QList<n_mtautodelineationdialog::ST_SketchModel> allSketchModelList = AutoDelineationDataOpt::getDefSketchModel(true);
    allSketchModelList.append(AutoDelineationDataOpt::getEmptySketchModel(true));
    allSketchModelList.append(AutoDelineationDataOpt::getALSketchModel(true));
    //所有排序后的模板信息
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> allSketchCollectionMap = AutoDelineationDataOpt::getAllSketchCollectionSort();
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> oldTemplateIdMap;

    for (QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>::iterator it = allSketchCollectionMap.begin();
         it != allSketchCollectionMap.end(); it++)
    {
        QList<n_mtautodelineationdialog::ST_SketchModelCollection> collectionList = it.value();

        for (int i = 0; i < collectionList.size(); i++)
        {
            oldTemplateIdMap[it.key()].push_back(collectionList[i].id);
        }
    }

    m_lastTemplateIdMap = oldTemplateIdMap;
    //初始化界面
    roiTemplateWidget->Init(allGroupInfoList, allSketchModelList, allSketchCollectionMap, autoSketchCallBack);
}

void MTAutoDelineateManager::DealDelineteTemplateWidgetResult(QWidget* templateWidget)
{
    n_mtautodelineationdialog::MTExportRoiTemplateWidget* roiTemplateWidget = qobject_cast<n_mtautodelineationdialog::MTExportRoiTemplateWidget*>(templateWidget);

    if (nullptr == roiTemplateWidget)
    {
        return;
    }

    //如果模板顺序有更新，则刷新
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> newTemplateIdMap = roiTemplateWidget->GetNewTemplateIdSortMap();

    for (QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>>::iterator it = newTemplateIdMap.begin(); it != newTemplateIdMap.end(); it++)
    {
        n_mtautodelineationdialog::EM_OptDcmType optDcmTypeEnum = it.key();
        QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> templateIdMap;
        templateIdMap[optDcmTypeEnum] = it.value();

        if (m_lastTemplateIdMap.contains(optDcmTypeEnum) == false)
        {
            AutoDelineationDataOpt::updateSketchTemplateIdSort(templateIdMap);
            continue;
        }
        else
        {
            if (m_lastTemplateIdMap[optDcmTypeEnum] != templateIdMap[optDcmTypeEnum])
            {
                AutoDelineationDataOpt::updateSketchTemplateIdSort(templateIdMap);
                continue;
            }
        }
    }

    //记忆是否修改模板时，该模板是无人值守模板提示
    AutoDelineationDataOpt::setIsShowTipOfModUnattendUsed(roiTemplateWidget->GetNewIsShowTipOfModUnattendUsed());
}

bool MTAutoDelineateManager::IsDelineateTemplateWidgetEditState(QWidget* templateWidget, bool showErrDlg)
{
    n_mtautodelineationdialog::MTExportRoiTemplateWidget* roiTemplateWidget = qobject_cast<n_mtautodelineationdialog::MTExportRoiTemplateWidget*>(templateWidget);

    if (nullptr == roiTemplateWidget)
    {
        return false;
    }

    return roiTemplateWidget->IsEditState(showErrDlg);
}

QWidget* MTAutoDelineateManager::GetRoiLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* roiSettingWidget = new n_mtautodelineationdialog::MTRoiLibraryWidget(parentWdgt);
    return roiSettingWidget;
}

bool MTAutoDelineateManager::IsInfoChanged(QWidget* roiLibraryWidget)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(roiLibraryWidget);

    if (nullptr == tempWidget)
    {
        return false;
    }

    return tempWidget->IsModified();
}

bool MTAutoDelineateManager::InitRoiLibraryWidget(QWidget* roiLibraryWidget, const QString& organDefaultConfigInfoPath)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(roiLibraryWidget);

    if (nullptr == tempWidget)
    {
        return false;
    }

    QStringList roiTypeList = {  };
    QList<n_mtautodelineationdialog::ST_Organ> stOrganInfoVec;
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoList;
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> allGroupList;
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> modelCollectionList;
    QMap<int/*modelId*/, n_mtautodelineationdialog::ST_SketchModel> modelInfoMap;       //所有模型信息
    CommonUtil::SetClientConfigPath(organDefaultConfigInfoPath);
    //获取标签
    std::vector<DBRoiLibraryConfig>  roiLibraryCfgVec;
    AutoDelineationDataOpt::getAllRoiLabelInfoList(stRoiLabelInfoList, roiLibraryCfgVec);
    //所有组信息
    AutoDelineationDataOpt::getAllGroupInfoAndSort(allGroupList);
    //获取器官信息
    QMap<int, n_mtautodelineationdialog::ST_Organ> outOrganMap;
    AutoDelineationDataOpt::getAllOrganUnique(false, stOrganInfoVec, outOrganMap);
    //获取模型信息
    modelInfoMap = AutoDelineationDataOpt::getALSketchModelMap(false);
    //获取模板信息
    modelCollectionList = AutoDelineationDataOpt::getAllSketchCollectionOriginal();
    //连接信号
    ConnectRoiSettingWidgetSignal(tempWidget);
    //显示窗口
    tempWidget->initRoiLibrarySettingWidget(roiTypeList, stRoiLabelInfoList, allGroupList, stOrganInfoVec, modelInfoMap, modelCollectionList);
    return true;
}

void MTAutoDelineateManager::SetRoiLibraryWidgetDestroying(QWidget* roiLibraryWidget)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(roiLibraryWidget);

    if (nullptr == tempWidget)
    {
        return;
    }

    tempWidget->widgetDestroying();
}

bool MTAutoDelineateManager::SavRoiLibrarySettingData(QWidget* roiLibraryWidget)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(roiLibraryWidget);

    if (nullptr == tempWidget)
    {
        return false;
    }

    QList<n_mtautodelineationdialog::ST_Organ> outStOrganInfoVec;
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> outRoiLabelInfoList;
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> outModelCollectionList;
    tempWidget->getWidgetData(outStOrganInfoVec, outModelCollectionList, outRoiLabelInfoList);
    //保存数据
    {
        MtProgressDialog progressDlg;
        progressDlg.setWindowTitle(tr("正在更新数据..."));
        progressDlg.setLabelText(tr("正在更新数据..."));
        progressDlg.setValue(0);
        progressDlg.setCancelButtonVisible(false);
        progressDlg.setHidden(false);

        int totalCount = outStOrganInfoVec.size() + outModelCollectionList.size() + outRoiLabelInfoList.size();
        QMap<int/*organId*/, QString/*organDefName*/> organIdNameMap;

        //1. roi库
        for (int i = 0; i < outStOrganInfoVec.size(); ++i)
        {
            DBOrganInfoConfig obj;
            QStringList groupStrList;
            const auto& organInfo = outStOrganInfoVec[i];

            if (organInfo.customOrganName.isEmpty() && organInfo.defaultOrganName.isEmpty())
            {
                continue;
            }

            if (organInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Mod || organInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Add)
            {
                QList<int> gIdList = organInfo.organGroupInfoMap.keys();

                for (int id : gIdList)
                {
                    groupStrList << QString::number(id);
                }

                obj.SetBodypart(organInfo.bodypart.toStdString());
                obj.SetCustomColor(organInfo.customColor.toStdString());
                obj.SetCustomOrganName(organInfo.customOrganName.toStdString());
                obj.SetDefaultColor(organInfo.defaultColor.toStdString());
                obj.SetDefaultOrganName(organInfo.defaultOrganName.toStdString());
                obj.SetEnable(organInfo.enable ? 1 : 0);
                obj.SetId(organInfo.id);
                obj.SetIsVisiable(organInfo.isVisiable ? 1 : 0);
                obj.SetModelid(organInfo.modelId);
                obj.SetOrganChineseName(organInfo.organChineseName.toStdString());
                obj.SetOrganEnglishName(organInfo.organEnglishName.toStdString());
                obj.SetOrgangroupinfoIdList(groupStrList.join(";").toStdString());
                obj.SetRoiDesc(organInfo.roiDesc.toStdString());
                obj.SetRoiLabel(organInfo.roiLabel.toStdString());
                obj.SetRoiParam(organInfo.roiParam.toStdString());
                obj.SetRoiType(organInfo.roiType.toStdString());

                if (organInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Mod)
                {
                    DBDataUtils::UpdateOrganInfoConfigById(obj);
                }
                else
                {
                    DBDataUtils::AddOrganInfoConfig(obj);
                    organIdNameMap[organInfo.id] = organInfo.defaultOrganName;
                }
            }
            else if (organInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Del && organInfo.id > -1)
            {
                DBDataUtils::DeleteOrganInfoConfigById(organInfo.id);
            }

            progressDlg.setLabelText(tr("正在更新数据..."));
            progressDlg.setValue((i + 1) * 100 / totalCount);
            QApplication::processEvents();
        }

        //2. 修正模板中加入的器官 id<0 的organId值
        QMap<int/*organIdOld*/, int/*organIdNew*/> oldNewOrganIdMap;
        std::vector<DBOrganInfoConfig> newOrganInfoVec = DBDataUtils::FindAllOrganInfoConfigRecords();

        for (int i = 0; i < newOrganInfoVec.size(); ++i)
        {
            QMap<int, QString>::const_iterator itor = organIdNameMap.constBegin();

            for (; itor != organIdNameMap.constEnd(); ++itor)
            {
                if (newOrganInfoVec[i].GetDefaultOrganName() == itor.value().toStdString())
                {
                    oldNewOrganIdMap[itor.key()] = newOrganInfoVec[i].GetId();
                    break;
                }
            }
        }

        //3. 模板库
        for (int i = 0; i < outModelCollectionList.size(); ++i)
        {
            QMap<int, QSet<int>>::iterator itor;
            QJsonObject jsonObj;
            QJsonArray organInfoArr;
            //当前所有模板信息
            QList<n_mtautodelineationdialog::ST_SketchModelCollection>modelCollectionList = AutoDelineationDataOpt::getAllSketchCollectionOriginal();
            QMap<int/*organId*/, QJsonArray> organId_show_groupId_arrMap;
            QMap<int/*organId*/, QJsonArray> organId_sub_in_main_group_arrMap;

            for (itor = modelCollectionList[i].showGroupIdMap.begin(); itor != modelCollectionList[i].showGroupIdMap.end(); ++itor)
            {
                QJsonArray show_groupId_arr;

                for (int gid : itor.value())
                {
                    show_groupId_arr.append(QString::number(gid));
                }

                organId_show_groupId_arrMap[itor.key()] = show_groupId_arr;
            }

            for (itor = modelCollectionList[i].subInMainGroupIdMap.begin(); itor != modelCollectionList[i].subInMainGroupIdMap.end(); ++itor)
            {
                QJsonArray sub_in_main_group_arr;

                for (int gid : itor.value())
                {
                    sub_in_main_group_arr.append(QString::number(gid));
                }

                organId_sub_in_main_group_arrMap[itor.key()] = sub_in_main_group_arr;
            }

            QList<int> organIDList = organId_show_groupId_arrMap.keys();

            for (int organId : organId_sub_in_main_group_arrMap.keys())
            {
                if (organIDList.contains(organId))
                {
                    continue;
                }

                organIDList.append(organId);
            }

            for (int organId : organIDList)
            {
                QJsonObject organObj;
                organObj["id"] = QString::number(organId);
                organObj["show_groupId_arr"] = organId_show_groupId_arrMap[organId];
                organObj["sub_in_main_group_arr"] = organId_sub_in_main_group_arrMap[organId];
                organInfoArr.append(organObj);
            }

            jsonObj["organ_arr"] = organInfoArr;
            jsonObj["modality"] = outModelCollectionList[i].modality;
            QString modalDataStrOld = QJsonDocument(jsonObj).toJson(QJsonDocument::Compact);
            //
            QJsonObject newJsonObj;
            QJsonArray newOrganInfoArr;
            QMap<int/*organId*/, QJsonArray> newOrganId_show_groupId_arrMap;
            QMap<int/*organId*/, QJsonArray> newOrganId_sub_in_main_group_arrMap;

            for (itor = outModelCollectionList[i].showGroupIdMap.begin(); itor != outModelCollectionList[i].showGroupIdMap.end(); ++itor)
            {
                QJsonArray show_groupId_arr;

                for (int gid : itor.value())
                {
                    show_groupId_arr.append(QString::number(gid));
                }

                //入库后的organId，模板中记入的所包含的器官的id可能是刚创建的，其id是临时的，所以需要在入库后加以修正
                int organNewId = itor.key();

                if (organNewId < 0 && oldNewOrganIdMap.contains(itor.key()))
                {
                    organNewId = oldNewOrganIdMap[organNewId];
                }

                newOrganId_show_groupId_arrMap[organNewId] = show_groupId_arr;
            }

            for (itor = outModelCollectionList[i].subInMainGroupIdMap.begin(); itor != outModelCollectionList[i].subInMainGroupIdMap.end(); ++itor)
            {
                QJsonArray sub_in_main_group_arr;

                for (int gid : itor.value())
                {
                    sub_in_main_group_arr.append(QString::number(gid));
                }

                //入库后的organId，模板中记入的所包含的器官的id可能是刚创建的，其id是临时的，所以需要在入库后加以修正
                int organNewId = itor.key();

                if (organNewId < 0 && oldNewOrganIdMap.contains(itor.key()))
                {
                    organNewId = oldNewOrganIdMap[organNewId];
                }

                newOrganId_sub_in_main_group_arrMap[organNewId] = sub_in_main_group_arr;
            }

            organIDList = newOrganId_show_groupId_arrMap.keys();

            for (int organId : newOrganId_sub_in_main_group_arrMap.keys())
            {
                if (organIDList.contains(organId))
                {
                    continue;
                }

                organIDList.append(organId);
            }

            for (int organId : organIDList)
            {
                QJsonObject organObj;
                organObj["id"] = QString::number(organId);
                organObj["show_groupId_arr"] = newOrganId_show_groupId_arrMap[organId];
                organObj["sub_in_main_group_arr"] = newOrganId_sub_in_main_group_arrMap[organId];
                newOrganInfoArr.append(organObj);
            }

            newJsonObj["organ_arr"] = newOrganInfoArr;
            newJsonObj["modality"] = outModelCollectionList[i].modality;
            QString modalDataStr = QJsonDocument(newJsonObj).toJson(QJsonDocument::Compact);

            if (modalDataStr != modalDataStrOld)
            {
                DBSketchModelCollection collectionItem;
                collectionItem.SetId(outModelCollectionList[i].id);
                collectionItem.SetTitle(outModelCollectionList[i].templateName.toStdString());
                collectionItem.SetRemark(outModelCollectionList[i].remark.toStdString());
                collectionItem.SetIsUnattended(outModelCollectionList[i].isUnattended ? 1 : 0);
                collectionItem.SetModelData(modalDataStr.replace(",\"sub_in_main_group_arr\":[]", "").toStdString());
                DBDataUtils::UpdateModelCollection(collectionItem);
            }

            progressDlg.setLabelText(tr("正在更新数据..."));
            progressDlg.setValue((i + 1 + outStOrganInfoVec.size()) * 100 / totalCount);
            QApplication::processEvents();
        }

        //4. 标签库
        for (int r = 0; r < outRoiLabelInfoList.size(); ++r)
        {
            const auto& roiLabelInfo = outRoiLabelInfoList[r];

            if (roiLabelInfo.optTypeEnum != n_mtautodelineationdialog::OptType_Mod)
            {
                continue;
            }

            DBRoiLibraryConfig cfgItem;
            QString roiAlias = roiLabelInfo.roiAlias;
            QStringList roiAliasList = roiAlias.split(";");

            if (!roiAliasList.contains(roiLabelInfo.roiName))//将ROI名添加到别名
            {
                roiAlias = roiLabelInfo.roiName + ";" + roiLabelInfo.roiAlias;
            }

            cfgItem.SetRoiLabel(roiLabelInfo.manteiaRoiLabel.toStdString());
            cfgItem.SetIsDefaultRoi(roiLabelInfo.isbuiltIn ? 1 : 0);
            cfgItem.SetRoiColor(roiLabelInfo.roiColor.toStdString());
            cfgItem.SetRoiName(roiLabelInfo.roiName.toStdString());
            cfgItem.SetRoiChineseName(roiLabelInfo.roiChName.toStdString());
            cfgItem.SetAliasList(roiAlias.toStdString());
            cfgItem.SetRoiType(roiLabelInfo.roiType.toStdString());

            if (roiLabelInfo.roiCodeMap.contains(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse))
            {
                QJsonObject jsonObject;
                QJsonObject eclipseObj;
                const QMap<QString, QString>& codeInfoMap = roiLabelInfo.roiCodeMap.value(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse);
                eclipseObj.insert("code", codeInfoMap.value("code"));
                eclipseObj.insert("codeMean", codeInfoMap.value("codeMean"));
                eclipseObj.insert("codeScheme", codeInfoMap.value("codeScheme"));
                eclipseObj.insert("codeSchemeVer", codeInfoMap.value("codeSchemeVer"));
                eclipseObj.insert("groupVer", codeInfoMap.value("groupVer"));
                eclipseObj.insert("identifier", codeInfoMap.value("identifier"));
                eclipseObj.insert("label", codeInfoMap.value("label"));
                eclipseObj.insert("mapRes", codeInfoMap.value("mapRes"));
                jsonObject.insert("eclipse", eclipseObj);
                QJsonDocument jsonDocument(jsonObject);
                QString jsonString = jsonDocument.toJson(QJsonDocument::Compact);
                cfgItem.SetStructureCodeInfo(jsonString.toStdString());
            }

            DBDataUtils::UpdateROILibraryConfig(roiLabelInfo.manteiaRoiLabel, cfgItem);

            progressDlg.setLabelText(tr("正在更新数据..."));
            progressDlg.setValue((r + 1 + outModelCollectionList.size() + outStOrganInfoVec.size()) * 100 / totalCount);
            QApplication::processEvents();
        }

        progressDlg.setHidden(true);
    }
    tempWidget->resetInfoChangeStatus();
}

QWidget* MTAutoDelineateManager::GetLabelLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg)
{
    n_mtautodelineationdialog::MTLabelLibraryWidget* labelSettingWidget = new n_mtautodelineationdialog::MTLabelLibraryWidget(-1, -1, parentWdgt);
    labelSettingWidget->setHiddenColumn(QVector<int>({ 6 }));//隐藏编码设置列
    labelSettingWidget->setDisableColumn({ 0 });
    labelSettingWidget->setImportButtonHidden();
    labelSettingWidget->setUseROIFirstButtonHidden();
    return labelSettingWidget;
}

bool MTAutoDelineateManager::IsLabelInfoChanged(QWidget* labelLibraryWidget)
{
    n_mtautodelineationdialog::MTLabelLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTLabelLibraryWidget*>(labelLibraryWidget);

    if (nullptr == tempWidget)
    {
        return false;
    }

    return tempWidget->isInfoChanged();
}

bool MTAutoDelineateManager::InitLabelLibraryWidget(QWidget* labelLibraryWidget)
{
    n_mtautodelineationdialog::MTLabelLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTLabelLibraryWidget*>(labelLibraryWidget);

    if (nullptr == tempWidget)
    {
        return false;
    }

    QStringList roiTypeList = {  };
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoList;
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> outStRoiLabelInfoList;
    std::vector<DBRoiLibraryConfig>  roiLibraryCfgVec = DBDataUtils::FindAllROILibraryRecords();
    //获得标签库信息
    AutoDelineationDataOpt::getAllRoiLabelInfoList(stRoiLabelInfoList, roiLibraryCfgVec);
    //
    tempWidget->initLabelLibrarySettingWidget(roiTypeList, stRoiLabelInfoList);
    return true;
}

bool MTAutoDelineateManager::SavLabelLibrarySettingData(QWidget* labelLibraryWidget)
{
    n_mtautodelineationdialog::MTLabelLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTLabelLibraryWidget*>(labelLibraryWidget);

    if (nullptr == tempWidget)
    {
        return false;
    }

    QString dirPath;
    unsigned syncType = LabelInfo2ROI::Type_None;
    LabelInfo2ROI dlg2(tempWidget);

    if (QDialog::Accepted == dlg2.exec())
    {
        syncType = dlg2.GetSyncType();
    }

    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> outStRoiLabelInfoList;
    tempWidget->getWidgetData(outStRoiLabelInfoList);
    //
    MtProgressDialog progressDlg;
    progressDlg.setWindowTitle(tr("正在更新数据..."));
    progressDlg.setLabelText(tr("正在更新数据..."));
    progressDlg.setValue(0);
    progressDlg.setCancelButtonVisible(false);
    progressDlg.setHidden(false);

    //更新到数据库
    for (int r = 0; r < outStRoiLabelInfoList.size(); ++r)
    {
        const auto& roiLabelInfo = outStRoiLabelInfoList[r];

        //删除
        if (roiLabelInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Del)
        {
            DBDataUtils::DeleteROILibraryConfigByLabelName(roiLabelInfo.manteiaRoiLabel);
            continue;
        }

        DBRoiLibraryConfig cfgItem;
        cfgItem.SetRoiLabel(roiLabelInfo.manteiaRoiLabel.toStdString());
        cfgItem.SetIsDefaultRoi(roiLabelInfo.isbuiltIn ? 1 : 0);
        cfgItem.SetRoiColor(roiLabelInfo.roiColor.toStdString());
        cfgItem.SetRoiName(roiLabelInfo.roiName.toStdString());
        cfgItem.SetRoiChineseName(roiLabelInfo.roiChName.toStdString());
        cfgItem.SetAliasList(roiLabelInfo.roiAlias.toStdString());
        cfgItem.SetRoiType(roiLabelInfo.roiType.toStdString());

        if (roiLabelInfo.roiCodeMap.contains(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse))
        {
            QJsonObject jsonObject;
            QJsonObject eclipseObj;
            const QMap<QString, QString>& codeInfoMap = roiLabelInfo.roiCodeMap.value(n_mtautodelineationdialog::EM_Manufacturer::Manufacturer_Eclipse);
            eclipseObj.insert("code", codeInfoMap.value("code"));
            eclipseObj.insert("codeMean", codeInfoMap.value("codeMean"));
            eclipseObj.insert("codeScheme", codeInfoMap.value("codeScheme"));
            eclipseObj.insert("codeSchemeVer", codeInfoMap.value("codeSchemeVer"));
            eclipseObj.insert("groupVer", codeInfoMap.value("groupVer"));
            eclipseObj.insert("identifier", codeInfoMap.value("identifier"));
            eclipseObj.insert("label", codeInfoMap.value("label"));
            eclipseObj.insert("mapRes", codeInfoMap.value("mapRes"));
            jsonObject.insert("eclipse", eclipseObj);
            QJsonDocument jsonDocument(jsonObject);
            QString jsonString = jsonDocument.toJson(QJsonDocument::Compact);
            cfgItem.SetStructureCodeInfo(jsonString.toStdString());
        }

        if (roiLabelInfo.optTypeEnum == n_mtautodelineationdialog::OptType_Add)
        {
            cfgItem.SetIsDefaultRoi(0);//新增的设置为非默认标签
            DBDataUtils::AddROILibraryConfig(cfgItem);
        }
        else
        {
            DBDataUtils::UpdateROILibraryConfig(roiLabelInfo.manteiaRoiLabel, cfgItem);
        }

        //同步信息到ROI库
        if (syncType != LabelInfo2ROI::Type_None)
        {
            QString organCustName, organChineseName, organCustColor, roiType;

            if (syncType & LabelInfo2ROI::Type_Name)
            {
                organCustName = cfgItem.GetRoiName().c_str();
            }

            if (syncType & LabelInfo2ROI::Type_Color)
            {
                organCustColor = cfgItem.GetRoiColor().c_str();
            }

            if (syncType & LabelInfo2ROI::Type_Type)
            {
                roiType = cfgItem.GetRoiType().c_str();
            }

            if (syncType & LabelInfo2ROI::Type_ChName)
            {
                organChineseName = cfgItem.GetRoiChineseName().c_str();
            }

            DBDataUtils::UpdateOrganInfoByLabelName(cfgItem.GetRoiLabel().c_str(), organCustName, organChineseName, organCustColor, roiType);
        }

        progressDlg.setLabelText(tr("正在更新数据..."));
        progressDlg.setValue((r + 1) * 100 / outStRoiLabelInfoList.size());
        QApplication::processEvents();
    }

    tempWidget->resetInfoChangeStatus();
    return true;
}

void MTAutoDelineateManager::SlotHandleDelineateResponse(const QString& taskId, const ST_OutputAutoDelineate& respInfo)
{
    if (respInfo.status == SketchStatus_Processing)
    {
        //保存任务
        SaveDelineateTaskInfo(respInfo.moduleName, respInfo.seriesUid, taskId);
    }
    else
    {
        //删除任务
        if (respInfo.status != EM_DelineateStatus::SketchStatus_CancelFailed)
        {
            RemoveDelineateTaskInfo(respInfo.moduleName, respInfo.seriesUid);
        }
    }

    //执行所有的回调
    for (auto it = m_clientModuleCallbackMap.begin(); it != m_clientModuleCallbackMap.end(); ++it)
    {
        it.value()(respInfo);
    }
}

void MTAutoDelineateManager::InitTopicFuncMap()
{
    m_topicFuncMap.insert(TOPIC_MTACS_TASK_BROADCAST_AIMODEL_IMPORT_RESULT, std::bind(&MTAutoDelineateManager::OnExcuteModelImportResultEvent, this, std::placeholders::_1));
}

void MTAutoDelineateManager::OnExcuteModelImportResultEvent(const mtcore::MTEvent& event)
{
    RPCTcpClientRecvDataInfo recvData = event.getProperty("RPCTcpClientRecvDataInfo").value<RPCTcpClientRecvDataInfo>();
    QString msgStr = QJsonDocument(recvData.msg).toJson(QJsonDocument::Compact);
    qDebug() << "auto delineate msg: " << msgStr;
    QJsonObject& jsonObject = recvData.msg;
    QString msg_uid = jsonObject.value("msg_uid").toString();//msg_uid为发起任务的taskId

    if (!jsonObject.contains("clientPrivate") || !jsonObject.contains("success"))//没有私有字段和结果状态，不处理
    {
        return;
    }

    bool bSuccess = jsonObject.value("success").toBool();
    QString errorMessage = jsonObject.value("errorMessage").toString();
    QString clientPrivateStr = jsonObject.value("clientPrivate").toString();
    QJsonArray resultPaths = jsonObject.value("resultPaths").toArray();
    QJsonObject clientPrivateObj = CommonUtil::qStringToqJsonObject(clientPrivateStr);

    if (clientPrivateObj.isEmpty())//没有私有信息，不处理
    {
        return;
    }

    //提取
    QString clientId = clientPrivateObj.value("clientId").toString();
    QString funcName = clientPrivateObj.value("funcName").toString();
    QString moduleName = clientPrivateObj.value("moduleName").toString();
    QString patientId = clientPrivateObj.value("patientId").toString();
    QString seriesUid = clientPrivateObj.value("seriesUid").toString();
    QString optType = clientPrivateObj.value("optType").toString();
    EM_AutoDelineatePlatform platform = (EM_AutoDelineatePlatform)clientPrivateObj.value("platform").toInt();

    if (funcName != AiSketchTaskIdPrefix && funcName != CancelAiSketchTaskIdPrefix)//不是自动勾画的结果
    {
        return;
    }

    ST_OutputAutoDelineate outAutoInfo;
    outAutoInfo.platform = platform;
    outAutoInfo.clientId = clientId;
    outAutoInfo.moduleName = moduleName;
    outAutoInfo.patientId = patientId;
    outAutoInfo.seriesUid = seriesUid;
    outAutoInfo.optType = optType;
    outAutoInfo.errMsg = errorMessage;
    outAutoInfo.progress = 100;

    for (int i = 0; i < resultPaths.size(); ++i)
    {
        outAutoInfo.resultFileList.append(resultPaths[i].toString());
    }

    if (funcName == CancelAiSketchTaskIdPrefix)
    {
        if (bSuccess)
        {
            outAutoInfo.status = EM_DelineateStatus::SketchStatus_CancelSuccess;
        }
        else
        {
            outAutoInfo.status = EM_DelineateStatus::SketchStatus_CancelFailed;
        }
    }
    else
    {
        if (bSuccess)
        {
            outAutoInfo.status = EM_DelineateStatus::SketchStatus_DelineateSuccess;
        }
        else
        {
            outAutoInfo.status = EM_DelineateStatus::SketchStatus_DelineateFailed;
        }
    }

    emit SigHandleDelineateResponse(msg_uid, outAutoInfo);
}

QDialog::DialogCode MTAutoDelineateManager::CreateAutoDelineateDlg(const QString& sex, const Series& series, QWidget* parentWidget
                                                                   , bool bShowBottomFuncWidget, bool bShowTemplateBtnWidget
                                                                   , ST_SketchBusinessInfo& outBusinessParam
                                                                   , std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec
                                                                   , std::vector< ST_REQ_AutoSketchSuper_Organ>& trainOrganVec
                                                                   , std::vector< ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec)
{
    static int static_FTemplateId = -1; //女性模板
    static int static_MTemplateId = -1; //男性模板
    //回调函数
    n_mtautodelineationdialog::ST_CallBack_AutoSketch autoSketchCallBack = AutoDelineationCallBack::createAutoSketchCallBack();
    //自动勾画信息
    n_mtautodelineationdialog::ST_AutoSketch stAutoSketch;
    stAutoSketch.sketchModelList.reserve(50);
    stAutoSketch.seriesUID = series.getSeriesUID().c_str();
    stAutoSketch.imageModality = QString(series.getModality().c_str()).toUpper();
    stAutoSketch.sketchModelList = AutoDelineationDataOpt::getDefSketchModel(true);
    stAutoSketch.sketchModelList.append(AutoDelineationDataOpt::getEmptySketchModel(true));
    stAutoSketch.sketchModelList.append(AutoDelineationDataOpt::getALSketchModel(true));
    stAutoSketch.allSketchCollectionMap = AutoDelineationDataOpt::getAllSketchCollectionSort();
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> oldTemplateIdMap;

    for (QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>::iterator it = stAutoSketch.allSketchCollectionMap.begin();
         it != stAutoSketch.allSketchCollectionMap.end(); it++)
    {
        QList<n_mtautodelineationdialog::ST_SketchModelCollection> collectionList = it.value();

        for (int i = 0; i < collectionList.size(); i++)
        {
            oldTemplateIdMap[it.key()].push_back(collectionList[i].id);
        }
    }

    stAutoSketch.stAddrSimple = AutoDelineationDataOpt::getSoftMemoryAutoSketchExportAddr(stAutoSketch.checkedExport);
    //所有分组规则
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> allGroupInfoList = AutoDelineationDataOpt::getAllGroupInfoAndSort();
    //设置布局位置
    QList<QScreen*> screens = QApplication::screens();
    int dlgWidth = 1400;
    int dlgHeight = 760;

    if (screens.size() > 0)
    {
        QRect deskRect = screens[0]->availableGeometry();
        int deskWidth = deskRect.width();
        int deskHight = deskRect.height();

        if (deskWidth < 1920)
        {
            dlgWidth = deskWidth - 32 * 2;
            dlgHeight = deskHight - 32 * 2 - 102; //弹窗的宽度 里面内容的实际高度(实际高减去102)
        }
    }

    //打开自动勾画界面
    n_mtautodelineationdialog::MTAutoDelineationDialog dlg(dlgWidth, dlgHeight, parentWidget);
    //设置是否显示合并已有rtStruct选项
    QStringList mergeRtSopInsUIDList;
    QMap<QString, QString> mergeRtValMap;
    AutoDelineationDataOpt::getMergeRtInfo(stAutoSketch.seriesUID, mergeRtSopInsUIDList, mergeRtValMap);
    dlg.setIsShowMergeOption(true, mergeRtSopInsUIDList, mergeRtValMap);
    //设置是否显示勾画完后自动导出选项
    dlg.setIsShowAutoExport(true, { 1, 2, 3 });
    //设置选中的页签类型
    dlg.setSelectRadioPageType(m_autoSketchSelectRadioPageType);
    //设置提前选中的模板
    dlg.setSelectTemplateId(sex.toUpper() == "F" ? static_FTemplateId : static_MTemplateId);
    //设置修改模板涉及到无人值守时的提示
    dlg.setIsShowTipOfModUnattendUsed(AutoDelineationDataOpt::getIsShowTipOfModUnattendUsed());
    //设置虚拟模板
    dlg.setVirtualSketchCollection(m_stVirtualSketchCollection);
    //
    QDialog::DialogCode dlgCode = dlg.showAutoSketchDlg(allGroupInfoList, AutoDelineationDataOpt::getAllRemoteScpAddrList()
                                                        , stAutoSketch, bShowBottomFuncWidget, bShowTemplateBtnWidget, autoSketchCallBack);

    if (dlgCode == QDialog::Accepted)
    {
        //获取最新的勾画信息
        n_mtautodelineationdialog::ST_SketchModelCollection sketchCollection;
        n_mtautodelineationdialog::ST_AddrSimple stAddrSimple;
        bool checkedMergeRt, checkedExport;
        QString mergeRtSopInsUID;
        dlg.getNewOutInfo(sketchCollection, checkedMergeRt, mergeRtSopInsUID, checkedExport, stAddrSimple);
        outBusinessParam = AutoDelineationDataOpt::ToStructOfAutoSketch(stAutoSketch.seriesUID, sketchCollection, checkedMergeRt, mergeRtSopInsUID, checkedExport, stAddrSimple);
        AutoDelineationDataOpt::GetSketchOrganInfo(sketchCollection, sketchOrganVec, trainOrganVec, emptyOrganVec);

        if (sex.toUpper() == "F" && sketchCollection.id > 0)
            static_FTemplateId = sketchCollection.id;
        else
            static_MTemplateId = sketchCollection.id;

        //获取选中的页签类型
        m_autoSketchSelectRadioPageType = dlg.getSelectRadioPageType();
        //获取虚拟模板
        m_stVirtualSketchCollection = dlg.getVirtualSketchCollection();
        //更新导出文件夹是否创建患者目录和rt格式编码
        AutoDelineationDataOpt::updateExportDirConfigInfo(stAddrSimple);
    }

    //如果模板顺序有更新，则刷新
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> newTemplateIdMap = dlg.getNewTemplateIdSortMap();

    for (QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>>::iterator it = newTemplateIdMap.begin(); it != newTemplateIdMap.end(); it++)
    {
        n_mtautodelineationdialog::EM_OptDcmType optDcmTypeEnum = it.key();
        QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> templateIdMap;
        templateIdMap[optDcmTypeEnum] = it.value();

        if (oldTemplateIdMap.contains(optDcmTypeEnum) == false)
        {
            AutoDelineationDataOpt::updateSketchTemplateIdSort(templateIdMap);
            continue;
        }
        else
        {
            if (oldTemplateIdMap[optDcmTypeEnum] != templateIdMap[optDcmTypeEnum])
            {
                AutoDelineationDataOpt::updateSketchTemplateIdSort(templateIdMap);
                continue;
            }
        }
    }

    //记忆是否修改模板时，该模板是无人值守模板提示
    AutoDelineationDataOpt::setIsShowTipOfModUnattendUsed(dlg.getNewIsShowTipOfModUnattendUsed());
    return dlgCode;
}

void MTAutoDelineateManager::SaveDelineateTaskInfo(const QString& moduleId, const QString& seriesUid, const QString& taskId)
{
    //若不存在，则添加
    if (m_delineateTaskIdMap.contains(QPair<QString, QString>(moduleId, seriesUid)))
    {
        return;
    }

    m_delineateTaskIdMap.insert(QPair<QString, QString>(moduleId, seriesUid), taskId);
}

void MTAutoDelineateManager::RemoveDelineateTaskInfo(const QString& moduleId, const QString& seriesUid)
{
    m_delineateTaskIdMap.remove(QPair<QString, QString>(moduleId, seriesUid));
}

QString MTAutoDelineateManager::GetDelineateTaskInfo(const QString& moduleId, const QString& seriesUid)
{
    return m_delineateTaskIdMap.value(QPair<QString, QString>(moduleId, seriesUid));
}

void MTAutoDelineateManager::ConnectRoiSettingWidgetSignal(QWidget* roiLibraryWidget)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* tempWidget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(roiLibraryWidget);

    if (nullptr == tempWidget)
    {
        return;
    }

    //一个窗口只连接一次信号槽
    if (m_bRoiLibSettingWidgetInit)
    {
        return;
    }

    m_bRoiLibSettingWidgetInit = true;

    auto funcRemoveRoiRelatedGroup = [&](int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap)
    {
        if (refModelInfoMap.size() == 0)//roi的所有分组都被移除
        {
            if (-2 == groupID && groupName == "")
            {
                MtMessageBox::information(roiLibraryWidget, tr("ROI分组不可为空"));
            }
            else
            {
                MtMessageBox::information(roiLibraryWidget, tr("ROI不可选择该分组"));
            }
        }
        else
        {
            QStringList modelNameList = refModelInfoMap.values();
            MtMessageBox::information(roiLibraryWidget, tr("\"") + groupName + tr("\"中的 \"") + roiName + tr("\"已被下列模板引用，取消勾选将移除模板中的ROI:\r\n") + modelNameList.join('\n'));
        }
    };
    auto funcUpdateRoiGroup = [&](const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList,
                                  const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList,
                                  QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList)
    {
        MtMessageDialog msgDlg;
        msgDlg.setLabelText(tr("正在更新分组信息..."));
        msgDlg.showDialog();
        //
        QTime startTime = QTime::currentTime(); // 记录程序开始时间
        //所有组信息
        QList<n_mtautodelineationdialog::ST_OrganGroupInfo> allGroupList;
        AutoDelineationDataOpt::getAllGroupInfoAndSort(allGroupList);

        //1. 更新分组信息
        for (const auto& groupInfoItem : curGroupList)
        {
            //新增分组
            if (groupInfoItem.id < 0)
            {
                //主组信息
                DBOrganGroupInfo groupObj;
                groupObj.SetEnable(1);
                groupObj.SetId(-1);
                groupObj.SetName(groupInfoItem.name.toStdString());
                groupObj.SetType((EM_TableOrganGroupType)groupInfoItem.type);
                DBDataUtils::AddOrganGroupInfo(groupObj);
                continue;
            }

            for (auto& oldInfoItem : allGroupList)
            {
                if (oldInfoItem.id == groupInfoItem.id)
                {
                    if (oldInfoItem.name != groupInfoItem.name)//有更新组名
                    {
                        //更新缓存
                        oldInfoItem.name = groupInfoItem.name;
                        //更新数据库
                        DBOrganGroupInfo groupObj;
                        groupObj.SetEnable(1);
                        groupObj.SetId(groupInfoItem.id);
                        groupObj.SetName(groupInfoItem.name.toStdString());
                        groupObj.SetDefaultGroupName(groupInfoItem.defaultName.toStdString());
                        groupObj.SetType((EM_TableOrganGroupType)groupInfoItem.type);
                        DBDataUtils::UpdateOrganGroupInfo(groupObj);
                    }

                    break;
                }
            }
        }

        //2. 删除分组信息
        for (const auto& groupInfoItem : delGroupList)
        {
            DBDataUtils::deleteOrganGroupInfo(groupInfoItem.id);
        }

        //3. 更新回传的分组信息
        updtedGroupList.clear();
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> allGroupMap = AutoDelineationDataOpt::getAllGroupInfo();

        for (const auto& groupInfoItem : curGroupList)
        {
            updtedGroupList.append(groupInfoItem);

            if (groupInfoItem.id < 0)
            {
                QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = allGroupMap.constBegin();

                for (; itor != allGroupMap.constEnd(); ++itor)
                {
                    if (groupInfoItem.name == itor.value().name)
                    {
                        updtedGroupList[updtedGroupList.size() - 1].id = itor.key();
                        break;
                    }
                }
            }
        }

        //4. 更新亚组信息
        QSet<int> groupExtraInfoIDSet;
        std::vector<DBOrganGroupInfo_Extra> groupInfoExtraVec = DBDataUtils::FindAllOrganGroupInfo_Extra();

        ///解析组信息--获取组附加信息中已有的分组ID
        for (const DBOrganGroupInfo_Extra& itemExtra : groupInfoExtraVec)
        {
            groupExtraInfoIDSet.insert(itemExtra.GetOrganGroupInfoId());
        }

        for (const auto& groupInfoItem : updtedGroupList)
        {
            if (groupInfoItem.type == 3)
            {
                QJsonObject extraJsonObj;
                extraJsonObj["ref_organinfoconfigId"] = QString::number(groupInfoItem.refOrganId);
                DBOrganGroupInfo_Extra organGroupInfoExtraObj;
                organGroupInfoExtraObj.SetOrganGroupInfoId(groupInfoItem.id);
                organGroupInfoExtraObj.SetJsonInfo(QJsonDocument(extraJsonObj).toJson(QJsonDocument::Compact).toStdString());

                if (groupExtraInfoIDSet.contains(groupInfoItem.id))
                {
                    DBDataUtils::UpdateOrganGroupInfo_ExtraByOrgangroupinfoId(groupInfoItem.id, organGroupInfoExtraObj.GetJsonInfo());
                }
                else
                {
                    DBDataUtils::AddOrganGroupInfo_Extra(organGroupInfoExtraObj);
                }
            }
        }

        //5. 更新分组排序
        QJsonArray groupOrderIDArr;

        for (auto& groupInfoItem : updtedGroupList)
        {
            if (allGroupMap.contains(groupInfoItem.id))
            {
                groupOrderIDArr.append(QString::number(groupInfoItem.id));
            }
        }

        DBFeaturesConfig featureObj;
        QJsonDocument jsonDocument(groupOrderIDArr);
        featureObj.SetFeatureKey("organ_group_order");
        featureObj.SetFeatureValue(jsonDocument.toJson(QJsonDocument::Compact).toStdString());
        DBDataUtils::UpdateFeatureConfigInfo(featureObj);
        //
        QTime endTime = QTime::currentTime(); // 记录程序结束时间
        int elapsedTime = startTime.msecsTo(endTime); // 计算时间差（单位：毫秒）

        if (elapsedTime < 1000)//处理时间太短，会出现消息窗一闪而过，让弹窗多显示一会
        {
            QEventLoop eventLoop;
            QtConcurrent::run([&]
            {
                Sleep(1000 - elapsedTime);
                eventLoop.quit();
            });
            eventLoop.exec(QEventLoop::ExcludeUserInputEvents);
        }

        msgDlg.hideDialog();
    };
    auto funcGetLabelLibraryInfo = [&](QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)
    {
        std::vector<DBRoiLibraryConfig> roiLibraryCfgVec;
        AutoDelineationDataOpt::getAllRoiLabelInfoList(stRoiLabelInfoVec, roiLibraryCfgVec);
    };
    auto funcGetOrganDefaultInfo = [&](QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
    {
        QString cfgPath = CommonUtil::GetOrganDefaultConfigPath();
        AutoDelineationDataOpt::GetOrganDefaultConfigInfo(cfgPath, stOrganDefaultList);
    };
    auto funcSaveModelInfo = [&](const QString& modelId, const QString& modelName, const QString& desc, int& result)
    {
        result = DBDataUtils::UpdateAiModelNameAndDescriptionById(modelId, modelName, desc) ? 0 : 1;

        if (0 == result)
        {
            MtMessageBox::information(roiLibraryWidget, modelName + tr("保存成功"));
        }
        else
        {
            MtMessageBox::information(roiLibraryWidget, modelName + tr("保存失败"));
        }
    };
    //模板关联的roi取消了分组
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigRemoveRoiRelatedGroup, this, funcRemoveRoiRelatedGroup);
    //更新roi分组信息，并重新获取分组信息
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigUpdateRoiGroup, this, funcUpdateRoiGroup);
    //获取标签库信息
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigGetLabelLibraryInfo, this, funcGetLabelLibraryInfo);
    //获取器官ROI默认设置(根据默认名进行匹配)
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigGetOrganDefaultInfo, this, funcGetOrganDefaultInfo);
    //模型信息保存状态
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigSaveModelInfo, this, funcSaveModelInfo);
    //进行导入模型
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigModelImport, this, &MTAutoDelineateManager::SlotRoiLibModelImport);
    //模型删除信号
    connect(tempWidget, &n_mtautodelineationdialog::MTRoiLibraryWidget::sigDeleteModel, this, &MTAutoDelineateManager::SlotRoiLibDeleteModel);
}

void MTAutoDelineateManager::SlotRoiLibModelImport(const QString& modelPath)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* widget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(sender());

    if (nullptr == widget)
    {
        return;
    }

    QString errMsg;

    if (!m_modelImportHelper.UploadFile(modelPath, errMsg))
    {
        MtMessageBox::information(nullptr, tr("模型导入失败"), errMsg);
    }
}

void MTAutoDelineateManager::SlotRoiLibDeleteModel(const QString& modelId, const QString& modelName)
{
    n_mtautodelineationdialog::MTRoiLibraryWidget* widget = qobject_cast<n_mtautodelineationdialog::MTRoiLibraryWidget*>(sender());

    if (nullptr == widget)
    {
        return;
    }
}
