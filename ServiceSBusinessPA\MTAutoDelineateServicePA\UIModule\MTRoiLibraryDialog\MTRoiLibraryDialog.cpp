﻿#include "MTRoiLibraryDialog.h"
#include "ui_MTRoiLibraryDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "RoiLibrarySub/RoiLibraryWidget.h"
#include "CommonUtil.h"
#include "MultiSelectComboBox.h"
#include "MtMessageBox.h"
#include "CMtLanguageUtil.h"

namespace n_mtautodelineationdialog
{

MTRoiLibraryDialog::MTRoiLibraryDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTRoiLibraryDialogClass;
    ui->setupUi(this);
    //基本属性
    this->setMainLayout(ui->verticalLayout);            //设置布局
    this->setDialogWidthAndContentHeight(CMtLanguageUtil::type == english ? 1583 : /*1328*/1713, 860); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("模型和ROI设置"));                 //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    //getWidgetButton()->hide();                        //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(false);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTRoiLibraryDialog, " << errMsg.toStdString();
        }
    }
    //
    this->getButton(MtTemplateDialog::BtnRight1)->installEventFilter(this);
}
MTRoiLibraryDialog::~MTRoiLibraryDialog()
{
}

QDialog::DialogCode MTRoiLibraryDialog::showRoiLibrarySettingDlg(const QStringList& allRoiTypeList
                                                                 , const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList
                                                                 , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList
                                                                 , const QList<ST_Organ>& stOrganList
                                                                 , const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
                                                                 , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList
                                                                 , QList<ST_Organ>& outStOrganList
                                                                 , QList<n_mtautodelineationdialog::ST_SketchModelCollection>& outModelCollectionInfoList
                                                                 , QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& outRoiLabelInfoList
/*, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& outAllGroupList*/)
{
    RoiLibraryWidget* roiLibraryWidget = new RoiLibraryWidget(this);
    roiLibraryWidget->setImagePathHash(m_imagePathHash);
    roiLibraryWidget->init(allRoiTypeList.isEmpty() == true ? CommonUtil::getRoiTypeList() : allRoiTypeList, stRoiLabelInfoList, allGroupList, stOrganList, modelInfoMap, modelCollectionInfoList);
    ui->verticalLayout_2->addWidget(roiLibraryWidget);
    doConnect(roiLibraryWidget);

    if (this->exec() == QDialog::Accepted && roiLibraryWidget->isNeedSave2File())
    {
        outStOrganList = roiLibraryWidget->getAllOrganInfo();
        outModelCollectionInfoList = roiLibraryWidget->getAllModelCollectionInfoList();
        outRoiLabelInfoList = roiLibraryWidget->getAllLabelInfoList();
        //outAllGroupList = roiLibraryWidget->getAllOrganGroupInfo();
        return QDialog::Accepted;
    }

    //outAllGroupList = roiLibraryWidget->getAllOrganGroupInfo();
    return QDialog::Rejected;
}
/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTRoiLibraryDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}
/// <summary>
/// 关闭按钮
/// </summary>
void MTRoiLibraryDialog::onBtnCloseClicked()
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(wdg);

    //     for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
    //     {
    //         QLayoutItem* item = ui->verticalLayout_2->itemAt(i);
    //
    //         if (item->widget())
    //         {
    //             tabWidget = qobject_cast<RoiLibraryWidget*>(item->widget());

    if (tabWidget)
    {
        tabWidget->removeFocusFromTable();
    }

    //         }
    //     }
    bool bNeedSave2File = false;

    if (nullptr != tabWidget)
    {
        bNeedSave2File = tabWidget->isNeedSave2File();
        tabWidget->widgetDestroying();
    }

    if (/*this->getButton(MtTemplateDialog::BtnRight1)->isEnabled()*/bNeedSave2File && QMessageBox::Yes == MtMessageBox::NoIcon::question_Title(this, tr("是否保存本次修改？")))
    {
        this->accept();
    }
    else
    {
        this->reject();
    }
}
/// <summary>
/// 取消按钮
/// </summary>
void MTRoiLibraryDialog::onBtnRight2Clicked()
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(wdg);

    if (tabWidget)
    {
        tabWidget->widgetDestroying();
    }

    this->reject();
}
/// <summary>
/// 确定按钮
/// </summary>
void MTRoiLibraryDialog::onBtnRight1Clicked()
{
    QWidget* wdg = getContentWidget();
    RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(wdg);

    if (tabWidget)
    {
        tabWidget->widgetDestroying();
    }

    this->accept();
}

bool MTRoiLibraryDialog::eventFilter(QObject* obj, QEvent* event)
{
    //点击保存和取消按钮时，让焦点设置到列表上，使列表输入框失去焦点而触发编辑完成事件
    if (obj == getButton(MtTemplateDialog::BtnRight1))
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent* mouse_event = static_cast<QMouseEvent*>(event);

            if (mouse_event->button() == Qt::LeftButton)
            {
                for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
                {
                    QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

                    if (item->widget())
                    {
                        RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(item->widget());

                        if (tabWidget)
                        {
                            tabWidget->removeFocusFromTable();
                        }
                    }
                }
            }
        }
    }

    return QWidget::eventFilter(obj, event);
}

void MTRoiLibraryDialog::doConnect(QWidget* widget)
{
    RoiLibraryWidget* roiLibraryWidget = qobject_cast<RoiLibraryWidget*>(widget);
    connect(this, &MTRoiLibraryDialog::sigModelImportProgress, roiLibraryWidget, &RoiLibraryWidget::sigModelImportProgress);
    connect(this, &MTRoiLibraryDialog::sigModelImportFinish, roiLibraryWidget, &RoiLibraryWidget::sigModelImportFinish);
    connect(this, &MTRoiLibraryDialog::sigModelImportResult, roiLibraryWidget, &RoiLibraryWidget::sigModelImportResult);
    connect(this, &MTRoiLibraryDialog::sigModelDeleteResult, roiLibraryWidget, &RoiLibraryWidget::sigModelDeleteResult);
    connect(roiLibraryWidget, &RoiLibraryWidget::sigGetLabelLibraryInfo, this, [&](QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)
    {
        emit sigGetLabelLibraryInfo(stRoiLabelInfoVec);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigGetOrganDefaultInfo, this, [&](QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
    {
        emit sigGetOrganDefaultInfo(stOrganDefaultList);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigModelImport, this, [&](const QString& modelPath)
    {
        emit sigModelImport(modelPath);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigDeleteModel, this, [&](const QString& modelId, const QString& modelName)
    {
        emit sigDeleteModel(modelId, modelName);
    });
    connect(roiLibraryWidget, &RoiLibraryWidget::sigSaveModelInfo, this, [&](const QString& modelId, const QString& modelName, const QString& desc, int& result)
    {
        emit sigSaveModelInfo(modelId, modelName, desc, result);
    });
    //连接列表组取消信号
    connect(roiLibraryWidget->getTableWidget(), &RoiLibraryTable::sigRemoveRoiRelatedGroup, this, [&](int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int, QString>& refModelInfoMap)
    {
        emit sigRemoveRoiRelatedGroup(roiID, roiName, groupID, groupName, refModelInfoMap);
    });
    //连接分组信息更新信号
    connect(roiLibraryWidget, &RoiLibraryWidget::sigUpdateRoiGroup, this, [&](const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList, const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList)
    {
        emit sigUpdateRoiGroup(curGroupList, delGroupList, updtedGroupList);
    });
}
QWidget* MTRoiLibraryDialog::getContentWidget()
{
    for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
    {
        QLayoutItem* item = ui->verticalLayout_2->itemAt(i);
        RoiLibraryWidget* tabWidget = qobject_cast<RoiLibraryWidget*>(item->widget());

        if (tabWidget)
        {
            return item->widget();
        }
    }

    return nullptr;
}
}
