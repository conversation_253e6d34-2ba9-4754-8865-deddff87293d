<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ImportEclipseTemplateDialogClass</class>
 <widget class="QDialog" name="ImportEclipseTemplateDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>664</width>
    <height>460</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ImportEclipseTemplateDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrame" name="mtFrame">
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>6</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <property name="spacing">
           <number>10</number>
          </property>
          <item>
           <widget class="MtLineEdit" name="mtLineEdit">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>30</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>30</height>
             </size>
            </property>
            <property name="readOnly">
             <bool>true</bool>
            </property>
            <property name="placeholderText">
             <string>请选择文件夹</string>
            </property>
            <property name="elideMode">
             <enum>Qt::ElideRight</enum>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtLineEdit::lineedit1</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MtPushButton" name="mtPushButton">
            <property name="minimumSize">
             <size>
              <width>40</width>
              <height>30</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>40</width>
              <height>30</height>
             </size>
            </property>
            <property name="text">
             <string>...</string>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtPushButton::pushbutton13</enum>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="MtCheckBox" name="mtCheckBox_sub">
          <property name="text">
           <string>检索所有子文件夹中的数据</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtCheckBox::checkbox1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <property name="spacing">
         <number>6</number>
        </property>
        <item>
         <widget class="MtLabel" name="mtLabel">
          <property name="text">
           <string>选择需要导入的Eclipse的勾画模板XML文件，快捷自定义ROI名称、颜色以及新增ROI</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_1</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="EclipseTemplateTable" name="table_widget" native="true"/>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx">
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="spacing">
            <number>24</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtCheckBox" name="mtCheckBox_all">
             <property name="text">
              <string>全选</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtCheckBox" name="mtCheckBox_syncInfo">
             <property name="text">
              <string>将eclipse模板的配置信息应用于本系统</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrame</class>
   <extends>QFrame</extends>
   <header>MtFrame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>EclipseTemplateTable</class>
   <extends>QWidget</extends>
   <header>eclipsetemplatetable.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
