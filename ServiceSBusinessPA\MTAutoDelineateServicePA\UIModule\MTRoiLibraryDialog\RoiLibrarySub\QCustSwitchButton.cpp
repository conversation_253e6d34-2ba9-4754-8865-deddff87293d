﻿#include "QCustSwitchButton.h"


QCustSwitchButtonParam::QCustSwitchButtonParam()
{
    _cellWidgetType = DELEAGATE_TYPE_User + 4;
}

QCustSwitchButtonParam::~QCustSwitchButtonParam()
{
}

QWidget* QCustSwitchButtonParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QCustSwitchButton* switchBtn = new QCustSwitchButton(parent);
    switchBtn->setCheckedState(_state != 0);
    return switchBtn;
}

/*********************************************************/
QCustSwitchButton::QCustSwitchButton(QWidget* parent /*= Q_NULLPTR*/)
    : QWidget(parent)
{
    ui.setupUi(this);
    connect(ui.mtSwitchButton, &MtSwitchButton::clicked, this, &QCustSwitchButton::slotBtnClicked);
}

QCustSwitchButton::~QCustSwitchButton()
{
}

bool QCustSwitchButton::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::Bool == userType)
    {
        bool bChecked = updateData.toBool();
        setCheckedState(bChecked);
        return true;
    }

    return false;
}

void QCustSwitchButton::SetEnableEdit(bool bEdit)
{
    setEnabled(bEdit);
}

void QCustSwitchButton::setCheckedState(bool bChecked)
{
    ui.mtSwitchButton->setChecked(bChecked);
}

void QCustSwitchButton::slotBtnClicked(bool bChecked)
{
    emit sigCheckStateChanged(bChecked);
}