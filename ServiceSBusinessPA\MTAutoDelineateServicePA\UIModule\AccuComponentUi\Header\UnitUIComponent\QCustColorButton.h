﻿// ************************************************************
// <remarks>
// Author      : wangjun
// CreateTime  : 2024-11-18
// Description : 自定义颜色按钮
// </remarks>
// ************************************************************
#pragma once

#include <QWidget>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"


/// <summary>
/// 自定义颜色按钮参数
/// </summary>
class  QCustColorButtonParam : public ICellWidgetParam
{
public:
    /// <summary>
    /// Enum EM_ToShowType
    /// </summary>
    enum EM_ToShowType
    {
        /// <summary>
        /// 左对齐类型
        /// </summary>
        Type_LeftJustifying = 0,
        /// <summary>
        /// 居中类型
        /// </summary>
        Type_Center,

    };
public:
    /// <summary>
    /// 颜色
    /// </summary>
    QColor color = QColor(255, 0, 0);
    /// <summary>
    /// 文本
    /// </summary>
    QString text;
    /// <summary>
    /// 宽度
    /// </summary>
    int width = 10;
    /// <summary>
    /// 高度
    /// </summary>
    int hight = 10;
    /// <summary>
    /// 是否颜色可以弹窗选择
    /// </summary>
    bool canSelectColor = false;
    /// <summary>
    /// 区分是左对齐还是居中对齐
    /// </summary>
    int showType = Type_LeftJustifying;
    /// <summary>
    /// 上边距
    /// </summary>
    int topMarginNub = -1;
    /// <summary>
    /// 下边距
    /// </summary>
    int lowMarginNub = -1;
    /// <summary>
    /// 左边距
    /// </summary>
    int leftMarginNub = -1;
    /// <summary>
    /// 右边距
    /// </summary>
    int rightMarginNub = -1;
    /// <summary>
    /// 初始化 <see cref="QCustColorButtonParam"/> 类的新实例
    /// </summary>
    QCustColorButtonParam();
    /// <summary>
    /// 创建 UI 模块
    /// </summary>
    /// <param name="parent">父对象</param>
    /// <returns>QWidget *</returns>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    virtual QWidget* CreateUIModule(QWidget* parent = nullptr);
};
Q_DECLARE_METATYPE(QCustColorButtonParam)

namespace Ui
{
class QCustColorButton;
}

class MtFrameEx;
class MtColorButton;

/// <summary>
/// 自定义颜色按钮
/// </summary>
class  QCustColorButton : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustColorButton(QWidget* parent = Q_NULLPTR);
    /// <summary>
    /// 析构 <see cref="QCustColorButton"/> 类的实例
    /// </summary>
    ~QCustColorButton();

    /// <summary>
    /// 设置单元格
    /// </summary>
    /// <param name="cellWidgetParam">单元格参数</param>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    void SetupCellWidget(QCustColorButtonParam& cellWidgetParam);

    /****************单元格公共接口*********************/
    /// <summary>
    /// 更新 UI
    /// </summary>
    /// <param name="updateData">更新数据</param>
    /// <returns>bool</returns>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    virtual bool UpdateUi(const QVariant& updateData);      //更新界面接口
    /// <summary>
    /// 获取文本
    /// </summary>
    /// <returns>QString</returns>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    QString GetText();
    /// <summary>
    /// 设置是否在点击颜色时弹出选择颜色弹窗
    /// </summary>
    /// <param name="isVisable">The is visable.</param>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    void SetShowColorSelectDialog(bool isVisable);
    /// <summary>
    /// 获取颜色
    /// </summary>
    /// <returns>std.vector&lt;_Ty, _Alloc&gt;</returns>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    std::vector<int> GetColor();
    /// <summary>
    /// 根据展示类型初始化
    /// </summary>
    /// <param name="ToShowType">展示类型</param>
    /// <param name="topMarginNub">上边距</param>
    /// <param name="lowMarginNub">下边距</param>
    /// <param name="leftMarginNub">左边距</param>
    /// <param name="rightMarginNub">右边距</param>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    void CreateByType(int ToShowType, int topMarginNub = -1, int lowMarginNub = -1, int leftMarginNub = -1, int rightMarginNub = -1);
signals:
    /// <summary>
    /// 颜色改变了
    /// </summary>
    /// <param name="color">The color.</param>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    void sigIndicatorColorChanged(QColor color);
private:
    /// <summary>
    /// The UI
    /// </summary>
    Ui::QCustColorButton* ui = nullptr;
    /// <summary>
    /// 包含框架
    /// </summary>
    MtFrameEx* m_containFrame = nullptr;
    /// <summary>
    /// 颜色按钮
    /// </summary>
    MtColorButton* m_colorButton = nullptr;
};
