﻿#include "AccuComponentUi\Header\QMTAbsTableWidgetItem.h"
#include "ui_QMTAbsTableWidgetItem.h"
#include <QLabel>
#include <QDir>
#include <qDebug>

#if 0
#include "QMTHorizontalButtons.h"
#endif
#include "AccuComponentUi\Header\UnitUIComponent\QMTVDoubleLabelDot.h"
#include "AccuComponentUi\Header\UnitUIComponent\QMTAbsHorizontalBtns.h"
#include "AccuComponentUi\Header\QMTLineEdit.h"
#include "AccuComponentUi\Header\UnitUIComponent\QMTAbsComboBox.h"
#include "AccuComponentUi\Header\UnitUIComponent\QMTCheckBox.h"
#include "AccuComponentUi\Header\UnitUIComponent\QMTCheckBoxLabel.h"
#include "AccuComponentUi\Header\QMTButtonMenuWidget.h"
#include "AccuComponentUi\Header\UnitUIComponent\QCustMtLabel.h"

using namespace mtuiData;

QMap<int, QMTAbsTableWidgetItem*> QMTAbsTableWidgetItem::m_pPrevious = QMap<int, QMTAbsTableWidgetItem*>();

QMTAbsTableWidgetItem* QMTAbsTableWidgetItem::PreviousRowItemWidget(RowWidgetTemplateType type)
{
    return m_pPrevious[type];
}

QMTAbsTableWidgetItem::QMTAbsTableWidgetItem(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTAbsTableWidgetItem();
    ui->setupUi(this);
    //this->setAttribute(Qt::WA_DeleteOnClose);
    _contentParent = ui->widget_content;
}

QMTAbsTableWidgetItem::~QMTAbsTableWidgetItem()
{
    _perRowWidgetItemParam = nullptr;
    //delete all columns
    DeleteWidgetList(_columnWidgets);

    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QMTAbsTableWidgetItem::enterEvent(QEvent* event)
{
    _isHover = true;
    QString rowValue = _uniqueValue;

    if (_isSelect || false == _enableSelect || nullptr == _perRowWidgetItemParam)
    {
        emit sigItemEnter(rowValue, true);
        QWidget::enterEvent(event);
        return;
    }

    if (true == _perRowWidgetItemParam->_isEnableHoverChangeBorderColor ||
        true == _perRowWidgetItemParam->_isEnableHoverChangeBackColor)
    {
        //this->style()->unpolish(this);
        this->setStyleSheet(_hoverSheet);
        //this->style()->polish(this);
    }

    emit sigItemEnter(rowValue, true);
    QWidget::enterEvent(event);
}

void QMTAbsTableWidgetItem::leaveEvent(QEvent* event)
{
    _isHover = false;
    QString rowValue = _uniqueValue;

    if (false == _enableSelect)
    {
        emit sigItemEnter(rowValue, false);
        QWidget::leaveEvent(event);
        return;
    }

    if (_isSelect)
    {
        //this->style()->unpolish(this);
        this->setStyleSheet(_selectSheet);
        //this->style()->polish(this);
    }
    else
    {
        //this->style()->unpolish(this);
        this->setStyleSheet(_unselectSheet);
        //this->style()->polish(this);
    }

    emit sigItemEnter(rowValue, false);
    QWidget::enterEvent(event);
}

QWidget* QMTAbsTableWidgetItem::CreateGridWidget(QWidget* parent)
{
    if (nullptr == _perRowWidgetItemParam)
        return nullptr;

    QPushButton* btn = new QPushButton(parent);
    btn->setFixedWidth(1);

    if (_perRowWidgetItemParam->_rowWidgetHeight > 2)
    {
        btn->setFixedHeight(_perRowWidgetItemParam->_rowWidgetHeight - 2);
    }

    QString textColor;
    int rgb[3];
    rgb[0] = _perRowWidgetItemParam->_gridColor.red();
    rgb[1] = _perRowWidgetItemParam->_gridColor.green();
    rgb[2] = _perRowWidgetItemParam->_gridColor.blue();
    textColor = "background-color:rgb(" + QString::number(rgb[0]) + "," + QString::number(rgb[1]) + "," + QString::number(rgb[2]) + ");";
    textColor += "border: none;";
    QString sheetStr = "QPushButton{" + textColor + ";}";
    btn->setStyleSheet(sheetStr);
    //btn->setCursor(Qt::SplitHCursor);
    return btn;
}

bool QMTAbsTableWidgetItem::eventFilter(QObject* watched, QEvent* event)
{
    QString className = watched->metaObject()->className();

    if (TOString(QMTAbsLineEdit) == className)
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            mousePressEvent(NULL);
            return true;    //表示已经处理
        }
    }

    return QWidget::eventFilter(watched, event);
}

void QMTAbsTableWidgetItem::DeleteWidgetList(QList<QWidget*>& widgetList)
{
    for (int i = 0; i < widgetList.size(); ++i)
    {
        QWidget* widget = widgetList.at(i);
        delete widget;
        widgetList[i] = nullptr;
    }

    widgetList.clear();
}

void QMTAbsTableWidgetItem::ResetSelect()
{
    if (_isSelect)//当前正是被选中的
    {
        int type = _perRowWidgetItemParam->_templateWidgetType;
        this->m_pPrevious[type] = nullptr;
        this->SetSelect(false);
        this->setStyleSheet(_unselectSheet);
    }
}

void QMTAbsTableWidgetItem::SetContentWidgetMargins(int left, int top, int right, int bottom)
{
    _contentParent->layout()->setContentsMargins(left, top, right, bottom);
}

void QMTAbsTableWidgetItem::SetContentSpacing(int space)
{
    _contentParent->layout()->setSpacing(space);
}

void QMTAbsTableWidgetItem::ResizeWidthMap(QMap<int, int>& map)
{
    int count = _columnWidgets.size() - 1;

    for (int i = 0; i < count; ++i)
    {
        QWidget* widget = _columnWidgets.at(i);
        int preWidth = widget->width();
        int width = map.value(i);

        if (preWidth == width)
            continue;

        if (width > 0)
        {
            widget->setFixedWidth(width);
            //qWarning() << "widget column : " << i << "width : " << width;
        }
    }
}

void QMTAbsTableWidgetItem::SetAllColumnHeight(int height)
{
    for (int i = 0; i < _columnWidgets.size(); ++i)
    {
        _columnWidgets[i]->setFixedHeight(height);
    }
}

void QMTAbsTableWidgetItem::SetLeftMarginWidth(int width)
{
    ui->widget_left->setFixedWidth(width);
}

void QMTAbsTableWidgetItem::SetRightMarginWidth(int width)
{
    ui->widget_right->setFixedWidth(width);
}

void QMTAbsTableWidgetItem::ClearPreItem(int type /*= Template_None*/)
{
    if (m_pPrevious.size() > 0)
    {
        m_pPrevious[type] = NULL;
    }
}

#if 0
void QMTAbsTableWidgetItem::SetDependenceTableWidget(QMTAbstractTableWidget* tableWidget)
{
    _dependeceTableWidget = tableWidget;
}
#endif


void QMTAbsTableWidgetItem::CreateRowWidgetItem(QMTAbsRowWidgetItemParam& itemParam)
{
    //参数
    _perRowWidgetItemParam = &itemParam;

    //字体大小如果外部没有设置就使用默认的
    if (_perRowWidgetItemParam->_fontsize < 0)
    {
        if (Language::type == English)
        {
            _perRowWidgetItemParam->_fontsize = ENGLISH_FONTSIZE;
        }
        else
        {
            _perRowWidgetItemParam->_fontsize = CHINESE_FONTSIZE;
        }
    }

    //间隔
    if (_perRowWidgetItemParam->_itemLayouSpacing > 0)
    {
        ui->widget_content->layout()->setSpacing(_perRowWidgetItemParam->_itemLayouSpacing);
    }

    /*********************ui***************************/
    this->setFixedHeight(_perRowWidgetItemParam->_rowWidgetHeight);
    SetLeftMarginWidth(_perRowWidgetItemParam->_leftMargin);
    SetRightMarginWidth(_perRowWidgetItemParam->_rightMargin);
    QString tmpColorStr;
    //unselect sheet
    {
        QString backgroundColor;
        QString unselectColor;
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowWidgetItemParam->_unselectBackColor, true);
        backgroundColor = QString("background-color:%1;").arg(tmpColorStr);
        unselectColor = backgroundColor;
        _unselectSheet = "#tableItemContent{" + unselectColor + ";}";
        {
            if (_perRowWidgetItemParam->_showGrid)
            {
                ui->tableItemGrid->show();
                ui->tableItemGrid->setFixedHeight(_perRowWidgetItemParam->_rowSpacing);
                tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowWidgetItemParam->_gridColor, true);
                QString gridWidgetStyleStr = QString("#tableItemGrid{background-color:%1;}").arg(tmpColorStr);
                _unselectSheet += gridWidgetStyleStr;
                ui->tableItemGrid->setStyleSheet(gridWidgetStyleStr);
            }
            else
            {
                ui->tableItemGrid->hide();
            }
        }
        this->setStyleSheet(_unselectSheet);
        ui->tableItemGrid->update();
        //unselectColor += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        //_unselectSheet = "#widget_content{" + unselectColor + ";}";
        //unselectColor = backgroundColor;
        //unselectColor += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        //_unselectSheet += "#widget_left{" + unselectColor + ";}";
        //unselectColor = backgroundColor;
        //unselectColor += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        //_unselectSheet += "#widget_right{" + unselectColor + ";}";
        //
    }
    //select sheet
    {
        QString backgroundColor;
        QString selectColor;
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowWidgetItemParam->_selectBackColor, true);
        backgroundColor = QString("background-color:%1;").arg(tmpColorStr);
        //backgroundColor += "border:0px;";
        selectColor = backgroundColor;
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowWidgetItemParam->_selectBorderColor, true);
        selectColor += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        _selectSheet = "#widget_content{" + selectColor + ";}";
        selectColor = backgroundColor;
        selectColor += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:1px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        _selectSheet += "#widget_left{" + selectColor + ";}";
        selectColor = backgroundColor;
        selectColor += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        _selectSheet += "#widget_right{" + selectColor + ";}";
        //_selectSheet = "#widget_content,#widget_left,#widget_right{" + selectColor + ";}";
        //_selectSheet += "#widget_content,#widget_left,#widget_right{" + backgroundColor + ";}";
    }
    //hover sheet
    {
        QString backgroundColor;
        QString borderColor;
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowWidgetItemParam->_unselectBackColor, true);
        backgroundColor = QString("background-color:%1;").arg(tmpColorStr);
        borderColor = backgroundColor;
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowWidgetItemParam->_borderColor, true);
        borderColor += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        _hoverSheet = "#widget_content{" + borderColor + ";}";
        borderColor = backgroundColor;
        borderColor += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:1px;border-right-width:0px; border-color:%1;").arg(tmpColorStr);
        _hoverSheet += "#widget_left{" + borderColor + ";}";
        borderColor = backgroundColor;
        borderColor += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        _hoverSheet += "#widget_right{" + borderColor + ";}";
        _hoverSheet += "#widget_content,#widget_left,#widget_right{" + backgroundColor + ";}";
    }
}

QWidget* QMTAbsTableWidgetItem::GetColumnWidget(int index)
{
    if (index < 0 || index >= _columnWidgets.size())
        return NULL;

    return _columnWidgets[index];
}

int QMTAbsTableWidgetItem::GetColumnWidgetCount()
{
    return _columnWidgets.size();
}

void QMTAbsTableWidgetItem::mousePressEvent(QMouseEvent* event)
{
    if (false == _enableSelect)
    {
        QWidget::mousePressEvent(event);
        return;
    }

    Qt::KeyboardModifiers  modState = event == nullptr ? Qt::NoModifier : event->modifiers();

    if (!((Qt::ShiftModifier | Qt::ControlModifier) & modState))
    {
        SetSelectState();
    }

    //QWidget::mousePressEvent(event);      //如果传入的是NULL，这边调用会奔溃，慎用
    emit sigItemClicked(_uniqueValue, modState);
    //emit sigItemClicked(_uniqueValue);---使用加上了键盘状态的消息，emit sigItemClicked(_uniqueValue, modState);以免消息发送两次
    TableWidgetItemIndex rowItemIndex;
    rowItemIndex._uniqueValue = _uniqueValue;
    rowItemIndex._parentValue = _parentValue;
    rowItemIndex._type = _rowItemType;
    emit sigItemClicked(rowItemIndex, modState);
    //emit sigItemClicked(rowItemIndex);---使用加上了键盘状态的消息，emit sigItemClicked(rowItemIndex, modState);以免消息发送两次
}

void QMTAbsTableWidgetItem::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (false == _enableSelect)
    {
        QWidget::mouseDoubleClickEvent(event);
        return;
    }

    Qt::KeyboardModifiers  modState = event == nullptr ? Qt::NoModifier : event->modifiers();
    emit sigItemDoubleClicked(_uniqueValue, modState);
    //emit sigItemDoubleClicked(_uniqueValue);---使用加上了键盘状态的消息，emit sigItemDoubleClicked(_uniqueValue, modState);以免消息发送两次
    TableWidgetItemIndex rowItemIndex;
    rowItemIndex._uniqueValue = _uniqueValue;
    rowItemIndex._parentValue = _parentValue;
    rowItemIndex._type = _rowItemType;
    emit sigItemDoubleClicked(rowItemIndex, modState);
    //emit sigItemDoubleClicked(rowItemIndex);---使用加上了键盘状态的消息，emit sigItemDoubleClicked(rowItemIndex, modState);以免消息发送两次
}


void QMTAbsTableWidgetItem::SetSelectState(bool isSelect)
{
    if (nullptr == _perRowWidgetItemParam)
    {
        qWarning() << "QMTAbsTableWidgetItem:nullptr == _perRowWidgetItemParam";
        return;
    }

    int type = _perRowWidgetItemParam->_templateWidgetType;

    if ((this->m_pPrevious[type] != this) && this->m_pPrevious[type])
    {
        this->m_pPrevious[type]->SetSelect(false);
        this->m_pPrevious[type]->setStyleSheet(_unselectSheet);
        /* this->m_pPrevious[type]->style()->unpolish(this->m_pPrevious[type]);
         this->m_pPrevious[type]->style()->polish(this->m_pPrevious[type]);*/
        this->m_pPrevious[type]->update();
    }
    else if (nullptr == this->m_pPrevious[type])
    {
        qWarning() << "QMTAbsTableWidgetItem: nullptr == this->m_pPrevious[type]";
    }

    if (this->m_pPrevious[type] != this)
    {
        this->m_pPrevious[type] = this;
        this->SetSelect(true);
        this->setStyleSheet(_selectSheet);
        // qDebug() << "SetSelectState _selectSheet:" << _selectSheet;
        this->repaint();
    }
}

void QMTAbsTableWidgetItem::SetSelect(bool isSelect)
{
    _isSelect = isSelect;
}

void QMTAbsTableWidgetItem::SetSpan(const QString& text, const QString& styleSheetStr)
{
    for (int i = 0; i < _columnWidgets.size(); ++i)
    {
        QWidget* widget = _columnWidgets.at(i);
        ((QVBoxLayout*)_contentParent->layout())->removeWidget(widget);
    }

    DeleteWidgetList(_columnWidgets);
    QCustMtLabelParam lableParam;
    lableParam._text = text;
    QWidget* createWidget = lableParam.CreateUIModule(_contentParent);
    QCustMtLabel* labelWidget = qobject_cast<QCustMtLabel*>(createWidget);
    labelWidget->GetLabel()->setAlignment(Qt::AlignLeft);
    SetCellWidget(0, createWidget);
    ResetSelect();

    if (styleSheetStr.size() > 0)
    {
        this->setStyleSheet(styleSheetStr);
    }
}

void QMTAbsTableWidgetItem::SetSelectEnable(bool enable, QString styleSheetStr)
{
    _enableSelect = enable;
    ResetSelect();

    if (styleSheetStr.size() == 0)
    {
        QString selectColor = "background-color: rgba(246,247,250,0.35);color:rgba(219, 226, 241, 0.6);";
        selectColor += "border:0px";
        styleSheetStr = "#widget_content,#widget_left,#widget_right{" + selectColor + ";}";
    }

    this->setStyleSheet(styleSheetStr);
}

void QMTAbsTableWidgetItem::HideColumn(int column, bool bHide)
{
    QWidget* widget = GetColumnWidget(column);

    if (true == bHide)
    {
        widget->hide();
    }
    else
    {
        widget->show();
    }
}

QMTAbstractCellWidget* QMTAbsTableWidgetItem::GetCellWidget(int column)
{
    if (column < 0 || column >= _columnWidgets.size())
        return NULL;

    QWidget* widget = _columnWidgets[column];
    QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(widget);
    return cellWidgetBase;
}

bool QMTAbsTableWidgetItem::GetSelect()
{
    return _isSelect;
}

void QMTAbsTableWidgetItem::ResetWidgetViewAndData()
{
    QMTAbsTableWidgetItem::ClearPreItem(_perRowWidgetItemParam->_templateWidgetType);
}

void QMTAbsTableWidgetItem::SetRowWidgetItemParam(QMTAbsRowWidgetItemParam* param)
{
    _perRowWidgetItemParam = param;
}

void QMTAbsTableWidgetItem::SetRowItemWidgetType(int type)
{
    _rowItemType = type;
}

void QMTAbsTableWidgetItem::SetUniqueValue(const QString& value)
{
    _uniqueValue = value;
}

void QMTAbsTableWidgetItem::SetParentValue(const QString& value)
{
    _parentValue = value;
}

void QMTAbsTableWidgetItem::SetBottomGridWidgetHeight(int gridHeight, int itemHeight /*= -1*/)
{
    ui->tableItemGrid->setFixedHeight(gridHeight);
    int rowHeight = itemHeight;

    if (rowHeight < 0)
    {
        rowHeight = _perRowWidgetItemParam->_rowWidgetHeight;
    }

    this->setFixedHeight(rowHeight);
}

void QMTAbsTableWidgetItem::SetCellWidget(int index, QWidget* widget)
{
    //widget->setAttribute(Qt::WA_DeleteOnClose);
    _columnWidgets.insert(index, widget);
    ((QVBoxLayout*)_contentParent->layout())->insertWidget(index, widget);
}

void QMTAbsTableWidgetItem::AddCellWidget(QWidget* cellWidget)
{
    _columnWidgets.append(cellWidget);
    ((QVBoxLayout*)_contentParent->layout())->addWidget(cellWidget);
}

void QMTAbsTableWidgetItem::AddStretch()
{
    ((QVBoxLayout*)_contentParent->layout())->addStretch();
}

QString QMTAbsTableWidgetItem::GetRowItemUniqueValue()
{
    return _uniqueValue;
}

int QMTAbsTableWidgetItem::GetCoumntIndex(QObject* obj)
{
    int ret = -1;

    for (int i = 0; i < _columnWidgets.size(); ++i)
    {
        if (obj == _columnWidgets.at(i))
        {
            ret = i;
            break;
        }
    }

    return ret;
}

QString QMTAbsTableWidgetItem::GetColumnText(int column)
{
    QString retStr;
    QWidget* widget = GetColumnWidget(column);

    if (widget == nullptr)
    {
        return QString();
    }

    QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(widget);

    if (cellWidgetBase)
    {
        retStr = cellWidgetBase->GetCurText();
    }

    return retStr;
}

QStringList QMTAbsTableWidgetItem::GetColumnTextList()
{
    int columns = _perRowWidgetItemParam->_headParam._defaultColumn;
    QStringList columnStrList;

    for (int i = 0; i < columns; ++i)
    {
        QString columnStr = GetColumnText(i);
        columnStrList << columnStr;
    }

    return columnStrList;
}

int QMTAbsTableWidgetItem::GetRowItemWidgetType()
{
    return _rowItemType;
}

QString QMTAbsTableWidgetItem::GetUniqueValue()
{
    return _uniqueValue;
}

QString QMTAbsTableWidgetItem::GetParentValue()
{
    return _parentValue;
}

int QMTAbsTableWidgetItem::GetCheckBoxState(int column)
{
    int state = Qt::Unchecked;
    QWidget* widget = GetColumnWidget(column);

    if (nullptr == widget)
        return state;

    QString className = widget->metaObject()->className();

    if (widget->inherits("QMTCheckBox"))
    {
        QMTCheckBox* comboBox = qobject_cast<QMTCheckBox*>(widget);

        if (comboBox)
        {
            state = comboBox->checkState();
        }
    }
    else if (widget->inherits("QMTCheckBoxLabel"))
    {
        QMTCheckBoxLabel* comboBox = qobject_cast<QMTCheckBoxLabel*>(widget);

        if (comboBox)
        {
            state = comboBox->GetCheckBoxState();
        }
    }

    return state;
}

