﻿#include "QCustMtLabel2.h"
#include "ui_QCustMtLabel2.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include <QMouseEvent>
#include <QPainter>
#include "CMtCoreDefine.h"
#include "MtComboBox.h"



/*********************单元组件的Param*********************************/
QCustMtLabel2Param::QCustMtLabel2Param()
{
    _cellWidgetType = DELEAGATE_QCustMtLabel;
}

QCustMtLabel2Param::~QCustMtLabel2Param()
{
}
QWidget* QCustMtLabel2Param::CreateUIModule(QWidget* parent)
{
    QCustMtLabel2* labelDot = new QCustMtLabel2(parent);
    labelDot->SetupCellWidget(*this);
    return labelDot;
}
/*****************************************************************/


/********************单元组件****************************/
QCustMtLabel2::QCustMtLabel2(QWidget* parent, bool bEnabaleDrawSquare/* = false*/)
    : QWidget(parent)
{
    ui = new Ui::QCustMtLabel2;
    ui->setupUi(this);
    this->GetLabel()->setMtType(MtLabel::myLabel1);
    this->GetLabel()->setElideMode(Qt::ElideRight);
}

QCustMtLabel2::~QCustMtLabel2()
{
    MT_DELETE(ui);
}

void QCustMtLabel2::SetupCellWidget(QCustMtLabel2Param& cellWidgetParam)
{
    if (cellWidgetParam._mtType > 0)
    {
        this->GetLabel()->setMtType((MtLabel::MtType)cellWidgetParam._mtType);
    }

    if (false == cellWidgetParam._enableDot)
    {
        this->GetLabel()->setWordWrap(cellWidgetParam._bWordWrap);
    }

    if (cellWidgetParam._showPix)
    {
        this->GetLabel()->setPixmap(QPixmap(cellWidgetParam._pixPath));
    }
    else
    {
        this->GetLabel()->setText(cellWidgetParam._text);
    }
}

bool QCustMtLabel2::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->GetLabel()->setText(text);
        return true;
    }

    return false;
}

QString QCustMtLabel2::GetCurText()
{
    return this->GetLabel()->text();
}

//void QCustMtLabel2::SetEnableEdit(bool bEdit)
//{
//    this->GetLabel()->setEnabled(bEdit);
//}

MtLabel* QCustMtLabel2::GetLabel()
{
    return ui->mtLabel;
}

QString QCustMtLabel2::GetFullString()
{
    return GetLabel()->text();
}

void QCustMtLabel2::SetEnableDot(bool bEnable)
{
    if (bEnable)
    {
        this->GetLabel()->setElideMode(Qt::ElideRight);
    }
    else
    {
        this->GetLabel()->setElideMode(Qt::ElideNone);
    }
}
