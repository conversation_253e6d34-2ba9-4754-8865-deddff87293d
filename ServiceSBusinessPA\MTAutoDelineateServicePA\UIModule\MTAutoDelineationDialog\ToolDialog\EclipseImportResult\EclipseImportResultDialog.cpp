﻿#include "EclipseImportResultDialog.h"

EclipseImportResultDialog::EclipseImportResultDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setDialogWidthAndContentHeight(560, 250); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("提示"));                     //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("关闭"));
    this->getButton(MtTemplateDialog::BtnRight2)->hide();
}

EclipseImportResultDialog::~EclipseImportResultDialog()
{
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="sketchCollectionList">[IN]勾画模板列表</param>
void EclipseImportResultDialog::init(const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& sketchCollectionList)
{
    ui.widget_table->init(sketchCollectionList);
}

/// <summary>
/// 关闭按钮
/// </summary>
void EclipseImportResultDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void EclipseImportResultDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void EclipseImportResultDialog::onBtnRight1Clicked()
{
    this->accept();
}
