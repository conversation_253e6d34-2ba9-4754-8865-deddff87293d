<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTInputDialog</class>
 <widget class="QDialog" name="QMTInputDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>466</width>
    <height>230</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>466</width>
    <height>186</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>466</width>
    <height>286</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QMTInputDialog</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true">#frame{
	background-color: rgb(56, 67, 85);
}

#QMTInputDialog {
    border: 10px;
	background-color: rgb(56,67,85);
	border-color: rgb(213, 213, 213);
}

QFrame{
	border-radius:4px;
	background-color:rgb(56, 67, 85);
}


QLineEdit {
	font-family: &quot;Effra Medium&quot;, &quot;Segoe UI Semibold&quot;, Arial;
	font-size: 14px;
	border: none;
	min-height: 38px; 
	max-height: 38px;
	padding-left: 10px;
	border-image: none;
	border-radius: 4px;
	color:rgba(74,74,74,1);
	background:rgba(219,226,241,1);
}

QPushButton{    
    border-radius: 4px;
}

#widget_line{
	background-color: rgba(219,226,241,0.1);
}

#widget{
border-radius: 4px;
border-style: outset; 
border-width: 1px;  
border-color:  rgb(146,155,170);
}

#pushButton_edit
{
	min-width:80px;
	min-height:30px;
	background-color: rgb(78,156,213);
	color: rgb(255,255,255);
}

#pushButton_edit:hover{ 
background-color: rgb(60,143,203);
}

#pushButton_close{
	min-height:16px;
	min-width:16px;
	max-height:16px;
	max-width:16px;
	border-image:url(:/AccuUIComponentImage/images/btn_close.png);
}

#pushButton_close:hover{ 
	background-color: rgba(216,216,216,0.1);
}

#pushButton_cancel{
	min-width:80px;
	min-height:30px;
	background-color: rgb(56,67,85);
	color: rgba(188,186,186,1);
	border-style: outset; 
	border-width: 1px;  
	border-color: rgba(188,186,186,0.34);
}

#pushButton_cancel:hover{ 
background-color: rgba(216,216,216,0.1);
}
#pushButton_color{
background-color: rgb(255,0,0);
}
</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QFrame" name="frame">
     <property name="frameShape">
      <enum>QFrame::StyledPanel</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>12</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>14</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>12</number>
        </property>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Minimum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>440</width>
            <height>12</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_close">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>48</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_title">
          <property name="text">
           <string>新建勾画</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Minimum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>24</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <property name="spacing">
         <number>18</number>
        </property>
        <property name="leftMargin">
         <number>48</number>
        </property>
        <property name="rightMargin">
         <number>58</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label">
          <property name="text">
           <string>名称</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLineEdit" name="lineEdit_name">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>38</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer_2">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Minimum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>19</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="spacing">
         <number>18</number>
        </property>
        <property name="leftMargin">
         <number>48</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QLabel" name="label_2">
          <property name="text">
           <string>颜色</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QWidget" name="widget" native="true">
          <property name="minimumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>26</width>
            <height>26</height>
           </size>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_6">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QPushButton" name="pushButton_color">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16</width>
               <height>16</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer_3">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Expanding</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>73</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="Line" name="widget_line">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>1</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>1</height>
         </size>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="verticalSpacer_4">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Minimum</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>12</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <spacer name="horizontalSpacer_3">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_cancel">
          <property name="text">
           <string>取消</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_4">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Minimum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>16</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="pushButton_edit">
          <property name="text">
           <string>确定</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_5">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeType">
           <enum>QSizePolicy::Minimum</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>42</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
