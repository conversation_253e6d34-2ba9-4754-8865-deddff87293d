﻿// *********************************************************************************
// <remarks>
// FileName    : EclipseImportResultTable
// Author      : zlw
// CreateTime  : 2024-04-07
// Description : Eclipse模板导入结果table列表(内嵌于: EclipseImportResultDialog Eclipse模板导入结果Dialog)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class EclipseImportResultTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum ColumnType
    {
        COL_Num = 0,
        COL_TemplateName,
        COL_TemplateStatus
    };
    EclipseImportResultTable(QWidget* parent = nullptr);
    ~EclipseImportResultTable();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="sketchCollectionList">[IN]勾画模板列表</param>
    void init(const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& sketchCollectionList);

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="noNum">[IN]编号</param>
    /// <param name="templateName">[IN]模板名称</param>
    /// <param name="status">[IN]创建状态</param>
    void addRow(const int noNum, const QString& templateName, const QString& status);

protected:
    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <param name="headList">[IN]表头文本集合</param>
    /// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);

private:

};