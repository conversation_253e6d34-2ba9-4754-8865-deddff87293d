﻿// *********************************************************************************
// <remarks>
// FileName    : EclipseTemplateTable
// Author      : zlw
// CreateTime  : 2023-11-03
// Description : Eclipse模板设置列表(适用于: ImportEclipseTemplateDialog Eclips模板设置)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include "DataDefine/InnerStruct.h"


class EclipseTemplateTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    EclipseTemplateTable(QWidget* parent = nullptr);
    ~EclipseTemplateTable();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="eclipseTemplateVec">[IN]Eclipse模板集合</param>
    void addEclipseTemplate(const QVector<ST_EclipseTemplate>& eclipseTemplateVec);

    /// <summary>
    /// 全选
    /// </summary>
    /// <param name="isCheck">[IN]true全选</param>
    void checkAll(const bool isCheck);

    /// <summary>
    /// 获取最新的Eclipse-ROI信息
    /// </summary>
    /// <returns>key-roiCode value-ST_EclipseRoi</returns>
    QMap<QString/*roiCode*/, ST_EclipseRoi> getNewEclipseRoiInfo();

    /// <summary>
    /// 获取导入的eclipse模板.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>eclipse模板.</returns>
    QMap<QString/*templateId*/, ST_EclipseTemplate> getNewEclipseTemplate();

protected slots:

protected:
    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <param name="headList">[IN]表头文本集合</param>
    /// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="num">[IN]自定义编号1,2,3... 作为列表行唯一值</param>
    /// <param name="stEclipseTemplate">[IN]Eclipse模板</param>
    void addRow(const int num, const ST_EclipseTemplate& stEclipseTemplate);

private:
    QMap<QString/*templateID*/, ST_EclipseTemplate> m_allEclipseTemplateMap;
};