﻿#include "AccuComponentUi\Header\QMTAbstractParamListWidget.h"

QMTAbstractParamListWidget::QMTAbstractParamListWidget(QWidget* parent)
    : QListWidget(parent)
{
}

QMTAbstractParamListWidget::~QMTAbstractParamListWidget()
{
    ClearDataModel();
}


void QMTAbstractParamListWidget::AddRowItem(QList<ItemParam>& itemParamList)
{
    int size = itemParamList.size();

    if (0 == size)
        return;

    for (int i = 0; i < size; i++)
    {
        ItemParam param;
        ItemParam param_2;
        ItemParam param_3;
        bool hide = false;
        bool hide_2 = true;
        bool hide_3 = true;
        ////
        param = itemParamList.at(i);

        if (++i < size)
        {
            hide_2 = false;
            param_2 = itemParamList.at(i);

            if (++i < size)
            {
                hide_3 = false;
                param_3 = itemParamList.at(i);
            }
        }

        QMTParamItemWidget* paramItemWidget = new QMTParamItemWidget(param, hide, param_2, hide_2, param_3, hide_3, this);
        QListWidgetItem* pQListWidgetItem = new QListWidgetItem(this);
        QSize size = pQListWidgetItem->sizeHint();
        //pQListWidgetItem->setSizeHint(QSize(size.width(), 35));
        setItemWidget(pQListWidgetItem, paramItemWidget);
    }
}

void QMTAbstractParamListWidget::AddRowItem(QString key1, QString value1, QString key2, QString value2, QString key3, QString value3)
{
    ItemParam param;
    ItemParam param_2;
    ItemParam param_3;
    bool hide = false;
    bool hide_2 = true;
    bool hide_3 = true;

    if (key1.size() == 0)
        return;

    if (key1.size() > 0)
    {
        param._key = key1;
        param._value = value1;
    }

    if (key2.size() > 0)
    {
        hide_2 = false;
        param_2._key = key2;
        param_2._value = value2;
    }

    if (key3.size() > 0)
    {
        hide_3 = false;
        param_3._key = key3;
        param_3._value = value3;
    }

    QMTParamItemWidget* paramItemWidget = new QMTParamItemWidget(param, hide, param_2, hide_2, param_3, hide_3, this);
    QListWidgetItem* pQListWidgetItem = new QListWidgetItem(this);
    QSize size = pQListWidgetItem->sizeHint();
    pQListWidgetItem->setSizeHint(QSize(size.width(), 35));
    setItemWidget(pQListWidgetItem, paramItemWidget);
}

void QMTAbstractParamListWidget::ClearDataModel()
{
    for (int i = this->count() - 1; i >= 0; i--)
    {
        QListWidgetItem* listWidgetItem = this->takeItem(i);
        QWidget* widget = this->itemWidget(listWidgetItem);
        delete listWidgetItem;
        delete widget;
    }
}