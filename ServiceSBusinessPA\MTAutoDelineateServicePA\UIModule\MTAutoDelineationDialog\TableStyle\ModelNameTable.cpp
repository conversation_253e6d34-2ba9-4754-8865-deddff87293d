﻿#include "ModelNameTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLineEdit.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtComboBox.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTAbsHorizontalBtns.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include <QColorDialog>
#include <QPainter>
#include <QScrollBar>

/// <summary>
/// 构造函数
/// </summary>
ModelNameTable::ModelNameTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    //创建拖拽背景
    initItemDragShade();
}

ModelNameTable::~ModelNameTable()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void ModelNameTable::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 初始化
/// </summary>
void ModelNameTable::init()
{
    initTableView();
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="modelNameTableInfo">[IN]模板名称信息</param>
/// <param name="isSelect">[IN]是否选中</param>
void ModelNameTable::addRow(const ModelNameTableInfo& modelNameTableInfo, bool isSelect, bool isEmit)
{
    QMap<int/*column*/, ICellWidgetParam*> cellWidgetParamMap;
    getNewCellParamMap(modelNameTableInfo, cellWidgetParamMap);
    QString rowValue = QString::number(modelNameTableInfo.templateId);
    m_allModelNameTableInfoMap.insert(modelNameTableInfo.templateId, modelNameTableInfo);
    //this->AddRowItem(rowValue, cellWidgetParamMap);
    this->InsertRowItem(0, rowValue, cellWidgetParamMap);//20240918 4.0.5需求在首位置插入
    this->scrollToTop();

    if (isSelect == true)
    {
        this->SetCurrentRow(rowValue);
        m_preSelectRowValue = rowValue;

        if (isEmit == true)
            emit this->sigItemSelect(m_preSelectRowValue, Qt::LeftButton, QPoint());
    }
    else
    {
        this->SetCurrentRow("");
    }
}

/// <summary>
/// 隐藏一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
/// <param name="isHide">[IN]true隐藏</param>
void ModelNameTable::hideRow(const QString& rowValue, const bool isHide)
{
    this->HideRowItem(rowValue, isHide);
}

/// <summary>
/// 删除一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
void ModelNameTable::delRow(const QString& rowValue)
{
    this->DeleteRowItem(rowValue);
    this->clearSelection();
    this->SetCurrentRow("");
}

/// <summary>
/// 删除所有行
/// </summary>
void ModelNameTable::delAllRow()
{
    int rowNum = this->GetRowCount();
    QList<QString> rowValueList;
    rowValueList.reserve(rowNum + 1);

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        rowValueList.push_back(rowValue);
    }

    for (int i = 0; i < rowValueList.size(); i++)
    {
        this->DeleteRowItem(rowValueList[i]);
    }

    this->clearSelection();
    this->SetCurrentRow("");
}

/// <summary>
/// 置顶一行
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
void ModelNameTable::topRow(const QString& rowValue)
{
    if (m_allModelNameTableInfoMap.contains(rowValue.toInt()) == false)
        return;

    //获取当前行号
    int curIndex = this->GetRowIndex(rowValue);

    if (curIndex <= 0)
        return;

    ModelNameTableInfo curInfo = m_allModelNameTableInfoMap[rowValue.toInt()];
    //删除原行
    delRow(rowValue);
    //插入到首行
    this->insertRow(0, curInfo);
    this->SetCurrentRow(rowValue);
    emit this->sigSortOccurs();
}

/// <summary>
/// 清空选中
/// </summary>
void ModelNameTable::clearSelect()
{
    this->clearSelection();
    this->SetCurrentRow("");
}

/// <summary>
/// 获取当前选中的行
/// </summary>
/// <returns>rowValue</returns>
QString ModelNameTable::getCurSelectRow()
{
    return this->GetCurUniqueValue();
}

/// <summary>
/// 获取指定的行
/// </summary>
/// <param name="row">[IN]行号</param>
/// <returns>rowValue</returns>
QString ModelNameTable::getCurSelectRow(int row)
{
    return this->GetRowUniqueValue(row);;
}

/// <summary>
/// 获取当前模板id排序
/// </summary>
/// <returns>模板id排序</returns>
QList<int> ModelNameTable::getCurTemplateIdSortList()
{
    int rowNum = this->GetRowCount();
    QList<int> sortList;
    sortList.reserve(rowNum + 1);

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);

        if (rowValue.isEmpty() == false && rowValue.toInt() >= 0)
        {
            sortList.push_back(rowValue.toInt());
        }
    }

    return sortList;
}

/// <summary>
/// 是否是首位item
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
/// <returns>true是</returns>
bool ModelNameTable::isTopItem(const QString& rowValue)
{
    int curIndex = this->GetRowIndex(rowValue);
    return curIndex <= 0 ? true : false;
}

/// <summary>
/// 更新模板名称
/// </summary>
/// <param name="rowValue">[IN]行唯一标识</param>
/// <param name="templateName">[IN]模板名称</param>
void ModelNameTable::updateTemplateName(const QString& rowValue, const QString& templateName)
{
    QWidget* widget = this->GetCellWidget(rowValue, COL_ModelName);

    if (widget != nullptr)
    {
        ((QCustMtLabel*)widget)->GetLabel()->setText(templateName);
    }

    //更新内存
    if (m_allModelNameTableInfoMap.contains(rowValue.toInt()) == true)
    {
        m_allModelNameTableInfoMap[rowValue.toInt()].templateName = templateName;
    }
}

/// <summary>
/// 设置拖拽使能
/// </summary>
/// <param name="enable">[IN]true使能</param>
void ModelNameTable::setIsDropEnable(const bool enable)
{
    m_dropEnable = enable;

    if (m_dropEnable)
    {
        initItemDragShade();
    }
    else
    {
        delete m_dragItemShade;
        m_dragItemShade = nullptr;
    }
}

/// <summary>
/// 设置是否是无人值守
/// </summary>
/// <param name="rowValue">[IN]模板id</param>
/// <param name="isUnattended">[IN]true是</param>
void ModelNameTable::setIsUnattended(const QString& rowValue, const bool isUnattended)
{
    QWidget* widget = this->GetCellWidget(rowValue, COL_Unattended);

    if (widget != nullptr)
    {
        if (isUnattended == false)
            ((QCustMtLabel*)widget)->GetLabel()->clear();
        else
            ((QCustMtLabel*)widget)->GetLabel()->setPixmap(m_imagePathHash["icon_unattended"]);
    }

    //更新内存
    if (m_allModelNameTableInfoMap.contains(rowValue.toInt()) == true)
    {
        m_allModelNameTableInfoMap[rowValue.toInt()].isUnattended = isUnattended;
    }
}

/// <summary>
/// 是否是无人值守item
/// </summary>
/// <param name="rowValue">[IN]模板id</param>
/// <returns>是无人值守item</returns>
bool ModelNameTable::isUnattendedItem(const QString& rowValue)
{
    if (m_allModelNameTableInfoMap.contains(rowValue.toInt()) == true)
    {
        return m_allModelNameTableInfoMap[rowValue.toInt()].isUnattended;
    }

    return false;
}


/// <summary>
/// 初始化表格
/// </summary>
void ModelNameTable::initTableView()
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._defaultColumn = 3;
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 0;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    //临时方案，英文系统下右边距很大，不知道为啥，ui组件库里的测试demo又正常，写法一样
    /* if (CommonUtil::windowIsChineseCode() == true)
    {
     firstViewParam._headParam._columnWidthMap.insert(0, 22);
     firstViewParam._headParam._columnWidthMap.insert(1, 245);
     firstViewParam._headParam._columnWidthMap.insert(2, 22);
    }
    else
    {
     firstViewParam._headParam._columnWidthMap.insert(0, 22);
     firstViewParam._headParam._columnWidthMap.insert(1, 230);
     firstViewParam._headParam._columnWidthMap.insert(2, 22);
    }*/
    firstViewParam._headParam._columnWidthMap.insert(0, 36);
    firstViewParam._headParam._columnWidthMap.insert(1, 230);
    firstViewParam._headParam._columnWidthMap.insert(2, 22);
    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._gridColor = QColor(55, 55, 55);
    firstViewParam._canvasBackColor = QColor(0, 0, 0, 128);
    firstViewParam._rowWidgetHeight = m_rowHeight;
    firstViewParam._isHideCloumnLine = true;
    firstViewParam._showGrid = false;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

void ModelNameTable::initItemDragShade()
{
    m_dragItemShade = new QLabel(this);
    m_dragItemShade->setScaledContents(true);
    m_dragItemShade->setWindowFlags(Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::Tool);
    m_dragItemShade->setAttribute(Qt::WA_TranslucentBackground);
    m_dragItemShade->hide();
}

void ModelNameTable::MoveRow(const QString& templateId, int destRow)
{
    //删除原行
    DeleteRowItem(templateId);

    if (m_allModelNameTableInfoMap.contains(templateId.toInt()) == true)
    {
        ModelNameTableInfo curInfo = m_allModelNameTableInfoMap[templateId.toInt()];
        {
            if (destRow >= 0)
            {
                //插入到目标行
                this->insertRow(destRow, curInfo);
            }
            else
            {
                //拖拽到末尾，插入到最后面
                addRow(curInfo);
            }

            //设置当前行
            this->SetCurrentRow(templateId);
        }
        emit this->sigSortOccurs();//发生排序
    }
}

/// <summary>
/// 获取一个新的CellParamMap
/// </summary>
/// <param name="modelNameTableInfo">[IN]模板名称信息</param>
/// <returns>CellParamMap</returns>
void ModelNameTable::getNewCellParamMap(const ModelNameTableInfo& modelNameTableInfo, QMap<int/*column*/, ICellWidgetParam*>& outCellParamMap)
{
    //拖动图标
    QCustMtLabelParam* moveLabelParam = new QCustMtLabelParam();
    moveLabelParam->_showPix = true;
    moveLabelParam->_pixPath = m_imagePathHash["icon_move2"];
    outCellParamMap.insert(COL_Move, moveLabelParam);
    //模板名称
    QCustMtLabelParam* modelNameLabelParam = new QCustMtLabelParam();
    modelNameLabelParam->_text = modelNameTableInfo.templateName;
    modelNameLabelParam->_styleSheetStr = QString("font-size:13px");
    outCellParamMap.insert(COL_ModelName, modelNameLabelParam);
    //无人值守图标
    QCustMtLabelParam* unattendedLabelParam = new QCustMtLabelParam();
    unattendedLabelParam->_showPix = modelNameTableInfo.isUnattended;
    unattendedLabelParam->_pixPath = m_imagePathHash["icon_unattended"];
    outCellParamMap.insert(COL_Unattended, unattendedLabelParam);
}

/// <summary>
/// 插入行
/// </summary>
/// <param name="row">[IN]行号</param>
/// <param name="modelNameTableInfo">[IN]行信息</param>
void ModelNameTable::insertRow(const int row, ModelNameTableInfo& modelNameTableInfo)
{
    QMap<int/*column*/, ICellWidgetParam*> cellWidgetParamMap;
    getNewCellParamMap(modelNameTableInfo, cellWidgetParamMap);
    InsertRowItem(row, QString::number(modelNameTableInfo.templateId), cellWidgetParamMap);
}

/// <summary>
/// 拖拽事件
/// </summary>
/// <param name="event"></param>
void ModelNameTable::dropEvent(QDropEvent* event)
{
    //qDebug() << "IOERoiClinicalObjectivesTableWidget::dropEvent";
    ModelNameTable* sourceTable = dynamic_cast<ModelNameTable*>(event->source());
    int srcRow = sourceTable->currentRow();                        // 原行号
    int dstRow = this->rowAt(event->pos().y());                     //鼠标落点对应行号
    //解析原行的内容
    QString templateId = sourceTable->GetRowUniqueValue(srcRow);
    //删除原行
    sourceTable->DeleteRowItem(templateId);

    if (m_allModelNameTableInfoMap.contains(templateId.toInt()) == true)
    {
        ModelNameTableInfo curInfo = m_allModelNameTableInfoMap[templateId.toInt()];
        {
            if (dstRow >= 0)
            {
                //插入到目标行
                this->insertRow(dstRow, curInfo);
            }
            else
            {
                //拖拽到末尾，插入到最后面
                addRow(curInfo);
            }

            //设置当前行
            this->SetCurrentRow(templateId);
            //this->setCurrentCell(dstRow, 0);
        }
        event->accept();
        emit this->sigSortOccurs();//发生排序
    }
}

/// <summary>
/// 鼠标按下事件
/// </summary>
void ModelNameTable::mousePressEvent(QMouseEvent* event)
{
    //qDebug() << "IOERoiClinicalObjectivesTableWidget::mousePressEvent";
    int col = this->columnAt(event->pos().x());
    m_dragging = false;
    Qt::MouseButton button = event->button();

    if (button == Qt::LeftButton)
    {
        if (col == COL_Move && m_dropEnable)
        {
            m_dragging = true;
            //显示拖动阴影
            QString modelName = GetColumnText(GetRowUniqueValue(rowAt(event->pos().y())), COL_ModelName);
            QPixmap drag_img(width(), m_rowHeight);
            QMTAbsRowWidgetItemParam& param = GetPerRowItemParam();
            drag_img.fill(/*CMtCoreWidgetUtil::formatColor("rgba(@color0,0.5)")*/param._selectBackColor);
            QPainter painter(&drag_img);
            painter.setPen(param._borderColor);
            painter.drawRect(0, 0, width() - 1, m_rowHeight - 1);
            painter.setPen(CMtCoreWidgetUtil::formatColor("rgba(@color4,1)"));
            painter.drawText(QRectF(40, 0, width(), m_rowHeight), modelName, QTextOption(Qt::AlignVCenter));
            m_dragItemShade->setPixmap(drag_img);
            m_dragItemShade->move(event->globalPos());
            m_dragItemShade->show();
        }
    }

    QTableWidget::mousePressEvent(event);
    m_preSelectRowValue = this->GetCurUniqueValue();
    emit this->sigItemSelect(m_preSelectRowValue, button, event->pos());
}

/// <summary>
/// 鼠标移动事件
/// </summary>
void ModelNameTable::mouseMoveEvent(QMouseEvent* event)
{
    if (m_dragging)
    {
        //QTableWidget::mouseMoveEvent(event);
        m_dragItemShade->move(event->globalPos());
        //
        int first_visible_index = rowAt(verticalScrollBar()->value());
        int ptY = event->pos().y();

        if (ptY < m_rowHeight / 2 && first_visible_index > 0)//移到了顶部，上滚
        {
            verticalScrollBar()->setValue(--first_visible_index);
        }
        else if ((ptY > height() - horizontalHeader()->height() - m_rowHeight / 2)
                 && (rowCount() > (height() - horizontalHeader()->height()) / m_rowHeight + first_visible_index)) //移到了底部，下滚
        {
            verticalScrollBar()->setValue(++first_visible_index);
        }

        return;
    }
}

/// <summary>
/// 鼠标释放事件
/// </summary>
void ModelNameTable::mouseReleaseEvent(QMouseEvent* event)
{
    if (m_dragging)
    {
        m_dragging = false;
        m_dragItemShade->hide();
        QTableWidget::mouseReleaseEvent(event);
        //移动列表项
        QRect rect = geometry();

        //超出列表范围不进行移动
        if (event->pos().rx() < 0 || event->pos().rx() > rect.width()
            || event->pos().ry() < 0 || event->pos().ry() > rect.height())
        {
            return;
        }

        int dstRow = this->rowAt(event->pos().y());                     //鼠标落点对应行号
        MoveRow(m_preSelectRowValue, dstRow);
        return;
    }
}
