﻿#pragma once
#include <QObject>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QRadioButton>
#include <QToolButton>
#include <QTabBar>


#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif

enum eLanguage
{
    English = 0,
    Chinese = 1
};

#define ENGLISH_FONTSIZE          12
#define CHINESE_FONTSIZE          14

#define ENGLISH_SIZE "font-size:12px;"
#define CHINESE_SIZE "font-size:14px;"

#define CHINESE_SIZE_BUTTON "font-size:12px;"
#define ENGLISH_SIZE_BUTTON "font-size:10px;"

#define CHINESE_SIZE_TITLE "font-size:16px;"
#define ENGLISH_SIZE_TITLE "font-size:14px;"

class  Language
{
public:
    static int type;
    static bool customize;
    template<typename T> static int setFontSize(T* ui, const QString FontSize = "");
    static void SelectionControl(QObject* obj1, QString sFontSize);
};
/// <summary>
/// 老方式，很，非常，特别 不建议使用，如果用到的，最好考虑弃用。
/// 因为子控件自身的字体大小也会被重置掉。
/// </summary>
/// <param name="obj1"></param>
/// <param name="sFontSize"></param>
template<typename T> int Language::setFontSize(T* ui, const QString FontSize)
{
    QObjectList list = ui->children();

    if (list.length() == 0)
    {
        return 0;
    }

    QString sFontSize = CHINESE_SIZE;

    if (FontSize == "")
    {
        switch (Language::type)
        {
            case English:
                sFontSize = ENGLISH_SIZE;
                break;

            case Chinese:
                sFontSize = CHINESE_SIZE;
                break;
        }

        Language::customize = false;
    }
    else
    {
        sFontSize = FontSize;
        Language::customize = true;
    }

    foreach (QObject* obj, list)
    {
        QString className = obj->metaObject()->className();

        if (className == "SketchPage_SeriesInfo" || "QMTProcessUi" == className)
            continue;

        SelectionControl(obj, sFontSize);

        if (obj->inherits("QWidget"))
        {
            QWidget* Widget = qobject_cast<QWidget*>(obj);
            QObjectList list1 = Widget->children();

            foreach (QObject* obj1, list1)
            {
                SelectionControl(obj1, sFontSize);
            }

            setFontSize<QWidget>(Widget, sFontSize);
        }
    }

    return 0;
}



