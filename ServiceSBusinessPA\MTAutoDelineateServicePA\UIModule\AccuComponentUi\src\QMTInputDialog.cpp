﻿#include "AccuComponentUi\Header\QMTInputDialog.h"
#include <QColorDialog>
#include <QRegExpValidator>
#include <QIcon>
#include "AccuComponentUi\Header\MMessageBox.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "ui_QMTInputDialog.h"

QMTInputDialog::QMTInputDialog(QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint), ui(nullptr)
{
    ui = new Ui::QMTInputDialog;
    ui->setupUi(this);
    ui->label_title->setProperty(QssPropertyKey, QssPropertyLabelMainTitle);
    ui->label->setProperty(QssPropertyKey, QssLabelSecondTitlComment);
    ui->label_2->setProperty(QssPropertyKey, QssLabelSecondTitlComment);
    this->setAttribute(Qt::WA_TranslucentBackground);
    {
        ui->label_2->hide();
        ui->widget->hide();
    }
    _ok = false;
    _bExec = false;
    _rgb[0] = 255;
    _rgb[1] = _rgb[2] = 0;
    _ROINameFormat = 3;
    QRegExp regExp("[A-Za-z0-9-_\\s]*");
    ui->lineEdit_name->setValidator(new QRegExpValidator(regExp, ui->lineEdit_name));
    connect(ui->pushButton_close, SIGNAL(clicked()), this, SLOT(on_pushButton_cancel_clicked()));
    connect(ui->lineEdit_name, SIGNAL(editingFinished()), this, SLOT(slotROINameFormat()));
    ui->lineEdit_name->setFocus();
    Language::setFontSize<QMTInputDialog>(this);
}

QMTInputDialog::~QMTInputDialog()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QMTInputDialog::EnableChangeColor(bool enable)
{
    if (enable)
    {
        ui->label_2->show();
        ui->widget->show();
    }
    else
    {
        ui->label_2->hide();
        ui->widget->hide();
    }
}

void QMTInputDialog::SetValidator(QString validator)
{
    QRegExp regExp(validator);
    ui->lineEdit_name->setValidator(new QRegExpValidator(regExp, ui->lineEdit_name));
}

void QMTInputDialog::SetValidator(const QValidator* validator)
{
    ui->lineEdit_name->setValidator(validator);
}

void QMTInputDialog::SetDialogTitle(QString text)
{
    ui->label_title->setText(text);
}

void QMTInputDialog::SetNameText(QString text)
{
    ui->label->setText(text);
}

void QMTInputDialog::SetTextValue(const QString& value)
{
    ui->lineEdit_name->setText(value);
}

void QMTInputDialog::setRoi(QString name, int* color)
{
    QString sheet = "background-color: rgb(" + QString::number(color[0]) + "," +
        QString::number(color[1]) + "," + QString::number(color[2]) + ");";
    ui->pushButton_color->setStyleSheet(sheet);
    _rgb[0] = color[0];
    _rgb[1] = color[1];
    _rgb[2] = color[2];
    ui->lineEdit_name->setText(name);
    _name = name;
    ui->pushButton_edit->setText(tr("修改"));
}

void QMTInputDialog::on_pushButton_color_clicked()
{
    QColorDialog colorDialog;
    colorDialog.setCurrentColor(QColor(_rgb[0], _rgb[1], _rgb[2]));
    colorDialog.setWindowIcon(QIcon(":/AccuUIComponentImage/images/logo.png"));
    // colorDialog.setWindowFlags(Qt::FramelessWindowHint);
    colorDialog.setStyleSheet("*{color:#000000}");

    if (colorDialog.exec() == QDialog::Accepted)
    {
        QColor color = colorDialog.selectedColor();
        //QString sheet = "background-color: rgb(" + QString::number(color.red()) + "," +
        //                QString::number(color.green()) + "," + QString::number(color.blue()) + ");";
        //ui->pushButton_color->setStyleSheet(sheet);
        ui->pushButton_color->setStyleSheet("background-color:" + colorDialog.selectedColor().name() + ";");
        _rgb[0] = color.red();
        _rgb[1] = color.green();
        _rgb[2] = color.blue();
    }
}

void QMTInputDialog::on_pushButton_edit_clicked()
{
    _name = ui->lineEdit_name->text();

    if (_name.length() == 0)
    {
        this->hide();
        MMessageBox::ShowCancel(tr("名称不允许为空！"), tr("确认"), true, false);
        return;
    }

    _ok = true;
    _bExec = true;
    //this->close();
    this->accept();
}

void QMTInputDialog::on_pushButton_cancel_clicked()
{
    _bExec = true;
    this->close();
}

QString QMTInputDialog::NameValue()
{
    return _name;
}
QColor QMTInputDialog::GetColor()
{
    QColor color(_rgb[0], _rgb[1], _rgb[2]);
    return color;
}

int QMTInputDialog::exec()
{
    int iResult = 0;
    _bExec = false;

    while (!_bExec)
    {
        iResult = QDialog::exec();
    }

    return iResult;
}