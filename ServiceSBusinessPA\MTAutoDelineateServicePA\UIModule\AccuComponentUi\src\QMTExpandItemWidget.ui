<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTExpandItemWidget</class>
 <widget class="QWidget" name="QMTExpandItemWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>878</width>
    <height>77</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTExpandItemWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
background:rgba(44, 50, 61,1);
}
</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel" name="label_marginleft">
        <property name="minimumSize">
         <size>
          <width>2</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_content" native="true">
        <property name="styleSheet">
         <string notr="true">QPushButton{
	border:none;
}


#pushButton_expand{
	background-image:url(:/AccuUIComponentImage/images/btn_order.png);}
#pushButton_expand:hover{
    background-image:url(:/AccuUIComponentImage/images/btn_order_hover.png);}

#pushButton_close{
	background-image:url(:/AccuUIComponentImage/images/btn_close.png);}
#pushButton_close:hover{
    background-image:url(:/AccuUIComponentImage/images/btn_close.png);}

</string>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_3">
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QLabel" name="label_marginright">
        <property name="minimumSize">
         <size>
          <width>6</width>
          <height>0</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
