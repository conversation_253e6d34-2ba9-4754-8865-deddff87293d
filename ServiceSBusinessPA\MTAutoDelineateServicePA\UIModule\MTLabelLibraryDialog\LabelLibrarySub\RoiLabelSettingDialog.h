﻿// *********************************************************************************
// <remarks>
// FileName    : RoiLabelSettingDialog
// Author      : zlw
// CreateTime  : 2023-11-03
// Description : RoiLabel创建弹窗
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_RoiLabelSettingDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class RoiLabelSettingDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="roiTypeList">[IN]roiType集合(为空则采用内置类型集合)</param>
    /// <param name="allRoiLibraryMap">[IN]已存在的manteiaRoiLabel集合(key-manteiaRoiLabel)</param>
    RoiLabelSettingDialog(const QStringList& roiTypeList, const QMap<QString, n_mtautodelineationdialog::ST_RoiLabelInfo>& allRoiLibraryMap, QWidget* parent = nullptr);
    ~RoiLabelSettingDialog();

    /// <summary>
    /// 获取最新的manteiaRoiLabel信息
    /// </summary>
    /// <param name="isPreDelete">[OUT]新增的这个之前是否是被删除的</param>
    void getNewRoiLabelInfo(QString& manteiaRoiLabel, QString& roiName, QString& roiAlias, QString& roiColor, QString& roiType, bool& isPreDelete);

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::RoiLabelSettingDialogClass ui;
    QMap<QString, n_mtautodelineationdialog::ST_RoiLabelInfo> m_allRoiLibraryMap;   //已存在的manteiaRoiLabel信息集合(key-manteiaRoiLabel-toLower())
};
