﻿// *********************************************************************************
// <remarks>
// FileName    : AutoSketchRoiWidget
// Author      : zlw
// CreateTime  : 2023-11-27
// Description : 自动勾画模板页签(内嵌于 MTAutoDelineationDialog 中)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include <QAction>
#include "ui_AutoSketchRoiWidget.h"
#include "MtMenu.h"
#include "OptSketchCollection.h"
#include "Config/DBOrganGroupInfo.h"


class AutoSketchRoiWidget : public QWidget
{
    Q_OBJECT

public:
    AutoSketchRoiWidget(QWidget* parent = nullptr);
    ~AutoSketchRoiWidget();

    /// <summary>
    /// 设置右侧roi-item的宽度
    /// </summary>
    /// <param name="widthNum">[IN]右侧roi-item的宽度</param>
    void setRoiItemWidth(const int widthNum);

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 设置边距
    /// </summary>
    void setMargin(int left = 16, int top = 16, int right = 16, int bottom = 16);

    /// <summary>
    /// 设置提前选中的页签类型
    /// \n 不设置: 默认选中1-选择ROI进行勾画
    /// </summary>
    /// <param name="pageType">页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
    void setSelectRadioPageType(const int pageType);

    /// <summary>
    /// 设置需提前选中的模板id
    /// </summary>
    /// <param name="templateId">模板id</param>
    void setSelectTemplateId(const int templateId);

    /// <summary>
    /// 设置虚拟模板
    /// \n用于=选择ROI进行勾画=页签进行器官提前选中
    /// </summary>
    void setVirtualSketchCollection(n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection);

    /// <summary>
    /// 初始化数据
    /// </summary>
    /// <param name="ptrOptSketchCollection">[IN]自动勾画模板信息</param>
    /// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
    void initData(OptSketchCollection* ptrOptSketchCollection, n_mtautodelineationdialog::ST_CallBack_AutoSketch& stCallBackAutoSketch);

    /// <summary>
    /// 重新初始化显示数据，将清空页面
    /// </summary>
    void reInitData();

    /// <summary>
    /// 获取选中的模板
    /// 如果是选择模板进行勾画页签-编辑模式下直接点击确定，则返回一个id为 -99 的临时模板
    /// 如果是选择ROI进行勾画页签-编辑模式下直接点击确定， 则返回一个id为 -98 的临时模板
    /// </summary>
    /// <returns>选中的模板</returns>
    n_mtautodelineationdialog::ST_SketchModelCollection getSelectSketchCollection();

    /// <summary>
    /// 获取最新的虚拟模板
    /// \n用于*选择ROI进行勾画*页签进行器官提前选中
    /// </summary>
    /// <returns>最新的虚拟模板</returns>
    n_mtautodelineationdialog::ST_SketchModelCollection getVirtualSketchCollection();

    /// <summary>
    /// 是否处于编辑状态
    /// </summary>
    /// <returns>true是</returns>
    bool isEditState();

signals:
    void sigWidgetLeftEnable(const bool isEnable); //左侧界面使能
    void sigEditTemplateStart(const bool isStart); //是否处于编辑模式
    /// <summary>
    /// 新增一条模板信号
    /// </summary>
    void SigAddOneNewTemplate(int newTemplateId, const QString& templateName, bool isUnattended);

protected slots: //按键相关
    /// <summary>
    /// 展开按钮
    /// </summary>
    void onMtToolButton_allTreeOpt();

    /// <summary>
    /// 清空选中按钮
    /// </summary>
    void onMtToolButton_cleanAllCheck();

    /// <summary>
    /// 搜索框文本变化
    /// </summary>
    void onMtLineEditTextChanged(const QString& text);

    /// <summary>
    /// 右侧roi搜索框文本变化
    /// </summary>
    void onMtLineEditTextChanged_ROI(const QString& text);

    /// <summary>
    /// 右侧roi搜索框清空
    /// </summary>
    void onRoiLineEditCleanAciontTriggered(bool checked);

    /// <summary>
    /// 右侧roi搜索框清空
    /// </summary>
    void onRoiLineEditCleanAciontTriggered2(bool checked);

    /// <summary>
    /// 取消编辑
    /// </summary>
    void onMPushButton_cancelEdit();

    /// <summary>
    /// 另存
    /// </summary>
    void onMtmtPushButton_saveas();

    /// <summary>
    /// 保存为模板
    /// </summary>
    void onMtPushButton_saveRoiSelect();

protected slots: //信号相关
    /// <summary>
    /// 鼠标右键菜单
    /// </summary>
    void slotRightMenu();

    /// <summary>
    /// 左侧列表item选中
    /// </summary>
    void slotLeftTableItemSelect(const QString rowValue, Qt::MouseButton button, QPoint point);

    /// <summary>
    /// 左侧列表发生排序
    /// </summary>
    void slotLeftTableSortOccurs();

    /// <summary>
    /// 响应OptSketchCollection拖拽使能信号
    /// </summary>
    /// <param name="enable">true使能</param>
    void slotTableNameDropEnable(const bool enable);

protected slots: //GroupItemListWidget相关
    /// <summary>
    /// item点击(itemId-器官id),来源于GroupItemListWidget
    /// </summary>
    /// <param name="groupId">[IN]器官分组id</param>
    /// <param name="itemIdSet">[IN]器官organId集合(key-organId)</param>
    /// <param name="checked">true使能</param>
    void slotOneItemCheckFromGroupItemListWidget(const int groupId, const QSet<int> itemIdSet, const bool checked);

    /// <summary>
    /// 是否全部展开/收起
    /// </summary>
    /// <param name="pageTypeEnum">[IN]页面类型</param>
    /// <param name="isExpand">[IN]是否全部展开</param>
    void slotAllItemExpandFromGroupItemListWidget(const GroupHTitle::EM_PageType pageTypeEnum, const bool isExpand);

protected:
    /// <summary>
    /// 输入是否正常
    /// </summary>
    /// <returns>true正常</returns>
    bool isInputNormal();

    /// <summary>
    /// 是否存在该模板id
    /// </summary>
    /// <param name="rowValue">[IN]模板id</param>
    /// <returns>true存在</returns>
    bool isExistTemplateId(const QString& rowValue);

    /// <summary>
    /// 是否可操作模板
    /// </summary>
    /// <param name="optTypeEnum">[IN]操作类型</param>
    /// <param name="templateId">[IN]模板id</param>
    /// <returns>true可操作</returns>
    bool isCanOptTemplate(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const int templateId);

    /// <summary>
    /// 设置左侧栏使能
    /// </summary>
    /// <param name="isEnable">[IN]true使能</param>
    void setWidgetLeftEnable(const bool isEnable);

    /// <summary>
    /// 删除左侧列表item
    /// </summary>
    /// <param name="rowValue">[IN]模板id</param>
    void delLeftTableRow(const QString rowValue);

    /// <summary>
    /// 重新初始化左侧列表item
    /// </summary>
    void reInitLeftTableRow();

    /// <summary>
    /// 清空右侧界面
    /// </summary>
    void clearRightWidget(bool stackedWidgetHidden = true, bool isReConnect = true);

    /// <summary>
    /// 获取要展示的器官集合
    /// </summary>
    /// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
    /// <param name="dcmTypeEnum">[IN]DICOM类型</param>
    /// <returns>要展示的器官集合(key-mainGroupId 亚器官已经被映射到主分组)</returns>
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> getOrganToShow(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection, n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum);

    /// <summary>
    /// 添加数据到widget_roiListWidget控件
    /// </summary>
    /// <param name="isEdit">[IN]是否是编辑模式</param>
    /// <param name="organByGroupIdMap">[IN]器官按照groupId分组(key-groupId)</param>
    /// <param name="allGroupInfoList">[IN]排序后的所有分组信息</param>
    /// <param name="subOrganTypeMap">[OUT]亚结构的信息(key-亚结构organId value-1:找到主结构 2:未找到主结构)</param>
    void addDataToRightRoiWidget(const bool isEdit, const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap, const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
                                 const QMap<int, int>& subOrganTypeMap);

    /// <summary>
    /// 更新右侧界面-不可编辑状态
    /// </summary>
    /// <param name="templateId">[IN]模板id</param>
    void updateRightWidgetNoEdit(const int templateId);

    /// <summary>
    /// 更新右侧界面-编辑状态
    /// </summary>
    /// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
    void updateRightWidgetEdit(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection);

    /// <summary>
    /// 更新右侧界面-编辑状态
    /// </summary>
    /// <param name="templateId">[IN]模板id</param>
    void updateRightWidgetEdit(const int templateId);

    /// <summary>
    /// 更新右侧界面-选择ROI进行自动勾画页签
    /// </summary>
    /// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
    void updateRightWidgetEdit_RadioSelectRoi(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection);

    /// <summary>
    /// 更新右侧界面-选择ROI进行自动勾画页签
    /// </summary>
    void updateRightWidgetEdit_RadioSelectRoi();

    /// <summary>
    /// 设置右侧上下StackWidget展示模式
    /// </summary>
    /// <param name="isRadioTemplateSelect">[IN]是否是选择模板进行勾画Radio选中</param>
    /// <param name="isEdit">[IN]是否处于编辑状态</param>
    void setRightStackWidgetShow(const bool isRadioTemplateSelect, const bool isEdit);

    /// <summary>
    /// 信号槽
    /// </summary>
    void connnectSignal(const bool isConnect);

    /// <summary>
    /// 选择ROI进行勾画选中
    /// </summary>
    void ShowAllRoi();

private:
    Ui::AutoSketchRoiWidget ui;
    int m_roiItemWidth = 300;               //右侧roi-item宽度
    QButtonGroup m_buttonGroup;             //用于MtRadioButton组
    QAction* m_clearSearchAction = nullptr; //搜索框中的图标
    QAction* m_clearSearchAction2 = nullptr; //搜索框中的图标
    bool m_showSelectRadioSelectBtn = true; //设置是否显示选择ROI进行勾画/选择模板进行勾画页签
    bool m_from_clickRadioTemplate = true;  //来源于直接点击MtRadioButton-Template
    int m_selectRadioPageType = 1;          //提前选中的页签类型 1-选择ROI进行勾画  2-选择模板进行勾画
    int m_selectTemplateId = -1;            //需提前选中的模板id
    QHash<QString, QString> m_imagePathHash;//图标map(key-name value-图片相对路径)
    OptSketchCollection* m_ptrOptSketchCollection = nullptr; //自动勾画模板信息
    n_mtautodelineationdialog::ST_CallBack_AutoSketch m_stCallBackAutoSketch; //数据回调
    n_mtautodelineationdialog::ST_SketchModelCollection m_stVirtualSketchTemplate; //虚拟模板
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> m_cacheOrganByGroupIdMap; //缓存的全部分组器官信息,每次调用updateRightWidgetEdit会刷新一次
    QMap<int/*groupId*/, DBOrganGroupInfo> m_groupInfoByGroupIdMap;
};
