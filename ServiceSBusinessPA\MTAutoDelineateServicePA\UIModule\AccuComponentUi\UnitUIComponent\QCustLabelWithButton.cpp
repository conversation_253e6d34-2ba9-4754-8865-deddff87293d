﻿#include "AccuComponentUi/Header/UnitUIComponent\QCustLabelWithButton.h"
#include "ui_QCustLabelWithButton.h"
#include "AccuComponentUi/Header/QMTUIDefine.h"
#include <qDebug>


QCustLabelWithButtonParam::QCustLabelWithButtonParam()
{
    _cellWidgetType = DELEAGATE_QCustLabelWithButton;
}

QWidget* QCustLabelWithButtonParam::CreateUIModule(QWidget* parent)
{
    QCustLabelWithButton* label = new QCustLabelWithButton(parent);
    label->SetupCellWidget(*this);
    return label;
}

/*****************************************************************/

QCustLabelWithButton::QCustLabelWithButton(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QCustLabelWithButton;
    ui->setupUi(this);
    ui->mtLabel->setMtType(MtLabel::MtType::myLabel1_80);
    connect(ui->mtToolButton, SIGNAL(clicked()), this, SLOT(SlotButtonClicked()));
}

QCustLabelWithButton::~QCustLabelWithButton()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QCustLabelWithButton::SetupCellWidget(QCustLabelWithButtonParam& cellWidgetParam)
{
    ui->mtLabel->setText(cellWidgetParam.labelText);

    if (false == cellWidgetParam.btnIconFile.isEmpty())
    {
        ui->mtToolButton->setPixmapFilename(cellWidgetParam.btnIconFile);
    }
}

QString QCustLabelWithButton::GetText()
{
    return ui->mtLabel->text();
}

bool QCustLabelWithButton::UpdateUi(const QVariant& updateData)
{
    if (updateData.canConvert<QCustLabelWithButtonParam>())
    {
        QCustLabelWithButtonParam editParam = updateData.value<QCustLabelWithButtonParam>();
        SetupCellWidget(editParam);
    }

    return true;
}

void QCustLabelWithButton::SlotButtonClicked()
{
    emit SigCustButtonClicked(0, true);
}
