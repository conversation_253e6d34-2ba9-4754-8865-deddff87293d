<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModelEditParamDialogClass</class>
 <widget class="QDialog" name="ModelEditParamDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>509</width>
    <height>390</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ModelEditParamDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_4">
   <property name="spacing">
    <number>16</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <property name="spacing">
      <number>8</number>
     </property>
     <item>
      <widget class="MtLineLabel" name="mtLineLabel">
       <property name="text">
        <string>过滤参数</string>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QFormLayout" name="formLayout">
       <property name="labelAlignment">
        <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
       </property>
       <property name="horizontalSpacing">
        <number>16</number>
       </property>
       <property name="verticalSpacing">
        <number>8</number>
       </property>
       <item row="0" column="0">
        <widget class="MtLabel" name="mtLabel">
         <property name="text">
          <string>最大连通域数量</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1_1</enum>
         </property>
        </widget>
       </item>
       <item row="0" column="1">
        <widget class="MtLineEdit" name="mtLineEdit_MaxNumCC3D">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="placeholderText">
          <string>请输入</string>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="trailingText">
          <string>个</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLineEdit::lineedit1</enum>
         </property>
        </widget>
       </item>
       <item row="1" column="0">
        <widget class="MtLabel" name="mtLabel_2">
         <property name="text">
          <string>l连通域内最小层数</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1_1</enum>
         </property>
        </widget>
       </item>
       <item row="2" column="0">
        <widget class="MtLabel" name="mtLabel_3">
         <property name="text">
          <string>连通域最小直径</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1_1</enum>
         </property>
        </widget>
       </item>
       <item row="3" column="0">
        <widget class="MtLabel" name="mtLabel_4">
         <property name="text">
          <string>连通域最小体积</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1_1</enum>
         </property>
        </widget>
       </item>
       <item row="1" column="1">
        <widget class="MtLineEdit" name="mtLineEdit_MinNumSlice">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="placeholderText">
          <string>请输入</string>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="trailingText">
          <string>层</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLineEdit::lineedit1</enum>
         </property>
        </widget>
       </item>
       <item row="2" column="1">
        <widget class="MtLineEdit" name="mtLineEdit_MinDiameter">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="placeholderText">
          <string>请输入</string>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="trailingText">
          <string>mm</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLineEdit::lineedit1</enum>
         </property>
        </widget>
       </item>
       <item row="3" column="1">
        <widget class="MtLineEdit" name="mtLineEdit_MinVolume">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>30</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>30</height>
          </size>
         </property>
         <property name="placeholderText">
          <string>请输入</string>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="trailingText">
          <string>mm³</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLineEdit::lineedit1</enum>
         </property>
        </widget>
       </item>
      </layout>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="spacing">
      <number>8</number>
     </property>
     <item>
      <widget class="MtLineLabel" name="mtLineLabel_2">
       <property name="text">
        <string>运算参数</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QWidget" name="widget_param2" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>78</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>78</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_5">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx">
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::frameEx3_30</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_7">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>16</number>
           </property>
           <property name="topMargin">
            <number>12</number>
           </property>
           <property name="rightMargin">
            <number>16</number>
           </property>
           <property name="bottomMargin">
            <number>12</number>
           </property>
           <item>
            <widget class="MtLabel" name="mtLabel_5">
             <property name="text">
              <string>隔层删除</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1_1</enum>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout">
             <property name="spacing">
              <number>16</number>
             </property>
             <item>
              <widget class="MtLabel" name="mtLabel_6">
               <property name="text">
                <string>每隔</string>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLabel::myLabel1_1</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="MtLineEdit" name="mtLineEdit_ClearX">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>30</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>请输入</string>
               </property>
               <property name="elideMode">
                <enum>Qt::ElideRight</enum>
               </property>
               <property name="trailingText">
                <string>层</string>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLineEdit::lineedit1</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="MtLabel" name="mtLabel_7">
               <property name="text">
                <string>删除</string>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLabel::myLabel1_1</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="MtLineEdit" name="mtLineEdit_ClearY">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>30</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>请输入</string>
               </property>
               <property name="elideMode">
                <enum>Qt::ElideRight</enum>
               </property>
               <property name="trailingText">
                <string>层</string>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLineEdit::lineedit1</enum>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <item>
      <widget class="QWidget" name="widget_param3" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>78</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>16777215</width>
         <height>78</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_6">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_2">
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_8">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>16</number>
           </property>
           <property name="topMargin">
            <number>12</number>
           </property>
           <property name="rightMargin">
            <number>16</number>
           </property>
           <property name="bottomMargin">
            <number>12</number>
           </property>
           <item>
            <widget class="MtLabel" name="mtLabel_8">
             <property name="text">
              <string>内缩/外扩</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1_1</enum>
             </property>
            </widget>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <property name="spacing">
              <number>16</number>
             </property>
             <item>
              <widget class="MtComboBox" name="mtComboBox_type">
               <property name="minimumSize">
                <size>
                 <width>180</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>180</width>
                 <height>30</height>
                </size>
               </property>
               <property name="elideMode">
                <enum>Qt::ElideRight</enum>
               </property>
               <property name="viewTextElideMode">
                <enum>Qt::ElideMiddle</enum>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtComboBox::combobox1</enum>
               </property>
               <item>
                <property name="text">
                 <string>内缩</string>
                </property>
               </item>
               <item>
                <property name="text">
                 <string>外扩</string>
                </property>
               </item>
              </widget>
             </item>
             <item>
              <widget class="MtLineEdit" name="mtLineEdit_DilateDist">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>30</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>30</height>
                </size>
               </property>
               <property name="placeholderText">
                <string>请输入</string>
               </property>
               <property name="elideMode">
                <enum>Qt::ElideRight</enum>
               </property>
               <property name="trailingText">
                <string>mm</string>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLineEdit::lineedit1</enum>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineLabel</class>
   <extends>QWidget</extends>
   <header>MtLineLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
