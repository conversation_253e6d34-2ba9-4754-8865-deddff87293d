﻿#pragma once
#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif

#include <QWidget>
#include <QItemDelegate>
#include <QStyleOptionViewItem>
#include <QStyledItemDelegate>
#include <QJsonArray>
#include <QMutex>
#include <QVector>
#include <QVariantHash>
#include "QMTListModel.h"
#include <QTableWidgetItem>


Q_DECLARE_METATYPE(QTableWidgetItem*)
#define TOString(key)   #key

#define TableWidgetItemTypeKey          "TableWidgetItemType"
#define TableWidgetItemKey              "tableWidgetItem_"


enum TableWidgetItemType
{
    TypeTableWidgetItem,
    TypeWidget
};

/*
此类已废弃，请使用QMTAbstractTableView
*/
//class NoFocusDelegate : public QStyledItemDelegate
//
//{
//    Q_OBJECT
//public:
//    NoFocusDelegate(QObject* parent = 0)
//    {}
//    ~NoFocusDelegate()
//    {}
//    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const;
//};
class QMTPerListViewTool
{
    // QString

};
namespace Ui
{
class QMTPerListView;
}

/// <summary>
/// 内部控件只有QTableWidget
/// 用于表格的显示
/// </summary>
class  QMTPerListView : public QWidget
{
    Q_OBJECT

public:
    QMTPerListView(QWidget* parent = Q_NULLPTR);
    ~QMTPerListView();
    void InitTableWidget();
    void InitAdjustTableWidget();
    //set
    void SetColumnCount(int columns);
    void SetKeyMap(QMap<int, QString>);
    void SetColumnWidth(int column, int width); // 设置列宽度，如果拉动，那么列会等宽（设计需求）
    bool SetHorizontalHeaderList(QStringList value);
    void SetHorizontalHeaderVisible(bool isvisible);
    void SetPerRowHeight(int);
    void SetMainKey(QString);
    void SetDataModel(QJsonArray);
    void AddRowItem(QJsonObject);
    //remove
    void RemoveRowItem(int row);
    void ClearDataModel();
    void ExportExcel(QString filename, QString sheetname);
    //update
    void ResizeHorizontalSection(int index, int size);
    //find
    int GetDefaultColumn()
    {
        return _defaultColumn;
    }
    QTableWidget* GetTableWidget(); //获取tablewidget
signals:
    void sigCurrentCellChanged(QMTListViewModelIndex, QMTListViewModelIndex);
    void sigCellClicked(QMTListViewModelIndex);
    void sigItemDoubleClicked(QMTListViewModelIndex);
private slots:
    void slotCurrentCellChanged(int row, int column, int preRow, int preColumn);
    void slotCellClicked(int row, int column);
    void slotCellDoubleClicked(int row, int column);
private:
    QVariantHash FromJsonObject(QJsonObject);
    void AddRowItemWidget(QJsonObject);//add widget
    QString GetModalityPixPath(QString, bool isReaded = false);
private:
    Ui::QMTPerListView* ui;
    QTableWidget* _tableWidget = nullptr;
    QStringList _headersList;
    int _defaultColumn = 6;
    int _perRowHeight = 76;
    QMap<int, int> _colDelegateTypeMap;//每列类型,0:tablewidgetitem,1:qpixmap,2:qwidget
    QMap<int, QString> _columnKeyMap;
    QMap<int, int> _columnWidthMap;
    QString _mainKey;
    QList<QVariantHash> _tableWidgetModelList;
    QMutex _mutex;
    bool _enableReaded = false;
};
