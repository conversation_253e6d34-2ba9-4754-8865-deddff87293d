﻿#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class ModelNameView : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum ColumnType
    {
        COL_ModelName = 0,
        COL_Modality
    };
    ModelNameView(QWidget* parent = nullptr);
    ~ModelNameView();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    void init(const QList<n_mtautodelineationdialog::ST_SketchModel>& modelInfoList);

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="modelNameViewInfo">[IN]模板名称信息</param>
    void addRow(const n_mtautodelineationdialog::ST_SketchModel& modelInfo);

    /// <summary>
    /// 删除一行
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    void delRow(const QString& rowValue);

    /// <summary>
    /// 获取当前选中的行
    /// </summary>
    /// <returns>rowValue</returns>
    QString getCurSelectRow();

    /// <summary>
    /// 删除当前行
    /// </summary>
    void delCurrentRow();

    /// <summary>
    /// 设置模板名称
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    /// <param name="templateName">[IN]模板名称</param>
    void setModelName(const QString& rowValue, const QString& templateName);

signals:
    void sigModelTableChanged(const QString& modelID);

protected:
    /// <summary>
    /// 初始化表格
    /// </summary>
    void initTableView();

    /// <summary>
    /// 获取一个新的CellParamMap
    /// </summary>
    /// <param name="modelInfo">[IN]模板名称信息</param>
    /// <param name="outCellParamMap">[OUT]CellParamMap(key-列号0开始)</param>
    void getNewCellParamMap(const n_mtautodelineationdialog::ST_SketchModel& modelInfo, QMap<int, ICellWidgetParam*>& outCellParamMap);

protected slots:
    void slotRowItemChanged(const mtuiData::TableWidgetItemIndex& indexInfo);

private:
    QMap<int, n_mtautodelineationdialog::ST_SketchModel> m_allModelInfoMap; //所有模板信息集合
    QHash<QString, QString> m_imagePathHash;    //图标map(key-name value-图片相对路径)
};