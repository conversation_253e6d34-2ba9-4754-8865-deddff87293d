﻿// *********************************************************************************
// <remarks>
// FileName    : MTRoiLibraryDialog
// Author      : zlw
// CreateTime  : 2024-01-10
// Description : 摩西设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTRoiModelDialogClass;
}

namespace n_mtautodelineationdialog
{

class MTRoiModelDialog : public  MtTemplateDialog, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    MTRoiModelDialog(QWidget* parent = nullptr);
    ~MTRoiModelDialog();

    /// <summary>
    /// 显示模型设置窗口.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="allRoiTypeList">[IN]所有的roi类型，若为空，则使用内置默认类型.</param>
    /// <param name="allLabelList">[IN]所有标签.</param>
    /// <param name="allGroupList">[IN]器官所有的组信息.</param>
    /// <param name="stOrganList">[IN]所有模型的器官信息.</param>
    /// <param name="modelInfoMap">[IN]所有模型信息.</param>
    /// <param name="modelCollectionInfoList">[IN]所有模板信息.</param>
    /// <param name="outStOrganList">[OUT]返回修改后的器官信息.</param>
    /// <param name="outModelInfoMap">[OUT]返回修改后的模型信息.</param>
    /// <param name="cb">[IN]内部事件处理回调，也可直接使用信号方式进行回调.</param>
    /// <returns>QDialog.DialogCode.</returns>
    QDialog::DialogCode showModelSettingDlg(const QStringList& allRoiTypeList, const QStringList& allLabelList
                                            , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList
                                            , const QList<ST_Organ>& stOrganList
                                            , const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
                                            , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList
                                            , QList<ST_Organ>& outStOrganList
                                            , QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& outModelInfoMap
                                            , const ST_CallBack_ROIModelSetting& cb);

signals:
    /**********************************************外部接收处理信号*************************************************/
    /*************************************也可以使用回调方式，二选一即可********************************************/
    /// <summary>
    /// 发送信号，以获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN]返回从标签库中获取的所有记录信息</param>
    void sigGetLabelLibraryInfo(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 发送信号，以获取器官ROI默认设置(根据默认名进行匹配)
    /// </summary>
    /// <param name="stOrganDefaultList">[IN]返回从获取的器官默认设置信息</param>
    void sigGetOrganDefaultInfo(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 发送信号，通知外部进行导入模型，需要进行模型导入处理
    /// </summary>
    /// <param name="modelPath">[OUT][模型文件路径</param>
    void sigModelImport(const QString& modelPath);

    /// <summary>
    /// 模型删除信号
    /// </summary>
    /// <param name="modelId">[OUT]要删除的模型ID</param>
    /// <param name="modelName">[OUT]要删除的模型名</param>
    void sigDeleteModel(const QString& modelId, const QString& modelName);

    /// <summary>
    /// 模板关联的roi取消了分组
    /// </summary>
    /// <param name="roiID">roiID</param>
    /// <param name="roiName">roi名称</param>
    /// <param name="groupID">取消的组ID</param>
    /// <param name="groupName">取消的组名</param>
    /// <param name="refModelInfoMap">关联roi分组的模板信息，当该参数为空时，表示该roi所有分组都被移除</param>
    /// <returns></returns>
    void sigRemoveRoiRelatedGroup(int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap);

    /// <summary>
    /// 模型信息保存结果.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">The model identifier.</param>
    /// <param name="modelName">Name of the model.</param>
    /// <param name="result">The result，0：success.</param>
    void sigSaveModelInfoResult(const QString& modelId, const QString& modelName, int result);

    /**************************************内部接收处理信号，由调用者发送*******************************************/
    /// <summary>
    /// 模型导入进度信号，用于更新模型导入进度条
    /// </summary>
    /// <param name="value">[IN]模型导入进度</param>
    void sigModelImportProgress(int value);

    /// <summary>
    /// 模型导入完成信号，用于更新导入进度
    /// </summary>
    /// <param name="bSuccess">[IN]导入是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelImportFinish(bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 模型导入入库完成信号，用于更新模型列表，只添加新导入的模型信息
    /// </summary>
    /// <param name="stOrganInfoVec">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    void sigModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 模型删除是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="modelId">[IN]删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    /// <param name="bSuccess">[IN]删除是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg);

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

private:
    Ui::MTRoiModelDialogClass* ui;
    QHash<QString, QString> m_imagePathHash;    //图标map(key-name value-图片相对路径)
};

}
