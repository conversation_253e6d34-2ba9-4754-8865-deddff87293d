﻿#include "SecGroupTableView.h"
#include <QApplication>
#include <QDrag>
#include <QMimeData>
#include <QPainter>
#include <QHeaderView>
#include <QScrollBar>
#include "DataDefine/InnerStruct.h"
#include "MtMessageBox.h"
#include "SecGroupItemEditWidget.h"

SecGroupTableView::SecGroupTableView(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    QString dragLineStyle = "border: 1px solid rgb(@color-gradient1-2);";
    CMtCoreWidgetUtil::formatStyleSheet(dragLineStyle);
    //
    initTableView({ tr("分组名称"), tr("关联ROI"), tr("操作") }, { 226, 172, 80 });
    //连接信号
    connect(this, &SecGroupTableView::sigCellWidgetButtonClicked, this, &SecGroupTableView::slotCellWidgetButtonClicked); //某个按键点击了
}

void SecGroupTableView::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

void SecGroupTableView::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    if (btnindex == 0)//编辑
    {
        SecGroupItemEditWidget dlg(m_organInfoList, m_modelInfoMap, SecGroupItemEditWidget::Edit, this);
        dlg.setGroupInfo(GetColumnText(cellItemIndex._uniqueValue, ColType_Name), m_groupIdOrganIdMap[cellItemIndex._uniqueValue.toInt()]);

        if (QDialog::Accepted == dlg.exec())
        {
            QString groupName;
            int organId = -1;
            dlg.getGroupInfo(groupName, organId);
            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Name, groupName);
            UpdateCellWidget(cellItemIndex._uniqueValue, ColType_RefRoi, getOrganName(organId));
            m_groupIdOrganIdMap[cellItemIndex._uniqueValue.toInt()] = organId;
        }
    }
    else if (btnindex == 1)//删除
    {
        for (const auto& organInfoItem : m_organInfoList)
        {
            int groupId = cellItemIndex._uniqueValue.toInt();

            if (organInfoItem.organGroupInfoMap.contains(groupId))
            {
                QString groupName = organInfoItem.organGroupInfoMap[groupId].name;
                MtMessageBox::NoIcon::information_Title(this, groupName + tr("该分组已被用于关联ROI，解除关联后才可以删除。"));
                return;
            }
        }

        DeleteRowItem(cellItemIndex._uniqueValue);
        m_groupIdOrganIdMap.remove(cellItemIndex._uniqueValue.toInt());
    }
}

void SecGroupTableView::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
    if (ColType_Name == column)
    {
    }
}

void SecGroupTableView::initTableList(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList
                                      , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allOrganGroupList
                                      , const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap)
{
    m_organInfoList = organInfoList;
    m_modelInfoMap = modelInfoMap;

    for (const auto& groupItem : allOrganGroupList)
    {
        if (groupItem.type != 3)
        {
            continue;
        }

        addRow(groupItem);
    }
}

bool SecGroupTableView::addNew(int newGroupId)
{
    SecGroupItemEditWidget dlg(m_organInfoList, m_modelInfoMap, SecGroupItemEditWidget::Add, this);
    dlg.setGroupInfo("", newGroupId);

    if (QDialog::Accepted == dlg.exec())
    {
        QString groupName;
        int organId = -1;
        dlg.getGroupInfo(groupName, organId);
        m_groupIdOrganIdMap[newGroupId] = organId;
        //
        n_mtautodelineationdialog::ST_OrganGroupInfo stGroupInfo = { newGroupId, 3, organId, groupName };
        addRow(stGroupInfo);
        //滚动到最下方
        verticalScrollBar()->setValue(verticalScrollBar()->maximum());
        return true;
    }

    return false;
}

QList <n_mtautodelineationdialog::ST_OrganGroupInfo> SecGroupTableView::getTableList()
{
    QList <n_mtautodelineationdialog::ST_OrganGroupInfo> retList;
    QStringList rowValueList = GetAllRowUniqueValueList();

    for (const QString& rowValue : rowValueList)
    {
        n_mtautodelineationdialog::ST_OrganGroupInfo groupItem;
        groupItem.id = rowValue.toInt();
        groupItem.name = GetColumnText(rowValue, ColType_Name);
        groupItem.type = 3;
        groupItem.refOrganId = m_groupIdOrganIdMap.contains(groupItem.id) ? m_groupIdOrganIdMap[groupItem.id] : -1;
        retList.append(groupItem);
    }

    return retList;
}

void SecGroupTableView::addRow(const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo)
{
    m_groupIdOrganIdMap[stGroupInfo.id] = stGroupInfo.refOrganId;
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //组名
    QCustMtLabelParam* nameParam = new QCustMtLabelParam();
    nameParam->_text = stGroupInfo.name;
    cellWidgetParamMap.insert(ColType_Name, nameParam);
    //关联ROI
    QCustMtLabelParam* refRoiParam = new QCustMtLabelParam();
    refRoiParam->_text = getOrganName(stGroupInfo.refOrganId);
    cellWidgetParamMap.insert(ColType_RefRoi, refRoiParam);
    //操作
    QMTAbsHorizontalBtnsParam* btnsOperation = new QMTAbsHorizontalBtnsParam();
    btnsOperation->_pixPathList.append(":/images/images/icon_edit_green.png");
    btnsOperation->_pixPathList.append(":/images/images/icon_del2_green.png");
    btnsOperation->_btnCount = 2;
    cellWidgetParamMap.insert(ColType_Operation, btnsOperation);
    this->AddRowItem(QString::number(stGroupInfo.id), cellWidgetParamMap);
}

void SecGroupTableView::insertRow(int rowIndex, const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo)
{
    m_groupIdOrganIdMap[stGroupInfo.id] = stGroupInfo.refOrganId;
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //组名
    QCustMtLabelParam* nameParam = new QCustMtLabelParam();
    nameParam->_text = stGroupInfo.name;
    cellWidgetParamMap.insert(ColType_Name, nameParam);
    //关联ROI
    QCustMtLabelParam* refRoiParam = new QCustMtLabelParam();
    refRoiParam->_text = getOrganName(stGroupInfo.refOrganId);
    cellWidgetParamMap.insert(ColType_RefRoi, refRoiParam);
    //操作
    QMTAbsHorizontalBtnsParam* btnsOperation = new QMTAbsHorizontalBtnsParam();
    btnsOperation->_pixPathList.append(":/images/images/icon_edit_green.png");
    btnsOperation->_pixPathList.append(":/images/images/icon_del2_green.png");
    cellWidgetParamMap.insert(ColType_Operation, btnsOperation);
    this->InsertRowItem(rowIndex, QString::number(stGroupInfo.id), cellWidgetParamMap);
}

QString SecGroupTableView::getOrganName(int organId)
{
    for (const auto& organItem : m_organInfoList)
    {
        if (organItem.id == organId)
        {
            return organItem.customOrganName;
        }
    }

    return "";
}