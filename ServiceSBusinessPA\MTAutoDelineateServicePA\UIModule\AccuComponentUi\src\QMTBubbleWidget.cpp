﻿#include "AccuComponentUi\Header\QMTBubbleWidget.h"
#include <QPainter>
#include <QRectF>

QMTBubbleWidget::QMTBubbleWidget(QWidget* parent) : QWidget(parent),
m_rotateAngle(0),
m_persent(0)
{
    //m_timer = new QTimer(this);
    //connect(m_timer, SIGNAL(timeout()), this, SLOT(updateArcColor()));
    // m_timer->start(500);
}

void QMTBubbleWidget::setPersent(int persent)
{
    if (persent != m_persent)
    {
        m_persent = persent;
        update();
    }
}

void QMTBubbleWidget::setValue(int value)
{
    if (value != m_value)
    {
        m_value = value;
        this->update();
    }
}


void QMTBubbleWidget::paintEvent(QPaintEvent*)
{
    QPainter p(this);
    p.setRenderHint(QPainter::Antialiasing);
    m_rotateAngle = 360 * m_persent / 100;
    int side = qMin(width(), height());
    QRectF outRect(0, 0, side, side);
    int arcWidth = m_arcSize.width();
    int arcHeight = m_arcSize.height();
    QRectF inRect(arcWidth, arcHeight, side - arcWidth * 2, side - arcHeight * 2);
    QString valueStr = QString("%1").arg(QString::number(m_value));
    //画外圆
    p.setPen(Qt::NoPen);
    p.setBrush(QBrush(m_arcColor));//p.setBrush(QBrush(QColor(97, 117, 118)));
    p.drawEllipse(outRect);
    p.setBrush(QBrush(m_arcColor));
    p.drawPie(outRect, (90 - m_rotateAngle) * 16, m_rotateAngle * 16);
#if 0
    //画遮罩
    p.setBrush(palette().window().color());
    p.drawEllipse(inRect);
#endif
    //画文字
    //p.setFont(f);
    p.setPen(QColor("#ffffff"));
    p.drawText(inRect, Qt::AlignCenter, valueStr);
}
