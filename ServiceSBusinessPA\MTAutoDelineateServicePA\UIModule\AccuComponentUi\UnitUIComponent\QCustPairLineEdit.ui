<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QCustPairLineEdit</class>
 <widget class="QWidget" name="QCustPairLineEdit">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>468</width>
    <height>23</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QCustPairLineEdit</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_lineBK" native="true">
     <property name="contextMenuPolicy">
      <enum>Qt::DefaultContextMenu</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtWarningLineEdit" name="lineEdit">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtLabel" name="label_mid">
        <property name="text">
         <string>至</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1_1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtWarningLineEdit" name="lineEdit_2">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtWarningLineEdit</class>
   <extends>QLineEdit</extends>
   <header>AccuComponentUi\Header\MtWarningLineEdit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
