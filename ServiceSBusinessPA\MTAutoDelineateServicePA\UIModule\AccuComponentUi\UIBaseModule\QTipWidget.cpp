﻿#include "AccuComponentUi\Header\QTipWidget.h"
#include<QCoreApplication>
#include <QTime>
#include <windows.h>
QTipWidget::QTipWidget(QWidget* parent)
    : QWidget(parent)
{
    setMouseTracking(true);
    m_title = "default_title";
    m_info = "default_info";
    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &QTipWidget::slotShowTips);
    m_toolTip = new CToolTip;
    show();
}

void QTipWidget::slotShowTips()
{
    POINT point;
    GetCursorPos(&point); // 获取鼠标指针位置（屏幕坐标）
    m_point.setX(point.x);
    m_point.setY(point.y);
    m_toolTip->showMessage(m_title, m_info, m_point);
    m_toolTip->show();
}

QTipWidget::~QTipWidget()
{
    delete m_toolTip;
    m_toolTip = nullptr;
}

void QTipWidget::setTipText(QString title, QString info)
{
    m_title = title;
    setTitleDisplayStatus(false);
    m_info = info;
}

void QTipWidget::setTipText(QString info)
{
    m_info = info;
    setTitleDisplayStatus(true);
}

void QTipWidget::setTitleDisplayStatus(bool isHidden)
{
    m_toolTip->setTitleDisplayStatus(isHidden);
}

void QTipWidget::setTipTitleText(QString text)
{
    m_title = text;
}


void QTipWidget::setTipInfoText(QString text)
{
    m_info = text;
}


void QTipWidget::setDelay(int msec)
{
    m_showDelay = msec;
}
void QTipWidget::setTipMaxWidth(int width)
{
    m_toolTip->setMaxWidth(width);
}

void QTipWidget::enterEvent(QEvent* event)
{
    if (false == m_timer->isActive())//避免重复调用start
    {
        m_timer->start(m_showDelay);
    }
}

void QTipWidget::leaveEvent(QEvent*)
{
    m_timer->stop();
    m_toolTip->showStop();
}

