﻿// *********************************************************************************
// <remarks>
// FileName    : ExportRuleEditWidget
// Author      : zlw
// CreateTime  : 2024-05-23
// Description : 无人值守规则界面导出信息Widget(内嵌于: UnattendSubTableEditItem)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include <QVBoxLayout>
#include "ui_ExportRuleEditWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class ExportRuleEditWidget : public QWidget
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="pageType">[IN]页签类型(0按钮 1文本)</param>
    /// <param name="addrUniqueKey">[IN]导出规则唯一标识</param>
    /// <param name="stAddr">[IN]当前导出地址</param>
    /// <param name="allRemoteServerList">[IN]所有导出地址</param>
    /// <param name="parantLayout">[IN]父控件</param>
    ExportRuleEditWidget(const int pageType,
                         const QString& addrUniqueKey,
                         const n_mtautodelineationdialog::ST_AddrSimple& stAddr,
                         QList<n_mtautodelineationdialog::ST_AddrSimple>& allRemoteServerList,
                         QVBoxLayout* parantLayout,
                         QWidget* parent = nullptr);
    ~ExportRuleEditWidget();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 更新状态-设置为默认按钮
    /// </summary>
    /// <param name="isDefault">[IN]设置为默认</param>
    void updateState_mtToolButton_def(const bool isDefault);

    /// <summary>
    /// 获取数据
    /// </summary>
    void getNewAddrInfo(n_mtautodelineationdialog::ST_AddrSimple& outStAddr, QString& outAddrUniqueKey);

    /// <summary>
    /// 获取导出规则唯一标识
    /// </summary>
    QString getAddrUniqueKey();

    /// <summary>
    /// 获取页签类型(0按钮 1文本)
    /// </summary>
    int getPageType();

signals:
    void sigCreateNewAddr(const n_mtautodelineationdialog::ST_AddrSimple stAddr); //新增了一个导出地址
    void sigDelOneAddr(const QString outAddrUniqueKey); //删除一个导出地址

protected slots:
    /// <summary>
    /// 新增规则按钮
    /// </summary>
    void onMtPushButton_add();

    /// <summary>
    /// 编辑规则按钮
    /// </summary>
    void onMtPushButton_mod();

    /// <summary>
    /// 删除规则按钮
    /// </summary>
    void onMtPushButton_del();

    /// <summary>
    /// 设置为默认按钮
    /// </summary>
    void onMtPushButton_def();

protected:
    /// <summary>
    /// 获取已经被其他导出规则使用的地址集合
    /// </summary>
    /// <returns>已经被其他导出规则使用的地址集合(key-唯一标识)</returns>
    QMap<QString, n_mtautodelineationdialog::ST_AddrSimple> getOtherUsedAddr();

    /// <summary>
    /// 是否是默认规则
    /// </summary>
    /// <returns>true是</returns>
    bool isDefaultRule();

    /// <summary>
    /// 鼠标移入事件
    /// </summary>
    void enterEvent(QEvent* event) override;

    /// <summary>
    /// 鼠标移出事件
    /// </summary>
    void leaveEvent(QEvent* event) override;

private:
    Ui::ExportRuleEditWidgetClass ui;
    int m_pageType;                 //0按钮 1文本
    QString m_addrUniqueKey;        //导出规则唯一标识
    QVBoxLayout* m_parantLayout;    //父控件
    n_mtautodelineationdialog::ST_AddrSimple m_curStAddr; //当前地址信息(实时更新)
    QList<n_mtautodelineationdialog::ST_AddrSimple> m_allRemoteServerList; //全部导出地址信息
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)
};
