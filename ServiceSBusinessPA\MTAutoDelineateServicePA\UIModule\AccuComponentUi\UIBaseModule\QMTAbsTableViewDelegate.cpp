﻿#include "QMTAbsTableViewDelegate.h"
#include <QHeaderView>
#include <QScrollBar>
#include <qDebug>
#include <QPainter>
#include <QApplication>
#include "AccuComponentUi\Header\QMTEnumDef.h"
#include "AccuComponentUi\Header\QMTTools.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "MtMessageBox.h"
#include "CMtCoreWidgetUtil.h"

using namespace mtuiData;

static QModelIndex s_preHoverIndex = QModelIndex();

QMTAbsTableViewDelegate::QMTAbsTableViewDelegate(QObject* parent /*= nullptr*/)
{
    m_drawHoverRowEnabled = false;
    m_hoveredBorderColor = CMtCoreWidgetUtil::formatColor("rgb(@colorB1)");
    m_selectBorderColor = CMtCoreWidgetUtil::formatColor("rgb(@colorB1)");
}

void QMTAbsTableViewDelegate::setDrawHoverRowEnabled(bool enabled)
{
    m_drawHoverRowEnabled = enabled;
}

void QMTAbsTableViewDelegate::setDrawSelectRowEnabled(bool enabled)
{
    m_drawSelectRowEnabled = enabled;
}

void QMTAbsTableViewDelegate::setHoveredRowForeground(const QBrush& brush)
{
    m_hoveredRowForeground = brush;
}

void QMTAbsTableViewDelegate::setHoveredRowBackground(const QBrush& brush)
{
    m_hoveredRowBackground = brush;
}

void QMTAbsTableViewDelegate::setHoveredRowBorderColor(const QColor& color)
{
    m_hoveredBorderColor = color;
    //QString tmpColorStr = CMtCoreWidgetUtil::formatColorStr(m_hoveredBorderColor, true);
    //qDebug() << "[setHoveredRowBorderColor] " << tmpColorStr;
}

void QMTAbsTableViewDelegate::setSelectRowBorderColor(const QColor& color)
{
    m_selectBorderColor = color;
}

void QMTAbsTableViewDelegate::setUnselectRowBackColor(const QColor& color)
{
    m_unselectBackColor = color;
    //QString tmpColorStr = CMtCoreWidgetUtil::formatColorStr(m_unselectBackColor, true);
    //qDebug() << "[setUnselectRowBackColor] " << tmpColorStr;
}

void QMTAbsTableViewDelegate::setCurHoverRow(int row)
{
    m_hoverRow = row;
}

void QMTAbsTableViewDelegate::setCurSelectRow(int row)
{
    m_selectRow = row;
    m_hoverRow = row;
}

void QMTAbsTableViewDelegate::setContentGridShow(bool bShow)
{
    m_contentGridShow = bShow;
}

void QMTAbsTableViewDelegate::setModelUiColumnMap(const QMap<int/*model column*/, int/*ui column*/>& modelUiColumnMap)
{
    m_modelUiColumnMap = modelUiColumnMap;
}

void QMTAbsTableViewDelegate::paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const
{
    QStyleOptionViewItem opt(option);

    if (opt.state.testFlag(QStyle::State_HasFocus))
    {
        opt.state = opt.state & ~QStyle::State_HasFocus;        //一定要有这个，不然单元格会有边框
    }

    QStyledItemDelegate::paint(painter, opt, index);

    //选中
    if (true == m_drawSelectRowEnabled && isSelectRow(opt, index) && m_selectBorderColor.isValid())
    {
        drawRowLines(painter, opt, index, m_selectBorderColor);
        return;
    }

    //悬浮
    if (true == m_drawHoverRowEnabled && isHoveredRow(opt, index) && m_hoveredBorderColor.isValid())
    {
        //draw border
        drawRowLines(painter, opt, index, m_hoveredBorderColor);
    }
    else
    {
        //未选中
        //draw border
        if (m_unselectBackColor.isValid())
        {
            drawRowLines(painter, opt, index, m_unselectBackColor);
        }
    }
}

void QMTAbsTableViewDelegate::initStyleOption(QStyleOptionViewItem* option, const QModelIndex& index) const
{
    QStyledItemDelegate::initStyleOption(option, index);

    if (!m_drawHoverRowEnabled)
    {
        return;
    }

    if (isHoveredRow(*option, index))
    {
        /*option->backgroundBrush = m_hoveredRowBackground;
        option->palette.setBrush(QPalette::Text, m_hoveredRowForeground);
        option->palette.setBrush(QPalette::Active, QPalette::Highlight, m_hoveredRowBackground);
        option->palette.setBrush(QPalette::Active, QPalette::HighlightedText, m_hoveredRowForeground);*/
    }
    else if (isSelectRow(*option, index))
    {
        /* option->backgroundBrush = CMtCoreWidgetUtil::formatColor("rgba(@colorB1,0.26)");
         option->palette.setBrush(QPalette::Text, option->backgroundBrush);
         option->palette.setBrush(QPalette::Active, QPalette::Highlight, option->backgroundBrush);
         option->palette.setBrush(QPalette::Active, QPalette::HighlightedText, option->backgroundBrush);*/
    }
    else
    {
        /* option->backgroundBrush = CMtCoreWidgetUtil::formatColor("rgba(@colorA0,0.3)");
         option->palette.setBrush(QPalette::Text, option->backgroundBrush);
         option->palette.setBrush(QPalette::Active, QPalette::Highlight, option->backgroundBrush);
         option->palette.setBrush(QPalette::Active, QPalette::HighlightedText, option->backgroundBrush);*/
    }
}

QVector<QLineF> QMTAbsTableViewDelegate::getRowLines(const QStyleOptionViewItem& option, const QModelIndex& index) const
{
    int firstColumn = 0;
    int lastColumn = index.model()->columnCount() - 1;
    const QTableView* tableView = qobject_cast<const QTableView*>(option.widget);

    if (tableView)
    {
        int columns = index.model()->columnCount();

        //找到未隐藏的第一行
        for (int i = 0; i < columns; ++i)
        {
            if (false == tableView->isColumnHidden(i))
            {
                firstColumn = i;
                break;
            }
        }

        if (m_modelUiColumnMap.contains(firstColumn))
        {
            firstColumn = m_modelUiColumnMap.value(firstColumn);
        }

        //找到未隐藏的最后一行
        for (int i = columns - 1; i >= 0; --i)
        {
            if (false == tableView->isColumnHidden(i))
            {
                lastColumn = i;
                break;
            }
        }

        if (m_modelUiColumnMap.contains(lastColumn))
        {
            lastColumn = m_modelUiColumnMap.value(lastColumn);
        }
    }

    QVector<QLineF> lines;
    lines << QLineF{ option.rect.topLeft(), option.rect.topRight() }
    << QLineF{ option.rect.bottomLeft(), option.rect.bottomRight() };

    if (index.column() == firstColumn)
    {
        QPoint first = option.rect.topLeft();
        QPoint second = option.rect.bottomLeft();

        if (m_contentGridShow)
        {
            first.setX(first.x() + 1);
            second.setX(second.x() + 1);
        }

        lines << QLineF{ first, second };
    }
    else if (index.column() == lastColumn)
    {
        lines << QLineF{ option.rect.topRight(), option.rect.bottomRight() };
    }

    return lines;
}

void QMTAbsTableViewDelegate::drawRowLines(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index, const QColor& lineColor) const
{
    if (false == index.isValid() || index.row() < 0)
    {
        return;
    }

    painter->save();
    QVector<QLineF> lines = getRowLines(option, index);
    painter->setPen(QPen(lineColor, 1));
    painter->drawLines(lines);
    painter->restore();
}

bool QMTAbsTableViewDelegate::isHoveredRow(const QStyleOptionViewItem& option, const QModelIndex& index) const
{
    bool ret = false;
    int gridWidth = 0;
    const QTableView* tableView = qobject_cast<const QTableView*>(option.widget);

    if (nullptr == tableView)
    {
        return false;
    }

    if (false == tableView->isVisible())
    {
        return false;
    }

    if (tableView)
    {
        gridWidth = tableView->showGrid() ? 1 : 0;
        /*    QModelIndex curIndex = tableView->currentIndex();

            if (curIndex.row() >= 0 && curIndex.row() == m_hoverRow)
            {
                return true;
            }*/

        if (m_hoverRow >= 0)
        {
            if (index.row() != m_hoverRow)
            {
                return false;
            }
            else
            {
                //return true;
            }
        }

        /*if (curIndex.row() == index.row())
        {
            return false;
        }*/
    }

    QPoint cursorPos = QCursor::pos();
    const QAbstractItemView* itemView = qobject_cast<const QAbstractItemView*>(option.widget);

    if (nullptr == itemView)
    {
        return false;
    }

    for (int i = 0; i < index.model()->columnCount(); ++i)
    {
        QModelIndex idx = index.model()->index(index.row(), i);

        if (itemView)
        {
            QStyleOptionViewItem o;
            o.initFrom(itemView);
            o.rect = itemView->visualRect(idx).adjusted(0, 0, gridWidth, gridWidth);

            if (o.rect.contains(itemView->viewport()->mapFromGlobal(cursorPos)))
            {
                ret = true;
                break;
            }
        }
    }

    return ret;
}

bool QMTAbsTableViewDelegate::isSelectRow(const QStyleOptionViewItem& option, const QModelIndex& index) const
{
    bool ret = false;
    int gridWidth = 0;

    if (const QTableView* tableView = qobject_cast<const QTableView*>(option.widget))
    {
        gridWidth = tableView->showGrid() ? 1 : 0;
        QModelIndex curIndex = tableView->currentIndex();

        if (curIndex.row() == index.row())
        {
            return true;
        }
    }

    return false;
}
