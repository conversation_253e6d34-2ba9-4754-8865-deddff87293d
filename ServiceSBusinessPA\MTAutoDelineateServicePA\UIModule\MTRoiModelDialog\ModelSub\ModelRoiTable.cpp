﻿#include "ModelRoiTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLineEdit.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtComboBox.h"
#include "MTRoiLibraryDialog\MultiSelectComboBox\CustMultiSelectComboBox.h"
#include "MTRoiLibraryDialog\RoiLibrarySub\GroupColumnButton.h"
#include "AccuComponentUi\Header\UnitUIComponent/MtUnitPushButtonGroup.h"
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"
#include <QColorDialog>
#include <Windows.h>
#include "MTRoiLibraryDialog\RoiParamEditor\AiModelRoiParamEditor.h"
#include <QtConcurrent/QtConcurrent>
#include "CMtLanguageUtil.h"

/// <summary>
/// 构造函数
/// </summary>
ModelRoiTable::ModelRoiTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    SetEnableDynamicCreateUi(false);        //都不需要动态创建
    connect(this, &ModelRoiTable::sigCellWidgetButtonClicked, this, &ModelRoiTable::slotCellWidgetButtonClicked); //某个按键点击了
    connect(this, &ModelRoiTable::sigCellWidgetTextChange, this, &ModelRoiTable::slotCellWidgetTextChange); //表格中的编辑框文本发生了改变
    connect(this, &ModelRoiTable::sigCreateTableRowItem, this, &ModelRoiTable::slotCreateTableRowItem, Qt::BlockingQueuedConnection);
}

ModelRoiTable::~ModelRoiTable()
{
    m_bDestroyed = true;
}

bool ModelRoiTable::isTableInitialized()
{
    return m_bInitialized;
}

void ModelRoiTable::init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
                         const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
                         const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
                         const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    n_mtautodelineationdialog::ST_OrganGroupInfo noneGroup = { -1000, 1, -1, "NONE" };
    m_allRoiTypeList = allRoiTypeList;
    m_allLabelList = allLabelList;
    m_allOrganGroupList.append(noneGroup);
    m_allOrganGroupList.append(allGroupList);
    m_modelCollectionInfoList = modelCollectionInfoList;

    for (int i = 0; i < stOrganList.size(); i++)
    {
        m_allOrganMap[stOrganList[i].id] = stOrganList[i];
    }

    initTableView(
        { tr("ROI名称"), tr("标签"), tr("器官名称"), tr("颜色"), tr("ROI类型"), tr(""), tr("ROI描述"), tr("参数") },
        { 148, 164, 130, 75, 130, 110, 170, 90 });

    if (CMtLanguageUtil::type == english)
    {
        HideColumn(ColType_ChName);
    }
}

void ModelRoiTable::refreshTable(const QString& modelID)
{
    QMap<QString/*rowValue*/, int/*rowNumber*/> rowValueNumberMap;
    int rowCount = this->GetRowCount();

    for (int i = 0; i < rowCount; ++i)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        rowValueNumberMap.insert(rowValue, i);
    }

    //
    m_curShowItemOrganMap.clear();
    //
    QMap<QString/*custName*/, n_mtautodelineationdialog::ST_Organ> newOrganMap;
    QMap<int, n_mtautodelineationdialog::ST_Organ>::const_iterator itor = m_allOrganMap.constBegin();

    for (; itor != m_allOrganMap.end(); ++itor)
    {
        const n_mtautodelineationdialog::ST_Organ& organInfo = itor.value();
        QString rowValue = QString::number(organInfo.id);
        bool bNeedShow = QString::number(organInfo.modelId) == modelID; //是否需要显示
        bool bCreated = rowValueNumberMap.contains(rowValue);           //是否已创建

        if (bNeedShow)
        {
            m_curShowItemOrganMap[itor.key()] = itor.value();           //保存编辑前的值

            if (bCreated)
            {
                setRowHidden(rowValueNumberMap[rowValue], false);
                //更新值
                UpdateCellWidget(rowValue, ColType_Name, QVariant::fromValue(organInfo.customOrganName));
                UpdateCellWidget(rowValue, ColType_Label, QVariant::fromValue(organInfo.roiLabel.isEmpty() ? "NONE" : organInfo.roiLabel));
                UpdateCellWidget(rowValue, ColType_ChName, QVariant::fromValue(organInfo.organChineseName.isEmpty() ? "-" : organInfo.organChineseName));
                UpdateCellWidget(rowValue, ColType_Color, QVariant::fromValue(QColor(QString("#") + organInfo.customColor)));
                UpdateCellWidget(rowValue, ColType_Type, QVariant::fromValue(organInfo.roiType.isEmpty() ? "NONE" : organInfo.roiType));
                UpdateCellWidget(rowValue, ColType_Desc, QVariant::fromValue(organInfo.roiDesc.isEmpty() ? "-" : organInfo.roiDesc));
                QStringList curGroupList;

                for (const auto& gItem : organInfo.organGroupInfoMap)
                {
                    curGroupList.append(gItem.name);
                }

                if (curGroupList.isEmpty())
                {
                    curGroupList.append("NONE");
                }

                UpdateCellWidget(rowValue, ColType_Group, QVariant::fromValue(curGroupList));
            }
            else
            {
                newOrganMap[organInfo.customOrganName] = organInfo;
            }
        }
        else
        {
            if (bCreated)
            {
                setRowHidden(rowValueNumberMap[rowValue], true);
            }
        }
    }

    //使用线程发消息方式创建，不然创建列表要很久
    m_bInitialized = false;
    m_bDestroyed = false;
    QEventLoop eventLoop;
    QtConcurrent::run([&]
    {
        QList<n_mtautodelineationdialog::ST_Organ>& stOrganList = newOrganMap.values();

        for (int i = 0; i < stOrganList.size() && !m_bDestroyed; ++i)
        {
            emit sigCreateTableRowItem(stOrganList[i]);
            Sleep(36);
        }
        //
        m_bInitialized = true;
        emit sigTableInitialized();
        //
        eventLoop.quit();
    });
    eventLoop.exec(QEventLoop::ExcludeUserInputEvents);
    //
    m_curModelID = modelID;
}

void ModelRoiTable::syncLabelLibraryInfo(const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec,
                                         bool bLabel, bool bRoiType, bool bColor, bool bChName,
                                         const QStringList& groupList,
                                         bool bDesc, const QString& desc)
{
    int nCount = GetRowCount();

    for (int i = 0; i < rowCount(); ++i)
    {
        QString organId = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(organId, ColType_Name);

        if (nullptr != widget && !isRowHidden(i))
        {
            int nOrganId = organId.toInt();
            QString roiNameLower = GetColumnText(organId, ColType_Name).toLower();

            for (const auto& labelItemObj : stRoiLabelInfoVec)
            {
                QStringList roiAliasNameList = labelItemObj.roiAlias.isEmpty() ? QStringList() : labelItemObj.roiAlias.toLower().split(';');

                //通过标签库中的别名进行匹配
                if (roiAliasNameList.contains(roiNameLower))
                {
                    if (bLabel)
                    {
                        UpdateCellWidget(organId, ColType_Label, QVariant::fromValue(labelItemObj.manteiaRoiLabel));
                    }

                    if (bRoiType)
                    {
                        UpdateCellWidget(organId, ColType_Type, QVariant::fromValue(labelItemObj.roiType));
                    }

                    if (bColor)
                    {
                        UpdateCellWidget(organId, ColType_Color, QVariant::fromValue(QColor(QString("#") + labelItemObj.roiColor)));
                    }

                    if (bChName)
                    {
                        UpdateCellWidget(organId, ColType_ChName, QVariant::fromValue(labelItemObj.roiChName.isEmpty() ? "-" : labelItemObj.roiChName));
                    }

                    //更新缓存信息
                    m_allOrganMap[organId.toInt()].roiLabel = labelItemObj.manteiaRoiLabel;
                    m_allOrganMap[organId.toInt()].roiType = labelItemObj.roiType;
                    m_allOrganMap[organId.toInt()].customColor = labelItemObj.roiColor;
                    m_allOrganMap[organId.toInt()].organChineseName = labelItemObj.roiChName;
                    break;
                }
            }

            if (groupList.size() != 0)
            {
                UpdateCellWidget(organId, ColType_Group, QVariant::fromValue(groupList));
                rowGroupInfoChanging(organId, groupList.join(";"));
            }

            if (bDesc)
            {
                UpdateCellWidget(organId, ColType_Desc, QVariant::fromValue(desc.isEmpty() ? "-" : desc));
                m_allOrganMap[organId.toInt()].roiDesc = desc;
            }
        }
    }
}

void ModelRoiTable::resetDefaultInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
{
    int nCount = this->GetRowCount();

    for (int i = 0; i < rowCount(); ++i)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(rowValue, ColType_Name);
        int organID = rowValue.toInt();

        if (!m_allOrganMap.contains(organID))
            continue;

        if (nullptr == widget || isRowHidden(i))
            continue;

        const QString& defaultName = m_allOrganMap[organID].defaultOrganName;

        //查找默认值
        for (const auto& defaultItem : stOrganDefaultList)
        {
            if (defaultItem.defaultOrganName == defaultName)
            {
                UpdateCellWidget(rowValue, ColType_Name, QVariant::fromValue(defaultItem.customOrganName));//20240311 zlw 用organinfo.json中原始的customOrganName填充
                UpdateCellWidget(rowValue, ColType_Label, QVariant::fromValue(defaultItem.roiLabel.isEmpty() ? "NONE" : defaultItem.roiLabel));
                UpdateCellWidget(rowValue, ColType_ChName, QVariant::fromValue(defaultItem.organChineseName.isEmpty() ? "-" : defaultItem.organChineseName));
                UpdateCellWidget(rowValue, ColType_Type, QVariant::fromValue(defaultItem.roiType.isEmpty() ? "NONE" : defaultItem.roiType));
                UpdateCellWidget(rowValue, ColType_Color, QVariant::fromValue(QColor(QString("#") + defaultItem.defaultColor)));
                UpdateCellWidget(rowValue, ColType_Desc, QVariant::fromValue(defaultItem.roiDesc.isEmpty() ? "-" : defaultItem.roiDesc));
                //更新临时缓存
                m_allOrganMap[organID].customOrganName = defaultItem.customOrganName;
                m_allOrganMap[organID].roiLabel = defaultItem.roiLabel;
                m_allOrganMap[organID].organChineseName = defaultItem.organChineseName;
                m_allOrganMap[organID].roiType = defaultItem.roiType;
                m_allOrganMap[organID].customColor = defaultItem.defaultColor;
                m_allOrganMap[organID].roiDesc = defaultItem.roiDesc;
                break;
            }
        }
    }
}

void ModelRoiTable::saveEdit()
{
    for (int organID : m_curShowItemOrganMap.keys())
    {
        m_curShowItemOrganMap[organID] = m_allOrganMap[organID];
    }
}

void ModelRoiTable::cancelEdit()
{
    for (int organID : m_curShowItemOrganMap.keys())
    {
        m_allOrganMap[organID] = m_curShowItemOrganMap[organID];
    }

    //刷新列表
    refreshTable(m_curModelID);
}

QList<n_mtautodelineationdialog::ST_Organ> ModelRoiTable::getAllOrganInfo()
{
    /*int rowNum = this->GetRowCount();

    for (int i = 0; i < rowNum; i++)
    {
        QString organId = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(organId, 0);

        if (nullptr == widget)
        {
            //ROI名称
            QString roiName = this->GetColumnText(organId, 0);
            //标签
            QString roiLabel;
            QCustMtComboBoxParam* roiLabelParam = static_cast<QCustMtComboBoxParam*>(this->GetCellWidgetParam(organId, 1));

            if (nullptr != roiLabelParam && -1 < roiLabelParam->_comboBoxIndex && roiLabelParam->_comboBoxIndex < roiLabelParam->_textList.size())
            {
                roiLabel = roiLabelParam->_textList[roiLabelParam->_comboBoxIndex];
            }

            //Roi颜色
            QString roiColor = this->GetColumnText(organId, 2);
            //Roi类型
            QString roiType;
            QCustMtComboBoxParam* roiTypeParam = static_cast<QCustMtComboBoxParam*>(this->GetCellWidgetParam(organId, 3));

            if (nullptr != roiTypeParam && -1 < roiTypeParam->_comboBoxIndex && roiTypeParam->_comboBoxIndex < roiTypeParam->_textList.size())
            {
                roiType = roiTypeParam->_textList[roiTypeParam->_comboBoxIndex];
            }

            //分组
            QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupMap;
            CustMultiSelectComboBoxParam* groupParam = static_cast<CustMultiSelectComboBoxParam*>(this->GetCellWidgetParam(organId, 4));

            if (nullptr != groupParam)
            {
                for (int index : groupParam->_comboBoxIndexList)
                {
                    organGroupMap[m_allOrganGroupList[index].id] = m_allOrganGroupList[index];
                }
            }

            //来源
            QString source = this->GetColumnText(organId, 5);
            //ROI描述
            QString desc = this->GetColumnText(organId, 6);
        }
    }*/
    //返回数据
    QList<n_mtautodelineationdialog::ST_Organ> organList;
    organList.reserve(m_allOrganMap.size() + 1);

    for (QMap<int, n_mtautodelineationdialog::ST_Organ>::iterator it = m_allOrganMap.begin(); it != m_allOrganMap.end(); it++)
    {
        organList.push_back(it.value());
    }

    return organList;
}

const QMap<int, n_mtautodelineationdialog::ST_Organ>& ModelRoiTable::getAllOrganInfoMap()
{
    return m_allOrganMap;
}

void ModelRoiTable::mergeNewOrganInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec)
{
    for (const auto& newItem : stOrganInfoVec)
    {
        if (!m_allOrganMap.contains(newItem.id))
        {
            m_allOrganMap.insert(newItem.id, newItem);
        }
    }
}

QList<n_mtautodelineationdialog::ST_OrganGroupInfo> ModelRoiTable::getAllOrganGroupInfo()
{
    return m_allOrganGroupList;
}

void ModelRoiTable::setGroupColumnBtnImage(const QString& imagePath)
{
    m_groupColBtnImagePath = imagePath;
}

void ModelRoiTable::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
    if (DELEAGATE_TYPE_User + 3 == cellType)
    {
        CustMultiSelectComboBox* custMultiCheckbox = static_cast<CustMultiSelectComboBox*>(cellWidget);

        if (nullptr != custMultiCheckbox)
        {
            connect(custMultiCheckbox->GetMtComboBox(), &n_mtautodelineationdialog::MultiSelectComboBox::sigSelectionChange, this, &ModelRoiTable::slotGroupChanged);
            connect(custMultiCheckbox->GetMtComboBox(), &n_mtautodelineationdialog::MultiSelectComboBox::sigSelectedTextChange, this, [=](const QString& editText)
            {
                mtuiData::TableWidgetItemIndex cellItemIndex;
                cellItemIndex._column = column;
                cellItemIndex._type = cellType;
                cellItemIndex._uniqueValue = rowValue;
                slotCellWidgetTextChange(cellItemIndex, editText);
            });
        }
    }
    else
    {
        QMTAbstractTableView::CreateCellWidgtFinishCallBack(rowValue, column, cellType, cellWidget);
    }
}

void ModelRoiTable::ConnectHeadWidgetSignals(int cellWidgetType, QWidget* cellWidget)
{
    if (cellWidgetType == DELEAGATE_TYPE_User + 2)
    {
        connect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotGroupButtonClicked(int, bool)));
    }
    else
    {
        QMTAbstractTableView::ConnectHeadWidgetSignals(cellWidgetType, cellWidget);
    }
}
/// <summary>
/// 初始化表格
/// </summary>
/// <param name="headList">[IN]表头文本集合</param>
/// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
void ModelRoiTable::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    GroupColumnButtonParam* orderWidgetParam = new GroupColumnButtonParam();
    orderWidgetParam->_text = tr("分组");
    orderWidgetParam->_btnPixelPath = m_groupColBtnImagePath;
    firstViewParam._headParam._headCellParamMap.insert(ColType_Group, orderWidgetParam);

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="stEmptyOrgan">[IN]Organ信息</param>
void ModelRoiTable::addRow(const n_mtautodelineationdialog::ST_Organ& stOrganInfo)
{
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //ROI名称
    QCustMtLineEditParam* lineEditParam = new QCustMtLineEditParam();
    lineEditParam->_maxLength = 64;
    lineEditParam->_text = stOrganInfo.customOrganName;
    lineEditParam->_regExpStr = RegExp_CharNumber4;
    lineEditParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Name, lineEditParam);
    //标签
    QCustMtComboBoxParam* comboBoxLabelParam = new QCustMtComboBoxParam();
    comboBoxLabelParam->_textList = m_allLabelList;
    int index = m_allLabelList.indexOf(stOrganInfo.roiLabel);
    comboBoxLabelParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxLabelParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Label, comboBoxLabelParam);
    //中文名
    QCustMtLineEditParam* lineEditParam2 = new QCustMtLineEditParam();
    lineEditParam2->_maxLength = 64;
    lineEditParam2->_text = stOrganInfo.organChineseName.isEmpty() ? "-" : stOrganInfo.organChineseName;
    lineEditParam2->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_ChName, lineEditParam2);
    //Roi颜色
    QMTAbsHorizontalBtnsParam* btnsParam = new QMTAbsHorizontalBtnsParam();
    btnsParam->_pixPathList.clear();
    btnsParam->_width = 58;
    btnsParam->_height = 16;
    btnsParam->_btnBackgroundColor = QColor(QString("#") + stOrganInfo.customColor);
    btnsParam->_text = "";
    cellWidgetParamMap.insert(ColType_Color, btnsParam);
    //Roi类型
    QCustMtComboBoxParam* comboBoxTypeParam = new QCustMtComboBoxParam();
    comboBoxTypeParam->_textList = m_allRoiTypeList;
    index = m_allRoiTypeList.indexOf(stOrganInfo.roiType);
    comboBoxTypeParam->_comboBoxIndex = (index < 0 ? 0 : index);
    comboBoxTypeParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Type, comboBoxTypeParam);
    //分组
    CustMultiSelectComboBoxParam* comboBoxGroupParam = new CustMultiSelectComboBoxParam();
    int indexEmpty = -1;        //记录空勾画分组
    QList<int> indexAllList;    //所有的分组索引

    for (int i = 0; i < m_allOrganGroupList.size(); ++i)
    {
        comboBoxGroupParam->_textList.append(m_allOrganGroupList[i].name);
        indexAllList.append(i);

        if (m_allOrganGroupList[i].name == tr("空勾画"))
        {
            indexEmpty = i;
        }
    }

    QList<int> indexList;
    QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = stOrganInfo.organGroupInfoMap.constBegin();

    for (; itor != stOrganInfo.organGroupInfoMap.constEnd(); ++itor)
    {
        n_mtautodelineationdialog::ST_OrganGroupInfo v = itor.value();

        for (int index = 0; index < m_allOrganGroupList.size(); ++index)
        {
            if (m_allOrganGroupList[index].id == v.id && m_allOrganGroupList[index].name == v.name)
            {
                indexList.append(index);
                break;
            }
        }
    }

    //非空勾画类型的roi，不允许添加空勾画分组；空勾画类型roi，分组信息不能编辑
    if (stOrganInfo.modelId == -1)
    {
        indexAllList.removeAt(indexEmpty);
        comboBoxGroupParam->_comboBoxDisableIndexList = indexAllList;
    }
    else
    {
        comboBoxGroupParam->_comboBoxDisableIndexList.append(indexEmpty);
    }

    comboBoxGroupParam->_comboBoxIndexList = indexList.size() == 0 ? QList<int>() << 0 : indexList;
    comboBoxGroupParam->_styleSheetStr = "MtComboBox{max-height:24px;}";
    cellWidgetParamMap.insert(ColType_Group, comboBoxGroupParam);
    //ROI描述
    QCustMtLineEditParam* lineEditDescParam = new QCustMtLineEditParam();
    lineEditDescParam->_maxLength = 32;
    lineEditDescParam->_text = stOrganInfo.roiDesc.isEmpty() ? "-" : stOrganInfo.roiDesc;
    //lineEditDescParam->_regExpStr = RegExp_CharNumber2;
    lineEditDescParam->_styleSheetStr = "MtLineEdit{max-height:26px;}";
    cellWidgetParamMap.insert(ColType_Desc, lineEditDescParam);
    //后处理参数
    MtUnitPushButtonGroupParam* btnsSettingParam = new MtUnitPushButtonGroupParam();
    btnsSettingParam->_btnTextStrList = QStringList({ tr("详情") });
    btnsSettingParam->_btnIndexMtTypeMap.insert(0, 5);
    cellWidgetParamMap.insert(ColType_Operation, btnsSettingParam);
    this->AddRowItem(QString::number(stOrganInfo.id), cellWidgetParamMap);
}

void ModelRoiTable::delRow(const QString& rowValue)
{
    DeleteRowItem(rowValue);
    m_allOrganMap.remove(rowValue.toInt());
}

bool ModelRoiTable::rowGroupInfoChanging(const QString& rowValue, const QString& newGroupStr)
{
    int organID = rowValue.toInt();
    QStringList newGroupList = newGroupStr.split(';');

    if (newGroupStr.isEmpty())
    {
        newGroupList.clear();
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = m_allOrganMap[organID].organGroupInfoMap.constBegin();

        for (; itor != m_allOrganMap[organID].organGroupInfoMap.constEnd(); ++itor)
        {
            newGroupList.append(itor.value().name);
        }

        if (newGroupList.isEmpty())
        {
            newGroupList.append("NONE");
        }

        UpdateCellWidget(rowValue, ColType_Group, QVariant::fromValue(newGroupList));
        QMap<int/*modelID*/, QString/*modelName*/> refModelInfoMap;
        emit sigRemoveRoiRelatedGroup(organID, GetColumnText(rowValue, ColType_Name), itor.value().id, itor.value().name, refModelInfoMap);
        return false;
    }

    //检查取消的分组
    if (m_allOrganMap[organID].organGroupInfoMap.size() > newGroupList.size() || "NONE" == newGroupStr)
    {
        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = m_allOrganMap[organID].organGroupInfoMap.constBegin();

        for (; itor != m_allOrganMap[organID].organGroupInfoMap.constEnd(); ++itor)
        {
            if (newGroupList.contains(itor.value().name))
            {
                continue;
            }

            //检查模板是否引用该分组并记录模板信息
            QMap<int/*modelID*/, QString/*modelName*/> refModelInfoMap;

            for (const n_mtautodelineationdialog::ST_SketchModelCollection& modelItemInfo : m_modelCollectionInfoList)
            {
                //取消的分组在模板中被引用
                if (modelItemInfo.showGroupIdMap.contains(organID) && modelItemInfo.showGroupIdMap[organID].contains(itor.value().id))
                {
                    refModelInfoMap[modelItemInfo.id] = modelItemInfo.templateName;
                }
            }

            if (refModelInfoMap.size() > 0)
            {
                QString roiName = GetColumnText(rowValue, ColType_Name);
                emit sigRemoveRoiRelatedGroup(organID, roiName, itor.value().id, itor.value().name, refModelInfoMap);
            }

            break;
        }
    }

    m_allOrganMap[organID].organGroupInfoMap.clear();

    for (const auto& groupItem : m_allOrganGroupList)
    {
        if (newGroupList.contains(groupItem.name))
        {
            m_allOrganMap[organID].organGroupInfoMap[groupItem.id] = groupItem;
        }
    }

    return true;
}

void ModelRoiTable::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    int organId = cellItemIndex._uniqueValue.toInt();

    if (cellItemIndex._column == ColType_Color) //roi颜色按钮
    {
        QColor color = Qt::red;

        if (m_allOrganMap.contains(organId))
            color = QString("#") + m_allOrganMap[organId].customColor;

        QColor newColor = QColorDialog::getColor(color/*, this*/);//输入框样式会被父窗口影像，不设置父窗口

        if (newColor.isValid() == false || color == newColor)
            return;

        this->UpdateCellWidget(cellItemIndex._uniqueValue, ColType_Color, QVariant::fromValue(newColor));

        if (m_allOrganMap.contains(organId))
        {
            m_allOrganMap[organId].customColor = newColor.name().remove("#");
            emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_Color, m_allOrganMap[organId].customColor);
        }
    }
    else if (cellItemIndex._column == ColType_Operation && m_allOrganMap.contains(organId)) //后处理参数按钮
    {
        AiModelRoiParamEditor dlg(m_allOrganMap[organId].modelId, m_allOrganMap[organId].roiParam, this);

        if (QDialog::Accepted == dlg.exec())
        {
            m_allOrganMap[organId].roiParam = dlg.getParamInfo();
            emit sigTableInfoChanged(m_allOrganMap[organId].defaultOrganName, ColType_Operation, m_allOrganMap[organId].roiParam);
        }
    }
}

void ModelRoiTable::slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText)
{
    int organID = cellItemIndex._uniqueValue.toInt();

    if (m_allOrganMap.contains(organID))
    {
        switch (cellItemIndex._column)
        {
            case ColType_Name://ROI名称
                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(m_allOrganMap[organID].customOrganName));
                    MtMessageBox::warning(this, tr("ROI名称不能为空"));
                    return;
                }

                m_allOrganMap[organID].customOrganName = newText;
                break;

            case ColType_Label://标签
                m_allOrganMap[organID].roiLabel = newText == "NONE" ? "" : newText;
                break;

            case ColType_ChName://中文名
                m_allOrganMap[organID].organChineseName = newText == "-" ? "" : newText;

                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(QString("-")));
                }

                break;

            case ColType_Type://Roi类型
                m_allOrganMap[organID].roiType = newText == "NONE" ? "" : newText;
                break;

            case ColType_Group://分组
                if (!rowGroupInfoChanging(cellItemIndex._uniqueValue, newText))
                {
                    return;
                }

                break;

            case ColType_Desc://ROI描述
                m_allOrganMap[organID].roiDesc = newText == "-" ? "" : newText;

                if (newText.isEmpty())
                {
                    UpdateCellWidget(cellItemIndex._uniqueValue, cellItemIndex._column, QVariant::fromValue(QString("-")));
                }

                break;

            default:
                break;
        }
    }

    emit sigTableInfoChanged(m_allOrganMap[organID].defaultOrganName, cellItemIndex._column, newText);
}

void ModelRoiTable::slotGroupButtonClicked(int btnIndex, bool bChecked)
{
    MtMessageBox::NoIcon::information_Title(this, tr("分组为NONE时将不会出现在ROI库中；若当前ROI已被模板引用，将分组设为NONE则会移除模板中对应的ROI"));
}

void ModelRoiTable::slotCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo)
{
    addRow(organInfo);
    qApp->processEvents();
}

void ModelRoiTable::slotGroupChanged(int nIndex, int state, const QString& itemText)
{
    n_mtautodelineationdialog::MultiSelectComboBox* check_box = qobject_cast<n_mtautodelineationdialog::MultiSelectComboBox*>(sender());

    if (state == Qt::Checked)
    {
        if (tr("空勾画") == itemText || "NONE" == itemText)
        {
            check_box->setCurrentIndex(nIndex);
        }
        else
        {
            check_box->setItemsState(QStringList() << "NONE" << tr("空勾画"), Qt::Unchecked);
        }
    }
}