﻿// *********************************************************************************
// <remarks>
// FileName    : AutoExportDialog
// Author      : zlw
// CreateTime  : 2024-02-22
// Description : 自动导出弹窗
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_AutoExportDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class AutoExportDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    AutoExportDialog(QWidget* parent = nullptr);
    ~AutoExportDialog();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="stAddrInfo">[IN]当前使用的导出地址</param>
    /// <param name="allExportAddrList">[IN]所有导出地址信息</param>
    /// <param name="getNewRemoteScpList">[IN]获取最新的远程地址回调</param>
    void init(const n_mtautodelineationdialog::ST_AddrSimple& stAddrInfo,
              const QList<n_mtautodelineationdialog::ST_AddrSimple>& allExportAddrList,
              std::function<QList<n_mtautodelineationdialog::ST_AddrSimple>()> getNewRemoteScpListCallBack = nullptr);

    /// <summary>
    /// 获取最新的导出地址信息
    /// </summary>
    n_mtautodelineationdialog::ST_AddrSimple getNewAddrInfo();

protected slots:
    /// <summary>
    /// 添加-配置地址(远程节点)
    /// </summary>
    void onMtPushButton();

    /// <summary>
    /// 远程节点选中
    /// </summary>
    void onMtRadioButton_scp(bool checked);

    /// <summary>
    /// 远程节点下拉变化
    /// </summary>
    void onMtComboBox_scp(const QString& text);

    /// <summary>
    /// 选择共享目录
    /// </summary>
    void onBtnFile();

protected:
    /// <summary>
    /// 显示导出格式的选中内容
    /// </summary>
    /// <param name="isSelectScpRadio">[IN]是否选中SCP服务器RadioButton</param>
    /// <param name="scpServerName">[IN]远程SCP名称</param>
    void showExportFormatCombox(const bool isSelectScpRadio, const QString& scpServerName);

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::AutoExportDialogClass ui;
    n_mtautodelineationdialog::ST_AddrSimple m_oldAddrInfo; //原始导出地址
    std::function<QList<n_mtautodelineationdialog::ST_AddrSimple>()> m_getNewRemoteScpListCallBack; //获取远程服务器集合回调
};
