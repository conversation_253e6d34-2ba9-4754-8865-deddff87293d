﻿// ************************************************************
// <remarks>
// Author      : LiuHualin
// CreateTime  : 2025-09-27
// Description : 自动勾画操作服务数据定义
// </remarks>
// ************************************************************
#pragma once

#include <QObject>
#include <QString>


/*-----------------------------自动勾画请求--------------------------------------------------*/

/// <summary>
/// 服务调用平台（由哪个产品发起的）
/// </summary>
enum EM_AutoDelineatePlatform
{
    /// <summary>
    /// AccuContour
    /// </summary>
    Platform_AC = 0,
    /// <summary>
    /// Mozi
    /// </summary>
    PlatForm_MOZI,
    /// <summary>
    /// 质子
    /// </summary>
    PlatForm_Proton
};

/// <summary>
/// 勾画处理状态
/// </summary>
enum EM_DelineateStatus
{
    /// <summary>
    /// 正在处理，将返回勾画进度
    /// </summary>
    SketchStatus_Processing,
    /// <summary>
    /// 勾画成功，结束
    /// </summary>
    SketchStatus_DelineateSuccess,
    /// <summary>
    /// 勾画失败，结束
    /// </summary>
    SketchStatus_DelineateFailed,
    /// <summary>
    /// 取消成功
    /// </summary>
    SketchStatus_CancelSuccess,
    /// <summary>
    /// 取消失败
    /// </summary>
    SketchStatus_CancelFailed
};
/// <summary>
/// 自动勾画-请求
/// </summary>
struct ST_InputAutoDelineate
{
    /// <summary>
    /// 患者patientID
    /// </summary>
    QString patientID;
    /// <summary>
    /// 图像seriesUID
    /// </summary>
    QString seriesUID;
    /// <summary>
    /// 客户端配置文件路径，用于存取一些用户操作设置，如c:/manteia.ini  
    /// </summary>
    QString clientConfigPath;
    /// <summary>
    /// 指定父窗口
    /// </summary>
    QWidget* parentWidget = nullptr;
    /// <summary>
    /// 是否显示底部勾画完成后的操作栏
    /// </summary>
    bool bShowPostFuncWidget = true;
    /// <summary>
    /// 是否显示模板编辑按钮
    /// </summary>
    bool bShowTemplateBtnWidget = false;
};

/// <summary>
/// 模板设置窗口入参
/// </summary>
struct ST_InputDelineateTemplateSetting
{
    /// <summary>
    /// 左侧模板名列表宽度
    /// </summary>
    int templateNameListWidth = 360;
    /// <summary>
    /// 是否显示左侧ROI器官分组列表
    /// </summary>
    bool bShowRoiGroupList = false;
    /// <summary>
    /// 无人值守启用是是否进行提示
    /// </summary>
    bool bShowUnattendUsedTip = true;
    /// <summary>
    /// 指定父窗口
    /// </summary>
    QWidget* parentWidget = nullptr;
    /// <summary>
    /// 客户端配置文件路径，用于存取一些用户操作设置，如c:/manteia.ini，与自动勾画请求入参一致
    /// </summary>
    QString clientConfigPath;
};

/*-----------------------------自动勾画器官选择输出信息--------------------------------------------------*/
/// <summary>
/// 导出目录信息
/// </summary>
struct ST_SketchBusiness_ExportDir
{
    /// <summary>
    /// 创建目录方式（0:不创建 1:创建patientName_patientID目录 2:创建patientID目录）
    /// </summary>
    std::string create_sharedir = "0";

    /// <summary>
    /// 导出到共享目录路径
    /// </summary>
    std::string export_dir = "";

    /// <summary>
    /// 导出格式模式GUID
    /// </summary>
    std::string export_format_guid = "";
};

/// <summary>
/// 导出服务器信息
/// </summary>
struct ST_SketchBusiness_ExportServer
{
    /// <summary>
    /// 服务器名称
    /// </summary>
    std::string export_serverName = "";

    /// <summary>
    /// 服务器AE
    /// </summary>
    std::string export_aeTitle = "";

    /// <summary>
    /// 服务器地址
    /// </summary>
    std::string export_ip = "";

    /// <summary>
    /// 服务器端口号
    /// </summary>
    std::string export_port = "";

    /// <summary>
    /// 导出格式模式GUID
    /// </summary>
    std::string export_format_guid = "";
};

/// <summary>
/// 导出信息
/// </summary>
struct ST_SketchBusiness_ExportInfo
{
    /// <summary>
    /// 导出方式（0:手动 1:自动）
    /// </summary>
    std::string export_type = "0";

    /// <summary>
    /// 导出范围（0:忽略 1:当前患者全部图像数据 2:当前勾画的RT文件 3:当前勾画的序列和RT文件 4:不导出）
    /// </summary>
    std::string export_range = "0";

    /// <summary>
    /// 导出目录数组
    /// </summary>
    std::vector<ST_SketchBusiness_ExportDir> export_dir_arr;

    /// <summary>
    /// 导出服务器数组
    /// </summary>
    std::vector<ST_SketchBusiness_ExportServer> export_server_arr;
};

/// <summary>
/// 器官自定义信息
/// </summary>
struct ST_SketchBusiness_OrganCustomInfo
{
    /// <summary>
    /// 器官新名
    /// </summary>
    std::string customOrganName = "";

    /// <summary>
    /// 器官新颜色
    /// </summary>
    std::string customColor = "";

    /// <summary>
    /// ROI类型
    /// </summary>
    std::string roiType = "";
};

/// <summary>
/// 勾画器官信息
/// </summary>
struct ST_SketchBusiness_SketchOrgan
{
    /// <summary>
    /// 器官默认名
    /// </summary>
    std::string defaultOrganName = "";

    /// <summary>
    /// 器官修改信息
    /// </summary>
    ST_SketchBusiness_OrganCustomInfo custom_info;
};

/// <summary>
/// 自动勾画配置
/// </summary>
struct ST_SketchBusiness_AutoSketch
{
    /// <summary>
    /// 隐藏的器官
    /// </summary>
    std::string hide_organ = "";

    /// <summary>
    /// 勾画部位
    /// </summary>
    std::string sketch_part = "";

    /// <summary>
    /// 勾画器官数组
    /// </summary>
    std::vector<ST_SketchBusiness_SketchOrgan> sketch_organ;
};

/// <summary>
/// 训练勾画配置
/// </summary>
struct ST_SketchBusiness_TrainSketch
{
    /// <summary>
    /// 模型名（不包括后缀名.pb）
    /// </summary>
    std::string model_name = "";

    /// <summary>
    /// 模型全部器官
    /// </summary>
    std::string model_organ = "";

    /// <summary>
    /// 模型密钥
    /// </summary>
    std::string model_password = "";

    /// <summary>
    /// 勾画的器官
    /// </summary>
    std::vector<ST_SketchBusiness_SketchOrgan> sketch_organ;
};

/// <summary>
/// 添加空器官信息
/// </summary>
struct ST_SketchBusiness_AddEmptyOrgan
{
    /// <summary>
    /// 器官默认名
    /// </summary>
    std::string defaultOrganName = "";

    /// <summary>
    /// 器官默认颜色
    /// </summary>
    std::string defaultColor = "";

    /// <summary>
    /// ROI类型
    /// </summary>
    std::string roiType = "";

    /// <summary>
    /// 器官修改信息
    /// </summary>
    ST_SketchBusiness_OrganCustomInfo custom_info;
};

/// <summary>
/// DICOM标签修改信息
/// </summary>
struct ST_SketchBusiness_ModDicomTag
{
    /// <summary>
    /// DICOM标签
    /// </summary>
    std::string tag = "";

    /// <summary>
    /// 修改后的值
    /// </summary>
    std::string value = "";
};

/// <summary>
/// 勾画业务完整信息结构体
/// </summary>
struct ST_SketchBusinessInfo
{
    /// <summary>
    /// 勾画序列UID
    /// </summary>
    std::string seriesUID = "";

    /// <summary>
    /// 是否自动导出（0:不自动导出 1:勾画完自动导出）
    /// </summary>
    std::string auto_export = "1";

    /// <summary>
    /// 导出信息
    /// </summary>
    ST_SketchBusiness_ExportInfo export_info;

    /// <summary>
    /// 自动勾画配置数组
    /// </summary>
    std::vector<ST_SketchBusiness_AutoSketch> accucontour_sketch;

    /// <summary>
    /// 训练勾画配置数组
    /// </summary>
    std::vector<ST_SketchBusiness_TrainSketch> train_sketch;

    /// <summary>
    /// 添加空器官数组
    /// </summary>
    std::vector<ST_SketchBusiness_AddEmptyOrgan> add_empty_organ;

    /// <summary>
    /// 需要额外合并的RT结构SOP实例UID数组
    /// </summary>
    std::vector<std::string> merge_rt_sopInsUID_arr;
};

/// <summary>
///自动勾画-请求--器官信息
/// </summary>
struct ST_REQ_AutoSketchSuper_Organ
{
    /// <summary>
    /// 自定义颜色，未修改置空即可
    /// </summary>
    std::string customColor;
    /// <summary>
    /// 自定义名称，未修改置空即可
    /// </summary>
    std::string customOrganName;
    /// <summary>
    /// ROI类型，如果是ORGAN类型，置空即可
    /// </summary>
    std::string roiType;
    /// <summary>
    /// RoiObservationLabel(3006,0085),Manteia内部标签填充，没有置空即可
    /// </summary>
    std::string roiLabel;
    /// <summary>
    /// 后处理参数JSON，没有置空即可
    /// </summary>
    std::string roiParam;
    /// <summary>
    /// 默认名称
    /// </summary>
    std::string defaultOrganName;
    /// <summary>
    /// 是否勾画，固定填true即可
    /// </summary>
    bool isVisiable = true;
};
/// <summary>
/// 自动勾画-响应
/// </summary>
struct ST_OutputAutoDelineate
{
    /// <summary>
    /// 服务发起平台
    /// </summary>
    EM_AutoDelineatePlatform platform = Platform_AC;
    /// <summary>
    /// 勾画执行状态
    /// </summary>
    EM_DelineateStatus status = SketchStatus_Processing;
    /// <summary>
    /// 勾画生成的结果文件下载地址
    /// </summary>
    QStringList resultFileList;
    /// <summary>
    /// 发起服务的客户端id
    /// </summary>
    QString clientId;
    /// <summary>
    /// 发起服务的模块名
    /// </summary>
    QString moduleName;
    /// <summary>
    /// 患者id
    /// </summary>
    QString patientId;
    /// <summary>
    /// 勾画图像的seriesUid
    /// </summary>
    QString seriesUid;
    /// <summary>
    /// 勾画类型，1：手动发起，2：无人值守
    /// </summary>
    QString optType;
    /// <summary>
    /// 返回错误信息
    /// </summary>
    QString errMsg;
    /// <summary>
    /// 当前status为SketchStatus_Processing时，该值有效，表示勾画进度
    /// </summary>
    int progress = -1;
};