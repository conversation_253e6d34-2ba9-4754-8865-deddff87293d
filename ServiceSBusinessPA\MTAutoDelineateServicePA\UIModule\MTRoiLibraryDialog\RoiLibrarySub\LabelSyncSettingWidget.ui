<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LabelSyncSettingWidget</class>
 <widget class="QWidget" name="LabelSyncSettingWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>318</width>
    <height>40</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>650</width>
    <height>500</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QGridLayout" name="gridLayout" rowstretch="0" columnstretch="0,1">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="horizontalSpacing">
       <number>3</number>
      </property>
      <property name="verticalSpacing">
       <number>0</number>
      </property>
      <item row="0" column="1">
       <widget class="MtLabel" name="mtLabel">
        <property name="text">
         <string>勾选后，若ROI库的ROI名称、颜色、类型、器官名称发生变动，则会自动更新对应的标签信息</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1_1</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="0">
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>2</number>
        </property>
        <item>
         <widget class="MtCheckBox" name="mtCheckBox">
          <property name="text">
           <string/>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtCheckBox::checkbox1</enum>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="verticalSpacer">
          <property name="orientation">
           <enum>Qt::Vertical</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>20</width>
            <height>40</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
