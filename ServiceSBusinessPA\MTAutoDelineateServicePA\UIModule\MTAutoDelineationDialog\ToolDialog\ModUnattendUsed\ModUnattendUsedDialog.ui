<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModUnattendUsedDialogClass</class>
 <widget class="QDialog" name="ModUnattendUsedDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>489</width>
    <height>175</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ModUnattendUsedDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_2">
   <property name="spacing">
    <number>16</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="MtLabel" name="mtLabel">
       <property name="text">
        <string>MtTextLabel</string>
       </property>
       <property name="wordWrap">
        <bool>true</bool>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtLabel::myLabel1_1</enum>
       </property>
      </widget>
     </item>
     <item>
      <widget class="MtCheckBox" name="mtCheckBox">
       <property name="text">
        <string>以后不再提示</string>
       </property>
       <property name="_mtType" stdset="0">
        <enum>MtCheckBox::checkbox1</enum>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
