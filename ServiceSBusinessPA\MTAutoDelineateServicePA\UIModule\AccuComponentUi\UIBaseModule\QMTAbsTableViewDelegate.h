﻿#pragma once

#include <QHeaderView>
#include <QTableWidget>
#include <QJsonObject>
#include <QJsonArray>
#include <QItemDelegate>
#include <QStyledItemDelegate>
#include <QMutex>
#include <QMouseEvent>

/*
QMTAbsTableView样式代理类
*/
class QMTAbsTableViewDelegate : public QStyledItemDelegate
{
    Q_OBJECT
public:
    explicit QMTAbsTableViewDelegate(QObject* parent = nullptr);
    ~QMTAbsTableViewDelegate()
    {}

    void setDrawHoverRowEnabled(bool enabled);

    void setDrawSelectRowEnabled(bool enabled);

    void setHoveredRowForeground(const QBrush& brush);

    void setHoveredRowBackground(const QBrush& brush);

    void setHoveredRowBorderColor(const QColor& color);

    void setSelectRowBorderColor(const QColor& color);

    void setUnselectRowBackColor(const QColor& color);

    void setCurHoverRow(int row);

    void setCurSelectRow(int row);

    void setContentGridShow(bool bShow);        //表体是否显示了分割线

    void setModelUiColumnMap(const QMap<int/*model column*/, int/*ui column*/>& modelUiColumnMap);

protected:
    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const;

    virtual void initStyleOption(QStyleOptionViewItem* option, const QModelIndex& index) const;

private:
    QVector<QLineF> getRowLines(const QStyleOptionViewItem& option, const QModelIndex& index)const;
    void drawRowLines(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index, const QColor& lineColor)const;
    bool isHoveredRow(const QStyleOptionViewItem& option, const QModelIndex& index)const;
    bool isSelectRow(const QStyleOptionViewItem& option, const QModelIndex& index)const;
private:
    //悬浮相关变量
    bool m_drawHoverRowEnabled;
    QBrush m_hoveredRowForeground;
    QBrush m_hoveredRowBackground;
    QColor m_hoveredBorderColor;

    //选中相关变量
    QColor m_unselectBackColor;
    bool m_drawSelectRowEnabled;
    QColor m_selectBorderColor;

    int m_hoverRow = -1;
    int m_selectRow = -1;

    bool m_contentGridShow = true;

    QMap<int/*model column*/, int/*ui column*/> m_modelUiColumnMap;
};
