﻿#include "MTUnattendedDialog.h"
#include "ui_MTUnattendedDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MTUnattended/UnattendedSub/OptUnattendDataNew.h"


namespace n_mtautodelineationdialog
{

MTUnattendedDialog::MTUnattendedDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTUnattendedDialogClass;
    ui->setupUi(this);
    //基本属性
    this->setMainLayout(ui->verticalLayout);            //设置布局
    this->setDialogWidthAndContentHeight(1400, 780);    //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("无人值守设置"));                 //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    getWidgetButton()->hide();                          //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTUnattendedDialog, " << errMsg.toStdString();
        }
    }
    ui->widget->setMinHeight_widget_table_sketch(526);
}

MTUnattendedDialog::~MTUnattendedDialog()
{
}

/// <summary>
/// 设置默认导出地址
/// </summary>
/// <param name="stExportAddr">[IN]默认导出地址</param>
void MTUnattendedDialog::setDefaultExportAddr(const ST_AddrSimple& stExportAddr)
{
    OptUnattendDataNew::setDefaultExportAddr(stExportAddr);
}

/// <summary>
/// 显示无人值守设置弹窗
/// </summary>
/// <param name="unattendedConfigMap">[IN]无人值守信息集合(key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息)</param>
/// <param name="allTemplateNameMap">[IN]所有无人值守勾画模板id-模板名称集合(key-模板id value-模板名称)</param>
/// <param name="allRemoteScpInfoList">[IN]所有远程服务器信息</param>
/// <param name="allLocalServerNameMap">[IN]所有本地服务器名称集合(key-serverType value-serverName)</param>
/// <param name="stCallBackUnattended">[OUT]数据回调</param>
/// <returns>QDialog::DialogCode</returns>
QDialog::DialogCode MTUnattendedDialog::showUnattendedDlg(
    const QMap<QString, ST_UnattendedConfig>& unattendedConfigMap,
    const QMap<EM_OptDcmType, QMap<int, QString>>& allUnattendTemplateNameMap,
    const QList<ST_AddrSimple> allRemoteScpInfoList,
    const QMap<int, QStringList>& allLocalServerNameMap,
    ST_CallBack_Unattended& stCallBackUnattended)
{
    //初始化数据
    if (m_ptrOptUnattendData != nullptr)
    {
        delete m_ptrOptUnattendData;
        m_ptrOptUnattendData = nullptr;
    }

    m_ptrOptUnattendData = new OptUnattendDataNew(unattendedConfigMap, allUnattendTemplateNameMap, allRemoteScpInfoList, allLocalServerNameMap);
    ui->widget->init((OptUnattendDataNew*)m_ptrOptUnattendData, stCallBackUnattended);
    return (QDialog::DialogCode)(this->exec());
}

/// <summary>
/// 获取默认导出地址
/// </summary>
ST_AddrSimple MTUnattendedDialog::getDefaultExportAddr()
{
    return OptUnattendDataNew::getDefaultExportAddr();
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTUnattendedDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    QHash<QString, QString> imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
    ui->widget->setImagePathHash(imagePathHash);
}

/// <summary>
/// 关闭按钮
/// </summary>
void MTUnattendedDialog::onBtnCloseClicked()
{
    this->reject();
}

}
