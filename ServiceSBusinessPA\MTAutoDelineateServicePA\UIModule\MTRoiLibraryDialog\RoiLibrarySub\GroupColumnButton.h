﻿#pragma once

#include <QWidget>
#include <QPushButton>
#include <QToolButton>
#include <QMap>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtPushButton.h"
namespace Ui
{
class GroupColumnButton;
}
class MtPushButton;

//GroupColumnButton 参数
class GroupColumnButtonParam : public ICellWidgetParam
{
public:
    QString _btnPixelPath;
    QString _prefixText;
    QString _btnTipStr;
    GroupColumnButtonParam();
    ~GroupColumnButtonParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(GroupColumnButtonParam)

/*
MtPushButton集合
*/

class GroupColumnButton :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT
public:

    GroupColumnButton(QWidget* parent = Q_NULLPTR);
    ~GroupColumnButton();

    /*单元格公共接口*/
    //更新界面接口
    virtual bool UpdateUi(const QVariant& updateData);
    //设置是否允许编辑(有是否允许编辑状态的子控件必须实现)
    virtual void SetEnableEdit(bool bEdit);
    //按键使能设置
    virtual void SetButtonEnable(int btnIndex/**/, bool bEnable);
    //获取checked
    virtual bool GetCellChecked(int index);

    /******************ui************************/
    void SetupCellWidget(GroupColumnButtonParam& param);
    void SetButtonText(int btnIndex, QString& text);    //设置某个按键的文案

    int GetColumnNumber();

signals:
    void sigClicked(int);
    void sigButtonClicked(int/*btnIndex*/, bool/*ischecked*/);  //某个按键点击了

private slots:
    void slotButtonClicked(bool);

private:
    Ui::GroupColumnButton* ui;
    int _column = -1;
};
