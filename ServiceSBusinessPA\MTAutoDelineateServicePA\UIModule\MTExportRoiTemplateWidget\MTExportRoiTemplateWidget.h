﻿// *********************************************************************************
// <remarks>
// FileName    : MTRoiLibraryDialog
// Author      : zlw
// CreateTime  : 2024-01-10
// Description : 模板设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTExportRoiTemplateWidgetClass;
}

namespace n_mtautodelineationdialog
{

class MTExportRoiTemplateWidget : public QWidget, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="parent">[IN]父窗口</param>
    MTExportRoiTemplateWidget(QWidget* parent = nullptr);
    /// <summary>
    /// 析构函数
    /// </summary>
    ~MTExportRoiTemplateWidget();

    /// <summary>
    /// 设置右侧roi-item的宽度
    /// </summary>
    /// <param name="widthNum">[IN]右侧roi-item的宽度</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetRoiItemWidth(const int widthNum);

    /// <summary>
    /// 设置是否显示无人值守模板页签
    /// 不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true:显示</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetIsShowUnattendTab(const bool isShow);

    /// <summary>
    /// 设置是否显示选择ROI进行勾画/选择模板进行勾画页签
    /// \n不设置: 默认全显示
    /// </summary>
    /// <param name="isShow">true显示</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetShowSelectRadioSelectBtn(const bool isShow);

    /// <summary>
    /// 设置显示模态选择框
    /// </summary>
    /// <param name="isShow">true显示</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetShowModalityComboBox(bool bShow);

    /// <summary>
    /// 设置当前选中的模态下拉框
    /// \n不设置: 默认显示CT 可下拉
    /// </summary>
    /// <param name="modality">[IN]模态</param>
    /// <param name="enable">[IN]是否可下拉</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetMtComboBoxModality(const QString& modality, const bool enable);

    /// <summary>
    /// 设置需提前选中的模板id
    /// \n不设置: 默认不选中
    /// </summary>
    /// <param name="templateId">[IN]模板id</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetSelectTemplateId(const int templateId);

    /// <summary>
    /// 设置提前选中的页签类型
    /// \n 不设置: 默认选中2-选择模板进行勾画
    /// </summary>
    /// <param name="pageType">[IN]页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetSelectRadioPageType(const int pageType);

    /// <summary>
    /// 设置当模板被无人值守使用时，是否显示提示框
    /// \n不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true显示</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetIsShowTipOfModUnattendUsed(const bool isShow);

    /// <summary>
    /// 设置虚拟模板
    /// \n用于*选择ROI进行勾画*页签进行器官提前选中
    /// </summary>
    /// <param name="stSketchCollection">[IN]虚拟模板</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetVirtualSketchCollection(ST_SketchModelCollection& stSketchCollection);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="allGroupInfoList">[IN]所有分组信息</param>
    /// <param name="allSketchModelList">[IN]所有勾画模型</param>
    /// <param name="allSketchCollectionList">[IN]所有排序后的勾画模板</param>
    /// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void Init(
        const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
        const QList<ST_SketchModel>& allSketchModelList,
        const QMap<EM_OptDcmType, QList<ST_SketchModelCollection>>& allSketchCollectionMap,
        ST_CallBack_AutoSketch& stCallBackAutoSketch
    );

    /// <summary>
    /// 是否处于编辑状态
    /// </summary>
    /// <param name="showErrDlg">[IN]是否内部弹出请保存模板的弹窗</param>
    /// <returns>true是</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool IsEditState(bool showErrDlg = false);

    /// <summary>
    /// 检查输出的模板是否正常(异常时内部会弹窗)
    /// </summary>
    /// <returns>true可用</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool CheckOutCollectionAvail();

    /// <summary>
    /// 获取待勾画模板信息
    /// </summary>
    /// <returns>待勾画模板信息</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    ST_SketchModelCollection GetSelectCollection();

    /// <summary>
    /// 获取最新的虚拟模板
    /// \n用于*选择ROI进行勾画*页签进行器官提前选中
    /// </summary>
    /// <returns>最新的虚拟模板</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    ST_SketchModelCollection GetVirtualSketchCollection();

    /// <summary>
    /// 获取最新的模板排序信息
    /// \n随时调用拿到的都是当前最新的排序顺序
    /// </summary>
    /// <returns>最新的模板排序信息</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QMap<EM_OptDcmType, QList<int>> GetNewTemplateIdSortMap();

    /// <summary>
    /// 获取当模板被无人值守使用时，是否显示提示框
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    bool GetNewIsShowTipOfModUnattendUsed();

    /// <summary>
    /// 获取选中的页签类型
    /// </summary>
    /// <returns>页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    int GetSelectRadioPageType();

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    /// <remarks>[Version]:******* Change: </remarks>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

private:
    /// <summary>
    /// ui
    /// </summary>
    Ui::MTExportRoiTemplateWidgetClass* ui;
    /// <summary>
    /// 是否显示当模板被无人值守使用时的提示框
    /// </summary>
    bool m_IsShowTipUnattendUsed = true;
    /// <summary>
    /// 图标map(key-name value-图片相对路径)
    /// </summary>
    QHash<QString, QString> m_imagePathHash;
    /// <summary>
    /// 数据回调
    /// </summary>
    ST_CallBack_AutoSketch m_stCallBackAutoSketch;
    /// <summary>
    /// 自动勾画模板信息
    /// </summary>
    void* m_ptrOptSketchCollection = nullptr;
};

};
