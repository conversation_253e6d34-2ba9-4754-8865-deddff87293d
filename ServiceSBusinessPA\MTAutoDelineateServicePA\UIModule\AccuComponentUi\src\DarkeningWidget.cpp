﻿//*****************************************************************************
// Medical Imaging Solutions
// Contact number 17317554131
// xinbo.fu.
//
// Filename: DarkeningWidget.cpp
//
//*****************************************************************************

#include "AccuComponentUi\Header\DarkeningWidget.h"

#include <QDebug>
#include <QApplication>
#include <QVBoxLayout>
#include <QDesktopWidget>
#include <QIcon>
#include "CMtCoreWidgetUtil.h"

/**
 * Constructor creates and initializes the UI
 * @param parent parent widget
 * @param opacityPercentage required opacity in percentage
 */
DarkeningWidget::DarkeningWidget(QWidget* parent, double opacityPercentage, int toBottom) :
    QWidget(parent, Qt::FramelessWindowHint | Qt::Tool)
{
    //make the background transparent
    setAttribute(Qt::WA_TranslucentBackground);
    this->setWindowIcon(QIcon(":/AccuUIComponentImage/images/logo.png"));
    //set up the UI
    QVBoxLayout* verticalLayout(new QVBoxLayout(this));
    verticalLayout->setContentsMargins(0, 0, 0, 0);
    QWidget* mainWidget(new QWidget(this));
    mainWidget->setObjectName(QString::fromUtf8("mainWidget"));
    verticalLayout->addWidget(mainWidget);
    // Set opacity
    int opacityValue = opacityPercentage / 100.0 * 255.0;
    QString style = "#mainWidget { background-color: rgba(@colorA0, " + QString::number(opacityValue) + "); }";
    CMtCoreWidgetUtil::formatStyleSheet(style);
    this->setStyleSheet(style);
    QRect rec = QApplication::desktop()->screenGeometry();
    this->setFixedSize(rec.width(), rec.height() - toBottom);
    this->move(0, 0);
    //  if (qApp->activeWindow()) {
    //      //set the size to cover the entire active window
    //      this->setFixedSize(qApp->activeWindow()->size());
    //
    //      //position over active window
    //      this->move(qApp->activeWindow()->mapToGlobal(QPoint(0, 0)));
    //  }
}

/**
 * Virtual Destructor
 */
DarkeningWidget::~DarkeningWidget()
{
    //nothing to do here
    qDebug() << "DarkeningWidget destructor";
}

/**
 * Override this method to change the cursor when this widget is visible
 */
void DarkeningWidget::showEvent(QShowEvent* event)
{
    QApplication::setOverrideCursor(QCursor(QPixmap(QString::fromUtf8(":/Graphics/Graphics/Cursors/normal_cursor.png")), 11, 11));
}

/**
 * Override this method to restore the cursor when this widget is hidden
 */
void DarkeningWidget::hideEvent(QHideEvent* event)
{
    QApplication::restoreOverrideCursor();
}

/**
 * Emitted when the mouse clicked from Darkening Widget
 */
void DarkeningWidget::mousePressEvent(QMouseEvent* event)
{
    emit mouseClickedOnDarkening(event);
}
