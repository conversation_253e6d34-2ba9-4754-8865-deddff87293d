﻿// *********************************************************************************
// <remarks>
// FileName    : UnattendSubTableEditItem
// Author      : zlw
// CreateTime  : 2024-05-25
// Description : 无人值守规则界面列表item(内嵌于: UnattendSubWidget页签下的mtListWidget_right)
//             : AI识别的部位(111111 head chest chest_female abdomen prostate pelvis)
//             : DICOM字段(00180015 00081030 0008103E 00321060)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "MtListWidget.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "ui_UnattendSubTableEditItem.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class UnattendSubTableEditItem : public QWidget
{
    Q_OBJECT

public:
    UnattendSubTableEditItem(MtListWidget* parentListWidget, QListWidgetItem* curListWidgetItem, QWidget* parent = nullptr);
    ~UnattendSubTableEditItem();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="isAdd">[IN]true新增</param>
    /// <param name="sketchIdentifyUniqueKey">[IN]勾画规则唯一标识</param>
    /// <param name="stSketchIdentify">[IN]勾画识别信息</param>
    /// <param name="allRemoteServerList">[IN]所有导出地址</param>
    /// <param name="templateNameMap">[IN]模板名称集合(key-templateId value-templateName)</param>
    /// <returns>item高度</returns>
    int init(const bool isAdd,
             const QString sketchIdentifyUniqueKey,
             const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify,
             QList<n_mtautodelineationdialog::ST_AddrSimple>& allRemoteServerList,
             const QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*模板id*/, QString>>& templateNameMap);

    /// <summary>
    /// 获取勾画规则唯一标识
    /// </summary>
    QString getSketchIdentifyUniqueKey();

    /// <summary>
    /// 获取是否是AI部位识别
    /// </summary>
    bool getIsAI();

    /// <summary>
    /// 获取DICOM字段识别下的识别词
    /// </summary>
    /// <param name="outFlagStr">[OUT]标志 dcmTag.modality.sex</param>
    ///  <param name="outWordToUpperSet">[OUT]识别词集合(都转了大写)</param>
    void getDcmWord(QString& outFlagStr, QSet<QString>& outWordToUpperSet);

    /// <summary>
    /// 获取当前MtComboBox_keyValue选中项
    /// </summary>
    /// <param name="outIsAI">[OUT]true-AI识别</param>
    /// <param name="outCodeKey">[OUT]识别码(AI部位-111111 DICOM字段-00180015)</param>
    void getCurrent_MtComboBox_keyValue(bool& outIsAI, QString& outCodeKey);

    /// <summary>
    /// 设置AI部位识别下MtComboBox_keyValue可下拉的项
    /// </summary>
    /// <param name="codeKeyList">[IN]识别码(AI部位-111111)</param>
    void setAiItems_MtComboBox_keyValue(const QSet<QString>& codeKeySet);

    /// <summary>
    /// 删除前准备(用于将AI部位返还回可选)
    /// </summary>
    void prepareToBeforeDelete();

    /// <summary>
    /// 获取最新的勾画识别信息
    /// </summary>
    bool getNewSketchIdentify(QString& outSketchIdentifyUniqueKey, n_mtautodelineationdialog::ST_SketchIdentify& outStSketchIdentify, QString& errMsg);

    /// <summary>
    /// 获取Item打勾
    /// </summary>
    bool getIsCheckedItem();

    /// <summary>
    /// 设置Item打勾
    /// </summary>
    void setIsCheckedItem(bool ischeck);

    /// <summary>
    /// 获取最小高度
    /// </summary>
    int getItemMinHeight();

signals:
    void sigChangeHeight(const QString sketchIdentifyUniqueKey, const int heightNum); //增加/减少Item高度
    void sigEditItemChecked(const bool ischeck); //打勾选中

protected:
    /// <summary>
    /// 获取最小行高度
    /// </summary>
    /// <param name="recognitionType">[IN]部位识别类型(1:AI部位识别 2:DICOM字段)</param>
    /// <returns>最小行高度</returns>
    int getRowMinHeight(const int recognitionType);

    /// <summary>
    /// 获取AI部位识别模式规则数
    /// </summary>
    /// <returns>数目</returns>
    int getAIidentifyModuleNum();

    /// <summary>
    /// 初始化UI
    /// </summary>
    /// <param name="isAdd">[IN]true新增</param>
    /// <param name="stSketchIdentify">[IN]勾画识别信息</param>
    int initUI(const bool isAdd, const n_mtautodelineationdialog::ST_SketchIdentify& stSketchIdentify);

    /// <summary>
    /// 初始化选择模板下拉框
    /// </summary>
    /// <param name="modality">[IN]模态(CT/MR)</param>
    void init_MtComBox_Temlate(const QString& modality);

    /// <summary>
    /// 清空MtComboBox_keyValue-Item
    /// 直接clear会触发文本修改信号
    /// </summary>
    void clear_MtComboBox_keyValue();

    /// <summary>
    /// 添加MtComboBox_keyValue-Item
    /// 直接添加会触发文本修改信号
    /// </summary>
    /// <param name="keyStr">[IN]显示的文本</param>
    /// <param name="dataStr">[IN]实际数据</param>
    void addItem_MtComboBox_keyValue(const QString textStr, const QString dataStr);

    /// <summary>
    /// 设置当前MtComboBox_keyValue选中项
    /// </summary>
    /// <param name="outIsAI">[OUT]true-AI识别</param>
    /// <param name="outCodeKey">[OUT]识别码(AI部位-111111 DICOM字段-00180015)</param>
    void setCurrent_MtComBox_KeyValue(const bool isAI, const QString& codeKey);

    /// <summary>
    /// 重新初始化Dicom字段识别时的MtComBox_keyValue下拉项
    /// </summary>
    void reInitDicom_MtComBox_KeyValue();

    /// <summary>
    /// 重新初始化其他AI部位识别时的MtComBox_KeyValue下拉项
    /// </summary>
    /// <param name="curCodeKey">[IN]当前选择的识别码(AI部位-111111)</param>
    /// <param name="isDel">[IN]是否是删除时触发</param>
    void reInitAI_MtComBox_KeyValue(const QString& curCodeKey, bool isDel = false);

    /// <summary>
    /// 检查识别规则是否正常
    /// </summary>
    /// <returns>true正常</returns>
    bool checkIsNormal();

    /// <summary>
    /// 获取UnattendSubTableEditItem指针(编辑模式)
    /// 为nullptr时代表没找到
    /// </summary>
    UnattendSubTableEditItem* getUnattendSubTableEditItem(QListWidgetItem* listWidgetItem);

protected slots:
    /// <summary>
    /// 复选框打勾
    /// </summary>
    void onMtCheckBox(int state);

    /// <summary>
    /// mtComboBox_modality下拉变化
    /// </summary>
    void onMtComboBoxModalityChanged(const QString& text);

    /// <summary>
    /// mtComboBox_keyType下拉变化
    /// </summary>
    void onMtComboBoxKeyTypeChanged(const QString& text);

    /// <summary>
    /// mtComboBox_keyValue下拉变化
    /// </summary>
    void onMtComboBoxKeyValueChanged(const QString& text);

    /// <summary>
    /// MtRangeLineEdit失去焦点事件
    /// </summary>
    void slotMtRangeLineEditFocusOut();

private:
    Ui::UnattendSubTableEditItemClass ui;
    QString m_sketchIdentifyUniqueKey; //勾画规则唯一标识
    MtListWidget* m_parentListWidget; //父控件
    QListWidgetItem* m_curListWidgetItem; //用于滚动到错误行
    n_mtautodelineationdialog::ST_SketchIdentify m_oldSketchIdentify; //原始勾画识别信息
    QList<n_mtautodelineationdialog::ST_AddrSimple> m_allRemoteServerList; //全部导出地址信息
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*模板id*/, QString>> m_templateNameMap; //模板id-名称集合
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)
};
