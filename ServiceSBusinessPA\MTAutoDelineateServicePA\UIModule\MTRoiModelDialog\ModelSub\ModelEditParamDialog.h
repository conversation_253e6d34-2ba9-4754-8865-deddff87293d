﻿// *********************************************************************************
// <remarks>
// FileName    : ModelEditParamDialog
// Author      : zlw
// CreateTime  : 2023-12-12
// Description : 参数详情弹窗(适用于: 模型编辑table列表 ModelEditTable 中的参数详情按钮)
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_ModelEditParamDialog.h"


class ModelEditParamDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    ModelEditParamDialog(QWidget* parent = nullptr);
    ~ModelEditParamDialog();

    /// <summary>
    /// 初始化
    /// json: https://manteiatech.yuque.com/manteia/mgmrmw/chsd5gmg4u4tamuh
    /// </summary>
    /// <param name="paramJson">[IN]后处理参数json</param>
    void init(const QString& paramJson);

    /// <summary>
    /// 获取roi后处理参数json字符串
    /// </summary>
    /// <returns>roi后处理参数json字符串</returns>
    QString getParamJson();

protected:
    virtual void onBtnCloseClicked() override;  //关闭按钮
    virtual void onBtnRight2Clicked() override; //取消按钮
    virtual void onBtnRight1Clicked() override; //确认按钮

private:
    Ui::ModelEditParamDialogClass ui;
};
