﻿#pragma once

#include <QComboBox>

#include "AccuComponentUi\Header\QMTAbsTableWidgetItem.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtComboBox.h"


//QMTAbsComboBox 参数
class  QMTAbsComboBoxParam : public ICellWidgetParam
{
public:
    QStringList _textList;      //下拉框集合
    //QString _text;

    QMTAbsComboBoxParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTAbsComboBoxParam)

//
class  QMTAbsComboBox : public MtComboBox, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    explicit QMTAbsComboBox(QWidget* parent = Q_NULLPTR);
    virtual ~QMTAbsComboBox();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual QString GetCurText();                      //获取当前界面展示文案
    virtual void SetEnableEdit(bool bEdit);            //设置是否允许编辑

    /***********get*************/
    QStringList GetAllItemStrList();            //获取下拉框所有值的接口
#if 0
    /******************add*****************************/
    void AddStrList(QStringList textList, const QFont&);                 //根据字符串宽度设置item宽度
    void AddStrAndIconList(QStringList textList, QStringList iconList);       //根据字符串宽度设置item宽度
    void AddWidgetList(QList<QWidget*>widgetList);

    /*********************delete*******************************/
    void ClearDataModel();
    void RemoveItem(const QString& itemStr);    //清空下拉框的值

    /******************update*****************************/
    void SetItemEnable(int index, bool enable);

    /******************get*****************************/
    QListWidget* GetListWidget();
    QStringList GetAllItemStrList();            //获取下拉框所有值的接口
    /*********************signals**************************/
#endif
#if 0
signals:
    void sigClicked();
    void sigClicked(int);
    void sigItemTextChange(QString);
    void sigItemIndexChange(int);
#endif
#if 0
protected:
    virtual void wheelEvent(QWheelEvent* event);
    virtual void resizeEvent(QResizeEvent* event);
    virtual void mousePressEvent(QMouseEvent* event);
    ///
    int GetMaxStrWidth(const QStringList&);
#endif
private:
#if 0
    QListWidget* _listWidget = nullptr;
    bool _isText = false;
    QString _maxText;
    bool _bAutoDeleteItem = false;
    //
    QAbstractItemModel* _originModel = nullptr;
    QAbstractItemView* _originView = nullptr;
#endif
};
