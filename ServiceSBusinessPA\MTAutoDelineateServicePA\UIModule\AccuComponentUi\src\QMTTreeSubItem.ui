<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTTreeSubItem</class>
 <widget class="QWidget" name="QMTTreeSubItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>293</width>
    <height>35</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="windowTitle">
   <string>QMTTreeSubItem</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="frame">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>7</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>10</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="MtCheckBox" name="checkBox_show">
        <property name="maximumSize">
         <size>
          <width>22</width>
          <height>22</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">QCheckBox::indicator:unchecked {  
     image:  url(:/AccuUIComponentImage/images/eye_un_display.png);  
 }  
  
 QCheckBox::indicator:unchecked:hover {  
     image:  url(:/AccuUIComponentImage/images/eye_un_display_hover.png);  
 }  
  
 QCheckBox::indicator:unchecked:pressed {  
     image:  url(:/AccuUIComponentImage/images/eye_un_display.png);  
 }  
  
 QCheckBox::indicator:checked {  
     image: url(:/AccuUIComponentImage/images/eye_display.png);  
 }  
  
 QCheckBox::indicator:checked:hover {  
     image: url(:/AccuUIComponentImage/images/eye_display_hover.png);  
 }  
  
 QCheckBox::indicator:checked:pressed {  
     image:  url(:/AccuUIComponentImage/images/eye_display.png);  
 }  
  
 QCheckBox::indicator:indeterminate:hover {  
     image: url(:/AccuUIComponentImage/images/eye_display_hover.png);  
 }  
  
 QCheckBox::indicator:indeterminate:pressed {  
     image:  url(:/AccuUIComponentImage/images/eye_display.png);   
 }</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
        <property name="toolTipText">
         <string/>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtCheckBox::default_type</enum>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>8</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="Line" name="line">
        <property name="minimumSize">
         <size>
          <width>1</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>1</width>
          <height>16777215</height>
         </size>
        </property>
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>8</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QToolButton" name="toolButton_color">
        <property name="minimumSize">
         <size>
          <width>12</width>
          <height>12</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>12</width>
          <height>12</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="iconSize">
         <size>
          <width>12</width>
          <height>12</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>10</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QMTLineEdit" name="textName" native="true"/>
      </item>
      <item>
       <widget class="MtFrameEx" name="mtFrameEx">
        <property name="maximumSize">
         <size>
          <width>28</width>
          <height>16777215</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="MtToolButton" name="derivedBtnStatus">
           <property name="minimumSize">
            <size>
             <width>14</width>
             <height>14</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>14</width>
             <height>14</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="iconSize">
            <size>
             <width>28</width>
             <height>24</height>
            </size>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtToolButton::default_type</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MtToolButton" name="DerivedROIBtn">
           <property name="minimumSize">
            <size>
             <width>14</width>
             <height>14</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>14</width>
             <height>14</height>
            </size>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="iconSize">
            <size>
             <width>28</width>
             <height>24</height>
            </size>
           </property>
           <property name="toolTipText">
            <string/>
           </property>
           <property name="toolTipTextEx">
            <string/>
           </property>
           <property name="toolTipMaxWidth">
            <number>16777215</number>
           </property>
           <property name="elideMode">
            <enum>Qt::ElideRight</enum>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtToolButton::default_type</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_5">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>10</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="label_AI">
        <property name="minimumSize">
         <size>
          <width>22</width>
          <height>30</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>22</width>
          <height>30</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtToolButton" name="toolButton_select">
        <property name="minimumSize">
         <size>
          <width>20</width>
          <height>20</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>20</width>
          <height>25</height>
         </size>
        </property>
        <property name="styleSheet">
         <string notr="true">#toolButton_select{
	border:0px;
background-image: url(:/AccuUIComponentImage/images/icon_comboBox0.png);      
}
#toolButton_select:hover {
background-image: url(:/AccuUIComponentImage/images/icon_comboBox0.png);
}
#toolButton_select:checked {
background-image: url(:/AccuUIComponentImage/images/icon_comboBox0.png);
}
#toolButton_select:disabled {
background-image: url(:/AccuUIComponentImage/images/icon_comboBox0.png);
}</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="pixmapFilename">
         <string notr="true">:/AccuUIComponentImage/images/icon_comboBox.png</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtToolButton::toolbutton1_1</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>QMTLineEdit</class>
   <extends>QWidget</extends>
   <header>AccuComponentUi\Header\qmtlineedit.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
