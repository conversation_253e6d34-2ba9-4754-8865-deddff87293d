﻿#include "ExportRuleShowWidget.h"


ExportRuleShowWidget::ExportRuleShowWidget(const QString& rangeStr, const QString& formatStr, const QString& addrStr, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);

    if (rangeStr.isEmpty() == true)
    {
        ui.mtFrameEx_4->hide();
        ui.mtFrameEx_5->hide();
        ui.mtLabel_format->clear();
        ui.mtLabel_addr->clear();
        ui.mtLabel_range->setText("-");
    }
    else
    {
        ui.mtLabel_range->setText(tr("内容：") + rangeStr);
        ui.mtLabel_format->setText(tr("格式：") + formatStr);
        ui.mtLabel_addr->setText(tr("地址：") + addrStr);
    }
}

ExportRuleShowWidget::~ExportRuleShowWidget()
{
}
