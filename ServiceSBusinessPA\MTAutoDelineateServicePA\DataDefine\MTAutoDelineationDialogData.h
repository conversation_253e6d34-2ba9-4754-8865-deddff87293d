﻿// *********************************************************************************
// <remarks>
// FileName    : MTAutoDelineationDialogData
// Author      : zlw
// CreateTime  : 2023-10-30
// Description : 对外数据交互及回调
// </remarks>
// **********************************************************************************
#pragma once

#include <QObject>
#include <QMetaType>
#include <QMap>
#include <QSet>
#include <QVariant>
#include <functional>
#include <iostream>


namespace n_mtautodelineationdialog
{

/// <summary>
/// 厂商
/// </summary>
enum EM_Manufacturer
{
    Manufacturer_Eclipse = 0                //Eclipse
};


/// <summary>
/// 操作类型
/// </summary>
enum EM_OptType
{
    /// <summary>
    /// 不变
    /// </summary>
    OptType_None = 0,
    /// <summary>
    /// 增加
    /// </summary>
    OptType_Add = 1,
    /// <summary>
    /// 删除
    /// </summary>
    OptType_Del = 2,
    /// <summary>
    /// 修改
    /// </summary>
    OptType_Mod = 3,
    /// <summary>
    /// 移出无人值守
    /// </summary>
    OptType_OutUnattend = 4
};

/// <summary>
/// 操作的DICOM类型
/// </summary>
enum EM_OptDcmType
{
    /// <summary>
    /// CT
    /// </summary>
    OptDcmType_CT = 1,
    /// <summary>
    /// MR
    /// </summary>
    OptDcmType_MR = 2
};


/// <summary>
/// 地址简易信息
/// </summary>
struct ST_AddrSimple
{
    /// <summary>
    /// 导出地址类型(0:无 1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)
    /// </summary>
    int addrType = 0;
    /// <summary>
    /// 导出范围(1:导出该患者所有数据 2:只导出当前勾画RtStructure 3:导出当前勾画图像及RtStructure 4:不导出)
    /// </summary>
    int exportRange = 4;
    /// <summary>
    /// 导出格式(0:普通格式  "3239DF85-89FB-419D-99BE-56E9C1D5DE50":Eclipse15  "7E42D45A-2447-4196-A1B9-20AA0B4BE6A1":Cyberknife)
    /// </summary>
    QString exportFormat;

    /// <summary>
    /// 服务器信息
    /// </summary>
    struct
    {
        /// <summary>
        /// 服务器名
        /// </summary>
        QString serverName;
    } stScpInfo;

    /// <summary>
    /// 共享文件夹信息
    /// </summary>
    struct
    {
        /// <summary>
        /// 文件夹完整路径
        /// </summary>
        QString dirPath;
        /// <summary>
        /// 导出到文件夹时是否创建相应目录(0-不创建 1-按患者PatientID创建目录)
        /// </summary>
        int mkSubType = 0;
    } stDirInfo;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_AddrSimple);


/// <summary>
/// ROI标签信息
/// </summary>
struct ST_RoiLabelInfo
{
    /// <summary>
    /// 操作类型(0不变 1新增 2修改 3删除)
    /// </summary>
    EM_OptType optTypeEnum = OptType_None;
    /// <summary>
    /// 是否是内置标签(不允许删除)
    /// </summary>
    bool isbuiltIn = true;
    /// <summary>
    /// Manteia-ROI标签名
    /// </summary>
    QString manteiaRoiLabel;
    /// <summary>
    /// ROI名称
    /// </summary>
    QString roiName;
    /// <summary>
    /// ROI中文名
    /// </summary>
    QString roiChName;
    /// <summary>
    /// ROI别名
    /// </summary>
    QString roiAlias;
    /// <summary>
    /// ROI颜色(FFFFFF...)
    /// </summary>
    QString roiColor;
    /// <summary>
    /// ROI类型(ORGAN...)
    /// </summary>
    QString roiType;
    /// <summary>
    /// ROI编码集合(没有就不写)
    /// </summary>
    QMap<EM_Manufacturer, QMap<QString, QString>> roiCodeMap;
    /// <summary>
    /// Manufacturer_Eclipse
    /// "code"          : ROI编码(codeValue)
    /// "label"         : ROI标签(roiObservationLabel)
    /// "codeScheme"    : 编码方案标识(codingSchemeDesignator)，FMA、99VMS_STRUCTCODE...
    /// "codeSchemeVer" : 编码方案版本(codingSchemeVersion), 3.2、1.0...
    /// "codeMean"      : ROI含义(codeMeaning)
    /// </summary>
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_RoiLabelInfo);


/// <summary>
/// Organ分组信息
/// </summary>
struct ST_OrganGroupInfo
{
    /// <summary>
    /// id(分组id)
    /// </summary>
    int id = -1;
    /// <summary>
    /// 分组类型(1:系统内置 2:自定义 3:亚分组)
    /// </summary>
    int type = 1;
    /// <summary>
    /// 关联主结构ROI器官，如果是亚组则必填
    /// </summary>
    int refOrganId = -1;
    /// <summary>
    /// 分组名称
    /// </summary>
    QString name;
    /// <summary>
    /// 分组默认名称
    /// </summary>
    QString defaultName;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_OrganGroupInfo);


/// <summary>
/// Organ信息
/// </summary>
struct ST_Organ
{
    /// <summary>
    /// 操作类型(0不变 1新增 2修改 3删除)
    /// </summary>
    EM_OptType optTypeEnum = OptType_None;
    /// <summary>
    /// id(organId)
    /// </summary>
    int id = -1;
    /// <summary>
    /// 所属模型id(空勾画:-1 默认模型:0 训练模型:>0)
    /// </summary>
    int modelId = -1;
    /// <summary>
    /// 是否勾选(内部使用,不用赋值)
    /// </summary>
    bool isVisiable = false;
    /// <summary>
    /// 是否启用
    /// </summary>
    bool enable = true;
    /// <summary>
    /// 是否关联模板
    /// </summary>
    bool isAssociateTemplate = false;
    /// <summary>
    /// 部位(空勾画:empty_sketch 默认模型:head/chest_female/chest/abdomen/pelvis/prostate/other 训练模型:为空)
    /// </summary>
    QString bodypart;
    /// <summary>
    /// 中文名称
    /// </summary>
    QString organChineseName;
    /// <summary>
    /// 英文名称
    /// </summary>
    QString organEnglishName;
    /// <summary>
    /// 默认名称
    /// </summary>
    QString defaultOrganName;
    /// <summary>
    /// 自定义名称
    /// </summary>
    QString customOrganName;
    /// <summary>
    /// 默认颜色
    /// </summary>
    QString defaultColor;
    /// <summary>
    /// 自定义颜色
    /// </summary>
    QString customColor;
    /// <summary>
    /// Roi类型
    /// </summary>
    QString roiType;
    /// <summary>
    /// Roi标签
    /// </summary>
    QString roiLabel;
    /// <summary>
    /// Roi附加参数Json
    /// </summary>
    QString roiParam;
    /// <summary>
    /// Roi描述
    /// </summary>
    QString roiDesc;
    /// <summary>
    /// 所属分组信息集合(key-ST_OrganGroupInfo:id)
    /// </summary>
    QMap<int, ST_OrganGroupInfo> organGroupInfoMap;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_Organ);


/// <summary>
/// 勾画模型信息
/// </summary>
struct ST_SketchModel
{
    /// <summary>
    /// id(模型id 空勾画:-1 默认模型:0 训练模型:>0)
    /// </summary>
    int id = -1;
    /// <summary>
    /// 模型类型(1:内置模型 2:导入AL模型)
    /// </summary>
    int modelType = 1;
    /// <summary>
    /// 模型名称(空勾画:empty_sketch 默认模型:Default Model)
    /// </summary>
    QString modelName;
    /// <summary>
    /// 模型描述
    /// </summary>
    QString modelDesc;
    /// <summary>
    /// 模型适用图像类型(CT、MR 空勾画传空)
    /// </summary>
    QString modality;
    /// <summary>
    /// 导入时间
    /// </summary>
    QString importTime;
    /// <summary>
    /// Roi信息集合
    /// </summary>
    QList<ST_Organ> organList;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_SketchModel);


/// <summary>
/// 勾画模板信息
/// </summary>
struct ST_SketchModelCollection
{
    /// <summary>
    /// id(模板id)
    /// </summary>
    int id = -1;
    /// <summary>
    /// 是否是无人值守模板
    /// </summary>
    bool isUnattended = false;
    /// <summary>
    /// 模板名称
    /// </summary>
    QString templateName;
    /// <summary>
    /// 模板备注
    /// </summary>
    QString remark;
    /// <summary>
    /// 模板适用图像类型(CT、MR)
    /// </summary>
    QString modality;
    /// <summary>
    /// showGroupIdMap中涉及到的分组,包括主亚分组(ST_OrganGroupInfo:id)
    /// </summary>
    QSet<int> groupIdSet;
    /// <summary>
    /// Organ在哪些分组下是显示的(key-ST_Organ:id value-ST_OrganGroupInfo:id)
    /// </summary>
    QMap<int, QSet<int>> showGroupIdMap;
    /// <summary>
    /// 亚组器官在哪些主分组下显示，如果不赋值则将在所有涉及到该亚组的主分组下显示(key-ST_Organ:id value-ST_OrganGroupInfo:id)
    /// </summary>
    QMap<int, QSet<int>> subInMainGroupIdMap;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_SketchModelCollection);


/// <summary>
/// 自动勾画信息
/// </summary>
struct ST_AutoSketch
{
    //图像信息
    /// <summary>
    /// 图像seriesUID
    /// </summary>
    QString seriesUID;
    /// <summary>
    /// 图像模态(CT、MR)
    /// </summary>
    QString imageModality;

    //全部模型信息
    /// <summary>
    /// 全部模型信息
    /// </summary>
    QList<ST_SketchModel> sketchModelList;

    //模板信息
    /// <summary>
    /// 排序后的全部模板信息
    /// </summary>
    QMap<EM_OptDcmType, QList<ST_SketchModelCollection>> allSketchCollectionMap;
    /// <summary>
    /// 输出待勾画的模板信息(不用赋值)
    /// </summary>
    ST_SketchModelCollection outSketchCollection;

    //勾画完后自动导出(显示勾画完后自动导出选项时有效)
    /// <summary>
    /// 勾画完后自动导出复选框勾选状态
    /// </summary>
    bool checkedExport = false;
    /// <summary>
    /// 导出地址信息
    /// </summary>
    ST_AddrSimple stAddrSimple;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_AutoSketch);


/// <summary>
/// 勾画识别信息
/// </summary>
struct ST_SketchIdentify
{
    /// <summary>
    /// 部位识别类型(1:AI部位识别 2:DICOM字段)
    /// </summary>
    int recognitionType = -1;
    /// <summary>
    /// 模板Id
    /// </summary>
    int sketchCollectionId = -1;
    /// <summary>
    /// 适用的模态(CT MR)
    /// </summary>
    QString modality;
    /// <summary>
    /// AI识别的部位(111111 head chest chest_female abdomen prostate pelvis)
    /// </summary>
    QString aiBodypart;
    /// <summary>
    /// 识别的DICOM字段-对应的性别(ALL F M)
    /// </summary>
    QString dcmTagSex;
    /// <summary>
    /// 识别的DICOM字段(key-dicomTag,0008103E value-dicomValue,多个使用/隔开)
    /// </summary>
    QMap<QString, QStringList> dcmTagMap;
    /// <summary>
    /// 当前导出地址信息集合(key-创建时间(yyyyMMddhhmmss) value-地址类型及信息)
    /// </summary>
    QMap<QString, ST_AddrSimple> addrInfoMap;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_SketchIdentify);


/// <summary>
/// 勾画规则
/// </summary>
struct ST_SketchRule
{
    /// <summary>
    /// 当前勾画识别信息集合(key-创建时间(yyyyMMddhhmmss) value-勾画规则信息)
    /// </summary>
    QMap<QString, ST_SketchIdentify> sketchIdentifyMap;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_SketchRule);


/// <summary>
/// 影像接收规则
/// </summary>
struct ST_LocalServerRule
{
    /// <summary>
    /// 影像字段过滤开关
    /// </summary>
    bool imagefilterEnable = false;
    /// <summary>
    /// 图层数过滤开关
    /// </summary>
    bool numfilterEnable = false;
    /// <summary>
    /// 当前本地服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log 4:SCP服务器)
    /// </summary>
    int curServerType = -1;
    /// <summary>
    /// 当前本地服务器名
    /// </summary>
    QString curServerName;
    /// <summary>
    /// 无需勾画的图层张数
    /// </summary>
    int numNotSketch = -1;
    /// <summary>
    /// 影像过滤条件集合(key-dicomTag,0008103E value-dicomValue)
    /// </summary>
    QMap<QString, QStringList> imagefilterMap;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_LocalServerRule);


/// <summary>
/// 导出规则
/// </summary>
struct ST_ExportRule
{
    /// <summary>
    /// 当前导出地址信息集合(key-创建时间(yyyyMMddhhmmss) value-地址类型及信息)
    /// </summary>
    QMap<QString, ST_AddrSimple> addrInfoMap;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_ExportRule);


/// <summary>
/// 无人值守配置
/// </summary>
struct ST_UnattendedConfig
{
    /// <summary>
    /// 是否使能
    /// </summary>
    bool isEnable = false;
    /// <summary>
    /// 勾画规则
    /// </summary>
    ST_SketchRule stSketchRule;
    /// <summary>
    /// 影像接收规则
    /// </summary>
    ST_LocalServerRule stLocalServerRule;
};
Q_DECLARE_METATYPE(n_mtautodelineationdialog::ST_UnattendedConfig);


/// <summary>
/// 自动勾画交互回调
/// </summary>
struct ST_CallBack_AutoSketch
{
    /// <summary>
    ///更新模板信息
    /// </summary>
    /// <param name="optTypeEnum">[IN]操作类型</param>
    /// <param name="stSketchCollection">[IN]模板信息</param>
    /// <param name="newTemplateId">[OUT]新增时返回入库后的模板id</param>
    /// <returns>true成功</returns>
    std::function<bool(const EM_OptType optTypeEnum, const ST_SketchModelCollection stSketchCollection, int& newTemplateId, QString& outErrMsg)> updateSketchCollectionCallBack = nullptr;

    /// <summary>
    /// 模板是否允许操作
    /// </summary>
    /// <param name="optTypeEnum">[IN]操作类型</param>
    /// <param name="templateId">[IN]模板id</param>
    /// <param name="outMsg">[IN]弹窗消息</param>
    /// <param name="outMsg">[IN]弹窗右一按钮文本(为空使用原生文字)</param>
    /// <returns>弹窗类型(0-允许操作 1-蓝色提示 2-蓝色选择 3-红色警告选择)</returns>
    std::function<int(const EM_OptType optTypeEnum, const int templateId, QString& outMsg, QString& rightBtnText)> canOptSketchCollectionCallBack = nullptr;

    /// <summary>
    /// 设置模板是否是无人值守
    /// </summary>
    /// <param name="templateId">[IN]模板id</param>
    /// <param name="isUnattended">[IN]true是无人值守</param>
    /// <returns>是否设置成功</returns>
    std::function<bool(const int templateId, const bool isUnattended, QString& outErrMsg)> setSketchCollectionUnattendCallBack = nullptr;

    /// <summary>
    /// 增删改远程导出地址回调
    /// </summary>
    /// <returns>操作后最新的远程导出地址信息</returns>
    std::function<QList<n_mtautodelineationdialog::ST_AddrSimple>()> editRemoteScpCallBack = nullptr;

    /// <summary>
    /// 模型和ROI设置按钮回调
    /// </summary>
    /// <returns>是否发生修改</returns>
    std::function<bool()> modelAndRoiSettingCallBack = nullptr;

    /// <summary>
    /// 获取所有模板信息回调
    /// </summary>
    /// <param name="outAllGroupInfoList">[OUT]最新的分组集合(排序后的)</param>
    /// <param name="outAllModelList">[OUT]最新的所有模型信息集合</param>
    /// <param name="outAllSketchCollectionMap">[OUT]最新的所有模板信息集合</param>
    std::function<void(QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& outAllGroupInfoList, QList<n_mtautodelineationdialog::ST_SketchModel>& outAllModelList,
                       QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>& outAllSketchCollectionMap)> getAllSketchTemplateCallBack = nullptr;

    /// <summary>
    /// 获取所有的器官信息
    /// </summary>
    /// <returns>器官信息列表</returns>
    std::function<QList<n_mtautodelineationdialog::ST_Organ>()> getAllOrganInfoCallback = nullptr;

    /// <summary>
    /// 获取标签列表信息
    /// </summary>
    /// <returns>标签信息列表</returns>
    std::function<QList<n_mtautodelineationdialog::ST_RoiLabelInfo>()> getAllRoiLabelInfoCallback = nullptr;

    /// <summary>
    /// 更新保存器官列表
    /// </summary>
    /// <param name="organInfoList">[IN]器官列表信息</param>
    std::function<bool(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList)> updateOrganInfoCallback = nullptr;

    /// <summary>
    /// 更新保存ROI标签列表
    /// </summary>
    /// <param name="roiLabelInfoList">[IN]roi标签列表信息</param>
    std::function<bool(const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& roiLabelInfoList)> updateRoiLabelInfoCallback = nullptr;
};


/// <summary>
/// 无人值守交互回调
/// </summary>
struct ST_CallBack_Unattended
{
    /// <summary>
    /// 增删改本地服务器回调
    /// \n可以不用将所有的服务器类型都返回，返回啥内部更新啥
    /// </summary>
    /// <returns>操作后最新的本地服务器地址信息(key-1:共享文件夹 2:FTP服务器 4:SCP服务器 value-服务器别名)</returns>
    std::function<QMap<int/*serverType*/, QStringList>()> editLocalServerCallBack = nullptr;

    /// <summary>
    /// 无人值守数据更新
    /// </summary>
    /// <param name="optTypeEnum">[IN]操作类型</param>
    /// <param name="customId">[IN]唯一表示(即unattendedfeature表customId)</param>
    /// <param name="stUnattendedConfig">[IN]无人值守信息</param>
    ///  <returns>true成功</returns>
    std::function<bool(const EM_OptType optTypeEnum, const QString customId, const ST_UnattendedConfig stUnattendedConfig, QString& outErrMsg)> updateUnattendedCallBack = nullptr;

    /// <summary>
    /// 规则开关启停
    /// </summary>
    /// <param name="ruleEnable">[IN]规则开关启停</param>
    /// <param name="customId">[IN]唯一表示(即unattendedfeature表customId)</param>
    std::function<bool(const bool ruleEnable, const QString customId, QString& outErrMsg)> ruleEnableCallBack = nullptr;
};


/// <summary>
/// 模型设置窗口回调，也可直接采用处理窗口中的信号
/// </summary>
struct ST_CallBack_ROIModelSetting
{
    /// <summary>
    /// 获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN]返回从标签库中获取的所有记录信息</param>
    std::function<void(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)> cbGetLabelLibraryInfo = nullptr;

    /// <summary>
    /// 获取器官ROI默认设置(根据默认名进行匹配)
    /// </summary>
    /// <param name="stOrganDefaultList">[IN]返回从获取的器官默认设置信息</param>
    std::function<void(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)> cbGetOrganDefaultInfo = nullptr;

    /// <summary>
    /// 通知外部进行导入模型路径，需要进行模型导入处理
    /// </summary>
    /// <param name="modelPath">[OUT][模型文件路径</param>
    std::function<void(const QString& modelPath)> cbModelImport = nullptr;

    /// <summary>
    /// 模型删除
    /// </summary>
    /// <param name="modelId">[OUT]要删除的模型ID</param>
    /// <param name="modelName">[OUT]要删除的模型名</param>
    std::function<void(const QString& modelId, const QString& modelName)> cbDeleteModel = nullptr;

    /// <summary>
    /// 模板关联的roi取消了分组
    /// </summary>
    /// <param name="roiID">[OUT]roiID</param>
    /// <param name="roiName">[OUT]roi名称</param>
    /// <param name="groupID">[OUT]取消的组ID</param>
    /// <param name="groupName">[OUT]取消的组名</param>
    /// <param name="refModelInfoMap">[OUT]关联roi分组的模板信息，当该参数为空时，表示该roi所有分组都被移除</param>
    /// <returns></returns>
    std::function<void(int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap)> cbRemoveRoiRelatedGroup = nullptr;

    /// <summary>
    /// 模型信息保存结果.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">[OUT]The model identifier.</param>
    /// <param name="modelName">[OUT]Name of the model.</param>
    /// <param name="result">[OUT]The result，0：success.</param>
    std::function<void(const QString& modelId, const QString& modelName, int result)> cbSaveModelInfoResult = nullptr;
};


/// <summary>
/// ROI库设置窗口交互回调
/// </summary>
struct ST_CallBack_ROILibrarySetting
{
    /// <summary>
    /// 模板关联的roi取消了分组
    /// </summary>
    /// <param name="roiID">[OUT]roiID</param>
    /// <param name="roiName">[OUT]roi名称</param>
    /// <param name="groupID">[OUT]取消的组ID</param>
    /// <param name="groupName">[OUT]取消的组名</param>
    /// <param name="refModelInfoMap">[OUT]关联roi分组的模板信息，当该参数为空时，表示该roi所有分组都被移除</param>
    /// <returns></returns>
    std::function<void(int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap)> cbRemoveRoiRelatedGroup = nullptr;

    /// <summary>
    /// 更新ROI分组信息到数据库，并重新读取组信息
    /// </summary>
    /// <param name="curGroupList">[OUT]当前所有ROI分组，包括更新了组名和新增（组id小于0）的分组</param>
    /// <param name="delGroupList">[OUT]删除的ROI分组</param>
    /// <param name="updtedGroupList">[OUT]传回的分组信息，因为有新增和删除分组，组id会不合法。所以在入库后需要重新读取组信息</param>
    /// <returns></returns>
    std::function<void(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList
                       , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList
                       , QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList)> cbUpdateRoiGroup = nullptr;

    /// <summary>
    /// 获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN]返回从标签库中获取的所有记录信息</param>
    std::function<void(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)> cbGetLabelLibraryInfo = nullptr;

    /// <summary>
    /// 获取器官ROI默认设置(根据默认名进行匹配)
    /// </summary>
    /// <param name="stOrganDefaultList">[IN]返回从获取的器官默认设置信息</param>
    std::function<void(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)> cbGetOrganDefaultInfo = nullptr;

    /// <summary>
    /// 通知外部进行导入模型路径，需要进行模型导入处理
    /// </summary>
    /// <param name="modelPath">[OUT][模型文件路径</param>
    std::function<void(const QString& modelPath)> cbModelImport = nullptr;

    /// <summary>
    /// 模型删除
    /// </summary>
    /// <param name="modelId">[OUT]要删除的模型ID</param>
    /// <param name="modelName">[OUT]要删除的模型名</param>
    std::function<void(const QString& modelId, const QString& modelName)> cbDeleteModel = nullptr;

    /// <summary>
    /// 模型信息保存结果.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">[OUT]The model identifier.</param>
    /// <param name="modelName">[OUT]Name of the model.</param>
    /// <param name="desc">[OUT]description of the model.</param>
    /// <param name="result">[IN]The result，0：success.</param>
    std::function<void(const QString& modelId, const QString& modelName, const QString& desc, int& result)> cbSaveModelInfo = nullptr;
};


/// <summary>
/// 空勾画设置窗口交互回调
/// </summary>
struct ST_CallBack_EmptyRoiSetting
{
    /// <summary>
    /// 空勾画被删除
    /// </summary>
    /// <param name="roiID">[OUT]roiID</param>
    /// <param name="roiName">[OUT]roi名称</param>
    /// <returns></returns>
    std::function<void(int roiID, const QString& roiName)> cbRemoveRoi = nullptr;
};

}