﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTCheckBoxLabel.h"
#include "ui_QMTCheckBoxLabel.h"
#include "CMtCoreDefine.h"
#include <QFile>

QMTCheckBoxLabelParam::QMTCheckBoxLabelParam()
{
    _cellWidgetType = DELEAGATE_CheckBoxLabel;
}

//QJsonObject QMTCheckBoxLabelParam::ToJson()
//{
//    QJsonObject retObj;
//    retObj.insert("state", _state);
//    retObj.insert("bRead", _bRead);
//    return retObj;
//}
//
//void QMTCheckBoxLabelParam::ParseFromJson(QJsonObject& obj)
//{
//    if (0 == obj.size())
//    {
//        return;
//    }
//
//    this->_state = (Qt::CheckState)obj.value("state").toInt();
//    this->_bRead = obj.value("bRead").toBool();
//}

QWidget* QMTCheckBoxLabelParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTCheckBoxLabel* checkBoxLabel = new QMTCheckBoxLabel(parent);
    QCheckBox* checkBox = checkBoxLabel->GetCheckBox();
    checkBox->setCheckState(_state);
    checkBoxLabel->SetRead(_bRead);
    return checkBoxLabel;
}

QMTCheckBoxLabel::QMTCheckBoxLabel(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTCheckBoxLabel;
    ui->setupUi(this);
    //InitTemplateStyle();
    SetLabelPix(":/AccuUIComponentImage/images/read.png");
    connect(ui->checkBox, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxStateChange(int)));
}

QMTCheckBoxLabel::~QMTCheckBoxLabel()
{
    MT_DELETE(ui);
}

bool QMTCheckBoxLabel::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::Bool == userType)
    {
        bool bRead = updateData.toBool();
        SetRead(bRead);
        return true;
    }
    else if (QMetaType::Int == userType)
    {
        int state = updateData.toInt();
        SetCheckBoxState((Qt::CheckState)state);
        return true;
    }

    return false;
}

MtCheckBox* QMTCheckBoxLabel::GetCheckBox()
{
    return ui->checkBox;
}

void QMTCheckBoxLabel::SetCheckBoxStyle(QString styleStr)
{
    ui->checkBox->setStyleSheet(styleStr);
}

void QMTCheckBoxLabel::SetCheckBoxText(QString text)
{
    ui->checkBox->setText(text);
}

void QMTCheckBoxLabel::SetCheckBoxState(Qt::CheckState state)
{
    bool bPartially = (Qt::PartiallyChecked == state) ? true : false;
    //如果是半选，那么是手动设置，那么需要解绑信号，以防槽函数重置了state

    if (true == bPartially)
    {
        disconnect(ui->checkBox, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxStateChange(int)));
    }

    ui->checkBox->setCheckState(state);

    if (true == bPartially)
    {
        connect(ui->checkBox, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxStateChange(int)));
        emit sigStateChanged(state);
    }
}

int QMTCheckBoxLabel::GetCheckBoxState()
{
    return ui->checkBox->checkState();
}

void QMTCheckBoxLabel::SetLabelStyle(QString styleStr)
{
    ui->labelPix->setStyleSheet(styleStr);
}

void QMTCheckBoxLabel::SetLabelPix(QString pixPath)
{
    // bool bExists = QFile::exists(pixPath);
    QPixmap pixMap = QPixmap(pixPath);
    // style()->unpolish(ui->labelPix);
    ui->labelPix->setPixmap(pixMap);
    // style()->polish(ui->labelPix);
}

void QMTCheckBoxLabel::SetLabelText(QString text)
{
    ui->labelPix->setText(text);
}

void QMTCheckBoxLabel::SetRead(bool bRead)
{
    QString pixPath;

    if (bRead)
    {
    }
    else
    {
        pixPath = ":/AccuUIComponentImage/images/read.png";
    }

    SetLabelPix(pixPath);
}

void QMTCheckBoxLabel::slotCheckBoxStateChange(int state)
{
    //int stateTmp = GetCheckBoxState();
    //点击的情况是没有半选情况，所以这边转换
    if (Qt::PartiallyChecked == state)
    {
        disconnect(ui->checkBox, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxStateChange(int)));
        state = Qt::Checked;
        ui->checkBox->setCheckState(Qt::Checked);
        connect(ui->checkBox, SIGNAL(stateChanged(int)), this, SLOT(slotCheckBoxStateChange(int)));
    }

    emit sigStateChanged(state);
}

void QMTCheckBoxLabel::InitTemplateStyle()
{
    QString textColor;
    textColor += "QCheckBox::indicator {width: 16px;height: 16px;}";
    textColor += "QCheckBox::indicator:unchecked {image: url(:/AccuUIComponentImage/images/checkboxun.png);}";
    textColor += "QCheckBox::indicator:checked{image: url(:/AccuUIComponentImage/images/check.png);}";
    textColor += "QCheckBox::indicator:indeterminate{image: url(:/AccuUIComponentImage/images/check2.png);}";
    SetCheckBoxStyle(textColor);
}
