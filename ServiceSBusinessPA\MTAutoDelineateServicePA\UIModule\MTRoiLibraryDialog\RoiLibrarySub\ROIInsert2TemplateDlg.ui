<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ROIInsert2TemplateDlg</class>
 <widget class="QWidget" name="ROIInsert2TemplateDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>448</width>
    <height>213</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>12</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtLabel" name="mtLabel">
        <property name="text">
         <string>将勾选的ROI批量添加到模版中</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtLineEdit" name="edit_TemplateSearch">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>26</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>26</height>
         </size>
        </property>
        <property name="placeholderText">
         <string>搜索模板</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLineEdit::lineedit1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtFrameEx" name="mtFrameEx_2">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>1</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>1</height>
         </size>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtFrameEx::default_type</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtListWidget" name="listTemplate">
        <property name="scrollType">
         <enum>MtScrollBar::scrollbar1</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtListWidget::listwidget2</enum>
        </property>
        <property name="_mtItemType" stdset="0">
         <enum>MtListWidget::listitem1</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtListWidget</class>
   <extends>QListWidget</extends>
   <header>MtListWidget.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
