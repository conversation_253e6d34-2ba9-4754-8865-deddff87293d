﻿// *********************************************************************************
// <remarks>
// FileName    : UnattendSubTable
// Author      : zlw
// CreateTime  : 2024-05-23
// Description : 无人值守规则table列表(内嵌于: UnattendSubWidget 无人值守信息界面) -- 预留
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class UnattendSubTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum ColumnType
    {
        COL_Rule = 0
    };
    UnattendSubTable(QWidget* parent = nullptr);
    ~UnattendSubTable();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    void init();

protected:
    /// <summary>
    /// 初始化表格
    /// </summary>
    void initTableView();

private:
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)
};