﻿#include "MTRoiTemplateDialog.h"
#include "ui_MTRoiTemplateDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MTAutoDelineationDialog/AutoSketchSub/AutoSketchTemplateWidget.h"
#include "MTAutoDelineationDialog/AutoSketchSub/OptSketchCollection.h"
#include "CommonUtil.h"


namespace n_mtautodelineationdialog
{

MTRoiTemplateDialog::MTRoiTemplateDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTRoiTemplateDialogClass;
    ui->setupUi(this);
    //重新设置属性，防止subwindow把拖拽事件屏蔽了
    this->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    //基本属性
    this->setMainLayout(ui->verticalLayout);            //设置布局
    this->setDialogWidthAndContentHeight(1400, 760);    //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("勾画模板设置"));                 //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    getWidgetButton()->hide();                          //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    //页面设置
    ui->widget->setEnableButtonUnattend(true);          //默认无人值守按钮势能
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTRoiTemplateDialog, " << errMsg.toStdString();
        }
    }
}

MTRoiTemplateDialog::~MTRoiTemplateDialog()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        OptSketchCollection* temp = (OptSketchCollection*)m_ptrOptSketchCollection;
        delete temp;
        m_ptrOptSketchCollection = nullptr;
        temp = nullptr;
    }
}

/// <summary>
/// 设置是否显示无人值守模板页签
/// 不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true:显示</param>
void MTRoiTemplateDialog::setIsShowUnattendTab(const bool isShow)
{
    ui->widget->setEnableButtonUnattend(isShow);
}

/// <summary>
/// 设置当模板被无人值守使用时，是否显示提示框
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">true显示</param>
void MTRoiTemplateDialog::setIsShowTipOfModUnattendUsed(const bool isShow)
{
    m_IsShowTipUnattendUsed = isShow;
}

/// <summary>
/// 显示自动勾画模板界面
/// </summary>
/// <param name="allGroupInfoList">[IN]所有分组信息</param>
/// <param name="allSketchModelList">[IN]所有勾画模型</param>
/// <param name="allSketchCollectionList">[IN]所有排序后的勾画模板</param>
/// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
/// <returns>QDialog::DialogCode</returns>
QDialog::DialogCode MTRoiTemplateDialog::showSketchTemplateDlg(
    const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
    const QList<ST_SketchModel>& allSketchModelList,
    const QMap<EM_OptDcmType, QList<ST_SketchModelCollection>>& allSketchCollectionMap,
    ST_CallBack_AutoSketch& stCallBackAutoSketch)
{
    //数据初始化
    m_stCallBackAutoSketch = stCallBackAutoSketch;

    if (m_ptrOptSketchCollection != nullptr)
    {
        delete m_ptrOptSketchCollection;
        m_ptrOptSketchCollection = nullptr;
    }

    m_ptrOptSketchCollection = new OptSketchCollection();
    ((OptSketchCollection*)m_ptrOptSketchCollection)->init(allGroupInfoList, allSketchModelList, allSketchCollectionMap, n_mtautodelineationdialog::ST_SketchModelCollection());
    ((OptSketchCollection*)m_ptrOptSketchCollection)->setIsShowTipOfModUnattendUsed(m_IsShowTipUnattendUsed);
    //勾画模板
    ui->widget->setImagePathHash(m_imagePathHash);
    ui->widget->setMargin(0, 0, 0, 0);
    ui->widget->setShowSelectRadioSelectBtn(false);
    ui->widget->initData((OptSketchCollection*)m_ptrOptSketchCollection, stCallBackAutoSketch);
    ui->widget->setMtComboBoxModality("CT", true);
    connect(ui->widget, &AutoSketchTemplateWidget::sigEditTemplateStart, this, [=](const bool isStart) //是否处于模板编辑状态
    {
        this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(!isStart);
    });
    return (QDialog::DialogCode)(this->exec());
}

/// <summary>
/// 获取最新的模板排序信息
/// \n不管是否点击确定都会返回最新的
/// </summary>
/// <returns>最新的模板排序信息</returns>
QMap<EM_OptDcmType, QList<int>> MTRoiTemplateDialog::getNewTemplateIdSortMap()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        return ((OptSketchCollection*)m_ptrOptSketchCollection)->getTemplateIdSortMap();
    }

    return QMap<EM_OptDcmType, QList<int>>();
}

/// <summary>
/// 获取当模板被无人值守使用时，是否显示提示框
/// </summary>
bool MTRoiTemplateDialog::getNewIsShowTipOfModUnattendUsed()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        return ((OptSketchCollection*)m_ptrOptSketchCollection)->getIsShowTipOfModUnattendUsed();
    }

    return true;
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTRoiTemplateDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

}
