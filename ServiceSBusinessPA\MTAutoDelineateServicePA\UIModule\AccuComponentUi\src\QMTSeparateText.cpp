﻿#include "AccuComponentUi\Header\QMTSeparateText.h"
#include "AccuComponentUi\Header\Language.h"
#include "ui_QMTSeparateText.h"

QMTSeparateText::QMTSeparateText(QWidget* parent)
    : QWidget(parent), ui(nullptr)
{
    ui = new Ui::QMTSeparateText;
    ui->setupUi(this);
    SetSeparateText(tr("属性"));
}

QMTSeparateText::~QMTSeparateText()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QMTSeparateText::SetSeparateText(const QString& text)
{
    int with = this->GetTextWidth(this->font(), text);
    ui->label->setFixedWidth(with);
    ui->label->setText(text);
}
