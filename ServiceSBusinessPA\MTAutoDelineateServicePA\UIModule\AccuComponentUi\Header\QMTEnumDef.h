﻿#pragma once
#include <QMap>


/*
UI枚举数据结构定义
*/

#define TOString(key)   #key

/***********************一级列表相关定义*********************************/
#define TableItemParentValueKey  "parentValue"          //json中包含该key的值表示TableWidgetItemIndex中的变量_parentValue
#define TableItemRowItemTypeKey  "rowItemtype"          //json中包含该key的值表示TableWidgetItemIndex中的变量_type

/// <summary>
/// 单元格类型
/// </summary>
enum CellWidgetDelegateType
{
    DELEAGATE_TYPE_STRING = -100,   //文字
    DELEAGATE_QLabel,               //QLabelParam。建议使用新版本的DELEAGATE_QCustMtLabel
    DELEAGATE_TYPE_PIXMAP,          //未用（仅仅为了保证之前的编译）
    DELEAGATE_TYPE_TextWidget,      //蓝底text widget
    DELEAGATE_TYPE_PROCESSBAR,      //状态栏
    DELEAGATE_TYPE_BUTTONS,         //病人页面按键
    DELEAGATE_TYPE_ThumbnailWidget, //缩略图
    //DELEAGATE_HorizontalButtons,    //水平按键(QMTHorizontalButtons,无用)
    DELEAGATE_VDoubleLabelDot,      //多行显示labeldot
    DELEAGATE_QMTLineEdit,          //QMTLineEdit
    DELEAGATE_QMTCheckBox,          //QMTCheckBox
    DELEAGATE_CheckBoxLabel,        //QMTCheckBoxLabel,带有小红点
    DELEAGATE_QMTAbsComboBox,       //QcomboBoxParam。
    DELEAGATE_QMTButtonMenu,        //QMTButtonMenuWidgetParam
    DELEAGATE_QTipPushButton,       //QTipPushButtonParam,建议单个按键也使用委托DELEAGATE_QMTAbsHorizontalBtns
    DELEAGATE_QMTAbsHorizontalBtns, //QMTAbsHorizontalBtnsParam
    DELEAGATE_QMTVDoubleLabelDot,   //QMTVDoubleLabelDot
    DELEAGATE_QMTButtonWithOrder,   //QMTButtonWithOrder
    //新版皮肤UI新增的组件
    DELEAGATE_QCustMtLabel,         //QCustMtLabel。文案显示MtLabel
    DELEAGATE_QCustMtLineEdit,      //QCustMtLineEdit。表单的输入框MtRangeLineEdit
    DELEAGATE_QCustMtComboBox,      //QCustMtComboBox。表单的下拉框MtComboBox
    DELEAGATE_QCustMtSearchComboBox,        //QCustMtComboBox。表单的下拉框MtComboBox,带搜索
    DELEAGATE_MtUnitPushButtonGroup,        //MtUnitPushButtonGroup。MtPushButton的集合。
    DELEAGATE_MtUnitToolButtonGroup,        //MtUnitToolButtonGroup。MtToolButton的集合。
    DELEAGATE_MtUnitLabelWithColor,         //MtUnitLabelWithColor。color+label
    //移入MOZI旧框架下的单元UI
    DELEAGATE_QCustDoubleClickLineEdit,     //QCustDoubleClickLineEdit
    DELEAGATE_QCustPairLineEdit,            //QCustPairLineEdit
    DELEAGATE_QCustColorButton,             //QCustColorButton
    DELEAGATE_QCustLabelWithButton,             //QCustLabelWithButton
    DELEAGATE_TYPE_WIDGET,          //任意widget
    DELEAGATE_TYPE_User,            //用户自定义类型,可以从这个类型开始往上加数值
};

enum EPerRowType
{
    PerRow_None = 0,
    Patient_normal = 1,
    PerRow_FirstLevelItem = 2,//(如果是二级列表就是第一级)
    Series_Normal,//CT序列
    PerRow_RowItem, //<=主项(如果是二级列表就是第二级)
    PerRow_SubItems,//>=子项
    Series_RtFile,//RT文件
    Series_PlanFile,//Plan文件
    Series_DoseFile,//Dose文件
};
#define ExpandRowItem   Series_RtFile

//per row item param
typedef bool(*SetReadCallBack)(const char*);

enum RowWidgetTemplateType
{
    Template_None,
    Template_Patient,
    Template_Series,
    Template_Adjust,
    Template_User = 0x10,
};

//QMTPerRowItemWidgetParam::s_type = 0;
class  QMTPerRowItemWidgetParam  //后续废除
{
public:
    static int GetCurrentType()
    {
        return s_type++;
    }
public:
    int _templateWidgetType = Template_None;//此参数是界面模板必须重设置，使用GetCurrentType()
    EPerRowType _rowType = Patient_normal;  //区别同一级不同rowitem，参照EPerRowType
    int _defaultColumn = 1;                 //列个数
    QMap<int, int> _colDelegateTypeMap;     //委托类型参见CellWidgetDelegateType
    QMap<int, QString> _columnKeyMap;       //每一列key
    QMap<int, int> _columnWidthMap;         //宽度
    QMap<int, bool> _columnWordwrapMap;     //是否换行
    QMap<int, int> _columnLabelTypeMap;     //0:QLabel, 1:QLabel_Dot
    QString _mainKey;                       //当前行的key
    SetReadCallBack setReadCallBack = nullptr;
    //ui
    int _rowWidgetheight = 76;
    int _rightMargin = 16;
    int _leftMargin = 16;
    bool _hideCheckBox = false;
    int fontsize = -1;
    QString _unselectRgba = "37, 41, 48, 1";    //未选中背景色
    QString _selectRgba = "55, 64, 80, 1";      //选中背景色
    bool _isEnableHoverChangeBorderColor = false;   //悬浮是否改变边框色
    bool _isEnableHoverChangeBackColor = false;     //悬浮是否改变背景色
    int _layoutSpacing = 16;
    static int s_type;
};


namespace mtuiData
{

//按键排序状态
enum ButtonOrderState
{
    State_normal = 0,       //初始化状态
    State_asc,              //升序
    State_desc,             //降序
    State_number
};
}