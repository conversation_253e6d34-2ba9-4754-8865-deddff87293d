﻿#pragma once
#include <qlineedit.h>
#include <QValidator>
#include "AccuComponentUi/Header/TextManage.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"


/// <summary>
/// 文本靠左显示
/// 1. 文本更新后，光标始终在最左
/// 2. 使能后，光标位置重新靠右，禁能靠左
/// 3. 双击后才能编辑
///QMTAbstractTableView中使用QMTLineEdit控件
/***********************************************************************/
class  QMTAbsLineEdit :
    public QLineEdit, public TextManage, public QMTAbstractCellWidget
{
    Q_OBJECT
public:

    QMTAbsLineEdit(QWidget* parent = Q_NULLPTR);
    QMTAbsLineEdit(const QString& contents, QWidget* parent = Q_NULLPTR);
    virtual ~QMTAbsLineEdit();
    void SetEnableEdit(bool);   //设置是否可以双击编辑
    void SetRegExpStr(QString&);//设置正则表达式
    void setText(const QString&);
    void setEnabled(bool);
    void setReadOnly(bool);

signals:
    void sigEditFinish(QString);
protected:
    void mouseDoubleClickEvent(QMouseEvent* event);
    void UpdateNewTextDot();
private:
    bool _isDClickEdit = true;//默认双击可以修改
    QValidator* _validator = nullptr;
    QString _fullString;
};

