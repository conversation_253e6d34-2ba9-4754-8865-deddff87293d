﻿#include "ModelOrganItem.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include <iostream>


ModelOrganItem::ModelOrganItem(const QString& organName, const QString& organName2, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    ui.mtLabel_organName1->setText(organName);
    ui.mtLabel_organName2->setText(organName2);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,ModelOrganItem, " << errMsg.toStdString();
        }
    }
}

ModelOrganItem::~ModelOrganItem()
{}
