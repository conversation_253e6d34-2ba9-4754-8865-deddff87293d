﻿#include "AccuComponentUi\Header\QMTUnattendedStateWidget.h"
#include "ui_QMTUnattendedStateWidget.h"
#include "CMtCoreDefine.h"

QMTUnattendedStateWidget::QMTUnattendedStateWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTUnattendedStateWidget;
    ui->setupUi(this);
    SetWidgetLogoSize(54, 54);
    QString logoPath = ":/AccuUIComponentImage/images/unattended_logo.png";
    SetWidgetLogoPaht(logoPath);
    _stateStrList.append(tr("无人值守中..."));
    _stateStrList.append(tr("无人值守勾画中..."));
    SetCurState(0);
}

QMTUnattendedStateWidget::~QMTUnattendedStateWidget()
{
    MT_DELETE(ui);
}

void QMTUnattendedStateWidget::SetWidgetLogoSize(int width, int height)
{
    ui->widget_logo->setFixedSize(width, height);
}

void QMTUnattendedStateWidget::SetWidgetLogoPaht(const QString& logoPath)
{
    style()->unpolish(ui->widget_logo);           //移除原先的样式
    QString styleSheetStr = QString("#widget_logo{border-image: url(%1);}").arg(logoPath);
    ui->widget_logo->setStyleSheet(styleSheetStr);
    style()->polish(ui->widget_logo);             //设置当前样式生效，为了解决手动设置不生效问题
}

void QMTUnattendedStateWidget::SetRotatePath(const QString& rotatePath)
{
    ui->label_rotate->setUrl(rotatePath);
}

void QMTUnattendedStateWidget::HideStateStr(bool bHide)
{
    if (bHide)
    {
        ui->label_state->hide();
    }
    else
    {
        ui->label_state->show();
    }
}

void QMTUnattendedStateWidget::SetStateStrList(QStringList& stateStrList)
{
    _stateStrList = stateStrList;
}

void QMTUnattendedStateWidget::SetCurState(int state)
{
    if (state < 0 || state >= _stateStrList.size())
        return;

    _state = state;
    ui->label_state->setText(_stateStrList.at(state));
}

void QMTUnattendedStateWidget::setInterval(int interval)
{
    ui->label_rotate->setInterval(interval);
}

void QMTUnattendedStateWidget::setClockwise(bool clockwise)
{
    ui->label_rotate->setClockwise(clockwise);
}

int QMTUnattendedStateWidget::GetCurState()
{
    return _state;
}

bool QMTUnattendedStateWidget::event(QEvent* event)
{
    tooltipEvent(event, this);
    return QWidget::event(event);
}
