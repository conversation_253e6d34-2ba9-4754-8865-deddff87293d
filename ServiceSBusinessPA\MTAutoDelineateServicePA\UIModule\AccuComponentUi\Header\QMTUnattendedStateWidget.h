﻿#pragma once

#include <QWidget>
#include "Language.h"

#include "IMtToolTip.h"

namespace Ui
{
class QMTUnattendedStateWidget;
}

class  QMTUnattendedStateWidget : public QWidget
    , public IMtToolTip
{
    Q_OBJECT

public:
    QMTUnattendedStateWidget(QWidget* parent = Q_NULLPTR);
    ~QMTUnattendedStateWidget();
    /*************ui*****************/
    void SetWidgetLogoSize(int width, int height);      //设置logo的图标背景大小
    void SetWidgetLogoPaht(const QString& logoPath);    //设置logo路径
    void SetRotatePath(const QString& rotatePath);      //设置旋转的图片
    void HideStateStr(bool bHide);              //隐藏状态显示的label

    /**************set***********************/
    void SetStateStrList(QStringList& stateStrList);        //设置不同状态显示的文案
    void SetCurState(int state);            //设置当前状态
    void setInterval(int interval);         //设置动态旋转评率
    void setClockwise(bool clockwise);      //设置是否顺时针

    /**************get***********************/
    int GetCurState();                      //获取当前状态
    virtual bool event(QEvent* event) override;
private:
    Ui::QMTUnattendedStateWidget* ui;
    QStringList _stateStrList;
    int _state;
};
