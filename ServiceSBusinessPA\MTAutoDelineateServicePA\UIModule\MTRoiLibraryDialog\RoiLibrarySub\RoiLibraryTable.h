﻿// *********************************************************************************
// <remarks>
// FileName    : RoiLibraryTable
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : Roi库设置列表(适用于: RoiLibraryWidget 空勾画设置)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include <thread>
#include <mutex>
#include <condition_variable>

class RoiLibraryTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum
    {
        ColType_CheckBox = 0,
        ColType_Name,
        ColType_Label,
        ColType_ChName,
        ColType_Color,
        ColType_Type,
        ColType_Group,
        ColType_Source,
        ColType_Desc,
        ColType_Modality,
        ColType_State,
        ColType_Operation
    };
    /// <summary>
    /// 构造函数
    /// </summary>
    RoiLibraryTable(QWidget* parent = nullptr);
    ~RoiLibraryTable();

    /// <summary>
    /// 设置外层对话框，用于获取/设置对话框的确定按钮状态
    /// </summary>
    void setFrameDialog(QWidget* parent);

    void tableDestroying();

    /// <summary>
    /// 列表是否初始化完成.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>bool.</returns>
    bool isTableInitialized();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="allRoiTypeList">[IN]全部Roi类型集合</param>
    /// <param name="stRoiLabelInfoList">[IN]全部标签</param>
    /// <param name="allGroupList">[IN]全部分组信息</param>
    /// <param name="stOrganList">[IN]Organ信息集合</param>
    /// <param name="modelInfoMap">[IN]模型信息集合</param>
    /// <param name="modelCollectionInfoList">[IN]模板信息集合</param>
    void init(const QStringList& allRoiTypeList,
              const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList,
              const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
              const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
              const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 刷新表格
    /// </summary>
    /// <param name="stOrganList">[IN]表格中要展示的Organ信息集合</param>
    void updateTable(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList);

    /// <summary>
    /// 过滤展示行
    /// </summary>
    void filterTable(const QString& custName, const QString& label, const QString& chName, const QString& group);

    /// <summary>
    /// 勾选的ROI恢复默认设置
    /// </summary>
    void resetROIDefaultInfo();

    /// <summary>
    /// 批量设置ROI
    /// </summary>
    void batchROISetting();

    /// <summary>
    /// 勾选的ROI加入到模板
    /// </summary>
    void addROI2Template();

    /// <summary>
    /// 重置默认信息
    /// </summary>
    void resetDefaultInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 将标签库中同名的roi的信息同步到当前模型ROI信息中
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="stRoiLabelInfoVec">标签库中获取到的信息</param>
    /// <param name="bLabel">是否同步标签名</param>
    /// <param name="bRoiType">是否同步roi类型</param>
    /// <param name="bColor">是否同步roi颜色</param>
    /// <param name="bChName">是否同步roi中文名</param>
    /// <param name="groupList">更新的分组</param>
    /// <param name="bDesc">是否同步roi描述</param>
    /// <param name="desc">更新的roi描述</param>
    void syncLabelLibraryInfo(/*const QVector<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec,*/
                              bool bLabel, bool bRoiType, bool bColor, bool bChName, const QStringList& groupList,
                              bool bDesc, const QString& desc);

    /// <summary>
    /// 添加可编辑行
    /// </summary>
    /// <param name="stOrganInfo">[IN]Organ信息</param>
    /// <param name="nRowIndex">[IN]添加行所在的行号，若为-1，则添加到末尾</param>
    void addRow(const n_mtautodelineationdialog::ST_Organ& stOrganInfo, int nRowIndex = -1);

    /// <summary>
    /// 添加只读辑行
    /// </summary>
    /// <param name="stOrganInfo">[IN]Organ信息</param>
    /// <param name="nRowIndex">[IN]添加行所在的行号，若为-1，则添加到末尾</param>
    void addStaticRow(const n_mtautodelineationdialog::ST_Organ& stOrganInfo, int nRowIndex = -1);

    /// <summary>
    /// 删除选中行
    /// </summary>
    bool delSelectedRow();

    /// <summary>
    /// 获取所有Organ信息
    /// 发生修改的optTypeEnum会设置为OptType_Mod
    /// </summary>
    /// <returns>所有Organ信息</returns>
    QList<n_mtautodelineationdialog::ST_Organ> getAllOrganInfo();

    /// <summary>
    /// 获取所有分组信息
    /// </summary>
    /// <returns>所有分组信息</returns>
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getAllOrganGroupInfo();

    /// <summary>
    /// 返回所有的模板信息
    /// </summary>
    /// <returns>所有模板信息</returns>
    const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& getAllModelCollectionInfoList();

    /// <summary>
    /// 返回所有的标签信息
    /// </summary>
    /// <returns>所有标签信息</returns>
    const QList<n_mtautodelineationdialog::ST_RoiLabelInfo> getAllLabelInfoList();

    /// <summary>
    /// 设置分组列按钮图片
    /// 图片路径
    /// </summary>
    void setGroupColumnBtnImage(const QString& imagePath);

    /// <summary>
    /// 合并新增的器官信息到缓存
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="stOrganInfoVec">所有的器官信息.</param>
    void mergeNewOrganInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec);

    /// <summary>
    /// 合并新增的模型信息到缓存.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelInfoMap">所有的模型信息.</param>
    void mergeNewModelInfo(const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 移除模型ID为modelId的器官和模型信息缓存.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">The model identifier.</param>
    void removeOrganAndModelCacheItem(int modelId);

    /// <summary>
    /// 修改模型缓存信息.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">The model identifier.</param>
    /// <param name="modelName">Name of the model.</param>
    /// <param name="modelDesc">The model desc.</param>
    void updateModelInfo(int modelId, const QString& modelName, const QString& modelDesc);

signals:
    /// <summary>
    /// 列表初始化完成
    /// </summary>
    void sigTableInitialized();

    /// <summary>
    /// 列表信息发生了改变
    /// </summary>
    void sigTableInfoChanged(const QString& defOrganName, int col, const QString& newText);

    /// <summary>
    /// 清除列表
    /// </summary>
    void sigClearTableItems();

    /// <summary>
    /// 创建列表Item信号
    /// </summary>
    void sigCreateTableRowItem(n_mtautodelineationdialog::ST_Organ organInfo, bool bLast);

    /// <summary>
    /// 模板关联的roi取消了分组
    /// </summary>
    void sigRemoveRoiRelatedGroup(int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap);

    /// <summary>
    /// 更新ROI分组信息到数据库，并重新读取组信息
    /// </summary>
    /// <param name="curGroupList">当前所有ROI分组，包括更新了组名和新增（组id小于0）的分组</param>
    /// <param name="delGroupList">删除的ROI分组</param>
    /// <param name="updtedGroupList">传回的分组信息，因为有新增和删除分组，组id会不合法。所以在入库后需要重新读取组信息</param>
    /// <returns></returns>
    void sigUpdateRoiGroup(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList
                           , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList
                           , QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList);

    /// <summary>
    /// 器官缓存信息修改.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="organInfo">修改的器官信息.</param>
    void sigOrganInfoChanged(const n_mtautodelineationdialog::ST_Organ& organInfo);

    /// <summary>
    /// 发送信号，以获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN][OUT]返回从标签库中获取的所有记录信息</param>
    void sigGetLabelLibraryInfo(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 发送信号，以获取器官默认设置
    /// </summary>
    /// <param name="stOrganDefaultList">[IN][OUT]返回从标签库中获取的所有记录信息</param>
    void sigGetOrganDefaultInfo(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

protected slots:
    /// <summary>
    /// 某个按键点击了
    /// </summary>
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);

    /// <summary>
    /// 单元格文本发送了改变
    /// </summary>
    void slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText);

    /// <summary>
    /// 分组设置按键点击了
    /// </summary>
    void slotGroupButtonClicked(int btnIndex, bool bChecked);

    /// <summary>
    /// 标签设置按键点击了
    /// </summary>
    void slotLabelButtonSettingClicked(int btnIndex, bool bChecked);

    /// <summary>
    /// 清除列表
    /// </summary>
    void slotClearTableItems();

    /// <summary>
    /// 添加一条记录
    /// </summary>
    void slotCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo, bool bLast);

    /// <summary>
    /// 组发生了变化
    /// </summary>
    void slotGroupChanged(int nIndex, int state, const QString& itemText);

    /// <summary>
    /// 点击了单元格
    /// </summary>
    void slotCellItemClicked(const mtuiData::TableWidgetItemIndex& tableItemIndex);

protected:
    /// <summary>
    /// 重载单元格创建完成后回调
    /// </summary>
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget);

    /// <summary>
    /// 创建行界面完成回调,用于外部设置定制化UI刷新
    /// </summary>
    virtual void CreateRowWidgetFinishCallBack(const QString& rowValue, int rowItemType);

    /// <summary>
    /// 重载表头按钮重载信号连接
    /// </summary>
    virtual void ConnectHeadWidgetSignals(int cellWidgetType, QWidget* cellWidget);

    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <param name="headList">[IN]表头文本集合</param>
    /// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);

    /// <summary>
    /// 处理行组发生改变
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="newGroupStr">改变后的分组</param>
    /// <returns>返回true表示允许发生变化，返回false表示分组改变被终止</returns>
    bool rowGroupInfoChanging(const QString& rowValue, const QString& newGroupStr);

    /// <summary>
    /// 是否选择了item
    /// </summary>
    bool isItemSelected();

    void createUpdateTableThreadMgr();

private:
    QStringList m_allRoiTypeList;                                                               //全部Roi类型集合
    QStringList m_allLabelList;                                                                 //全部标签名
    QMap<QString/*labelName*/, n_mtautodelineationdialog::ST_RoiLabelInfo> m_roiLabelInfoMap;   //全部标签
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> m_allOrganGroupList;                    //所有的分组信息
    QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel> m_modelInfoMap;             //所有模型信息
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> m_modelCollectionInfoList;       //所有模板信息
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> m_modelCollectionInfoList2;      //所有模板信息(记录初始时的模板信息，不做修改，用于还原m_modelCollectionInfoList)
    QMap<int, n_mtautodelineationdialog::ST_Organ> m_allOrganMap;                               //全部Organ信息集合(key-organId)
    QString m_groupColBtnImagePath;                                                             //分组列设置按钮图片
    QWidget* m_frameDialog = nullptr;                                                           //外层对话框，用于获取确定按钮的状态

    std::mutex m_mtxNewUpdtTask;
    std::mutex m_mtxCreateTableItem;
    std::condition_variable m_cvNewUpdtTask;
    std::condition_variable m_cvCreateTableItem;
    std::thread m_thrUpdateTable;                                                               //刷新列表线程
    std::thread m_thrMgrUpdateTable;                                                            //刷新列表管理线程
    QList<QList<n_mtautodelineationdialog::ST_Organ>>   m_updateOrganDataList;

    int m_editingOrganID = -2000;                                                               //正在编辑的Organ id

    bool m_bInitialized = true;                                                                 //列表是否初始化完成
    bool m_bDestroyed = false;                                                                  //列表是否被销毁
    bool m_bTableItemCreated = true;                                                            //线程是否已经完成了一条Item的创建

    static bool m_bSyncInfo2Label;                                                              //是否同步更新到标签库(程序生命周期内，该选项有效)
};