﻿// *********************************************************************************
// <remarks>
// FileName    : MTAutoDelineationDialog
// Author      : zlw
// CreateTime  : 2023-10-30
// Description : 自动勾画器官选择界面
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTAutoDelineationDialogClass;
}

namespace n_mtautodelineationdialog
{

/// <summary>
/// 自动勾画器官选择界面
/// </summary>
class MTAutoDelineationDialog : public MtTemplateDialog, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    MTAutoDelineationDialog(QWidget* parent = nullptr);
    MTAutoDelineationDialog(const int width, const int height, QWidget* parent = nullptr);
    ~MTAutoDelineationDialog();

    /// <summary>
    /// 设置是否显示合并已有rtStruct选项
    /// \n不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true:显示合并已有rtStruct选项</param>
    /// <param name="mergeRtMap">[IN]可选择的所有可合并的rtStruct-sopInsUID集合</param>
    /// <param name="selectMergeRtSopUID">[IN]可选择的所有可合并的rtStruct集合(key-rtSopInsUID value-需显示的文本)</param>
    void setIsShowMergeOption(const bool isShow, const QStringList& allMergeRtSopInsUIDList, const QMap<QString, QString>& allMergeRtValMap);

    /// <summary>
    /// 设置是否显示勾画完后自动导出选项
    /// 不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true:显示勾画完后自动导出选项</param>
    /// <param name="allExportRangeMap">[IN]所有的导出范围选项(key-范围类型(1:导出该患者所有数据 2:只导出当前勾画RtStructure 3:导出当前勾画图像及RtStructure)</param>
    void setIsShowAutoExport(const bool isShow, const QList<int>& allExportRangeList);

    /// <summary>
    /// 设置是否显示无人值守模板页签
    /// \n不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true:显示</param>
    void setIsShowUnattendTab(const bool isShow);

    /// <summary>
    /// 设置需提前选中的模板id
    /// \n不设置: 默认不选中
    /// </summary>
    /// <param name="templateId">[IN]模板id</param>
    void setSelectTemplateId(const int templateId);

    /// <summary>
    /// 设置提前选中的页签类型
    /// \n 不设置: 默认选中2-选择模板进行勾画
    /// </summary>
    /// <param name="pageType">[IN]页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
    void setSelectRadioPageType(const int pageType);

    /// <summary>
    /// 设置当模板被无人值守使用时，是否显示提示框
    /// \n不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true显示</param>
    void setIsShowTipOfModUnattendUsed(const bool isShow);

    /// <summary>
    /// 设置虚拟模板
    /// \n用于*选择ROI进行勾画*页签进行器官提前选中
    /// </summary>
    void setVirtualSketchCollection(ST_SketchModelCollection& stSketchCollection);

    /// <summary>
    /// 显示自动勾画界面
    /// </summary>
    /// <param name="allGroupInfoList">[IN]所有分组信息</param>
    /// <param name="allExportAddr">[IN]所有导出地址信息</param>
    /// <param name="srcStAutoSketch">[IN]勾画信息</param>
    /// <param name="bShowBottomFuncWidget">[IN]是否显示勾画结束的操作栏</param>
    /// <param name="bShowTemplateBtnWidget">[IN]是否显模板操作栏</param>
    /// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
    /// <returns>QDialog::DialogCode</returns>
    QDialog::DialogCode showAutoSketchDlg(
        const QList<ST_OrganGroupInfo>& allGroupInfoList,
        const QList<ST_AddrSimple>& allExportAddr,
        const ST_AutoSketch& stAutoSketch,
        bool bShowBottomFuncWidget,
        bool bShowTemplateBtnWidget,
        ST_CallBack_AutoSketch& stCallBackAutoSketch
    );

    /// <summary>
    /// 获取最新的勾画信息
    /// \n点击确定按钮生效
    /// </summary>
    /// <param name="outSketchCollection">[OUT]待勾画的模板信息</param>
    /// <param name="outCheckedMergeRt">[OUT]是否选择了合并已有rtStruct选项</param>
    /// <param name="outMergeRtSopInsUID">[OUT]合并已有rtStruct-sopInsUID</param>
    /// <param name="outCheckedExport">[OUT]是否选择了勾画完导出选项</param>
    /// <param name="outStAddrSimple">[OUT]导出地址简易信息</param>
    void getNewOutInfo(ST_SketchModelCollection& outSketchCollection, bool& outCheckedMergeRt, QString& outMergeRtSopInsUID, bool& outCheckedExport, ST_AddrSimple& outStAddrSimple);

    /// <summary>
    /// 获取最新的虚拟模板
    /// \n用于*选择ROI进行勾画*页签进行器官提前选中
    /// </summary>
    /// <returns>最新的虚拟模板</returns>
    ST_SketchModelCollection getVirtualSketchCollection();

    /// <summary>
    /// 获取最新的模板排序信息
    /// \n不管是否点击确定都会返回最新的
    /// </summary>
    /// <returns>最新的模板排序信息</returns>
    QMap<EM_OptDcmType, QList<int>> getNewTemplateIdSortMap();

    /// <summary>
    /// 获取当模板被无人值守使用时，是否显示提示框
    /// </summary>
    bool getNewIsShowTipOfModUnattendUsed();

    /// <summary>
    /// 获取选中的页签类型
    /// </summary>
    /// <returns>页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</returns>
    int getSelectRadioPageType();

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

    /// <summary>
    /// 检查模板是否可用
    /// </summary>
    /// <returns>true可用</returns>
    bool checkSketchCollectionAvail();

protected slots:
    /// <summary>
    /// 导出按钮
    /// </summary>
    void onMtPushButtonExport();

    /// <summary>
    /// 合并已有rt
    /// </summary>
    void onMtCheckBoxRt(int state);

    /// <summary>
    /// 勾画完自动导出
    /// </summary>
    void onMtCheckBoxExport(int state);

    /// <summary>
    /// 模型和ROI设置
    /// </summary>
    void onMtPushButtonModelRoiSetting();

protected:
    virtual void onBtnCloseClicked() override;      //关闭按钮
    virtual void onBtnRight2Clicked() override;     //取消按钮
    virtual void onBtnRight1Clicked() override;     //确认按钮

private:
    Ui::MTAutoDelineationDialogClass* ui;
    bool m_IsShowMergeOption = true;
    bool m_IsShowAutoExport = true;
    bool m_IsShowTipUnattendUsed = true;
    ST_AddrSimple m_addrSimple;                     //地址简易信息
    QList<ST_AddrSimple> m_allExportAddr;           //所有导出地址信息
    QHash<QString, QString> m_imagePathHash;        //图片资源路径(key-name value-图片相对路径)
    ST_CallBack_AutoSketch m_stCallBackAutoSketch;  //数据回调
    void* m_ptrOptSketchCollection = nullptr;       //勾画模板信息
};

}
