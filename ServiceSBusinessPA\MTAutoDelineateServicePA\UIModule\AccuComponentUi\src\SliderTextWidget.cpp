﻿#include "AccuComponentUi\Header\SliderTextWidget.h"
#include "ui_SliderTextWidget.h"
#include "CMtCoreDefine.h"
#include <qrgb.h>
#include <QRect>
#include "AccuComponentUi\Header\QMTUIDefine.h"

SliderTextWidget::SliderTextWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::SliderTextWidget;
    ui->setupUi(this);
    ui->label_name->setProperty(QssPropertyKey, QssPropertyLabelThirdTitle);
    QRect re = this->geometry();
    ui->label_name->setGeometry(re);
    //ui->horizontalSlider_opacity->setGeometry(re);
    //connect(ui->horizontalSlider_opacity, SIGNAL(valueChanged(int)), this, SIGNAL(sigSliderValueChange(int)));
    QFont font = ui->label_name->font();
    font.setBold(true); // 设置字体为加粗
    ui->label_name->setFont(font);
}

SliderTextWidget::~SliderTextWidget()
{
    MT_DELETE(ui);
}

void SliderTextWidget::setColor(QColor color)
{
    QString sheet = "background-color: rgb(" + QString::number(color.red()) + "," +
        QString::number(color.green()) + "," + QString::number(color.blue()) + ");";
    ui->label_name->setStyleSheet(sheet);
}

void SliderTextWidget::setName(QString name)
{
    _name = name;
    ui->label_name->setText(name);
}

void SliderTextWidget::setTextElided(const QString& text)
{
    setName(text);
}

void SliderTextWidget::setText(QString name)
{
    setName(name);
}
