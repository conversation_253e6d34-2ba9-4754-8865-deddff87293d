﻿#include "MTAutoDelineateServicePA.h"
#include <QDebug>
#include <QTranslator>
#include <QJsonArray>

#include "MTAutoDelineateService.h"
#include "MTErrorMsg.h"

#include "Skin/CMtSkinDefine.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
//内部头文件
#include "CommonUtil.h"

#define SkinDefault_AutoDelineateServiceQssPath     ":/MTAutoDelineateServicePA/qss"                      //自动勾画模块库带的qss文件的路径
#define SkinDefault_AutoDelineateServiceXMLPath     ":/MTAutoDelineateServicePA/qssSetting"                   //自动勾画模块库带的xml文件的路径
#define SkinOrange_AutoDelineateServiceQssPath      SkinDefault_AutoDelineateServiceQssPath           //自动勾画模块库带的qss文件的路径
#define SkinOrange_AutoDelineateServiceXMLPath      ":/MTAutoDelineateServicePA/qssSettingOrange"         //自动勾画模块库带的xml文件的路径


MTAutoDelineateServicePA::MTAutoDelineateServicePA()
    : mtcore::IMTPluginBase(this)
{
}

MTAutoDelineateServicePA::~MTAutoDelineateServicePA()
{
}

int MTAutoDelineateServicePA::InitService(const QStringList& lstBusinessPluginPath, const QString& configDir, QString& errMsg)
{
    QString configJson = configDir + "/MTAutoDelineateServiceConfig.json";
    return 0;
}

void MTAutoDelineateServicePA::Start(mtcore::IMTContextBase* context)
{
    m_context = context;
    RegisterService();
}

void MTAutoDelineateServicePA::Stop(mtcore::IMTContextBase* context)
{
    UnRegisterService();
}

void MTAutoDelineateServicePA::RegisterService()
{
    m_delineateServices = new MTAutoDelineateService(m_context);

    mtcore::MTErrorMsg errMsg;
    QString serviceClassName = mtcore::MTGlobalFuc_GetClassName<IMTAutoDelineateService>();
    bool bOK = m_context->RegisterService(serviceClassName, m_delineateServices, this, m_servicePassword, errMsg);

    if (!bOK)
    {
        qWarning() << errMsg.GetErrMsg();
    }

    AddSkinQssPath_DoMain(mtSkin::SkinStyle_Default, SkinDefault_AutoDelineateServiceQssPath, SkinDefault_AutoDelineateServiceXMLPath);
    AddSkinQssPath_DoMain(mtSkin::SkinStyle_Default, SkinDefault_AutoDelineateServiceQssPath, SkinDefault_AutoDelineateServiceXMLPath);
}

void MTAutoDelineateServicePA::UnRegisterService()
{
    if (m_servicePassword.isEmpty())
    {
        return;
    }

    mtcore::MTErrorMsg errMsg;
    bool bOK = m_context->UnRegisterService(m_servicePassword, true, true, errMsg);

    if (!bOK)
    {
        qWarning() << errMsg.GetErrMsg();
    }
    else
    {
        delete m_delineateServices;
        m_delineateServices = nullptr;
    }
}

void MTAutoDelineateServicePA::AddSkinQssPath_DoMain(mtSkin::AppSkinStyle skinStyle, const QString& qssPath, const QString& qssSettingPath)
{
    mtSkin::DomainSkinStyleLibInfo domainSkinStyleInfo;
    domainSkinStyleInfo.qssPath = qssPath;
    domainSkinStyleInfo.qssSettingPath = qssSettingPath;
    CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();
    QString errMsg;

    if (false == skinManager->AddDomainSkinStyleLibInfo(skinStyle, domainSkinStyleInfo, errMsg))
    {
        qWarning() << "CMtSkinManager AddSkinQssPath_DoMain failed,qss=" << qssPath << ",xml=" << qssSettingPath << ",errMsg=" << errMsg;
    }
}
