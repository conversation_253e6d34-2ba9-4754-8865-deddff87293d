﻿#pragma once

#include <QWidget>

//#include "LearnParam.h"

//显示下拉或者显示label


class ItemParam
{
public:
    bool _isEdit = false;
    QString _key;//左侧显示
    QString _value;//右侧值
    QStringList _valueList;//右侧可选值
};
namespace Ui
{
class QMTParamItemWidget;
}

class  QMTParamItemWidget : public QWidget
{
    Q_OBJECT

public:
    QMTParamItemWidget(QWidget* parent = Q_NULLPTR);
    QMTParamItemWidget(ItemParam& param, bool hide,
                       ItemParam& param_2, bool hide_2,
                       ItemParam& param_3, bool hide_3, QWidget* parent = Q_NULLPTR);
    ~QMTParamItemWidget();

    inline void setRow(const int& row)
    {
        m_row = row;
    };
    //void setLearnParam(LearnParam* learnParam);
    //void SetLearnParam(int index, LearnParam* learnParam);
    void SetItemParam(int index, ItemParam& param);
protected slots:
    void slotValueChanged(const QString& value);
signals:
    void sigValueChanged(const int& row, const QString& value);
private:
    Ui::QMTParamItemWidget* ui;
    int m_row;
};
