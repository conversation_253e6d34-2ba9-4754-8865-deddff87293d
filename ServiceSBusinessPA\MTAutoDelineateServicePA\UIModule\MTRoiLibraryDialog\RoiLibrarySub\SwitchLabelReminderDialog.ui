<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SwitchLabelReminderWidget</class>
 <widget class="QWidget" name="SwitchLabelReminderWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>354</width>
    <height>122</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="box_widget" native="true">
     <property name="styleSheet">
      <string notr="true">#label_icon{
background-image:url(:/Manteia.Core.Widget/images/messagebox_info.png);
background-repeat:no-repeat;
background-position:center;
}

#main_text
{
	width: 230px;
	height: 19px;
	font-size: 14px;
}

#mtLabel
{
	width: 156px;
	height: 16px;
	font-size: 12px;
}

#mtCheckBox_color, #mtCheckBox_name, #mtCheckBox_type
{
	width: 68px;
	height: 16px;
	font-size: 12px;
}</string>
     </property>
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="horizontalSpacing">
       <number>24</number>
      </property>
      <property name="verticalSpacing">
       <number>0</number>
      </property>
      <item row="0" column="0">
       <widget class="MtLabel" name="main_text">
        <property name="text">
         <string>是否确定将标签切换为“xxxxxx”?</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="elideMode">
         <enum>Qt::ElideNone</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QWidget" name="widget_2" native="true">
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="spacing">
          <number>8</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="MtLabel" name="mtLabel">
           <property name="text">
            <string>使用选中标签对应的配置信息</string>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtLabel::myLabel1</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MtFrameEx" name="mtFrameEx">
           <property name="_mtType" stdset="0">
            <enum>MtFrameEx::default_type</enum>
           </property>
           <layout class="QGridLayout" name="gridLayout_2">
            <property name="leftMargin">
             <number>25</number>
            </property>
            <property name="topMargin">
             <number>14</number>
            </property>
            <property name="rightMargin">
             <number>25</number>
            </property>
            <property name="bottomMargin">
             <number>14</number>
            </property>
            <property name="horizontalSpacing">
             <number>50</number>
            </property>
            <property name="verticalSpacing">
             <number>12</number>
            </property>
            <item row="1" column="0">
             <widget class="MtCheckBox" name="mtCheckBox_type">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>68</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>ROI类型</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item row="1" column="1">
             <widget class="MtCheckBox" name="mtCheckBox_ROIChName">
              <property name="text">
               <string>器官名称</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item row="0" column="0">
             <widget class="MtCheckBox" name="mtCheckBox_name">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>16</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>100</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>ROI名称</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item row="0" column="1">
             <widget class="MtCheckBox" name="mtCheckBox_color">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>16</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>68</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>颜色</string>
              </property>
              <property name="hitArea">
               <enum>MtCheckBox::default_area</enum>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item row="0" column="2">
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
