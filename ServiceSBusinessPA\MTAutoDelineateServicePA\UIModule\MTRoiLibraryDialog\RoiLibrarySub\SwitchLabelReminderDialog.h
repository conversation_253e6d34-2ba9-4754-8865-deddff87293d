﻿#pragma once

#include "MtTemplateDialog.h"
#include "ui_SwitchLabelReminderDialog.h"

class SwitchLabelReminderDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    SwitchLabelReminderDialog(QWidget* parent = nullptr);
    ~SwitchLabelReminderDialog();

    void setMainText(const QString& str);

    bool isNameChecked();

    bool isChineseNameChecked();

    bool isTypeChecked();

    bool isColorChecked();

public slots:

protected:
    virtual void onBtnCloseClicked() override;
    virtual void onBtnRight2Clicked() override;
    virtual void onBtnRight1Clicked() override;

private:
    void initCheckBox();

    Ui::SwitchLabelReminderWidget* ui = nullptr;
};
