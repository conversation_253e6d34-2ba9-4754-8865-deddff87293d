﻿#include <QMouseEvent>
#include <Windows.h>

#include "CWidgetButton.h"
#include "AccuComponentUi\Header\Language.h"
#include "Skin/CMtSkinManager.h"
#include "CMtCoreWidgetUtil.h"

CWidgetButton::CWidgetButton(QWidget* parent)
    : QWidget(parent)
    , m_bChecked(false)
{
    ui.setupUi(this);
    ui.listBtnImportModel->setVisible(false);
}

CWidgetButton::~CWidgetButton()
{
}

bool CWidgetButton::isChecked()
{
    return m_bChecked;
}

void CWidgetButton::setChecked(bool bChecked /* = true */)
{
    m_bChecked = bChecked;
    updateStatus();
}

void CWidgetButton::setEnabled(bool bEnable)
{
    if (bEnable)
    {
        QString styleSheet = "color:rgba(@colorA3, %1)";
        CMtCoreWidgetUtil::formatStyleSheet(styleSheet);
        ui.mtLabel_name->setStyleSheet(styleSheet.arg(QString::number(1)));
    }
    else
    {
        QString styleSheet = "color:rgba(@colorA3, 0.24)";
        CMtCoreWidgetUtil::formatStyleSheet(styleSheet);
        ui.mtLabel_name->setStyleSheet(styleSheet);
    }

    ui.mtLabel_name->setEnabled(bEnable);
    ui.listBtnImportModel->setEnabled(bEnable);
    QWidget::setEnabled(bEnable);
}

void CWidgetButton::setDisabled(bool bDisable)
{
    setEnabled(!bDisable);
}

QString CWidgetButton::text()
{
    return ui.mtLabel_name->text();
}

void CWidgetButton::setType(ButtonType type)
{
    switch (type)
    {
        case CWidgetButton::btnType_All:
            ui.mtLabel_name->setText(tr("全部ROI"));
            break;

        case CWidgetButton::btnType_Default:
            ui.mtLabel_name->setText(tr("默认模型ROI"));
            break;

        case CWidgetButton::btnType_Empty:
            ui.mtLabel_name->setText(tr("空勾画ROI"));
            break;

        case CWidgetButton::btnType_Model:
            ui.mtLabel_name->setText(tr("自定义模型"));
            ui.listBtnImportModel->setVisible(true);
            break;

        default:
            break;
    }

    m_type = type;
}

void CWidgetButton::setImportModelBtnPixmap(const QString& pixmap)
{
    ui.listBtnImportModel->setPixmapFilename(pixmap);
    updateStatus(false);
}

void CWidgetButton::updateStatus(bool bHover /* = false */)
{
    QString borderStyle = "#mtFrameEx{background: rgba(@colorB1, 0.1); border: 1px solid rgba(@colorB1, 1);}";
    QString textStyle = "#mtLabel_name{color: rgba(@colorB1, 1);}";
    QString btnStyle = "#listBtnImportModel{color: rgba(@colorB1, 1);}";

    if (!m_bChecked && !bHover)
    {
        textStyle = "#mtLabel_name{color: rgba(@colorA3, 0.8);}";
        borderStyle = "#mtFrameEx{border: 1px solid rgba(@colorA0, 0);}";
    }

    CMtCoreWidgetUtil::formatStyleSheet(borderStyle);
    CMtCoreWidgetUtil::formatStyleSheet(textStyle);
    CMtCoreWidgetUtil::formatStyleSheet(btnStyle);
    ui.mtFrameEx->setStyleSheet(borderStyle);
    ui.mtLabel_name->setStyleSheet(textStyle);
    ui.listBtnImportModel->setStyleSheet(btnStyle);
}

void CWidgetButton::enterEvent(QEvent* event)
{
    if (!isEnabled() || m_type == btnType_Model)
    {
        return;
    }

    updateStatus(true);
}

void CWidgetButton::leaveEvent(QEvent* event)
{
    if (!isEnabled())
    {
        return;
    }

    updateStatus(false);
}

void CWidgetButton::mouseReleaseEvent(QMouseEvent* event)
{
    QRect rt = rect();

    // 在区域内弹起鼠标，认为点击鼠标
    if (event->pos().x() > rt.left() && event->pos().y() > rt.top() && event->pos().x() < rt.right() && event->pos().y() < rt.bottom())
    {
        m_bChecked = !m_bChecked;
        emit sigButtonClicked(m_type);
    }
}

void CWidgetButton::on_listBtnImportModel_clicked()
{
    emit sigImportModelClicked();
}