﻿#include "AccuComponentUi\Header\MtWarningLineEdit.h"
#include <QDebug>
#include <QPalette>
#include "MtRangeValidator.h"

#pragma execution_character_set("utf-8")

MtWarningLineEdit::MtWarningLineEdit(QWidget* parent) : MtLineEdit(parent)
{
    this->setMtType(MtLineEdit::MtType::lineedit1);
    this->setElideMode(Qt::TextElideMode::ElideRight);
    m_regExp = new QRegExpValidator;
    //connect(this, &QLineEdit::textChanged, this, &MtWarningLineEdit::slotTextChanged);
    connect(this, &QLineEdit::textChanged, this, &MtWarningLineEdit::SlotTextChangedHideTips);
}

void MtWarningLineEdit::SetEditRange(const RangeParamInfo& inputInfo)
{
    m_rangeInfo = inputInfo;

    if (m_lineEditValidator)        //防止重复设置引发内存泄露
    {
        delete m_lineEditValidator;
        m_lineEditValidator = nullptr;
    }

    m_lineEditValidator = new MtRangeValidator(m_rangeInfo.minValue, m_rangeInfo.maxValue, m_rangeInfo.decimals, m_rangeInfo.seperateLeftValue, m_rangeInfo.seperateRightValue, this);
    this->setValidator(m_lineEditValidator);
    m_isInitRange = true;

    if (m_rangeInfo.unit.size() > 0)
    {
        this->setTrailingText(m_rangeInfo.unit);
    }

    if (m_bIsShowPlaceholderText)
    {
        QString placeholderText = GetRangeTipString();
        this->setPlaceholderText(placeholderText);
        _tips = placeholderText;
    }

    if (m_regExp)
    {
        delete m_regExp;
        m_regExp = nullptr;
    }
}

void MtWarningLineEdit::SetShowPlaceholderText(bool bIsShowPlaceholderText)
{
    m_bIsShowPlaceholderText = bIsShowPlaceholderText;
}

void MtWarningLineEdit::setTips(const QString& text)
{
    _tips = text;

    if (text.isEmpty())
    {
        MtToolTip::showText(QPoint(), QStringList(), this, QRect(this->rect()));
    }
}

QString MtWarningLineEdit::getTips()
{
    return _tips;
}

void MtWarningLineEdit::setWarningBorderStatus(QString tipText, bool isAdaptText /*= false*/, QColor color /*= QColor(177, 89, 89, 1)*/)
{
    //弹窗
    QPoint pCenterUP;
    QPoint pp = this->mapToGlobal(QPoint(0, 0));
    pCenterUP = QPoint(pp.x(), pp.y() + height() / 2 + 2);
    QSize pSize = this->size();;

    if (isAdaptText)
    {
        ////这个获得了字符串所占的像素宽度
        //QFontMetrics fm(m_toolTip->font());
        //int textWidth = fm.width(tipText);
        //pSize.setWidth(textWidth);
    }

    SetBoderShowStatus(false);
    QStringList tipList;
    //tipList << QString("<font style='color:#DD5C5C'>%1").arg(tipText);
    tipList << tipText;
    MtToolTip::showText(pCenterUP, tipList, this, QRect(this->rect()));
}

void MtWarningLineEdit::setRegExpression(QString regExp)
{
    m_regExpression = regExp;
    QRegExp rx(m_regExpression);
    m_regExp->setRegExp(rx);
    this->setValidator(m_regExp);
}

bool MtWarningLineEdit::CheckInputDataIsValid()
{
    QString curText = this->text();
    bool isOK = false;
    double curDouble = curText.toDouble(&isOK);

    if (!isOK)
    {
        return false;
    }

    auto minRange = m_rangeInfo.minValue;
    auto maxRange = m_rangeInfo.maxValue;

    if (minRange > maxRange)
    {
        auto seperateLeftValue = m_rangeInfo.seperateLeftValue;    //分节值左侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
        auto seperateRightValue = m_rangeInfo.seperateRightValue;     //分节值右侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
        //是否包含
        bool bOK = (m_rangeInfo.bContainMin ? curDouble >= minRange : curDouble > minRange) &&
            (true ? curDouble <= seperateLeftValue : curDouble < seperateLeftValue);

        if (true == bOK)
        {
            return bOK;
        }

        bOK = (true ? curDouble >= seperateRightValue : curDouble > seperateRightValue) &&
            (m_rangeInfo.bContainMax ? curDouble <= maxRange : curDouble < maxRange);
        return bOK;
    }

    //是否包含
    return (m_rangeInfo.bContainMin ? curDouble >= minRange : curDouble > minRange) &&
        (m_rangeInfo.bContainMax ? curDouble <= maxRange : curDouble < maxRange);
}

void MtWarningLineEdit::SetBoderShowStatus(bool isNormal)
{
    m_isWarningBorder = !isNormal;

    if (isNormal)
    {
        //设置objName不会影响样式
        QString styleNormal = QString("#%1{border:1px solid rgba(31, 35, 41, 1);}").arg(this->objectName());
        setStyleSheet(styleSheet() + styleNormal);
    }
    else
    {
        QString styleRed = QString("#%1{border:1px solid rgba(177, 89, 89, 1);}").arg(this->objectName());
        setStyleSheet(styleSheet() + styleRed);
    }
}

QString MtWarningLineEdit::GetFinalFitNumber()
{
    double curDouble = m_rangeInfo.initValue;
    auto minRange = m_rangeInfo.minValue;
    auto maxRange = m_rangeInfo.maxValue;

    if (minRange > maxRange)
    {
        //先判断默认值是否在范围内
        bool bContain = IsContansValue(curDouble, minRange, m_rangeInfo.bContainMin, m_rangeInfo.seperateLeftValue, true);

        if (true == bContain)
        {
            return QString::number(curDouble, 'f', m_rangeInfo.decimals);
        }

        bContain = IsContansValue(curDouble, m_rangeInfo.seperateRightValue, true, m_rangeInfo.maxValue, m_rangeInfo.bContainMax);

        if (true == bContain)
        {
            return QString::number(curDouble, 'f', m_rangeInfo.decimals);
        }

        QString text = this->text();

        //如果为空，那么判断0.00是否在范围内
        if (text.isEmpty())
        {
            curDouble = text.toDouble();
            bContain = IsContansValue(curDouble, minRange, m_rangeInfo.bContainMin, m_rangeInfo.seperateLeftValue, true);

            if (true == bContain)
            {
                return QString::number(curDouble, 'f', m_rangeInfo.decimals);
            }

            bContain = IsContansValue(curDouble, m_rangeInfo.seperateRightValue, true, m_rangeInfo.maxValue, m_rangeInfo.bContainMax);

            if (true == bContain)
            {
                return QString::number(curDouble, 'f', m_rangeInfo.decimals);
            }
        }

        //取临界值
        if (abs(curDouble - minRange) < abs(curDouble - maxRange))
        {
            if (m_rangeInfo.bContainMin)
            {
                curDouble = minRange;
            }
            else
            {
                curDouble = (maxRange + minRange) / 2;
            }
        }
        else
        {
            if (m_rangeInfo.bContainMax)
            {
                curDouble = maxRange;
            }
            else
            {
                curDouble = (maxRange + minRange) / 2;
            }
        }

        return QString::number(curDouble, 'f', m_rangeInfo.decimals);
    }
    else
    {
        //是否包含
        bool isContain = (m_rangeInfo.bContainMin ? curDouble >= minRange : curDouble > minRange) &&
            (m_rangeInfo.bContainMax ? curDouble <= maxRange : curDouble < maxRange);

        if (isContain)
        {
            return QString::number(curDouble, 'f', m_rangeInfo.decimals);
        }
        else
        {
            QString text = this->text();

            if (text.isEmpty())
            {
                curDouble = text.toDouble();

                if ((m_rangeInfo.bContainMin ? curDouble >= minRange : curDouble > minRange) &&
                    (m_rangeInfo.bContainMax ? curDouble <= maxRange : curDouble < maxRange))
                {
                    return QString::number(curDouble, 'f', m_rangeInfo.decimals);
                }
            }

            double textValue = text.toDouble();

            if (textValue <= minRange)
            {
                if (m_rangeInfo.bContainMin)
                {
                    curDouble = minRange;
                }
                else
                {
                    curDouble = (maxRange + minRange) / 2;
                }
            }
            else
            {
                if (m_rangeInfo.bContainMax)
                {
                    curDouble = maxRange;
                }
                else
                {
                    curDouble = (maxRange + minRange) / 2;
                }
            }

            return QString::number(curDouble, 'f', m_rangeInfo.decimals);
        }
    }

    return QString::number(curDouble, 'f', m_rangeInfo.decimals);
}

bool MtWarningLineEdit::IsContansValue(double curDouble, double minRange, bool bContainMin, double maxRange, bool bContainMax)
{
    bool isContain = (bContainMin ? curDouble >= minRange : curDouble > minRange) &&
        (bContainMax ? curDouble <= maxRange : curDouble < maxRange);

    if (isContain)
    {
        return true;
    }

    return false;
}

QString MtWarningLineEdit::GetRangeTipString()
{
    QString tipString;

    if (m_rangeInfo.minValue > m_rangeInfo.maxValue)
    {
        QString leftString = m_rangeInfo.bContainMin ? "[" : "(";
        QString rightString = m_rangeInfo.bContainMax ? "]" : ")";
        tipString = leftString + QString::number(m_rangeInfo.minValue, 'f', m_rangeInfo.decimals) + " - " + QString::number(m_rangeInfo.seperateLeftValue, 'f', m_rangeInfo.decimals) + "]";
        tipString += " or ";
        tipString += "[" + QString::number(m_rangeInfo.seperateRightValue, 'f', m_rangeInfo.decimals) + " - " + QString::number(m_rangeInfo.maxValue, 'f', m_rangeInfo.decimals) + rightString;
    }
    else
    {
        QString leftString = m_rangeInfo.bContainMin ? "[" : "(";
        QString rightString = m_rangeInfo.bContainMax ? "]" : ")";
        tipString = leftString + QString::number(m_rangeInfo.minValue, 'f', m_rangeInfo.decimals) + " - " + QString::number(m_rangeInfo.maxValue, 'f', m_rangeInfo.decimals) + rightString;
    }

    return tipString;
}

void MtWarningLineEdit::focusOutEvent(QFocusEvent* e)
{
    if (false == m_isInitRange)
    {
        QLineEdit::focusOutEvent(e);
        emit sigFocusOut();
        return;
    }

    if (CheckInputDataIsValid())
    {
        QString curText = this->text();

        //位数为0自动补0
        if (curText.size() > 0)
        {
            //找其他位数不够0的补齐
            int posIndex = curText.indexOf('.');

            if (posIndex > 0)
            {
                int posCount = curText.size() - 1 - posIndex;
                int posLeftCount = m_rangeInfo.decimals - posCount;

                for (int i = 0; i < posLeftCount; ++i)
                {
                    curText += QString::number(0);
                }

                this->setText(curText);
            }
            else//没找到0，根据位数补.00
            {
                if (m_rangeInfo.decimals > 0)//大于0才重新setText
                {
                    curText += ".";

                    for (int i = 0; i < m_rangeInfo.decimals; ++i)
                    {
                        curText += QString::number(0);
                    }

                    this->setText(curText);
                }
            }
        }
    }

    //如果不在范围里还原成默认值
    if (CheckInputDataIsValid() == false)
    {
        //this->setText(GetFinalFitNumber());
    }

    QLineEdit::focusOutEvent(e);
    emit sigFocusOut();
}

void MtWarningLineEdit::slotTextChanged()
{
    if (false == this->isVisible())
    {
        return;
    }

    if (false == m_isInitRange)
    {
        if (m_isWarningBorder)//还原，如果有警告
        {
            SetBoderShowStatus(true);
        }

        //tip无论如何都要消失
        MtToolTip::showText(QPoint(), QStringList(), this, QRect(this->rect()));
        return;
    }

    if (!CheckInputDataIsValid())
    {
        if (text().isEmpty() && !isEnabled())
        {
            SetBoderShowStatus(true);
        }
        else
        {
            SetBoderShowStatus(false);

            if (m_isShowWarning)
            {
                auto pp1 = mapToGlobal(QPoint(0, 0));
                auto targetPos = QPoint(pp1.x(), pp1.y() + height() / 2 + 2);
                QString tipsString = QString(tr("正确范围为%1")).arg(GetRangeTipString());
                QStringList tipList = { tipsString };
                MtToolTip::showText(targetPos, tipList, this, QRect(this->rect()));
            }
        }
    }
    else
    {
        SetBoderShowStatus(true);
    }
}

void MtWarningLineEdit::SetWarningStatus(bool isShowWarning)
{
    m_isShowWarning = isShowWarning;
}

void MtWarningLineEdit::mouseMoveEvent(QMouseEvent* e)
{
    if (_tips.isEmpty() || !text().isEmpty())
    {
        QLineEdit::mouseMoveEvent(e);
        return;
    }

    //if (this->geometry().contains(this->mapFromGlobal(QCursor::pos())))
    {
        // do something...
        QPoint pCenterUP;
        QPoint pp = this->mapToGlobal(QPoint(0, 0));
        pCenterUP = QPoint(pp.x(), pp.y() + height() / 2 + 2);
        QSize pSize = this->size();
        QStringList tipList;
        //tipList << QString("<font style='color:#DD5C5C'>%1").arg(tipText);
        tipList << _tips;
        MtToolTip::showText(pCenterUP, tipList, this, QRect(this->rect()));
    }

    if (false == m_isWarningBorder)
    {
        QLineEdit::mouseMoveEvent(e);
        return;
    }

    QLineEdit::mouseMoveEvent(e);
}

void MtWarningLineEdit::focusInEvent(QFocusEvent* e)
{
    emit SigFocusIn();

    if (false == m_bIsFocusInClearWarning)
    {
        QLineEdit::focusInEvent(e);
        return;
    }

    if (m_isWarningBorder)
    {
        SetBoderShowStatus(true);
        setTips("");
    }

    QLineEdit::focusInEvent(e);
}

void MtWarningLineEdit::SetIsFocusInClearWarning(bool bSet)
{
    m_bIsFocusInClearWarning = bSet;
}

void MtWarningLineEdit::SlotTextChangedHideTips()
{
    if (!text().isEmpty())
    {
        MtToolTip::showText(QPoint(), QStringList(), this, QRect(this->rect()));
    }
}
