﻿#pragma once

#include <QTreeWidget>
#include "QMTTreeGroupItem.h"
#include "QMTTreeSubItem.h"
#include "QMTTreeGroupPacketItem.h"

//列表控件的基本属性
typedef struct ST_TreeWidgetProperty
{
    int groupItemHeight = 24;
    int subItemHeight = 30;
} TreeWidgetProperty;

//roi列表的靶区/非靶区枚举
enum EM_ROICellFormat
{
    NotROI = -1,//不是roi
    IsPTV = 0,//是roi，是靶区
    IsNotPTV = 1,//是roi，不是靶区
    IsSFRTPTV = 2, //是roi，是晶格靶区
    UnClear = 3//是roi，不知道是不是靶区
};
/*ROI/POI/DOSE列表基础组件*/
class  QMTAbstractTreeWidget : public QTreeWidget
{
    Q_OBJECT

public:
    QMTAbstractTreeWidget(QWidget* parent);
    ~QMTAbstractTreeWidget();
    virtual void SubItemSelectButtonClickedCallBack(QString parentValue, QString uniqueValue, QMTAbstractMenu* menu);//subItem的menu回调,可以设置menu相关属性
    //add
    void AddGroupItem(QString text, QString uniqueValue = "", bool havePacket = false);               //新增group
    void InsertGroupItem(int index, QString& groupName, QString groupKey = ""); //插入group

    /**************************新增和更新数据建议使用此接口*********************************/
    void AddSubItem(const TreeSubItemAddUIInfo& subItemUIInfo, bool havePacket = false);                  //新增数据
    void AddSubItemList(QList<TreeSubItemAddUIInfo>& subItemUIInfoList);
    void InsertSubItem(int index, TreeSubItemAddUIInfo& subItemUIInfo);     //某个位置插入数据
    void UpdateSubItem(TreeSubItemUpdateInfo& subItemUpdateInfo);           //更新某个subitem

    /************************************************************************************/
    void AddRowItem(bool& isCheck, QColor&, QString& /*item key*/, const QString& groupKey = tr(""));
    void AddRowItem(QColor&, QString& /*item name*/, QString& type, QString& /*item key*/, const QString& groupKey = tr(""));
    void InsertRowItem(int& index, bool& isCheck, QColor&, QString& /*item key*/, QString& /*group key*/);
    void InsertRowItem(int& index, QColor&, QString& /*item name*/, QString& type, QString& /*item key*/, QString& /*group key*/, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI);
    // Ac新增需求标签
    void AddRowItem(QColor&, QString& /*item name*/, QString& type, QString& label, QString& /*item key*/, const QString& groupKey);
    void InsertRowItem(int& index, QColor&, QString& /*item name*/, QString& type, QString& label, QString& /*item key*/, QString& /*group key*/, int IsPTV = -1);
    //插入UI数据
    void InsertGroupItemData(QString groupKey, QVariant value);//设置group的数据
    void InsertSubItemData(QString groupKey, QString itemKey, QVariant value);//设置sub item的用户数据

    /***********************************get*************************************************/
    QStringList GetAllGroupKey();                                               //获取所有的groupKey
    QTreeWidgetItem* GetGroupWidgetItem(const QString& groupKey);                     //获取group的WidgetItem
    QTreeWidgetItem* GetGroupPackWidgetItem(QTreeWidgetItem* groupWidgetItem, const QString& groupPackKey);                    //获取group的WidgetItem
    QTreeWidgetItem* GetItemWidgetItem(QString& groupKey, QString& itemKey, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI); //获取subItem的WidgetItem
    QMTTreeGroupItem* GetGroupItemWidget(QString& groupKey);                    //获取group的Widget
    QMTTreeSubItem* GetSubItemWidget(QString& groupKey, QString& itemKey, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI);     //获取subItemde的widget
    bool GetSubItemWidgetList(QString& groupKey, QList<QMTTreeSubItem*>&);      //获取某个group下所有的subItemWidget
    bool GetSubItemUniqueValueList(QString& groupKey, QStringList&);            //获取某个group下所有的subItem的唯一值
    QVariant GetGroupItemData(QString groupKey);
    bool GetSubItemData(QString groupKey, QString subKey, QVariant&);
    bool GetCurItemKey(QString& groupKey, QString& itemKey);                    //获取当前的item
    bool GetItemIsShow(QString groupKey, QString itemKey, bool& isShow);
    bool GetSubItemColor(QString groupKey, QString itemKey, QColor& color);
    bool GetGroupSubItemStrList(QString groupKey, QStringList&, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI);
    bool GetGroupSubItemNameList(QString groupKey, QStringList&, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI);
    bool GetAllGroupIsHide();                                   //所有的group item是否都是隐藏状态
    const TreeWidgetProperty& GetTreeWidgetProperty();           //获取列表属性
    const TreeSubItemProperty& GetSubItemProperty();            //获取子项的属性

    bool GetIsHadGroupItemData();                               //判断是否有group
    bool GetIsHadSubItemData();                                 //判断是否有subItem的值
    /***********************************delete*************************************************/
    void DeleteCurItem();                                           //删除当前选中项，可以是group，也可以是subItem
    void DeleteSubItem(QString groupKey, QString itemKey, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI);         //删除某一个subItem
    void ClearAllItems();                                           //删除所有界面数据
    void DeleteGroupItem(QString groupKey);                         //删除某个group及其所有子项

    /***********************************update and set**************************************/
    void SetTreeWidgetProperty(const TreeWidgetProperty& treeWidgetProperty);   //设置列表属性
    void SetSubItemProperty(TreeSubItemProperty&);                              //设置子项属性
    void SetCurrentGroup(QString groupKey);
    void SetCurrentItem(QString gourpKey, QString itemKey, EM_ROICellFormat roiCellFormat = EM_ROICellFormat::NotROI);    //设置当前选中的subitem
    void SetGroupHide(int, bool);
    void SetGroupHide(QString, bool);           //仅仅对group起作用
    void SetEnableChangeName(bool enable);      //是否允许修改名称
    void SetEnableChangeColor(bool enable);     //是否允许修改颜色
    void SetAIiconShow(bool isShow);            //是否需要显示AI
    void SetGroupAllItemsHide(QString, bool);   //设置gourp下所有item显示/隐藏，含group
    void SetGroupIsShow(QString groupKey, bool isShow, bool bEmitSig = true);     //设置group是否显示
    void SetAllGroupIsShow(bool isShow);        //设置所有group是否显示
    void SetSubItemIsShow(QString groupKey, QString itemKey, bool isShow);          //设置item是否显示
    void SetSubItemIsShow(QString groupKey, QStringList itemKeyList, bool isShow);  //设置item list是否显示
    void SetSubItemValidator(QValidator*);      //设置item的输入规则
    void SetComboBoxVisiable(bool isShow);      //下拉框是否显示
    void SetEyeBtnVisiable(bool isShow);      //小眼睛是否显示
    //judge
    bool CheckGroupItemUnique(QString groupKey, QString name = ""); //判断group key是否唯一
    bool CheckSubItemUnique(QString groupKey, QString subKey);      //判断子项是否唯一
    //新的clicked信号。只要点击了就往外发
    void SetClickSignalReplaceChanged();
signals:
    void sigIsShowItemList(QString, QStringList, bool);                 //是否展示,parentValue, subValueList,isShow
    void sigIsFillAllGroupItem(QString, bool);                          //是否填充所有
    void sigCurrentNameChange(QString, QString, QString);               //名称修改parentValue, old, new
    void sigCurrentColorChange(QString, QString, QColor, QColor);       //颜色修改 parentValue, old, new
    void sigTreeItemClicked(QString curGroupKey, QString curSub, QString preGroupKey, QString preSub);//点击item
    void sigTreeItemDoubleClicked(QString curGroupKey, QString curSub);//双击item
    void sigComboBoxSelectTextChange(QString, QString, QString);//下拉框选择变化parentValue,uniquevalue,text
    void sigSelectIndexChange(QString, QString, int);//下拉框选择变化parentValue,uniquevalue,index
    /// <summary>
    /// 衍生ROI点击了菜单
    /// </summary>
    /// <param name="">The .</param>
    /// <param name="">The .</param>
    /// <param name="">The .</param>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    void SigClickedDerivedMenu(QString, QString, EM_DerivedROIMenu);
private slots:
    //tree widget slots
    void slotTreeItemClicked(QTreeWidgetItem*, QTreeWidgetItem*);
    void slotTreeItemClicked_New(QTreeWidgetItem* curItem, int column);
    void slotItemDoubleClicked(QTreeWidgetItem*, int);
    //group item slots
    void slotShowAllGroupItem(QString, bool);
    void slotExpandGroup(QString, bool);
    void slotExpandPackGroup(QString, QString, bool);
    void slotFillAllGroupItem(QString, bool);
    void slotComboBoxSelectTextChange(QString, QString, QString);//下拉框选择变化parentValue,uniquevalue,text
    void slotComboBoxSelectIndexChange(QString, QString, int);//下拉框选择变化parentValue,uniquevalue,index
    void slotSubItemSelectButtonClicked(QString parentValue, QString uniqueValue);//点击了选择按键
    void slotSubItemSelectButtonClickedButNotShowMenu(QString parentValue, QString uniqueValue); //20220523 zlw点击了选择按键但不显示Menu
    void slotSelectMenuTriggered(int);                      //menu选择了第几个
protected:
    void deleteWidgetItem(QTreeWidgetItem* node);
    bool isHideAllGroupItem(QString);
private:
    void ConnectTreeWidgetSignals();
    //绑定的是QMTTreeGroupItem的信号
    void ConnectTreeGroupSignals(QMTTreeGroupItem* widget);
    void DisConnectTreeGroupSignals(QMTTreeGroupItem* widget);
    //绑定子项QMTTreeGroupPacketItem的信号
    void ConnectTreeGroupPacketSignals(QMTTreeGroupPacketItem* widget);
    void DisConnectTreeGroupPacketSignals(QMTTreeGroupPacketItem* widget);
    //绑定的是QMTTreeSubItem的信号
    void ConnectTreeSubItemSignals(QMTTreeSubItem* widget);
    void DisConnectTreeSubItemSignals(QMTTreeSubItem* widget);
private:
    TreeWidgetProperty _treeWidgetProperty;                 //列表属性
    TreeSubItemProperty _subItemProperty;                   //子项属性参数
    QMap<QString, QVariant> _groupItemDataMap;              //group的缓存数据
    QMap<QString, QMap<QString, QVariant>> _subItemDataMap; //sub的缓存数据
    QValidator* _validator = nullptr;                       //item输入规则设置
    bool _bAllGroupHide = false;
    //menu相关
    QMTAbstractMenu* _selectMenu = nullptr;                 //下拉选择不同的勾画ROI
    QString _menuSelectParanValue;
    QString _menuUniqueValue;                               //点击的是哪个subitem的menu
    //QTreeWidgetItem* _curClickItem = nullptr;               //当前click信号触发的item
    bool _isSetClickSignal = false;                         //是否使用了clieck作为点击信号
};
