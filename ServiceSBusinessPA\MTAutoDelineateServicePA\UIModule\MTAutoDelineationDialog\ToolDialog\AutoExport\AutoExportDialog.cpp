﻿#include "AutoExportDialog.h"
#include "MtToolButton.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "DataDefine/InnerStruct.h"
#include <QFileDialog>


AutoExportDialog::AutoExportDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setDialogWidthAndContentHeight(466, 217); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("导出文件"));                     //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
}

AutoExportDialog::~AutoExportDialog()
{
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="stAddrInfo">[IN]当前使用的导出地址</param>
/// <param name="allExportScpInfoList">[IN]所有导出地址信息</param>
/// <param name="getNewRemoteScpList">[IN]获取最新的远程地址回调</param>
void AutoExportDialog::init(const n_mtautodelineationdialog::ST_AddrSimple& stAddrInfo,
                            const QList<n_mtautodelineationdialog::ST_AddrSimple>& allExportAddrList,
                            std::function<QList<n_mtautodelineationdialog::ST_AddrSimple>()> getNewRemoteScpListCallBack)
{
    //初始化数据
    m_oldAddrInfo = stAddrInfo;
    m_getNewRemoteScpListCallBack = getNewRemoteScpListCallBack;
    ui.mtRadioButton_scp->setChecked(true);

    //远程服务器地址
    for (int i = 0; i < allExportAddrList.size(); i++)
    {
        n_mtautodelineationdialog::ST_AddrSimple stAddr = allExportAddrList[i];

        if (stAddr.addrType == 4)//导出地址类型(0:无 1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)
        {
            ui.mtComboBox_scp->addItem(stAddr.stScpInfo.serverName, QVariant::fromValue(stAddr));
        }
        else if (stAddr.addrType == 1)
        {
            ui.lineEdit_dir->setText(stAddr.stDirInfo.dirPath);
            ui.mtCheckBox_dir->setChecked(stAddr.stDirInfo.mkSubType == 0 ? false : true);
        }
    }

    //导出格式
    ui.mtComboBox_format->addItem(tr("默认格式"), "0");
    ui.mtComboBox_format->addItem(tr("Eclipse 15+"), "3239DF85-89FB-419D-99BE-56E9C1D5DE50");
    ui.mtComboBox_format->setCurrentIndex(0);

    //编辑模式下切换选中的项目
    if (m_oldAddrInfo.addrType > 0)
    {
        //导出格式(0:普通格式  "3239DF85-89FB-419D-99BE-56E9C1D5DE50":Eclipse15  "7E42D45A-2447-4196-A1B9-20AA0B4BE6A1":Cyberknife)
        if (m_oldAddrInfo.exportFormat == "0")
            ui.mtComboBox_format->setCurrentIndex(0);
        else if (m_oldAddrInfo.exportFormat == "3239DF85-89FB-419D-99BE-56E9C1D5DE50")
            ui.mtComboBox_format->setCurrentIndex(1);

        if (m_oldAddrInfo.addrType == 1) //共享文件夹
        {
            onMtRadioButton_scp(false);
            ui.mtRadioButton_dir->setChecked(true);
            ui.lineEdit_dir->setText(m_oldAddrInfo.stDirInfo.dirPath);
            ui.mtCheckBox_dir->setChecked(m_oldAddrInfo.stDirInfo.mkSubType == 0 ? false : true);
        }
        else if (m_oldAddrInfo.addrType == 4) //远程SCP服务器
        {
            onMtRadioButton_scp(true);
            ui.mtRadioButton_scp->setChecked(true);
            int count = ui.mtComboBox_scp->count();

            for (int i = 0; i < count; i++)
            {
                n_mtautodelineationdialog::ST_AddrSimple stAddr = ui.mtComboBox_scp->itemData(i).value<n_mtautodelineationdialog::ST_AddrSimple>();

                if (stAddr.stScpInfo.serverName == m_oldAddrInfo.stScpInfo.serverName)
                {
                    ui.mtComboBox_scp->setCurrentIndex(i);
                    break;
                }
            }
        }
    }
    else
    {
        onMtRadioButton_scp(true);
    }

    //信号槽
    connect(ui.mtPushButton, &QPushButton::clicked, this, &AutoExportDialog::onMtPushButton);               //添加-配置地址
    connect(ui.mtRadioButton_scp, &QRadioButton::toggled, this, &AutoExportDialog::onMtRadioButton_scp);    //远程服务器选中与否变化
    connect(ui.mtComboBox_scp, &QComboBox::currentTextChanged, this, &AutoExportDialog::onMtComboBox_scp);  //远程服务器下拉框文本变化
    connect(ui.mtPushButton_dir, &QPushButton::clicked, this, &AutoExportDialog::onBtnFile);                //配置共享目录
}

/// <summary>
/// 获取最新的导出地址信息
/// </summary>
/// <returns>最新的导出地址信息</returns>
n_mtautodelineationdialog::ST_AddrSimple AutoExportDialog::getNewAddrInfo()
{
    n_mtautodelineationdialog::ST_AddrSimple outAddrInfo;
    outAddrInfo.exportRange = -1;
    outAddrInfo.exportFormat = ui.mtComboBox_format->currentData().value<QString>();

    if (ui.mtRadioButton_scp->isChecked() == true) //远程服务器
    {
        outAddrInfo.addrType = 4;
        outAddrInfo.stScpInfo.serverName = ui.mtComboBox_scp->currentText();
    }
    else //共享文件夹
    {
        outAddrInfo.addrType = 1;
        outAddrInfo.stDirInfo.dirPath = ui.lineEdit_dir->text();
        outAddrInfo.stDirInfo.mkSubType = (ui.mtCheckBox_dir->isChecked() == true ? 1 : 0);
    }

    return outAddrInfo;
}

/// <summary>
/// 添加-配置地址(远程节点)
/// </summary>
void AutoExportDialog::onMtPushButton()
{
    if (m_getNewRemoteScpListCallBack != nullptr)
    {
        disconnect(ui.mtComboBox_scp, &QComboBox::currentTextChanged, this, &AutoExportDialog::onMtComboBox_scp);  //远程服务器下拉框文本变化
        ui.mtComboBox_scp->clear();
        QList<n_mtautodelineationdialog::ST_AddrSimple> allExportAddrList = m_getNewRemoteScpListCallBack();

        //远程服务器地址
        for (int i = 0; i < allExportAddrList.size(); i++)
        {
            n_mtautodelineationdialog::ST_AddrSimple stAddr = allExportAddrList[i];

            if (stAddr.addrType == 4)//导出地址类型(0:无 1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)
            {
                ui.mtComboBox_scp->addItem(stAddr.stScpInfo.serverName, QVariant::fromValue(stAddr));
            }
        }

        ui.mtComboBox_scp->setCurrentIndex(0);
        showExportFormatCombox(true, ui.mtComboBox_scp->currentText());
        connect(ui.mtComboBox_scp, &QComboBox::currentTextChanged, this, &AutoExportDialog::onMtComboBox_scp);  //远程服务器下拉框文本变化
    }
}

/// <summary>
/// 远程节点选中
/// </summary>
void AutoExportDialog::onMtRadioButton_scp(bool checked)
{
    if (checked)
    {
        ui.mtComboBox_scp->setEnabled(true);
        ui.lineEdit_dir->setEnabled(false);
        ui.mtPushButton_dir->setEnabled(false);
        ui.mtCheckBox_dir->setEnabled(false);
        ui.mtComboBox_format->setEnabled(false);
        showExportFormatCombox(true, ui.mtComboBox_scp->currentText());
    }
    else
    {
        ui.mtComboBox_scp->setEnabled(false);
        ui.lineEdit_dir->setEnabled(true);
        ui.mtPushButton_dir->setEnabled(true);
        ui.mtCheckBox_dir->setEnabled(true);
        ui.mtComboBox_format->setEnabled(true);
        showExportFormatCombox(false, QString());
    }
}

/// <summary>
/// 远程节点下拉变化
/// </summary>
void AutoExportDialog::onMtComboBox_scp(const QString& text)
{
    showExportFormatCombox(true, text);
}

/// <summary>
/// 选择共享目录
/// </summary>
void AutoExportDialog::onBtnFile()
{
    QFileDialog* fileDialog = new QFileDialog(this);
    fileDialog->resize(240, 320);
    fileDialog->setFileMode(QFileDialog::Directory);
    QString path = fileDialog->getExistingDirectory();

    if (path.isEmpty())
        return;

    ui.lineEdit_dir->setText(path);
    ui.lineEdit_dir->setCursorPosition(0);
    this->repaint();
}

/// <summary>
/// 显示导出格式的选中内容
/// </summary>
/// <param name="isSelectScpRadio">[IN]是否选中SCP服务器RadioButton</param>
/// <param name="scpServerName">[IN]远程SCP名称</param>
void AutoExportDialog::showExportFormatCombox(const bool isSelectScpRadio, const QString& scpServerName)
{
    if (isSelectScpRadio) //远程服务器
    {
        int count = ui.mtComboBox_scp->count();

        for (int i = 0; i < count; i++)
        {
            n_mtautodelineationdialog::ST_AddrSimple stAddr = ui.mtComboBox_scp->itemData(i).value<n_mtautodelineationdialog::ST_AddrSimple>();

            if (stAddr.stScpInfo.serverName == scpServerName)
            {
                for (int m = 0; m < ui.mtComboBox_format->count(); m++)
                {
                    QString formatStr = ui.mtComboBox_format->itemData(m).value<QString>();

                    if (formatStr == stAddr.exportFormat)
                    {
                        ui.mtComboBox_format->setCurrentIndex(m);
                        return;
                    }
                }
            }
        }
    }
    else //共享文件夹
    {
        for (int i = 0; i < ui.mtComboBox_format->count(); i++)
        {
            QString formatStr = ui.mtComboBox_scp->itemData(i).value<QString>();

            if (formatStr == m_oldAddrInfo.exportFormat)
            {
                ui.mtComboBox_format->setCurrentIndex(i);
                return;
            }
        }
    }

    return;
}

/// <summary>
/// 关闭按钮
/// </summary>
void AutoExportDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void AutoExportDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void AutoExportDialog::onBtnRight1Clicked()
{
    if (ui.mtRadioButton_scp->isChecked() == true)
    {
        if (ui.mtComboBox_scp->count() <= 0)
        {
            MtMessageBox::NoIcon::information_Title(this, tr("请添加远程节点"), QString());
            return;
        }
    }
    else if (ui.mtRadioButton_dir->isChecked() == true)
    {
        if (ui.lineEdit_dir->text().isEmpty() == true || ui.lineEdit_dir->text().left(2) != "//")
        {
            MtMessageBox::NoIcon::information_Title(this, tr("请选择共享文件夹地址"), QString());
            return;
        }
    }

    this->accept();
}
