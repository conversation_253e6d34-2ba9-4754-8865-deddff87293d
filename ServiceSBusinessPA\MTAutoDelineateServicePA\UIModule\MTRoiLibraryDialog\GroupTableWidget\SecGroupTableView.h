﻿#pragma once

//#include <QTableView>
#include <QLabel>
#include <QMouseEvent>
#include <QStandardItemModel>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"

class SecGroupTableView : public QMTAbstractTableView
{
    Q_OBJECT
public:
    enum
    {
        ColType_Name = 0,
        ColType_RefRoi = 1,
        ColType_Operation
    };
    SecGroupTableView(QWidget* parent = Q_NULLPTR);

    /// <summary>
    /// 初始化列表
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="organInfoList">器官列表信息，用于删除组是判断是否可行</param>
    /// <param name="allOrganGroupList">分组列表信息</param>
    void initTableList(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList
                       , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allOrganGroupList
                       , const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 添加新的组
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    bool addNew(int newGroupId);

    /// <summary>
    /// 获取列表信息
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QList&lt;T&gt;.</returns>
    QList <n_mtautodelineationdialog::ST_OrganGroupInfo> getTableList();

protected slots:
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);

protected:
    //单元格创建完成回调(用于外部刷新定制化单元格界面)
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget) override;

protected:
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);
    void addRow(const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo);
    void insertRow(int rowIndex, const n_mtautodelineationdialog::ST_OrganGroupInfo& stGroupInfo);

    QString getOrganName(int organId);

private:
    QList<n_mtautodelineationdialog::ST_Organ>  m_organInfoList;        //缓存的器官信息
    QMap<int, n_mtautodelineationdialog::ST_SketchModel> m_modelInfoMap;//模型信息
    QMap<int/*groupId*/, int/*organId*/>        m_groupIdOrganIdMap;    //列表中分组Id和关联器官Id映射
};
