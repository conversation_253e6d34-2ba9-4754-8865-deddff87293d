﻿#include "AccuComponentUi\Header\MRotate.h"
#include <QMatrix>
#include <QPainter>

MRotate::MRotate(QWidget* parent, QString url, int interval, bool clockwise)
    : QLabel(parent)
{
    this->timerId = 0;
    this->setUrl(url);
    this->setInterval(interval);
    this->setClockwise(clockwise);
    this->setScaledContents(true);
    rotate = 0;
}

MRotate::~MRotate()
{
}

void MRotate::setUrl(QString url)
{
    this->_Url = url;
    this->setPixmap(QPixmap(url));
    //this->setStyleSheet("QPushButton{border-image:" + this->_Url + ";}");
};

void MRotate::setInterval(int interval)
{
    this->_Interval = interval;

    if (this->timerId != 0)
    {
        this->killTimer(this->timerId);
    }

    this->timerId = this->startTimer(this->_Interval);
};


void MRotate::paintEvent(QPaintEvent* event)
{
    Q_UNUSED(event);
    QPainter painter(this);
    painter.setRenderHint(QPainter::SmoothPixmapTransform, true);//平滑像素图
    painter.translate(width() / 2, height() / 2);  //移动绘图坐标系原点到画布中心

    if (this->_Clockwise)
    {
        painter.rotate(rotate++);                //顺时针旋转绘图坐标系
    }
    else
    {
        painter.rotate(rotate--);                //逆时针旋转绘图坐标系
    }

    QPixmap pixmap(this->_Url);
    int width = this->width();
    int height = this->height();
    painter.drawPixmap(-width / 2, -height / 2, width, height, pixmap);// 如果有特殊需要，可以在这里缩放pixmap）
}

void MRotate::timerEvent(QTimerEvent* event)
{
    this->update();
    {
        // 这种方法，图像边缘会始终在Label内，
        //QPixmap pixmap(this->_Url);
        //QMatrix rm;
        ////rotate_disc.translate(pixmap.width() / 2.0, pixmap.height() / 2.0);
        //rm.translate(pixmap.width(), pixmap.height());
        //rm.rotate(rotate--);
        //pixmap = pixmap.transformed(rm);
        //this->setPixmap(pixmap);
    }
}
