﻿#include "AccuComponentUi\Header\QMTAbstractMenu.h"

QMTAbstractMenu::QMTAbstractMenu(QObject* parent)
    : QObject(parent)
{
    InitMenu();
}

QMTAbstractMenu::~QMTAbstractMenu()
{
    if (_popMenu)
    {
        delete _popMenu;
        _popMenu = nullptr;
    }
}

void QMTAbstractMenu::InitMenu()
{
    _popMenu = new MtMenu(NULL);
    _popMenu->setMtType(MtMenu::menu1);
    connect(_popMenu, SIGNAL(triggered(QAction*)), this, SIGNAL(sigTriggered(QAction*)));
    connect(_popMenu, SIGNAL(triggered(QAction*)), this, SLOT(slotActionTriggered(QAction*)));
}

void QMTAbstractMenu::PopMenu()
{
    _popMenu->exec(QCursor::pos());
}

void QMTAbstractMenu::PopMenu(const QPoint& point)
{
    _popMenu->exec(point);
}

QAction* QMTAbstractMenu::AddAction(const QString& text)
{
    QAction* action = _popMenu->addAction(text);
    _actionList.append(action);
    _actionCallbackList.append(NULL);
    return action;
}

QAction* QMTAbstractMenu::AddAction(const QString& text, QMTMenuCallback funcCallback)
{
    QAction* action = _popMenu->addAction(text, funcCallback);
    _actionList.append(action);
    _actionCallbackList.append(funcCallback);
    return action;
}

QAction* QMTAbstractMenu::AddAction(MtMenu* menu, const QString& text, QMTMenuCallback funcCallback)
{
    QAction* action = nullptr;

    if (menu)
    {
        action = menu->addAction(text, funcCallback);
        _actionList.append(action);
        _actionCallbackList.append(funcCallback);
    }
    else
    {
        action = AddAction(text, funcCallback);
    }

    return action;
}

QAction* QMTAbstractMenu::AddAction(const QIcon& icon, const QString& text, QMTMenuCallback funcCallback)
{
    QAction* action = nullptr;

    if (nullptr == funcCallback)
    {
        action = _popMenu->addAction(icon, text);
    }
    else
    {
        action = _popMenu->addAction(icon, text, funcCallback);
    }

    _actionList.append(action);
    _actionCallbackList.append(funcCallback);
    return action;
}

QAction* QMTAbstractMenu::AddAction(const QString& text, const QObject* receiver, const char* member, const QKeySequence& shortcut)
{
    QAction* action = _popMenu->addAction(text, receiver, member, shortcut);
    _actionList.append(action);
    return action;
}

QAction* QMTAbstractMenu::AddAction(const QIcon& icon, const QString& text, const QObject* receiver, const char* member, const QKeySequence& shortcut)
{
    QAction* action = _popMenu->addAction(icon, text, receiver, member, shortcut);
    _actionList.append(action);
    return action;
}

QAction* QMTAbstractMenu::AddMenu(MtMenu* menu)
{
    QAction* action = _popMenu->addMenu(menu);
    //_menuList.append(action);
    return action;
}

MtMenu* QMTAbstractMenu::AddMenu(const QString& title)
{
    MtMenu* menu = _popMenu->addMenu(title);
    _menuList.append(menu);
    return menu;
}

MtMenu* QMTAbstractMenu::AddMenu(MtMenu* menu, const QString& title)
{
    MtMenu* ret = nullptr;

    if (menu)
    {
        ret = menu->addMenu(title);
        _menuList.append(ret);
    }
    else
    {
        ret = AddMenu(title);
    }

    return ret;
}

MtMenu* QMTAbstractMenu::AddMenu(const QIcon& icon, const QString& title)
{
    MtMenu* menu = _popMenu->addMenu(icon, title);
    _menuList.append(menu);
    return menu;
}

void QMTAbstractMenu::AddSeparator(MtMenu* menu)
{
    if (menu)
    {
        menu->addSeparator();
    }
    else
    {
        _popMenu->addSeparator();
    }
}

void QMTAbstractMenu::SetActionEnable(int index, bool enable, QString iconPath /*= ""*/)
{
    if (index < 0 || index >= _actionList.size())
        return;

    QAction* actionTemp = _actionList.at(index);

    if (iconPath.isEmpty())
    {
        iconPath = GetIconPath(index, enable);
    }

    if (iconPath.size() > 0)
    {
        actionTemp->setIcon(QIcon(iconPath));
    }

    actionTemp->setEnabled(enable);
}



void QMTAbstractMenu::SetAllActionEnable(bool enable)
{
    for (int i = 0; i < _actionList.size(); ++i)
    {
        // QAction* actionTemp = _actionList.at(i);
        // actionTemp->setEnabled(enable);
        SetActionEnable(i, enable);
    }
}

void QMTAbstractMenu::SetMenuEnable(int index, bool enable)
{
    if (index < 0 || index >= _menuList.size())
        return;

    MtMenu* menuTemp = _menuList.at(index);
    menuTemp->setEnabled(enable);
}

void QMTAbstractMenu::SetMenuFixedWidth(int width)
{
    if (_popMenu)
        _popMenu->setFixedWidth(width);
}

void QMTAbstractMenu::SetIconPathList(bool enable, QStringList& iconPathList)
{
    if (enable)
    {
        _enableIconPathList = iconPathList;
    }
    else
    {
        _disableIconPathList = iconPathList;
    }
}

QList<QAction*> QMTAbstractMenu::GetAllActionList()
{
    return _actionList;
}

MtMenu* QMTAbstractMenu::GetMenu()
{
    return _popMenu;
}

QStringList QMTAbstractMenu::GetIconPathList(bool enable)
{
    QStringList retStrList;
    retStrList = (enable == true ? _enableIconPathList : _disableIconPathList);
    return retStrList;
}

QString QMTAbstractMenu::GetIconPath(int index, bool enable)
{
    QString iconPath;
    QStringList strList = GetIconPathList(enable);

    if (index < strList.size())
    {
        iconPath = strList.at(index);
    }

    return iconPath;
}

void QMTAbstractMenu::Clear()
{
    _actionCallbackList.clear();
    _actionList.clear();
    _menuList.clear();
    _popMenu->clear();
}

void QMTAbstractMenu::slotActionTriggered(QAction* action)
{
    int index = -1;

    for (int i = 0; i < _actionList.size(); ++i)
    {
        QAction* actionTemp = _actionList.at(i);

        if (action == actionTemp)
        {
            index = i;
            break;
        }
    }

    if (index >= 0)
        emit sigTriggered(index);
}