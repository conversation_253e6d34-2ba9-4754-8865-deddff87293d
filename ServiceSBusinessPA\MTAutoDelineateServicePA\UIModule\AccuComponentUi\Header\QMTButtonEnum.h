﻿#pragma once
/*********************************************************************************
    *Copyright(C),2019,福建自贸试验区厦门片区Manteia数据科技有限公司
    *FileName: QMTButtonEnum.h     //文件名
    *Author: hzg     //作者
    *Version: *******            //版本号
    *CreateDate: 20190829 //创建日期
    *Description: 从QMTButtonUI中摘取除了，因为要增加类QMTButtonUI_Rt    //用于说明此程序文件完成的主要功能
    *History:  //修改历史记录列表，每条修改记录应包含修改日期、修改者及修改内容简介
        1.Date:
            Author:
            ModInfo:
        2.…………
**********************************************************************************/


/// </summary>
/// QMTButtonsUi 中的类型，主要用于设置显示及返回按键按下时是哪个状态
/// </summary>
enum ButtonType
{
    Button_Delinete = 0,
    Button_Cancel = 1,
    Button_Redo = 2,
    Button_Open = 3,
    Button_Dose = 4, // 剂量预测
    Button_Dose_Redo = 5, //重新预测
    Button_Dose_Cancel = 6, //取消预测
    Button_Dose_Invalid = 7 // 无法剂量预测（如MR的剂量暂时无法预测）
};

enum ButtonStyle
{
    Style_Gray,
    Style_Blue,
    Sytle_DisableGray,
};