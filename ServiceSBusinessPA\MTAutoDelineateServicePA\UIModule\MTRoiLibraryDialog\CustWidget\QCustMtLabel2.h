﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QColor>
#include <QVariant>
#include <QList>
#include "AccuComponentUi\Header\QMTUIModuleParam.h"
#include "MtLabel.h"

/*******
由MtLabel组成
*********/

//QCustMtLabel2参数
class QCustMtLabel2Param : public ICellWidgetParam
{
public:
    bool _showPix = false;  //是否显示图片
    QString _pixPath;       //图片路径
    bool _enableDot = true; //是否需要...
    bool _bWordWrap = false;    //是否自动换行，如果_enableDot为false的时候生效

    QCustMtLabel2Param();
    ~QCustMtLabel2Param();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustMtLabel2Param)

namespace Ui
{
class QCustMtLabel2;
}
class MtComboBox;

class QCustMtLabel2 :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustMtLabel2(QWidget* parent = Q_NULLPTR, bool bEnabaleDrawSquare = false);
    ~QCustMtLabel2();
    void SetupCellWidget(QCustMtLabel2Param& cellWidgetParam);

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual QString GetCurText();                            //获取当前界面展示文案
    //virtual void SetEnableEdit(bool bEdit);                  //设置是否允许编辑

    MtLabel* GetLabel();                                    //获取label对象
    QString GetFullString();                        //获取完整字符串
    void SetEnableDot(bool bEnable);
private:
    Ui::QCustMtLabel2* ui = nullptr;
};
