﻿#include "AutoSketchTemplateWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "CommonUtil.h"
#include "MtMessageBox.h"
#include "MtProgressDialog.h"
#include "DataDefine/InnerStruct.h"
#include "MTAutoDelineationDialog/ToolDialog/SketchTemplateSaveAs/SketchTemplateSaveAsDialog.h"
#include "MTAutoDelineationDialog/ToolDialog/EclipseImportResult/EclipseImportResultDialog.h"
#include "MTAutoDelineationDialog/ToolDialog/ModUnattendUsed/ModUnattendUsedDialog.h"
#include "MTRoiLibraryDialog/EclipseSub/ImportEclipseTemplateDialog.h"

#define Def_EveryItemCount                  33              //每个item中的子控件数
#define Def_TreeClose                       "treeClose"     //收起
#define Def_TreeExpand                      "treeExpand"    //展开


AutoSketchTemplateWidget::AutoSketchTemplateWidget(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    setWidgetLeftEnable(true);
    ui.mtStackedWidget->setHidden(true);
    ui.widget_rightBottom->setHidden(true);
    ui.mtToolButton_unattend->hide();
    ui.frame_left_line->hide();
    ui.mtComboBox->hide();
    //鼠标右键
    m_menu = new MtMenu(this);
    m_menu->setMtType(MtMenu::menu1);
    //模板名称输入框设置输入条件
    ui.lineEdit_templateName->setRegExpression(RegExp_NotSingleQuote);
    //初始化表格
    ui.table_widget_left->init();
    //UI样式
    ui.mtLabel->setStyleSheet("font-weight:bold;");
    ui.mtLabel_templateName->setStyleSheet("font-weight:bold;");
    QString mtFrameStyle = CommonUtil::formatStyleByCMtCoreWidgetUtil("background-color:rgba(@colorA0,0.7);");
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,AutoSketchTemplateWidget, " << errMsg.toStdString();
        }
    }
    //信号槽
    connnectSignal(true);
}

/// <summary>
/// 设置右侧roi-item的宽度
/// </summary>
/// <param name="widthNum">[IN]右侧roi-item的宽度</param>
AutoSketchTemplateWidget::~AutoSketchTemplateWidget()
{
}

void AutoSketchTemplateWidget::setRoiItemWidth(const int widthNum)
{
    m_roiItemWidth = widthNum;
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void AutoSketchTemplateWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
    ui.widget_roiListWidget->setImagePathHash(imagePathHash);
    ui.table_widget_left->setImagePathHash(imagePathHash);
    ui.mtToolButton_add->setPixmapFilename(imagePathHash["icon_add"]);
    ui.mtToolButton_edit->setPixmapFilename(imagePathHash["icon_edit"]);
    ui.mtToolButton_copy->setPixmapFilename(imagePathHash["icon_copy"]);
    ui.mtToolButton_del->setPixmapFilename(imagePathHash["icon_del2"]);
    ui.mtToolButton_unattend->setPixmapFilename(imagePathHash["icon_unattended"]);
    ui.mtToolButton_eclipse->setPixmapFilename(imagePathHash["import"]);
    ui.mtToolButton_top->setPixmapFilename(imagePathHash["icon_top"]);

    //清除图标
    if (m_clearSearchAction == nullptr)
    {
        m_clearSearchAction = new QAction(QIcon::QIcon(m_imagePathHash["icon_clean"]), "");
        ui.mtLineEdit_roiSearch->addAction(m_clearSearchAction, QLineEdit::TrailingPosition);
        connect(m_clearSearchAction, &QAction::triggered, this, &AutoSketchTemplateWidget::onRoiLineEditCleanAciontTriggered);
    }

    if (m_clearSearchAction2 == nullptr)
    {
        m_clearSearchAction2 = new QAction(QIcon::QIcon(m_imagePathHash["icon_clean"]), "");
        ui.mtLineEdit_roiSearch_RoiSelect->addAction(m_clearSearchAction2, QLineEdit::TrailingPosition);
        connect(m_clearSearchAction2, &QAction::triggered, this, &AutoSketchTemplateWidget::onRoiLineEditCleanAciontTriggered2);
    }
}

/// <summary>
/// 设置边距
/// </summary>
void AutoSketchTemplateWidget::setMargin(int left, int top, int right, int bottom)
{
    ui.verticalLayout->setContentsMargins(left, top, right, bottom);
}

void AutoSketchTemplateWidget::SetShowModalityComboBox(bool bShow)
{
    ui.mtComboBox->setVisible(bShow);
}

/// <summary>
/// 设置模态下拉框
/// </summary>
void AutoSketchTemplateWidget::setMtComboBoxModality(const QString& modality, const bool enable)
{
    ui.mtComboBox->setCurrentText(modality.toUpper());
    ui.mtComboBox->setEnabled(enable);
    ui.mtToolButton_eclipse->setHidden(modality.toUpper() != "CT");
}

void AutoSketchTemplateWidget::SetTemplateBtnWidgetVisible(bool bVisible)
{
    ui.frame_btn->setVisible(bVisible);
}

/// <summary>
/// 设置无人值守按钮使能
/// \n 默认true使能
/// </summary>
/// <param name="enable">[IN]使能</param>
void AutoSketchTemplateWidget::setEnableButtonUnattend(const bool enable)
{
    if (enable == true)
    {
        ui.mtToolButton_unattend->setEnabled(true);
    }
    else
    {
        ui.mtToolButton_unattend->setEnabled(false);
        ui.mtToolButton_unattend->setHidden(true);
    }
}

/// <summary>
/// 设置提前选中的页签类型
/// \n 不设置: 默认选中1-选择ROI进行勾画
/// </summary>
/// <param name="pageType">页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
void AutoSketchTemplateWidget::setSelectRadioPageType(const int pageType)
{
    m_selectRadioPageType = pageType;
}

/// <summary>
/// 设置需提前选中的模板id
/// </summary>
/// <param name="templateId">模板id</param>
void AutoSketchTemplateWidget::setSelectTemplateId(const int templateId)
{
    m_selectTemplateId = templateId;
}

/// <summary>
/// 设置是否显示选择ROI进行勾画/选择模板进行勾画页签
/// </summary>
/// <param name="isShow">true显示</param>
void AutoSketchTemplateWidget::setShowSelectRadioSelectBtn(const bool isShow)
{
    m_showSelectRadioSelectBtn = isShow;
}

/// <summary>
/// 设置虚拟模板
/// \n用于=选择ROI进行勾画=页签进行器官提前选中
/// </summary>
void AutoSketchTemplateWidget::setVirtualSketchCollection(n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection)
{
    m_stVirtualSketchTemplate = stSketchCollection;
}

/// <summary>
/// 初始化数据
/// </summary>
/// <param name="isConnectOptSketchCollection">[IN]设置连接OptSketchCollection信号,只有当同时存在全部模板和无人值守模板时才设置为true</param>
/// <param name="ptrOptSketchCollection">[IN]自动勾画模板信息</param>
/// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
void AutoSketchTemplateWidget::initData(OptSketchCollection* ptrOptSketchCollection, n_mtautodelineationdialog::ST_CallBack_AutoSketch& stCallBackAutoSketch)
{
    m_ptrOptSketchCollection = ptrOptSketchCollection;
    m_stCallBackAutoSketch = stCallBackAutoSketch;
    //重置展开/收起按钮
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));
    //清空右侧
    clearRightWidget(true, true);
    //重新初始化左侧列表item
    reInitLeftTableRow();
    ui.table_widget_left->scrollToTop();
    ui.mtToolButton_top->setHidden(true);

    //提前选中一个模板,提前选中 选择模板进行勾画 页签时生效
    QString rowValue;

    if (m_selectTemplateId > 0)
    {
        rowValue = QString::number(m_selectTemplateId);
    }
    else if (ui.table_widget_left->rowCount() > 0)
    {
        rowValue = ui.table_widget_left->GetRowUniqueValue(0);
    }

    ui.table_widget_left->SetCurrentRow(rowValue);
    slotLeftTableItemSelect(rowValue, Qt::LeftButton, QPoint());
}

/// <summary>
/// 重新初始化显示数据，将清空页面
/// </summary>
void AutoSketchTemplateWidget::reInitData()
{
    //初始化MtRadioButton
    ShowAllTemplate(true);
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));
    //清空右侧
    clearRightWidget();
    //重新初始化左侧列表item
    reInitLeftTableRow();
    ui.table_widget_left->scrollToTop();
    ui.mtToolButton_unattend->setHidden(true);
    ui.mtToolButton_top->setHidden(true);
}

/// <summary>
/// 选择ROI进行勾画选中
/// </summary>
void AutoSketchTemplateWidget::ShowAllTemplate(bool checked)
{
    if (m_showSelectRadioSelectBtn == false)
    {
        //选中左侧模板第一个
        if (ui.table_widget_left->rowCount() > 0)
        {
            ui.mtLineEdit_search->clear();
            QString rowValue = ui.table_widget_left->getCurSelectRow(0);
            ui.table_widget_left->SetCurrentRow(rowValue, false);
            slotLeftTableItemSelect(rowValue, Qt::LeftButton, QPoint());
        }

        return;
    }

    QString mtFrameStyle = CommonUtil::formatStyleByCMtCoreWidgetUtil("background-color:rgba(@colorA0,0.7);");
    QString fontStyle1 = CommonUtil::formatStyleByCMtCoreWidgetUtil("color:rgb(@colorA4);background-color:transparent;");
    QString fontStyle2 = CommonUtil::formatStyleByCMtCoreWidgetUtil("color:rgba(@colorA3,0.8);background-color:transparent;");

    ui.mtToolButton_edit->setEnabled(true);
    ui.mtToolButton_copy->setEnabled(true);
    ui.mtToolButton_del->setEnabled(true);
    ui.mtToolButton_top->setEnabled(true);
    ui.stackedWidget_save->setCurrentWidget(ui.page_edit1);

    if (m_from_clickRadioTemplate == true)
    {
        //如果是RadioButton是从选择ROI进行勾画--直接点击选择模板进行勾画，触发4清空右侧及重新选中第一个
        clearRightWidget();

        //选中左侧模板第一个
        if (ui.table_widget_left->rowCount() > 0)
        {
            ui.mtLineEdit_search->clear();
            QString rowValue = ui.table_widget_left->getCurSelectRow(0);
            ui.table_widget_left->SetCurrentRow(rowValue, false);
            slotLeftTableItemSelect(rowValue, Qt::LeftButton, QPoint());
        }
    }

    //重设展开/收起按钮状态
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));

    m_from_clickRadioTemplate = true;
}

/// <summary>
/// 展开按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_allTreeOpt()
{
    disconnect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchTemplateWidget::slotAllItemExpandFromGroupItemListWidget);

    //当前需求只有编辑状态下有全部展开/收起功能
    //判断前一个状态
    if (ui.mtPushButton_expand->whatsThis() == Def_TreeClose)
    {
        ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
        ui.mtPushButton_expand->setText(tr("全部收起"));
        ui.widget_roiListWidget->expandTreeAll(GroupHTitle::Page_Check, true);
    }
    else if (ui.mtPushButton_expand->whatsThis() == Def_TreeExpand)
    {
        ui.mtPushButton_expand->setWhatsThis(Def_TreeClose);
        ui.mtPushButton_expand->setText(tr("全部展开"));
        ui.widget_roiListWidget->expandTreeAll(GroupHTitle::Page_Check, false);
    }

    connect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchTemplateWidget::slotAllItemExpandFromGroupItemListWidget);
}

/// <summary>
/// 清空选中按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_cleanAllCheck()
{
    //当前需求只有选择ROI进行勾画页签才有清空选中功能
    ui.widget_roiListWidget->checkTreeAll(GroupHTitle::Page_Check, false);
}

/// <summary>
/// 模态下拉
/// </summary>
void AutoSketchTemplateWidget::onMtComboBox(const QString& text)
{
    if (ui.mtLineEdit_search->text().isEmpty() == true)
    {
        //清空左侧
        ui.table_widget_left->delAllRow();
        //清空右侧
        clearRightWidget();
        //获取排序后的模板队列信息并挂载
        reInitLeftTableRow();
    }
    else
    {
        //清空右侧
        clearRightWidget();
        //获取排序后的模板队列信息并挂载
        reInitLeftTableRow();
        ui.mtLineEdit_search->clear();
    }

    ui.mtToolButton_eclipse->setHidden(text.toUpper() != "CT");
}

/// <summary>
/// 搜索框文本变化
/// </summary>
void AutoSketchTemplateWidget::onMtLineEditTextChanged(const QString& text)
{
    //清空右侧
    clearRightWidget();
    //清空左侧
    ui.table_widget_left->clearSelect();
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);

    //如果为空，还原回原来状态，并恢复可拖拽状态
    if (text.isEmpty() == true)
    {
        QList<n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionList = m_ptrOptSketchCollection->getSortSketchCollectionList(dcmTypeEnum);

        for (int i = 0; i < sketchCollectionList.size(); i++)
        {
            QString rowValue = QString::number(sketchCollectionList[i].id);
            ui.table_widget_left->hideRow(rowValue, false);
        }

        //恢复列表拖拽功能
        m_ptrOptSketchCollection->setTableNameDropEnable(true);
        return;
    }

    //禁用列表拖拽功能
    m_ptrOptSketchCollection->setTableNameDropEnable(false);
    //如果不为空，实时检索，不区分大小写
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionList = m_ptrOptSketchCollection->getSortSketchCollectionList(dcmTypeEnum);

    for (int i = 0; i < sketchCollectionList.size(); i++)
    {
        QString rowValue = QString::number(sketchCollectionList[i].id);
        QString templateName = sketchCollectionList[i].templateName;
        bool isExist = templateName.contains(text, Qt::CaseInsensitive);
        ui.table_widget_left->hideRow(rowValue, !isExist);
    }
}

/// <summary>
/// 右侧roi搜索框文本变化
/// </summary>
void AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI(const QString& text)
{
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap;

    //如果为空，还原回原来状态
    if (text.isEmpty() == true)
    {
        newOrganByGroupIdMap = m_cacheOrganByGroupIdMap;
    }
    else //如果不为空，实时检索，不区分大小写
    {
        QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin();

        for (it; it != m_cacheOrganByGroupIdMap.end(); it++)
        {
            int groupId = it.key();
            QList<n_mtautodelineationdialog::ST_Organ>& organList = it.value();

            for (int i = 0; i < organList.size(); i++)
            {
                n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

                if (stOrgan.customOrganName.contains(text, Qt::CaseInsensitive) == true ||
                    stOrgan.organChineseName.contains(text, Qt::CaseInsensitive) == true ||
                    stOrgan.roiDesc.contains(text, Qt::CaseInsensitive) == true)
                {
                    newOrganByGroupIdMap[groupId].push_back(stOrgan);
                }
            }
        }
    }

    int templateId = ui.widget_roiListWidget->getUniqueKey(); //重新获取一下原有的唯一id，否则会被clearAllItem清掉
    ui.widget_roiListWidget->clearAllItem();
    ui.widget_roiListWidget->setUniqueKey(templateId);
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    newOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(newOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, newOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 右侧roi搜索框清空
/// </summary>
void AutoSketchTemplateWidget::onRoiLineEditCleanAciontTriggered(bool checked)
{
    if (ui.mtLineEdit_roiSearch->text().isEmpty() == false)
        ui.mtLineEdit_roiSearch->clear();
}

/// <summary>
/// 右侧roi搜索框清空
/// </summary>
void AutoSketchTemplateWidget::onRoiLineEditCleanAciontTriggered2(bool checked)
{
    if (ui.mtLineEdit_roiSearch_RoiSelect->text().isEmpty() == false)
        ui.mtLineEdit_roiSearch_RoiSelect->clear();
}

/// <summary>
/// 新增按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_add()
{
    //新建左侧item
    setWidgetLeftEnable(false);
    ModelNameTableInfo modelNameTableInfo;
    modelNameTableInfo.templateId = Def_NewCreateId;
    modelNameTableInfo.templateName = QString(tr("未命名模板"));
    modelNameTableInfo.isUnattended = (false);
    ui.table_widget_left->addRow(modelNameTableInfo, true);
    //新建右侧item
    clearRightWidget();
    setRightStackWidgetShow(true, true);
    ui.widget_roiListWidget->setUniqueKey(Def_NewCreateId);//用来代表新建
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = m_ptrOptSketchCollection->getOrganByGroupIdMap(dcmTypeEnum);
    m_cacheOrganByGroupIdMap = tempOrganByGroupIdMap;
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    tempOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(tempOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, tempOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
    //发送正处于编辑模式
    emit this->sigEditTemplateStart(true);
}

/// <summary>
/// 编辑按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_edit()
{
    //重设展开/收起按钮状态
    QString rowValue = ui.table_widget_left->getCurSelectRow();

    if (isExistTemplateId(rowValue) == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要编辑的模板"));
        return;
    }

    //判断模板是否允许操作
    if (isCanOptTemplate(n_mtautodelineationdialog::EM_OptType::OptType_Mod, rowValue.toInt()) == false)
    {
        return;
    }

    //更新右侧界面-编辑状态
    updateRightWidgetEdit(rowValue.toInt());
    //发送正处于编辑模式
    emit this->sigEditTemplateStart(true);
}

/// <summary>
/// 复制按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_copy()
{
    QString rowValue = ui.table_widget_left->getCurSelectRow();

    if (isExistTemplateId(rowValue) == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要复制的模板"));
        return;
    }

    //获取原始数据
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());
    //新建左侧item
    setWidgetLeftEnable(false);
    ModelNameTableInfo modelNameTableInfo;
    modelNameTableInfo.templateId = Def_NewCreateId;
    QString newName = m_ptrOptSketchCollection->createNewTemplateName(curSketchCollection.templateName);
    modelNameTableInfo.templateName = (newName.isEmpty() ? QString(tr("未命名模板")) : newName);
    modelNameTableInfo.isUnattended = curSketchCollection.isUnattended;
    ui.table_widget_left->addRow(modelNameTableInfo, true);
    //新建右侧item
    curSketchCollection.templateName = modelNameTableInfo.templateName;
    curSketchCollection.id = Def_NewCreateId; //重置一下模板id
    updateRightWidgetEdit(curSketchCollection);
    //发送正处于编辑模式
    emit this->sigEditTemplateStart(true);
}

/// <summary>
/// 删除按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_del()
{
    QString rowValue = ui.table_widget_left->getCurSelectRow();

    //判断是否选中
    if (isExistTemplateId(rowValue) == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要删除的模板"));
        return;
    }

    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    n_mtautodelineationdialog::ST_SketchModelCollection delSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());

    //判断模板是否允许操作
    if (isCanOptTemplate(n_mtautodelineationdialog::EM_OptType::OptType_Del, delSketchCollection.id) == false)
    {
        return;
    }

    //删除二次确认
    QString msg = QString(tr("您确定要删除%1吗？")).arg(delSketchCollection.templateName);
    int ret = MtMessageBox::redWarning(this, msg, QString(), tr("删除"));

    if (QMessageBox::Yes == ret)
    {
        //数据回调
        if (m_stCallBackAutoSketch.updateSketchCollectionCallBack != nullptr)
        {
            int newTemplateId = -1;
            QString errMsg;

            if (m_stCallBackAutoSketch.updateSketchCollectionCallBack(n_mtautodelineationdialog::EM_OptType::OptType_Del, delSketchCollection, newTemplateId, errMsg) == false)
            {
                MtMessageBox::yellowWarning(this, tr("模板删除失败"), errMsg);
                return;
            }

            //更新内存数据
            m_ptrOptSketchCollection->delCollection(dcmTypeEnum, delSketchCollection.id);
            //清空右侧
            clearRightWidget();
            //删除左侧
            delLeftTableRow(rowValue);
        }
    }
}

/// <summary>
/// 无人值守按钮
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_unattend()
{
    QString rowValue = ui.table_widget_left->getCurSelectRow();

    if (isExistTemplateId(rowValue) == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要设置的模板"));
        setToolButtonUnattendState(false, false);
        return;
    }

    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    n_mtautodelineationdialog::ST_SketchModelCollection sketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());

    //判断模板是否允许操作
    if (isCanOptTemplate(n_mtautodelineationdialog::EM_OptType::OptType_OutUnattend, sketchCollection.id) == false)
    {
        return;
    }

    //数据回调
    if (m_stCallBackAutoSketch.setSketchCollectionUnattendCallBack != nullptr)
    {
        QString errMsg;

        if (m_stCallBackAutoSketch.setSketchCollectionUnattendCallBack(sketchCollection.id, !sketchCollection.isUnattended, errMsg) == false)
        {
            QString str = (sketchCollection.isUnattended == true ? tr("设置为常规模板失败") : tr("设置为无人值守模板失败"));
            MtMessageBox::yellowWarning(this, str, errMsg);
            return;
        }
    }

    //内存更新
    if (sketchCollection.isUnattended == true)
    {
        //如果原始是无人值守，此时点击代表移出
        sketchCollection.isUnattended = false;
        ui.table_widget_left->setIsUnattended(rowValue, false);
        m_ptrOptSketchCollection->updateCollection(sketchCollection);
        ui.mtToolButton_unattend->setToolTipText(tr("设置为无人值守模板"));
        ui.mtToolButton_unattend->setPixmapFilename(m_imagePathHash["icon_unattended"]);
    }
    else
    {
        //如果原始是普通模板，此时点击代表移入无人值守
        sketchCollection.isUnattended = true;
        ui.table_widget_left->setIsUnattended(rowValue, true);
        m_ptrOptSketchCollection->updateCollection(sketchCollection);
        ui.mtToolButton_unattend->setToolTipText(tr("设置为常规模板"));
        ui.mtToolButton_unattend->setPixmapFilename(m_imagePathHash["icon_remove_unattended"]);
    }
}

/// <summary>
/// 置顶
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_toTop()
{
    QString rowValue = ui.table_widget_left->getCurSelectRow();
    ui.table_widget_left->topRow(rowValue);
    ui.mtToolButton_top->setHidden(true);
}

/// <summary>
/// 导入eclipse模板
/// </summary>
void AutoSketchTemplateWidget::onMtToolButton_eclipse()
{
    QSet<QString> templateNameSet;
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionList = m_ptrOptSketchCollection->getSketchCollectionListNotSort();

    for (int i = 0; i < sketchCollectionList.size(); i++)
    {
        templateNameSet.insert(sketchCollectionList[i].templateName);
    }

    QMap<QString/*新模板名称*/, QList<n_mtautodelineationdialog::ST_Organ>> templateOrganMap;
    QMap<int/*organID*/, n_mtautodelineationdialog::ST_Organ> emptyRoiMap;
    /*************************************************填充/更新数据*******************************************************/
    //更新organ信息
    auto funcUpdateOrganInfoList = [](QList<n_mtautodelineationdialog::ST_Organ>& allOrganInfoList
                                      , const n_mtautodelineationdialog::ST_RoiLabelInfo& labelInfo
                                      , bool bNeedSyncInfo) -> QList<n_mtautodelineationdialog::ST_Organ>
    {
        QList<n_mtautodelineationdialog::ST_Organ> retOrganList;
        n_mtautodelineationdialog::ST_Organ existOrganEmpty;//记录同名空勾画

        for (n_mtautodelineationdialog::ST_Organ& organItem : allOrganInfoList)
        {
            if (organItem.roiLabel.toLower() == labelInfo.manteiaRoiLabel.toLower())
            {
                //更新信息
                if (bNeedSyncInfo)
                {
                    organItem.customOrganName = labelInfo.roiName;
                    organItem.customColor = labelInfo.roiColor;
                    organItem.optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                }

                //加入返回队列
                retOrganList.append(organItem);
            }

            if (organItem.modelId == -1 && organItem.defaultOrganName.toLower() == labelInfo.manteiaRoiLabel.toLower() && existOrganEmpty.defaultOrganName.isEmpty())
            {
                existOrganEmpty = organItem;
            }
        }

        //如果没有对应的roi，则创建空roi
        if (retOrganList.isEmpty())
        {
            n_mtautodelineationdialog::ST_Organ organEmpty;
            organEmpty.bodypart;
            organEmpty.customColor = labelInfo.roiColor;
            organEmpty.customOrganName = labelInfo.manteiaRoiLabel;
            organEmpty.defaultColor = labelInfo.roiColor;
            organEmpty.defaultOrganName = labelInfo.manteiaRoiLabel;
            organEmpty.modelId = -1;
            organEmpty.optTypeEnum = n_mtautodelineationdialog::OptType_Add;
            //organEmpty.organChineseName = labelInfo.manteiaRoiLabel;
            organEmpty.organEnglishName = labelInfo.manteiaRoiLabel;
            organEmpty.roiLabel = labelInfo.manteiaRoiLabel;
            organEmpty.roiType = labelInfo.roiType;
            organEmpty.organGroupInfoMap.insert(7, { 7, 1, -1, tr("空勾画") });
            retOrganList.append(organEmpty);

            if (existOrganEmpty.defaultOrganName.isEmpty())//已存在同名空勾画，则不保存入库
            {
                allOrganInfoList.append(organEmpty);
            }
        }

        return retOrganList;
    };
    //更新标签信息--若已存在,则更新名称和颜色，否则创建标签
    auto funcUpdateLabelInfoList = [](QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& allRoiLabelInfoList
                                      , const ST_EclipseRoi& eclipseRoiInfo
                                      , bool bNeedSyncInfo
                                      , n_mtautodelineationdialog::ST_RoiLabelInfo& retLabelInfo)
    {
        //查找标签库是否有对应code编码
        bool bExist = false;

        for (n_mtautodelineationdialog::ST_RoiLabelInfo& labelItem : allRoiLabelInfoList)
        {
            if (!labelItem.roiCodeMap[n_mtautodelineationdialog::Manufacturer_Eclipse]["code"].isEmpty()
                && labelItem.roiCodeMap[n_mtautodelineationdialog::Manufacturer_Eclipse]["code"] == eclipseRoiInfo.roiCodeMap["code"])
            {
                //找到了对应code的label，则进行更新
                if (labelItem.optTypeEnum == n_mtautodelineationdialog::OptType_Add)
                {
                    labelItem.roiColor = eclipseRoiInfo.ColorAndStyle;
                    labelItem.roiName = eclipseRoiInfo.structureID;
                    labelItem.roiAlias = eclipseRoiInfo.structureID;
                    labelItem.manteiaRoiLabel = eclipseRoiInfo.structureID;
                }
                else
                {
                    if (bNeedSyncInfo)
                    {
                        labelItem.roiColor = eclipseRoiInfo.ColorAndStyle;
                        labelItem.roiName = eclipseRoiInfo.structureID;
                        labelItem.optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                    }

                    if (!labelItem.roiAlias.toLower().contains(eclipseRoiInfo.structureID.toLower()))
                    {
                        if (labelItem.roiAlias[labelItem.roiAlias.size() - 1] == ';')
                        {
                            labelItem.roiAlias.chop(1);
                        }

                        labelItem.roiAlias += ";" + eclipseRoiInfo.structureID;

                        if (labelItem.roiAlias[0] == ';')
                        {
                            labelItem.roiAlias.remove(0, 1);
                        }

                        labelItem.optTypeEnum = n_mtautodelineationdialog::OptType_Mod;
                    }
                }

                //填充返回label信息
                retLabelInfo = labelItem;
                retLabelInfo.roiColor = eclipseRoiInfo.ColorAndStyle;
                retLabelInfo.roiName = eclipseRoiInfo.structureID;
                bExist = true;
                break;
            }
        }

        //若标签库无对应的code编码，则将在标签库自动创建对应的标签
        if (!bExist)
        {
            retLabelInfo.isbuiltIn = false;
            retLabelInfo.manteiaRoiLabel = eclipseRoiInfo.structureID;
            retLabelInfo.roiName = eclipseRoiInfo.structureID;
            retLabelInfo.optTypeEnum = n_mtautodelineationdialog::OptType_Add;
            retLabelInfo.roiAlias = retLabelInfo.manteiaRoiLabel + ";" + retLabelInfo.roiName;
            retLabelInfo.roiColor = eclipseRoiInfo.ColorAndStyle;
            retLabelInfo.roiType = CommonUtil::getRoiTypeList().contains(eclipseRoiInfo.volumeType) ? eclipseRoiInfo.volumeType : "NONE";
            retLabelInfo.roiCodeMap[n_mtautodelineationdialog::Manufacturer_Eclipse] = eclipseRoiInfo.roiCodeMap;

            //code为空时，不入库
            if (!eclipseRoiInfo.roiCodeMap["code"].isEmpty())
            {
                allRoiLabelInfoList.append(retLabelInfo);
            }
        }
    };
    auto funcFixTemplateName = [](const QString& name, const QSet<QString>& nameSet) -> QString
    {
        QString retName = name;
        int nSame = 0;

        while (nameSet.contains(retName))
        {
            retName = name + QString("(%1)").arg(++nSame);
        }

        return retName;
    };
    //获取所有的器官信息
    QList<n_mtautodelineationdialog::ST_Organ> allOrganInfoList;

    if (nullptr != m_stCallBackAutoSketch.getAllOrganInfoCallback)
    {
        allOrganInfoList = m_stCallBackAutoSketch.getAllOrganInfoCallback();
    }

    //获取所有的标签信息
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> allRoiLabelInfoList;

    if (nullptr != m_stCallBackAutoSketch.getAllRoiLabelInfoCallback)
    {
        allRoiLabelInfoList = m_stCallBackAutoSketch.getAllRoiLabelInfoCallback();
    }

    QString searchDir;
    bool bSub = true;
    ImportEclipseTemplateDialog dlg(this);
    dlg.setSearchDirPath(searchDir, bSub);
    int dlgRet = dlg.exec();

    if (dlgRet == QDialog::Accepted)
    {
        bool bNeedSyncInfo = dlg.isNeedSyncInfo();
        QMap<QString/*templateId*/, ST_EclipseTemplate> eclipseTemplateMap = dlg.getNewEclipseTemplate();

        for (QMap<QString, ST_EclipseTemplate>::const_iterator itTemplate = eclipseTemplateMap.constBegin();
             itTemplate != eclipseTemplateMap.constEnd();
             ++itTemplate)
        {
            QString templateName = funcFixTemplateName(itTemplate.value().templateID, templateNameSet);   //模板名
            QList<n_mtautodelineationdialog::ST_Organ> templateOrganList;

            for (QMap<QString/*roiCode*/, ST_EclipseRoi>::const_iterator it = itTemplate.value().roiInfoMap.constBegin();
                 it != itTemplate.value().roiInfoMap.constEnd();
                 it++)
            {
                const ST_EclipseRoi& eclipseRoiInfo = it.value();

                if (eclipseRoiInfo.structureID.isEmpty() == true)
                    continue;

                n_mtautodelineationdialog::ST_RoiLabelInfo retLabelInfo;
                funcUpdateLabelInfoList(allRoiLabelInfoList, eclipseRoiInfo, bNeedSyncInfo, retLabelInfo);
                templateOrganMap[templateName].append(funcUpdateOrganInfoList(allOrganInfoList, retLabelInfo, bNeedSyncInfo));
            }
        }

        //更新数据库
        if (nullptr != m_stCallBackAutoSketch.updateRoiLabelInfoCallback)
        {
            m_stCallBackAutoSketch.updateRoiLabelInfoCallback(allRoiLabelInfoList);
        }

        if (nullptr != m_stCallBackAutoSketch.updateOrganInfoCallback)
        {
            m_stCallBackAutoSketch.updateOrganInfoCallback(allOrganInfoList);
        }

        //获取最新器官信息
        if (nullptr != m_stCallBackAutoSketch.getAllOrganInfoCallback)
        {
            allOrganInfoList = m_stCallBackAutoSketch.getAllOrganInfoCallback();
            //更新缓存
            QMap<QString, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = templateOrganMap.begin();

            for (; it != templateOrganMap.end(); ++it)
            {
                for (n_mtautodelineationdialog::ST_Organ& organItem : it.value())
                {
                    if (organItem.optTypeEnum != n_mtautodelineationdialog::OptType_Add)
                    {
                        continue;
                    }

                    //查找对应的器官，获取其id
                    for (const n_mtautodelineationdialog::ST_Organ& tempItem : allOrganInfoList)
                    {
                        if (organItem.optTypeEnum == n_mtautodelineationdialog::OptType_Add)
                        {
                            if (tempItem.defaultOrganName == organItem.defaultOrganName && tempItem.modelId == -1 && tempItem.roiLabel == organItem.roiLabel)
                            {
                                organItem.id = tempItem.id;
                                emptyRoiMap[tempItem.id] = tempItem;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    /*************************************************填充/更新数据*******************************************************/
    if (dlgRet == QDialog::Rejected)
        return;

    QList<n_mtautodelineationdialog::ST_SketchModelCollection> addSketchCollectionList;
    addSketchCollectionList.reserve(10);

    for (QMap<QString/*新模板名称*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = templateOrganMap.begin(); it != templateOrganMap.end(); it++)
    {
        n_mtautodelineationdialog::ST_SketchModelCollection newSketchCollection;
        newSketchCollection.isUnattended = false;
        newSketchCollection.templateName = it.key();
        newSketchCollection.modality = "CT";

        for (int i = 0; i < it.value().size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = it.value()[i];

            for (QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo>::Iterator iter_2 = stOrgan.organGroupInfoMap.begin(); iter_2 != stOrgan.organGroupInfoMap.end(); iter_2++)
            {
                newSketchCollection.showGroupIdMap[stOrgan.id].insert(iter_2.key());
                newSketchCollection.groupIdSet.insert(iter_2.key());
            }
        }

        if (newSketchCollection.showGroupIdMap.isEmpty() == true)
            continue;

        //数据回调
        int newTemplateId = -1;

        if (m_stCallBackAutoSketch.updateSketchCollectionCallBack != nullptr)
        {
            QString errMsg;
            m_stCallBackAutoSketch.updateSketchCollectionCallBack(n_mtautodelineationdialog::EM_OptType::OptType_Add, newSketchCollection, newTemplateId, errMsg);
            //更新内存数据
            newSketchCollection.id = newTemplateId; //更新模板id
            m_ptrOptSketchCollection->addCollection(newSketchCollection);
            m_ptrOptSketchCollection->addEmptyOrgan(emptyRoiMap);
            addSketchCollectionList.append(newSketchCollection);
            //操作左侧列表
            //获取当前选中的模板
            QString rowValue = ui.table_widget_left->getCurSelectRow();
            //新增
            ModelNameTableInfo modelNameTableInfo;
            modelNameTableInfo.templateId = newTemplateId;
            modelNameTableInfo.templateName = newSketchCollection.templateName;
            modelNameTableInfo.isUnattended = newSketchCollection.isUnattended;
            ui.table_widget_left->addRow(modelNameTableInfo, false);
            setToolButtonUnattendState(true, !modelNameTableInfo.isUnattended);
            ui.table_widget_left->scrollToTop();
            //选中之前的模板
            ui.table_widget_left->SetCurrentRow(rowValue, false);
        }
    }

    //重新初始化内存中的器官CustomName信息
    m_ptrOptSketchCollection->reInitOrganCustomName(allOrganInfoList);

    //弹窗提示创建几个完成
    if (addSketchCollectionList.isEmpty() == false)
    {
        EclipseImportResultDialog dlg(this);
        dlg.init(addSketchCollectionList);
        dlg.exec();
    }
}

/// <summary>
/// 编辑
/// </summary>
void AutoSketchTemplateWidget::onMPushButton_edit()
{
    QString rowValue = ui.table_widget_left->getCurSelectRow();

    if (rowValue.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要编辑的模板"));
        return;
    }

    //判断模板是否允许操作
    if (isCanOptTemplate(n_mtautodelineationdialog::EM_OptType::OptType_Mod, rowValue.toInt()) == false)
    {
        return;
    }

    //更新右侧界面-编辑状态
    updateRightWidgetEdit(rowValue.toInt());
    //发送正处于编辑模式
    emit this->sigEditTemplateStart(true);
}

/// <summary>
/// 取消编辑
/// </summary>
void AutoSketchTemplateWidget::onMPushButton_cancelEdit()
{
    //重设展开/收起按钮状态
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));
    int templateId = ui.widget_roiListWidget->getUniqueKey();

    if (templateId == Def_NewCreateId) //新建
    {
        clearRightWidget();
        setWidgetLeftEnable(true);
        delLeftTableRow(QString::number(templateId));
    }
    else //编辑
    {
        updateRightWidgetNoEdit(templateId);
    }

    //发送不处于编辑模式
    emit this->sigEditTemplateStart(false);
}

/// <summary>
/// 另存
/// </summary>
void AutoSketchTemplateWidget::onMtmtPushButton_saveas()
{
    n_mtautodelineationdialog::ST_SketchModelCollection newSketchCollection;
    newSketchCollection.isUnattended = ui.table_widget_left->isUnattendedItem(QString::number(ui.widget_roiListWidget->getUniqueKey()));
    newSketchCollection.templateName = ui.lineEdit_templateName->text();
    newSketchCollection.modality = ui.mtComboBox->currentText();
    //
    //整理选中的器官
    int templateId = ui.widget_roiListWidget->getUniqueKey(); //模板id

    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin(); it != m_cacheOrganByGroupIdMap.end(); it++)
    {
        int groupId = it.key();
        QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();
        int mianOrganNum = 0;

        for (int i = 0; i < organList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

            if (stOrgan.isVisiable == false)
            {
                continue;
            }

            int organId = stOrgan.id;

            //如果是亚结构器官的情况下，需把主分组id重新设置为亚分组
            if (stOrgan.organGroupInfoMap.size() == 1 && stOrgan.organGroupInfoMap.begin().value().type == 3)
            {
                int subGroupId = stOrgan.organGroupInfoMap.begin().value().id;
                newSketchCollection.showGroupIdMap[organId].insert(subGroupId);
                newSketchCollection.groupIdSet.insert(subGroupId);
                newSketchCollection.subInMainGroupIdMap[organId].insert(groupId); //亚组在哪些主分组下显示
                continue;
            }

            //主结构器官
            newSketchCollection.showGroupIdMap[organId].insert(groupId);
            mianOrganNum++;
        }

        if (mianOrganNum > 0)
            newSketchCollection.groupIdSet.insert(groupId);
    }

    //判断是否有选择器官
    if (newSketchCollection.showGroupIdMap.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择ROI"));
        return;
    }

    //判断是否只选择空勾画
    {
        bool onlyEmptyRoi = true;
        QSet<int> emptyOrganIdSet = ((OptSketchCollection*)m_ptrOptSketchCollection)->getEmptyOrganIdSet();

        for (QMap<int/*organId*/, QSet<int>>::iterator it = newSketchCollection.showGroupIdMap.begin(); it != newSketchCollection.showGroupIdMap.end(); it++)
        {
            if (emptyOrganIdSet.contains(it.key()) == false)
            {
                onlyEmptyRoi = false;
                break;
            }
        }

        if (onlyEmptyRoi == true)
        {
            MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许只选择空勾画，请再选择其他ROI"));
            return;
        }
    }
    //
    //弹出模板名称重命名窗口
    SketchTemplateSaveAsDialog dlg(m_ptrOptSketchCollection->getSketchCollectionListNotSort(), this);

    if (dlg.exec() != QDialog::Accepted)
        return;

    //数据回调
    int newTemplateId = -1;
    newSketchCollection.templateName = dlg.getNewTemplateName();

    if (m_stCallBackAutoSketch.updateSketchCollectionCallBack != nullptr)
    {
        QString errMsg;

        if (m_stCallBackAutoSketch.updateSketchCollectionCallBack(n_mtautodelineationdialog::EM_OptType::OptType_Add, newSketchCollection, newTemplateId, errMsg) == false)
        {
            MtMessageBox::yellowWarning(this, tr("模板保存失败"), errMsg);
            return;
        }

        newSketchCollection.id = newTemplateId; //更新模板id
        m_ptrOptSketchCollection->addCollection(newSketchCollection);
    }

    //获取当前选中的模板
    QString rowValue = ui.table_widget_left->getCurSelectRow();
    //左侧栏插入一列
    ModelNameTableInfo modelNameTableInfo;
    modelNameTableInfo.templateId = newTemplateId;
    modelNameTableInfo.templateName = newSketchCollection.templateName;
    modelNameTableInfo.isUnattended = ui.table_widget_left->isUnattendedItem(QString::number(ui.widget_roiListWidget->getUniqueKey()));;
    ui.table_widget_left->addRow(modelNameTableInfo, false);
    ui.table_widget_left->scrollToTop(); //不知道为啥，如果有滚动条出现，不移动到最下面，此时另存为再点击保存会崩溃在delRow(const QString& rowValue)中
    //选中之前选中的模板
    ui.table_widget_left->SetCurrentRow(rowValue);
}

/// <summary>
/// 保存
/// </summary>
void AutoSketchTemplateWidget::onMtmtPushButton_saveEdit()
{
    //重设展开/收起按钮状态
    ui.mtPushButton_expand->setWhatsThis(Def_TreeExpand);
    ui.mtPushButton_expand->setText(tr("全部收起"));

    if (isInputNormal() == false)
        return;

    n_mtautodelineationdialog::ST_SketchModelCollection newSketchCollection;
    newSketchCollection.isUnattended = ui.table_widget_left->isUnattendedItem(QString::number(ui.widget_roiListWidget->getUniqueKey()));
    newSketchCollection.templateName = ui.lineEdit_templateName->text();
    newSketchCollection.modality = ui.mtComboBox->currentText();
    //
    //整理选中的器官
    int templateId = ui.widget_roiListWidget->getUniqueKey(); //模板id

    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin(); it != m_cacheOrganByGroupIdMap.end(); it++)
    {
        int groupId = it.key(); //亚组已经被映射到主分组
        QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();
        int mianOrganNum = 0;

        for (int i = 0; i < organList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

            if (stOrgan.isVisiable == false)
            {
                continue;
            }

            int organId = stOrgan.id;

            //如果是亚结构器官的情况下，需把主分组id重新设置为亚分组
            if (stOrgan.organGroupInfoMap.size() == 1 && stOrgan.organGroupInfoMap.begin().value().type == 3)
            {
                int subGroupId = stOrgan.organGroupInfoMap.begin().value().id;
                newSketchCollection.showGroupIdMap[organId].insert(subGroupId);
                newSketchCollection.groupIdSet.insert(subGroupId);
                newSketchCollection.subInMainGroupIdMap[organId].insert(groupId); //亚组在哪些主分组下显示
                continue;
            }

            //主结构器官
            newSketchCollection.showGroupIdMap[organId].insert(groupId);
            mianOrganNum++;
        }

        if (mianOrganNum > 0)
            newSketchCollection.groupIdSet.insert(groupId);
    }

    //当前模板模态
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);

    //非新建
    if (templateId != Def_NewCreateId && isExistTemplateId(QString::number(templateId)) == true)
    {
        newSketchCollection.id = templateId;
        n_mtautodelineationdialog::ST_SketchModelCollection oldSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, templateId);
        newSketchCollection.isUnattended = oldSketchCollection.isUnattended;
    }

    //判断是否有选择器官
    if (newSketchCollection.showGroupIdMap.isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择ROI"));
        return;
    }

    //判断是否只选择空勾画
    {
        bool onlyEmptyRoi = true;
        QSet<int> emptyOrganIdSet = ((OptSketchCollection*)m_ptrOptSketchCollection)->getEmptyOrganIdSet();

        for (QMap<int/*organId*/, QSet<int>>::iterator it = newSketchCollection.showGroupIdMap.begin(); it != newSketchCollection.showGroupIdMap.end(); it++)
        {
            if (emptyOrganIdSet.contains(it.key()) == false)
            {
                onlyEmptyRoi = false;
                break;
            }
        }

        if (onlyEmptyRoi == true)
        {
            MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许只选择空勾画，请再选择其他ROI"));
            return;
        }
    }
    //
    //数据回调
    int newTemplateId = -1;

    if (m_stCallBackAutoSketch.updateSketchCollectionCallBack != nullptr)
    {
        n_mtautodelineationdialog::EM_OptType optType = (templateId == Def_NewCreateId ? n_mtautodelineationdialog::EM_OptType::OptType_Add : n_mtautodelineationdialog::EM_OptType::OptType_Mod);
        QString errMsg;

        if (m_stCallBackAutoSketch.updateSketchCollectionCallBack(optType, newSketchCollection, newTemplateId, errMsg) == false)
        {
            MtMessageBox::yellowWarning(this, tr("模板保存失败"), errMsg);
            return;
        }

        //更新内存数据
        if (templateId == Def_NewCreateId) //新建
        {
            newSketchCollection.id = newTemplateId; //更新模板id
            m_ptrOptSketchCollection->addCollection(newSketchCollection);
        }
        else //更新
        {
            m_ptrOptSketchCollection->updateCollection(newSketchCollection);
        }
    }

    //切换为展示页面,刷新右侧roi列表
    clearRightWidget();
    updateRightWidgetNoEdit(newSketchCollection.id);
    //使能左侧列表
    setWidgetLeftEnable(true);
    //发送不处于编辑模式
    emit this->sigEditTemplateStart(false);

    //操作左侧列表
    if (templateId == Def_NewCreateId) //新建
    {
        //先删除
        delLeftTableRow(QString::number(Def_NewCreateId));
        //新增
        ModelNameTableInfo modelNameTableInfo;
        modelNameTableInfo.templateId = newTemplateId;
        modelNameTableInfo.templateName = newSketchCollection.templateName;
        modelNameTableInfo.isUnattended = newSketchCollection.isUnattended;
        ui.table_widget_left->addRow(modelNameTableInfo, true);
        setToolButtonUnattendState(true, !modelNameTableInfo.isUnattended);
        ui.table_widget_left->scrollToTop();
        //更新排序
        slotLeftTableSortOccurs();
    }
    else //更新左侧列表
    {
        ui.table_widget_left->updateTemplateName(QString::number(templateId), newSketchCollection.templateName);
    }
}

/// <summary>
/// 保存为模板
/// </summary>
void AutoSketchTemplateWidget::onMtPushButton_saveRoiSelect()
{
    onMtmtPushButton_saveas();
}

void AutoSketchTemplateWidget::SlotAddOneNewTemplate(int newTemplateId, const QString& templateName, bool isUnattended)
{
    //获取当前选中的模板
    QString rowValue = ui.table_widget_left->getCurSelectRow();
    //左侧栏插入一列
    ModelNameTableInfo modelNameTableInfo;
    modelNameTableInfo.templateId = newTemplateId;
    modelNameTableInfo.templateName = templateName;
    modelNameTableInfo.isUnattended = false;;
    ui.table_widget_left->addRow(modelNameTableInfo, false);
    ui.table_widget_left->scrollToTop(); //不知道为啥，如果有滚动条出现，不移动到最下面，此时另存为再点击保存会崩溃在delRow(const QString& rowValue)中
    //选中之前选中的模板
    ui.table_widget_left->SetCurrentRow(rowValue);
}

/// <summary>
/// 鼠标右键菜单
/// </summary>
void AutoSketchTemplateWidget::slotRightMenu()
{
    QAction* action = (QAction*)sender();
    QString actionText = action->text();

    if (tr("编辑模板") == actionText)
    {
        onMtToolButton_edit();
    }
    else if (tr("复制模板") == actionText)
    {
        onMtToolButton_copy();
    }
    else if (tr("删除模板") == actionText)
    {
        onMtToolButton_del();
    }
    else if (tr("设置为无人值守模板") == actionText)
    {
        onMtToolButton_unattend();
    }
    else if (tr("设置为常规模板") == actionText)
    {
        onMtToolButton_unattend();
    }
    else if (tr("置顶") == actionText)
    {
        onMtToolButton_toTop();
    }
}

/// <summary>
/// 左侧列表item选中
/// </summary>
void AutoSketchTemplateWidget::slotLeftTableItemSelect(const QString rowValue, Qt::MouseButton button, QPoint point)
{
    //防止重复点击左键
    if (button == Qt::LeftButton && ui.widget_roiListWidget->getUniqueKey() == rowValue.toInt())
        return;

    //左侧按钮操作栏无人值守按钮变化
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    n_mtautodelineationdialog::ST_SketchModelCollection sketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());
    setToolButtonUnattendState(true, !sketchCollection.isUnattended);
    //进入展示模式
    setRightStackWidgetShow(true, false);
    updateRightWidgetNoEdit(rowValue.toInt());
    bool isTop = ui.table_widget_left->isTopItem(rowValue);

    if (false && button == Qt::RightButton)//去掉右键菜单功能，暂时不删代码以免又改回需求
    {
        updateRightMenu(!sketchCollection.isUnattended, !isTop);
        m_menu->popup(this->mapToGlobal(point));
    }

    //判断左侧列表是否是顶部item
    ui.mtToolButton_top->setHidden(isTop);
}

/// <summary>
/// 左侧列表发生排序
/// </summary>
void AutoSketchTemplateWidget::slotLeftTableSortOccurs()
{
    QList<int> templateIdSortList = ui.table_widget_left->getCurTemplateIdSortList();
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    m_ptrOptSketchCollection->updateAllTemplateIsSort(dcmTypeEnum, templateIdSortList);
}

/// <summary>
/// 响应OptSketchCollection拖拽使能信号
/// </summary>
/// <param name="enable">true使能</param>
void AutoSketchTemplateWidget::slotTableNameDropEnable(const bool enable)
{
    ui.table_widget_left->setIsDropEnable(enable);
}

/// <summary>
/// item点击(itemId-器官id),来源于GroupItemListWidget
/// </summary>
/// <param name="groupId">[IN]器官分组id</param>
/// <param name="itemIdSet">[IN]器官organId集合(key-organId)</param>
/// <param name="checked">true使能</param>
void AutoSketchTemplateWidget::slotOneItemCheckFromGroupItemListWidget(const int groupId, const QSet<int> itemIdSet, const bool checked)
{
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin();

    if (m_cacheOrganByGroupIdMap.contains(groupId) == true)
    {
        QList<n_mtautodelineationdialog::ST_Organ>& organList = m_cacheOrganByGroupIdMap[groupId];

        for (int i = 0; i < organList.size(); i++)
        {
            if (itemIdSet.contains(organList[i].id) == true)
            {
                organList[i].isVisiable = checked;
            }
        }
    }
}

/// <summary>
/// 是否全部展开/收起
/// </summary>
/// <param name="pageTypeEnum">[IN]页面类型</param>
/// <param name="isExpand">[IN]是否全部展开</param>
void AutoSketchTemplateWidget::slotAllItemExpandFromGroupItemListWidget(const GroupHTitle::EM_PageType pageTypeEnum, const bool isExpand)
{
    ui.mtPushButton_expand->setWhatsThis(isExpand == true ? Def_TreeExpand : Def_TreeClose);
    ui.mtPushButton_expand->setText(isExpand == true ? tr("全部收起") : tr("全部展开"));
}

/// <summary>
/// 输入是否正常
/// </summary>
/// <returns>true正常</returns>
bool AutoSketchTemplateWidget::isInputNormal()
{
    //编辑模式下，检查模板输入
    if (ui.mtStackedWidget->currentWidget() == ui.page_modelEdit)
    {
        int templateId = ui.widget_roiListWidget->getUniqueKey();

        if (ui.lineEdit_templateName->text().isEmpty())
        {
            ui.lineEdit_templateName->setWarningBorderStatus(tr("未填写模板名称"));
            return false;
        }

        if (m_ptrOptSketchCollection->isExistTemplateName(ui.lineEdit_templateName->text(), templateId) == true)
        {
            ui.lineEdit_templateName->setWarningBorderStatus(tr("模板名称已存在"));
            return false;
        }
    }

    return true;
}

/// <summary>
/// 是否存在该模板id
/// </summary>
/// <param name="rowValue">[IN]模板id</param>
/// <returns>true存在</returns>
bool AutoSketchTemplateWidget::isExistTemplateId(const QString& rowValue)
{
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);

    if (rowValue.isEmpty() == true || m_ptrOptSketchCollection->isExistTemplateId(dcmTypeEnum, rowValue.toInt()) == false)
    {
        return false;
    }

    return true;
}

/// <summary>
/// 是否可操作模板
/// </summary>
/// <param name="optTypeEnum">[IN]操作类型</param>
/// <param name="templateId">[IN]模板id</param>
/// <returns>true可操作</returns>
bool AutoSketchTemplateWidget::isCanOptTemplate(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const int templateId)
{
    if (m_stCallBackAutoSketch.canOptSketchCollectionCallBack != nullptr)
    {
        QString outMsg, rightBtnText;
        int dlgType = m_stCallBackAutoSketch.canOptSketchCollectionCallBack(optTypeEnum, templateId, outMsg, rightBtnText);

        if (dlgType == 1) //蓝色提示
        {
            MtMessageBox::NoIcon::information_Title(this->window(), outMsg);
            return false;
        }
        else if (dlgType == 2) //蓝色选择
        {
            /*int ret = MtMessageBox::NoIcon::question_Title(this, outMsg, QString());

            if (QMessageBox::Yes != ret)
                return false;*/
            if (m_ptrOptSketchCollection->getIsShowTipOfModUnattendUsed() == true)
            {
                ModUnattendUsedDialog dlg(outMsg, false, this);

                if (dlg.exec() == QDialog::Accepted)
                {
                    m_ptrOptSketchCollection->setIsShowTipOfModUnattendUsed(!dlg.getIsChecked());
                    return true;
                }

                return false;
            }

            return true;
        }
        else if (dlgType == 3) //红色警告选择
        {
            int ret = MtMessageBox::redWarning(this, outMsg, QString(), rightBtnText);

            if (QMessageBox::Yes != ret)
                return false;
        }
    }

    return true;
}

/// <summary>
/// 更新右键菜单
/// </summary>
/// <param name="isUnatanded">[IN设置为无人值守</param>
/// <param name="isUnatanded">[IN]显示置顶选项</param>
void AutoSketchTemplateWidget::updateRightMenu(const bool setUnatanded, const bool showTop)
{
    m_menu->clear();
    m_menu->addAction(tr("编辑模板"), this, SLOT(slotRightMenu()));
    m_menu->addAction(tr("复制模板"), this, SLOT(slotRightMenu()));
    m_menu->addAction(tr("删除模板"), this, SLOT(slotRightMenu()));

    if (ui.mtToolButton_unattend->isEnabled())
        m_menu->addAction(setUnatanded == true ? tr("设置为无人值守模板") : tr("设置为常规模板"), this, SLOT(slotRightMenu()));

    if (showTop == true)
        m_menu->addAction(tr("置顶"), this, SLOT(slotRightMenu()));
}

/// <summary>
/// 设置左侧栏使能
/// </summary>
/// <param name="isEnable">[IN]true使能</param>
void AutoSketchTemplateWidget::setWidgetLeftEnable(const bool isEnable)
{
    ui.frame_left->setEnabled(isEnable);
}

/// <summary>
/// 设置无人值守模板按钮状态
/// </summary>
/// <param name="isShow">[IN]是否显示</param>
/// <param name="isAddToUnattend">[IN]是否是切换成*加入无人值守按钮</param>
void AutoSketchTemplateWidget::setToolButtonUnattendState(const bool isShow, const bool isAddToUnattend)
{
    if (ui.mtToolButton_unattend->isEnabled() == false)
        return;

    if (isShow == false)
    {
        ui.mtToolButton_unattend->setHidden(true);
        return;
    }

    ui.mtToolButton_unattend->setHidden(false);
    ui.mtToolButton_unattend->setToolTipText(isAddToUnattend == true ? tr("设置为无人值守模板") : tr("设置为常规模板"));
    ui.mtToolButton_unattend->setPixmapFilename(isAddToUnattend == true ? m_imagePathHash["icon_unattended"] : m_imagePathHash["icon_remove_unattended"]);
}

/// <summary>
/// 删除左侧列表item
/// </summary>
/// <param name="rowValue">[IN]模板id</param>
void AutoSketchTemplateWidget::delLeftTableRow(const QString rowValue)
{
    ui.table_widget_left->delRow(rowValue);
    setToolButtonUnattendState(false, false);
    slotLeftTableSortOccurs();
}

/// <summary>
/// 重新初始化左侧列表item
/// </summary>
void AutoSketchTemplateWidget::reInitLeftTableRow()
{
    struct timespec tmv1, tmv2;
    timespec_get(&tmv1, 1);
    ui.mtToolButton_top->setHidden(true);
    ui.table_widget_left->delAllRow();
    //获取排序后的模板队列信息并挂载，其实两个页签都是完整的同一个队列
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR;
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> stSketchCollectionList = m_ptrOptSketchCollection->getSortSketchCollectionList(dcmTypeEnum);

    for (int i = 0; i < stSketchCollectionList.size(); i++)
    {
        n_mtautodelineationdialog::ST_SketchModelCollection stSketchCollection = stSketchCollectionList[i];
        ModelNameTableInfo info;
        info.templateId = stSketchCollection.id;
        info.templateName = stSketchCollection.templateName;
        info.isUnattended = stSketchCollection.isUnattended;
        ui.table_widget_left->addRow(info);
    }

    timespec_get(&tmv2, 1);
    printf("reInitLeftTableRow time: %lld\n", tmv2.tv_sec - tmv1.tv_sec);
}

/// <summary>
/// 清空右侧界面
/// </summary>
void AutoSketchTemplateWidget::clearRightWidget(bool stackedWidgetHidden, bool isReConnect)
{
    ui.lineEdit_templateName->clear();
    ui.mtStackedWidget->setHidden(stackedWidgetHidden);
    ui.stackedWidget_save->setHidden(stackedWidgetHidden);
    ui.widget_roiListWidget->clearAllItem();

    //
    if (isReConnect == true)
    {
        disconnect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);
        disconnect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);
    }

    ui.mtLineEdit_roiSearch->clear();
    ui.mtLineEdit_roiSearch_RoiSelect->clear();

    if (isReConnect == true)
    {
        connect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);
        connect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);
    }
}

/// <summary>
/// 获取要展示的器官集合
/// </summary>
/// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
/// <param name="dcmTypeEnum">[IN]DICOM类型</param>
/// <returns>要展示的器官集合(key-mainGroupId 亚器官已经被映射到主分组)</returns>
QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> AutoSketchTemplateWidget::getOrganToShow(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection,
                                                                                               n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum)
{
    //全部分组器官信息
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = m_ptrOptSketchCollection->getOrganByGroupIdMap(dcmTypeEnum);
    //将亚组映射到主分组中
    QSet<int> oldMainGroupIdInTemplateSet; //模板中的原始主分组集合
    //亚组映射到主分组中
    QSet<int> newGroupInTemplateSet = m_ptrOptSketchCollection->changeSubGroupIdToMainGroupId(curSketchCollection.groupIdSet, oldMainGroupIdInTemplateSet);
    //获取内存中亚分组和主分组关系
    QMap<int/*亚结构groupId*/, QSet<int/*主结构groupId*/>> mainGroupIdBySubGroupIdMap = m_ptrOptSketchCollection->getMainGroupIdBySubGroupIdMap();
    QSet<int/*亚结构organId*/> subOrganNotFindMainSet; //存储未找到主分组的亚器官，挂载到相关联的第一个主分组上
    //QSet<int/*亚结构organId*/> subOrganNormalFindMainSet; //存储正常找到主分组的亚器官

    //更新勾选状态
    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = tempOrganByGroupIdMap.begin(); it != tempOrganByGroupIdMap.end(); it++)
    {
        int mainGroupId = it.key(); //此时亚分组已经被映射到了主分组

        //不在模板里的分组,直接跳过保持未勾选状态
        //if (newGroupInTemplateSet.contains(mainGroupId) == false)
        //    continue;

        //勾选模板里有的器官
        for (int i = 0; i < it.value().size(); i++)
        {
            int organId = it.value()[i].id;
            QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo> organGroupInfoMap = it.value()[i].organGroupInfoMap; //实际器官中的分组情况

            //没归组的器官忽略
            if (organGroupInfoMap.isEmpty() == true)
                continue;

            //没在模板里的器官忽略
            if (curSketchCollection.showGroupIdMap.contains(organId) == false)
                continue;

            //当前模板内器官对应的分组id
            QSet<int> curTempalteGroupIdSet = curSketchCollection.showGroupIdMap[organId];

            //判断是否是亚组器官,因为亚结构只可能有一个分组
            if (mainGroupIdBySubGroupIdMap.contains(organGroupInfoMap.begin().key()) == true) //亚组器官
            {
                int subGroupId = organGroupInfoMap.begin().key(); //亚组id
                QSet<int> mainGroupIdBySubGroupIdSet = mainGroupIdBySubGroupIdMap[subGroupId];//实际器官中的亚组对应的主分组情况

                //已经挂载到涉及到该亚组的主分组第一个的忽略
                if (subOrganNotFindMainSet.contains(organId) == true)
                {
                    continue;
                }

                //查看是否在当前的主分组下显示亚分组
                if (curSketchCollection.subInMainGroupIdMap.contains(organId) == true)
                {
                    if (curSketchCollection.subInMainGroupIdMap[organId].contains(mainGroupId) == true)
                    {
                        it.value()[i].isVisiable = true;
                        subOrganNotFindMainSet.remove(organId);
                        continue;
                    }
                    else
                    {
                        //判断subInMainGroupIdMap是否是实际的子集,这样就一定会找到亚器官的挂载点
                        QSet<int> remainSet = curSketchCollection.subInMainGroupIdMap[organId] - mainGroupIdBySubGroupIdSet;

                        if (remainSet.size() < curSketchCollection.subInMainGroupIdMap[organId].size())
                        {
                            continue;
                        }
                    }
                }

                //模板中subInMainGroupIdMap没找到设置这个map就是空的时候(老版本升级上来的)
                //该模板里的原主分组有涉及到该亚器官对应的主器官分组
                if (oldMainGroupIdInTemplateSet.contains(mainGroupId) == true)
                {
                    it.value()[i].isVisiable = true;
                    subOrganNotFindMainSet.insert(organId);
                    continue;
                }
                else
                {
                    //根本没有主分组时，直接把当前的主分组加上去
                    if (oldMainGroupIdInTemplateSet.isEmpty() == true)
                    {
                        it.value()[i].isVisiable = true;
                        subOrganNotFindMainSet.insert(organId);
                        oldMainGroupIdInTemplateSet.insert(mainGroupId); //加上一个分组
                        continue;
                    }

                    //判断该模板里的原主分组有涉及到该亚器官对应的主器官分组
                    QSet<int> remainSet = oldMainGroupIdInTemplateSet - mainGroupIdBySubGroupIdSet;

                    //该模板里的原主分组有涉及到该亚器官对应的主器官分组
                    if (remainSet.size() < oldMainGroupIdInTemplateSet.size())
                    {
                        continue;
                    }
                    else //该模板里的原主分组没有有涉及到该亚器官对应的主器官分组
                    {
                        it.value()[i].isVisiable = true;
                        subOrganNotFindMainSet.insert(organId);
                        oldMainGroupIdInTemplateSet.insert(mainGroupId); //加上一个分组
                    }
                }
            }
            else //主分组器官
            {
                //查看是否在主分组里
                if (curTempalteGroupIdSet.contains(mainGroupId) == true)
                {
                    it.value()[i].isVisiable = true;
                    continue;
                }
            }
        }
    }

    return tempOrganByGroupIdMap;
}

/// <summary>
/// 当前页面是否选中了一个模型
/// </summary>
/// <returns>true</returns>
bool AutoSketchTemplateWidget::isSelectOneCollection()
{
    QString rowValue = ui.table_widget_left->getCurSelectRow();
    return !rowValue.isEmpty();
}

/// <summary>
/// 获取选中的模板
/// 如果是选择模板进行勾画页签-编辑模式下直接点击确定，则返回一个id为 -99 的临时模板
/// 如果是选择ROI进行勾画页签-编辑模式下直接点击确定， 则返回一个id为 -98 的临时模板
/// </summary>
/// <returns>选中的模板</returns>
n_mtautodelineationdialog::ST_SketchModelCollection AutoSketchTemplateWidget::getSelectSketchCollection()
{
    auto setSketchModelCollection = [this](n_mtautodelineationdialog::ST_SketchModelCollection& sketchCollection)->void
    {
        //整理选中的器官
        for (QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = m_cacheOrganByGroupIdMap.begin(); it != m_cacheOrganByGroupIdMap.end(); it++)
        {
            int groupId = it.key(); //亚组已经被映射到主分组
            QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();
            int mianOrganNum = 0;

            for (int i = 0; i < organList.size(); i++)
            {
                n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

                if (stOrgan.isVisiable == false)
                {
                    continue;
                }

                int organId = stOrgan.id;

                //如果是亚结构器官的情况下，需把主分组id重新设置为亚分组
                if (stOrgan.organGroupInfoMap.size() == 1 && stOrgan.organGroupInfoMap.begin().value().type == 3)
                {
                    int subGroupId = stOrgan.organGroupInfoMap.begin().value().id;
                    sketchCollection.showGroupIdMap[organId].insert(subGroupId);
                    sketchCollection.groupIdSet.insert(subGroupId);
                    sketchCollection.subInMainGroupIdMap[organId].insert(groupId); //亚组在哪些主分组下显示
                    continue;
                }

                //主结构器官
                sketchCollection.showGroupIdMap[organId].insert(groupId);
                mianOrganNum++;
            }

            if (mianOrganNum > 0)
                sketchCollection.groupIdSet.insert(groupId);
        }
    };

    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection;

    //模板设置界面模式
    if (m_showSelectRadioSelectBtn == false)
    {
        QString rowValue = ui.table_widget_left->getCurSelectRow();
        n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
        curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());
    }
    else if (ui.frame_left->isEnabled() == true) //非编辑模式
    {
        //自动勾画界面模式
        //如果是-选择模板进行勾画页签-选中
        QString rowValue = ui.table_widget_left->getCurSelectRow();
        n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
        curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, rowValue.toInt());
    }
    else
    {
        //非编辑模式
        curSketchCollection.id = Def_TempIdOfSelectTemplate;
        curSketchCollection.isUnattended = false;
        curSketchCollection.templateName = "Not-Template-From-SelectTemplate";
        curSketchCollection.modality = ui.mtComboBox->currentText();
        setSketchModelCollection(curSketchCollection);
    }

    //移除未勾选的器官
    QMap<int, QSet<int>> checkItemSet = ui.widget_roiListWidget->getCheckedItemMap();
    QSet<int> organIdSet;

    for (QMap<int, QSet<int>>::iterator it = checkItemSet.begin(); it != checkItemSet.end(); it++)
    {
        organIdSet.unite(it.value());
    }

    for (QMap<int, QSet<int>>::iterator it = curSketchCollection.showGroupIdMap.begin(); it != curSketchCollection.showGroupIdMap.end(); it++)
    {
        int organId = it.key();
        QSet<int> groupIdSet = it.value();

        if (organIdSet.contains(organId) == false)
        {
            curSketchCollection.showGroupIdMap.remove(organId);
        }
    }

    return curSketchCollection;
}

/// <summary>
/// 获取最新的虚拟模板
/// \n用于*选择ROI进行勾画*页签进行器官提前选中
/// </summary>
/// <returns>最新的虚拟模板</returns>
n_mtautodelineationdialog::ST_SketchModelCollection AutoSketchTemplateWidget::getVirtualSketchCollection()
{
    return m_stVirtualSketchTemplate;
}

/// <summary>
/// 获取选中的页签类型
/// </summary>
/// <returns>页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</returns>
int AutoSketchTemplateWidget::getSelectRadioPageType()
{
    return 2;
}

/// <summary>
/// 是否处于编辑状态
/// </summary>
/// <returns>true是</returns>
bool AutoSketchTemplateWidget::isEditState()
{
    if (ui.mtStackedWidget->isHidden() == false && ui.mtStackedWidget->currentWidget() == ui.page_modelEdit)
    {
        return true;
    }

    return false;
}

/// <summary>
/// 添加数据到widget_roiListWidget控件
/// </summary>
/// <param name="isEdit">[IN]是否是编辑模式</param>
/// <param name="organByGroupIdMap">[IN]器官按照groupId分组(key-groupId)</param>
/// <param name="allGroupInfoList">[IN]排序后的所有分组信息</param>
/// <param name="subOrganTypeMap">[OUT]亚结构的信息(key-亚结构organId value-1:找到主结构 2:未找到主结构)</param>
void AutoSketchTemplateWidget::addDataToRightRoiWidget(const bool isEdit, const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap,
                                                       const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList, const QMap<int, int>& subOrganTypeMap)
{
    for (int i = 0; i < allGroupInfoList.size(); i++)
    {
        int groupId = allGroupInfoList[i].id;

        if (organByGroupIdMap.contains(groupId) == true)
        {
            GroupHTitleData titleData;
            titleData.groupId = groupId;
            titleData.value = allGroupInfoList[i].name;
            ui.widget_roiListWidget->addTitle(isEdit == true ? GroupHTitle::Page_Check : GroupHTitle::Page_Label, titleData);
            //平分成10组
            QList<n_mtautodelineationdialog::ST_Organ> organList = organByGroupIdMap[groupId];
            int totalSize = organList.size();
            int startIndex = 0;

            while (startIndex < totalSize)
            {
                QList<GroupItemData> dataList;
                dataList.reserve(Def_EveryItemCount);

                for (int i = startIndex; i < startIndex + Def_EveryItemCount && i < totalSize; ++i)
                {
                    GroupItemData data;
                    data.groupId = groupId;
                    data.valId = organList[i].id;
                    data.isCheck = organList[i].isVisiable;
                    data.value1 = organList[i].customOrganName;
                    data.value2 = organList[i].organChineseName;
                    data.value3 = organList[i].roiDesc;

                    //设置亚结构缩进
                    if (subOrganTypeMap.contains(data.valId) == true)
                    {
                        data.organType = subOrganTypeMap[data.valId];
                    }

                    dataList.push_back(data);
                }

                ui.widget_roiListWidget->addItems(isEdit == true ? GroupItem::Page_CheckLabel : GroupItem::Page_LabelLabel, dataList, m_roiItemWidth);
                startIndex += Def_EveryItemCount;
            }
        }
    }

    //刷新一下标题勾选状态
    ui.widget_roiListWidget->flushAllTitleCheckStatus();
    //滚动到最上面
    ui.widget_roiListWidget->scrollTop(true);
}

/// <summary>
/// 更新右侧界面-不可编辑状态
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void AutoSketchTemplateWidget::updateRightWidgetNoEdit(const int templateId)
{
    //左侧使能
    setWidgetLeftEnable(true);
    //清空并重新初始化
    clearRightWidget();
    ui.widget_roiListWidget->setUniqueKey(templateId);
    //获取当前操作的模板
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, templateId);

    if (m_ptrOptSketchCollection->isExistTemplateId(dcmTypeEnum, templateId) == false)
        return;

    //切换编辑框
    setRightStackWidgetShow(true, false);
    ui.mtLabel_templateName->setText(curSketchCollection.templateName);
    //实际分组器官信息
    QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap;
    //全部分组器官信息
    QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = getOrganToShow(curSketchCollection, dcmTypeEnum);

    for (QMap<int/*mainGroupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = tempOrganByGroupIdMap.begin(); it != tempOrganByGroupIdMap.end(); it++)
    {
        int mainGroupId = it.key(); //此时亚分组已经被映射到了主分组
        QList<n_mtautodelineationdialog::ST_Organ> organList = it.value();

        for (int i = 0; i < organList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = organList[i];

            if (stOrgan.isVisiable == true)
            {
                newOrganByGroupIdMap[mainGroupId].push_back(stOrgan);
            }
        }
    }

    //填充数据
    newOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow_NoSortSub(newOrganByGroupIdMap);
    addDataToRightRoiWidget(true, newOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), QMap<int, int>());
}

/// <summary>
/// 更新右侧界面-编辑状态
/// </summary>
/// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
void AutoSketchTemplateWidget::updateRightWidgetEdit(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection)
{
    //左侧使能
    setWidgetLeftEnable(false);
    //清空并重新初始化
    clearRightWidget();
    ui.widget_roiListWidget->setUniqueKey(curSketchCollection.id);
    //切换编辑框
    setRightStackWidgetShow(true, true);
    ui.lineEdit_templateName->setText(curSketchCollection.templateName);
    //全部分组器官信息
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    //全部分组器官信息
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = getOrganToShow(curSketchCollection, dcmTypeEnum);
    //填充数据
    m_cacheOrganByGroupIdMap = tempOrganByGroupIdMap;
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    tempOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(tempOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, tempOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 更新右侧界面-编辑状态
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void AutoSketchTemplateWidget::updateRightWidgetEdit(const int templateId)
{
    //当前操作的模板
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection = m_ptrOptSketchCollection->getSketchColleciton(dcmTypeEnum, templateId);
    updateRightWidgetEdit(curSketchCollection);
}

/// <summary>
/// 更新右侧界面-选择ROI进行自动勾画页签
/// </summary>
/// <param name="curSketchCollection">[IN]当前选中的模板信息</param>
void AutoSketchTemplateWidget::updateRightWidgetEdit_RadioSelectRoi(n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection)
{
    //清空并重新初始化
    clearRightWidget();
    ui.widget_roiListWidget->setUniqueKey(Def_TempIdOfSelectRoi);
    //切换编辑框
    setRightStackWidgetShow(false, true);
    ui.lineEdit_templateName->setText(curSketchCollection.templateName);
    //全部分组器官信息
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum = (ui.mtComboBox->currentText() == "CT" ? n_mtautodelineationdialog::OptDcmType_CT : n_mtautodelineationdialog::OptDcmType_MR);
    //全部分组器官信息
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> tempOrganByGroupIdMap = getOrganToShow(curSketchCollection, dcmTypeEnum);
    //填充数据
    m_cacheOrganByGroupIdMap = tempOrganByGroupIdMap;
    //整理亚结构挂载关系
    QMap<int/*亚结构organId*/, int/*1:找到主结构 2:未找到主结构*/> subOrganTypeMap;
    tempOrganByGroupIdMap = m_ptrOptSketchCollection->sortOrganByGroupIdMapToShow(tempOrganByGroupIdMap, subOrganTypeMap);
    addDataToRightRoiWidget(true, tempOrganByGroupIdMap, m_ptrOptSketchCollection->getAllMainGroupInfoList(), subOrganTypeMap);
}

/// <summary>
/// 更新右侧界面-选择ROI进行自动勾画页签
/// </summary>
void AutoSketchTemplateWidget::updateRightWidgetEdit_RadioSelectRoi()
{
    updateRightWidgetEdit_RadioSelectRoi(m_stVirtualSketchTemplate.showGroupIdMap.isEmpty() == false ? m_stVirtualSketchTemplate : n_mtautodelineationdialog::ST_SketchModelCollection());
    //全部收起
    ui.mtPushButton_expand_RoiSelect->setWhatsThis(Def_TreeClose);
    ui.mtPushButton_expand_RoiSelect->setText(tr("全部展开"));
    ui.widget_roiListWidget->expandTreeAll(GroupHTitle::Page_Check, false);
}

/// <summary>
/// 设置右侧上下StackWidget展示模式
/// </summary>
/// <param name="isRadioTemplateSelect">[IN]是否是选择模板进行勾画Radio选中</param>
/// <param name="isEdit">[IN]是否处于编辑状态</param>
void AutoSketchTemplateWidget::setRightStackWidgetShow(const bool isRadioTemplateSelect, const bool isEdit)
{
    if (isRadioTemplateSelect == false)
    {
        ui.mtStackedWidget->setHidden(false);
        ui.stackedWidget_save->setHidden(false);
        ui.widget_rightBottom->setHidden(false);
        ui.mtStackedWidget->setCurrentWidget(ui.page_roiSelect);
        ui.stackedWidget_save->setCurrentWidget(ui.page_edit2);
        return;
    }

    if (isEdit == true)
    {
        ui.mtStackedWidget->setHidden(false);
        ui.stackedWidget_save->setHidden(false);
        ui.widget_rightBottom->setHidden(false);
        ui.mtStackedWidget->setCurrentWidget(ui.page_modelEdit);
        ui.stackedWidget_save->setCurrentWidget(ui.page_edit1);
        return;
    }

    ui.mtStackedWidget->setHidden(false);
    ui.stackedWidget_save->setHidden(false);
    ui.widget_rightBottom->setHidden(false);
    ui.stackedWidget_save->setCurrentWidget(ui.page_modelShowBottom);
    ui.mtStackedWidget->setCurrentWidget(ui.page_modelShow);
}

/// <summary>
/// 信号槽
/// </summary>
void AutoSketchTemplateWidget::connnectSignal(const bool isConnect)
{
    if (isConnect == true)
    {
        connect(ui.mtPushButton_expand, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_allTreeOpt);     //展开按钮
        connect(ui.mtPushButton_expand_RoiSelect, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_allTreeOpt);//展开按钮
        connect(ui.mtPushButton_cancelSelect_RoiSelect, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_cleanAllCheck);//悬浮工具-清空选中按钮
        connect(ui.mtComboBox, &QComboBox::currentTextChanged, this, &AutoSketchTemplateWidget::onMtComboBox);                  //模态下拉
        connect(ui.mtLineEdit_search, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged);       //搜索框文本变化
        connect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        connect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        connect(ui.mtToolButton_add, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_add);               //新增按钮
        connect(ui.mtToolButton_edit, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_edit);             //编辑按钮
        connect(ui.mtToolButton_copy, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_copy);             //拷贝按钮
        connect(ui.mtToolButton_del, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_del);               //删除按钮
        connect(ui.mtToolButton_unattend, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_unattend);     //无人值守按钮
        connect(ui.mtToolButton_top, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_toTop);             //置顶按钮
        connect(ui.mtToolButton_eclipse, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_eclipse);       //导入eclipse模板按钮
        connect(ui.table_widget_left, &ModelNameTable::sigItemSelect, this, &AutoSketchTemplateWidget::slotLeftTableItemSelect);//左侧列表点击
        connect(ui.table_widget_left, &ModelNameTable::sigSortOccurs, this, &AutoSketchTemplateWidget::slotLeftTableSortOccurs);//左侧列表发生排序
        connect(ui.mtPushButton_edit, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMPushButton_edit);              //编辑按钮
        connect(ui.mtPushButton_cancelEdit, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMPushButton_cancelEdit);  //取消编辑按钮
        connect(ui.mtPushButton_saveas, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtmtPushButton_saveas);       //另存按钮
        connect(ui.mtPushButton_saveEdit, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtmtPushButton_saveEdit);   //保存按钮
        connect(ui.mtPushButton_saveRoiSelect, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtPushButton_saveRoiSelect); //保存为模板按钮
        connect(ui.widget_roiListWidget, &GroupItemListWidget::sigOneItemCheckFromGroupItemListWidget, this, &AutoSketchTemplateWidget::slotOneItemCheckFromGroupItemListWidget); //GroupItemListWidget-item点击
        connect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchTemplateWidget::slotAllItemExpandFromGroupItemListWidget); //GroupItemListWidget-是否全部展开/收起
        connect(m_ptrOptSketchCollection, &OptSketchCollection::sigTableNameDropEnable, this, &AutoSketchTemplateWidget::slotTableNameDropEnable); //左侧列表拖拽使能
        connect(this, &AutoSketchTemplateWidget::SigAddOneNewTemplate, this, &AutoSketchTemplateWidget::SlotAddOneNewTemplate);
    }
    else
    {
        disconnect(ui.mtPushButton_expand, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_allTreeOpt);     //展开按钮
        disconnect(ui.mtPushButton_expand_RoiSelect, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_allTreeOpt);//展开按钮
        disconnect(ui.mtPushButton_cancelSelect_RoiSelect, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_cleanAllCheck);//悬浮工具-清空选中按钮
        disconnect(ui.mtComboBox, &QComboBox::currentTextChanged, this, &AutoSketchTemplateWidget::onMtComboBox);                  //模态下拉
        disconnect(ui.mtLineEdit_search, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged);       //搜索框文本变化
        disconnect(ui.mtLineEdit_roiSearch, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        disconnect(ui.mtLineEdit_roiSearch_RoiSelect, &QLineEdit::textChanged, this, &AutoSketchTemplateWidget::onMtLineEditTextChanged_ROI);//右侧roi搜索框文本变化
        disconnect(ui.mtToolButton_add, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_add);               //新增按钮
        disconnect(ui.mtToolButton_edit, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_edit);             //编辑按钮
        disconnect(ui.mtToolButton_copy, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_copy);             //拷贝按钮
        disconnect(ui.mtToolButton_del, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_del);               //删除按钮
        disconnect(ui.mtToolButton_unattend, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_unattend);     //无人值守按钮
        disconnect(ui.mtToolButton_top, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_toTop);             //置顶按钮
        disconnect(ui.mtToolButton_eclipse, &QToolButton::clicked, this, &AutoSketchTemplateWidget::onMtToolButton_eclipse);       //导入eclipse模板按钮
        disconnect(ui.table_widget_left, &ModelNameTable::sigItemSelect, this, &AutoSketchTemplateWidget::slotLeftTableItemSelect);//左侧列表点击
        disconnect(ui.table_widget_left, &ModelNameTable::sigSortOccurs, this, &AutoSketchTemplateWidget::slotLeftTableSortOccurs);//左侧列表发生排序
        disconnect(ui.mtPushButton_edit, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMPushButton_edit);              //编辑按钮
        disconnect(ui.mtPushButton_cancelEdit, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMPushButton_cancelEdit);  //取消编辑按钮
        disconnect(ui.mtPushButton_saveas, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtmtPushButton_saveas);       //另存按钮
        disconnect(ui.mtPushButton_saveEdit, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtmtPushButton_saveEdit);   //保存按钮
        disconnect(ui.mtPushButton_saveRoiSelect, &QPushButton::clicked, this, &AutoSketchTemplateWidget::onMtPushButton_saveRoiSelect); //保存为模板按钮
        disconnect(ui.widget_roiListWidget, &GroupItemListWidget::sigOneItemCheckFromGroupItemListWidget, this, &AutoSketchTemplateWidget::slotOneItemCheckFromGroupItemListWidget); //GroupItemListWidget-item点击
        disconnect(ui.widget_roiListWidget, &GroupItemListWidget::sigAllItemExpandFromGroupItemListWidget, this, &AutoSketchTemplateWidget::slotAllItemExpandFromGroupItemListWidget); //GroupItemListWidget-是否全部展开/收起
        disconnect(m_ptrOptSketchCollection, &OptSketchCollection::sigTableNameDropEnable, this, &AutoSketchTemplateWidget::slotTableNameDropEnable); //左侧列表拖拽使能
    }
}
