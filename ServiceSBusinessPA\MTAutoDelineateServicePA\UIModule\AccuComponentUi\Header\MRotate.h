﻿#pragma once

#include <QLabel>
#include <QString>
//#include "manteia_export_global.h"


/*
旋转显示某个图片
应用场景：
1.无人值守
*/
class  MRotate : public QLabel
{
    Q_OBJECT

public:
    MRotate(QWidget* parent = nullptr, QString url = ":/AccuUIComponentImage/images/unattended_whirl.png", int interval = 10, bool clockwise = false); //
    ~MRotate();

    void setUrl(QString url);
    void setInterval(int interval);
    void setClockwise(bool clockwise)
    {
        this->_Clockwise = clockwise;
    }
    QString Url()
    {
        return this->_Url;
    };
    int Interval()
    {
        return this->_Interval;
    };
    bool Clockwise()
    {
        return this->_Clockwise;
    };
protected:
    virtual void paintEvent(QPaintEvent* event) override;
    virtual void timerEvent(QTimerEvent* event) override;
    int timerId;// 定时器Id
    QString _Url;// 图像url
    int _Interval;// 定时器间隔
    int rotate;// 记录旋转角度
    bool _Clockwise;    // true:顺时针转
};
