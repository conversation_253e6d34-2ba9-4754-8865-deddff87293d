﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTAbsComboBox.h"
#include <QHelpEvent>
#include <QToolTip>
#include <QPainter>
#include <QTextLayout>
#include <QTextDocument>
#include <QTextBlock>
#include <QDebug>

QMTAbsComboBoxParam::QMTAbsComboBoxParam()
{
    _cellWidgetType = DELEAGATE_QMTAbsComboBox;
}

QWidget* QMTAbsComboBoxParam::CreateUIModule(QWidget* parent)
{
    QMTAbsComboBox* comboBox = new QMTAbsComboBox(parent);

    if (_mtType > 0)
    {
        comboBox->setMtType((MtComboBox::MtType)_mtType);
    }

    comboBox->addItems(_textList);

    if (_text.isEmpty())
    {
        comboBox->setCurrentIndex(-1);
    }
    else
    {
        QStringList itemStrList = comboBox->GetAllItemStrList();

        if (itemStrList.indexOf(_text) < 0)
        {
            comboBox->setCurrentIndex(-1);
        }
        else
        {
            comboBox->setCurrentText(_text);
        }
    }

    return comboBox;
}


/*******************************************************/
QMTAbsComboBox::QMTAbsComboBox(QWidget* parent)
    : MtComboBox(parent)
{
    this->setMtType(MtComboBox::combobox1);
#if 0
    _listWidget = new QListWidget(this);
    _originModel = this->model();
    _originView = this->view();
    this->setModel(_listWidget->model());
    this->setView(_listWidget);
    this->view()->window()->setWindowFlags(Qt::Popup | Qt::FramelessWindowHint | Qt::NoDropShadowWindowHint);
    this->view()->window()->setAttribute(Qt::WA_TranslucentBackground);
#endif
}

QMTAbsComboBox::~QMTAbsComboBox()
{
#if 0
    ClearDataModel();
#if 1//QObject及其派生类的对象，如果其parent非0，那么其parent析构时会析构该对象,官方建议仍需调用deleteLater

    if (_listWidget)
    {
        //delete _listWidget;
        _listWidget->deleteLater();
        _listWidget = nullptr;
    }

#endif
#endif
}

bool QMTAbsComboBox::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        QString preText = this->currentText();

        if (text != preText)    //防止多次抛出信号
        {
            QStringList itemStrList = GetAllItemStrList();

            if (itemStrList.indexOf(text) < 0)
            {
                this->setCurrentIndex(-1);
            }
            else
            {
                this->setCurrentText(text);
            }
        }

        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        setEnabled(bEditEnable);
        return true;
    }

    return false;
}

QString QMTAbsComboBox::GetCurText()
{
    return this->currentText();
}

void QMTAbsComboBox::SetEnableEdit(bool bEdit)
{
    this->setEnabled(bEdit);
}

#if 0
void QMTAbsComboBox::AddStrList(QStringList textList, const QFont& fontTmp)
{
    _isText = true;
    int width = GetMaxStrWidth(textList);//这个就获得了字符串所占的像素宽度
    int itemWidth = this->width();

    if (width > this->width())
    {
        itemWidth = width;
    }

    _listWidget->setMinimumWidth(itemWidth);
    //_listWidget->setFixedWidth(itemWidth + 20);

    for (int i = 0; i < textList.size(); ++i)
    {
        QListWidgetItem* item = new QListWidgetItem(textList.at(i), _listWidget);
        _listWidget->addItem(item);
    }

    _bAutoDeleteItem = true;
}

void QMTAbsComboBox::AddStrAndIconList(QStringList textList, QStringList iconList)
{
    if (0 == textList.size())
        return;

    if (textList.size() != iconList.size())
    {
        AddStrList(textList, QFont());
        return;
    }

    int width = GetMaxStrWidth(textList) + 24;//这个就获得了字符串所占的像素宽度,加上图片的24
    int itemWidth = this->width();

    if (width > this->width())
    {
        itemWidth = width;
    }

    _listWidget->setMinimumWidth(itemWidth);

    for (int i = 0; i < textList.size(); ++i)
    {
        QListWidgetItem* item = new QListWidgetItem(textList.at(i), _listWidget);
        item->setIcon(QIcon(iconList.at(i)));
        _listWidget->addItem(item);
    }

    _bAutoDeleteItem = true;
}

void QMTAbsComboBox::AddWidgetList(QList<QWidget*>widgetList)
{
    if (true == _isText)
        return;

    _isText = false;
}

void QMTAbsComboBox::ClearDataModel()
{
    this->disconnect();

    if (false == _bAutoDeleteItem)
    {
        _listWidget->clear();
        return;
    }

    int count = _listWidget->count();

    // for (int i = 0; i < count; ++i)
    for (int i = count - 1; i >= 0; --i)
    {
        QListWidgetItem* widgetItem = _listWidget->item(i);
        QWidget* widget = _listWidget->itemWidget(widgetItem);

        if (nullptr != widgetItem && nullptr != widget)
        {
            delete widgetItem;
            widgetItem = nullptr;
            delete widget;
            widget = nullptr;
        }
    }

    _listWidget->clear();
}

void QMTAbsComboBox::RemoveItem(const QString& itemStr)
{
    QStringList itemStrList = GetAllItemStrList();
    int index = itemStrList.indexOf(itemStr);
    this->removeItem(index);
}

void QMTAbsComboBox::SetItemEnable(int index, bool enable)
{
    QListWidgetItem* widgetItem = _listWidget->item(index);

    if (widgetItem)
    {
        widgetItem->setFlags(widgetItem->flags() & ~Qt::ItemIsEnabled & ~Qt::ItemIsSelectable);
    }
}

QListWidget* QMTAbsComboBox::GetListWidget()
{
    return _listWidget;
}
#endif

QStringList QMTAbsComboBox::GetAllItemStrList()
{
    QStringList itemStrList;

    for (int idx = 0; idx < this->count(); idx++)
    {
        QString itemName = this->itemText(idx);
        itemStrList << itemName;
    }

    return itemStrList;
}

#if 0

void QMTAbsComboBox::wheelEvent(QWheelEvent* event)
{
    //什么也不做，为了让滚动条不影响items
}

void QMTAbsComboBox::resizeEvent(QResizeEvent* event)
{
    QFont font = this->font();
    font.setPointSize(12);
    QFontMetrics fm(font);
    QRect rec = fm.boundingRect(_maxText);
    int width = rec.width();//这个就获得了字符串所占的像素宽度

    if (true == _isText)
    {
        width = width + 10;
    }
    else
    {
        width = width + 24;
    }

    int itemWidth = this->width();

    if (width > this->width())
    {
        itemWidth = width + 10;
    }

    _listWidget->setMinimumWidth(itemWidth);
}

void QMTAbsComboBox::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit sigClicked();
        emit sigClicked(1);
    }

    QComboBox::mousePressEvent(event);
}

int QMTAbsComboBox::GetMaxStrWidth(const QStringList& textList)
{
    if (0 == textList.size())
        return 0;

    _maxText = textList.at(0);

    //获取最大的字符串
    for (int i = 1; i < textList.size(); ++i)
    {
        if (_maxText.size() < textList.at(i).size())
        {
            _maxText = textList.at(i);
        }
    }

    QFont font = this->font();
    font.setPointSize(12);
    QFontMetrics fm(font);
    QRect rec = fm.boundingRect(_maxText);
    int width = rec.width();//这个就获得了字符串所占的像素宽度
    return width;
}
#endif