<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.1" language="en_US">
<context>
    <name>OptDBDicomServiceUtils</name>
    <message>
        <location filename="../../Utils/Proton/OptDBDicomServiceUtils.cpp" line="428"/>
        <source>计划为审批状态，TableID_RtPlanExtra中找不到相关审批信息</source>
        <translation type="unfinished">The plan is in approval status, and no relevant approval information can be found in TableID_RtPlanExtra</translation>
    </message>
</context>
<context>
    <name>ProtonDoseCalculationManager</name>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="74"/>
        <source>获取服务端IP和端口号失败：</source>
        <translation type="unfinished">Failed to obtain the server IP and port number:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="145"/>
        <source>自动勾画算法执行失败：</source>
        <translation type="unfinished">Dose calculation algorithm execution failed:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="194"/>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="493"/>
        <source>获取所有ROI信息失败：</source>
        <translation type="unfinished">Failed to obtain all ROI information:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="224"/>
        <source>未检索到Body信息</source>
        <translation type="unfinished">Body information not found</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="230"/>
        <source>检索到多个Body信息</source>
        <translation type="unfinished">Multiple Body information retrieved</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="245"/>
        <source>%1, %2 重叠区域存在重复附值，请重新调整ROI后再进行计算</source>
        <translation type="unfinished">%1, %2 There are duplicate values ​​in the overlapping area. Please readjust the ROI and then calculate again</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="363"/>
        <source>自动勾画算法目录不存在</source>
        <translation type="unfinished">The dose calculation algorithm directory does not exist</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="372"/>
        <source>自动勾画算法临时目录不存在</source>
        <translation type="unfinished">The dose calculation algorithm temporary directory does not exist</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="385"/>
        <source>获取RtPlan业务信息失败：</source>
        <translation type="unfinished">Failed to obtain RtPlan business information:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="397"/>
        <source>射束组业务信息为空</source>
        <translation type="unfinished">Beam group service information is empty</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="425"/>
        <source>未检索到加速器信息: %1</source>
        <translation type="unfinished">Accelerator information not retrieved: %1</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="436"/>
        <source>HU/Materials文件夹不存在, 加速器UUID: %1</source>
        <translation type="unfinished">HU/Materials folder does not exist, accelerator UUID: %1</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="444"/>
        <source>BDL文件夹不存在, 加速器UUID: %1</source>
        <translation type="unfinished">BDL folder does not exist, accelerator UUID: %1</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="464"/>
        <source>bdl文件不存在, 加速器UUID: %1, 文件名: %2</source>
        <translation type="unfinished">bdl file does not exist, accelerator UUID: %1, file name: %2</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="477"/>
        <source>未检索到ConfigTemplate.txt文件</source>
        <translation type="unfinished">ConfigTemplate.txt file not retrieved</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="554"/>
        <source>读取材料文件list.dat出错：</source>
        <translation type="unfinished">Error reading material file list.dat:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="686"/>
        <source>获取患者信息失败，patientID：</source>
        <translation type="unfinished">Failed to obtain patient information, patientID:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="697"/>
        <source>获取图像检查信息失败，seriesUID：</source>
        <translation type="unfinished">Failed to obtain image inspection information, seriesUID:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="706"/>
        <source>获取图像序列信息失败，seriesUID：</source>
        <translation type="unfinished">Failed to obtain image sequence information, seriesUID:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="749"/>
        <source>无法删除已存在的Robustness_scenarios.json文件，该文件被其他进程占用</source>
        <translation type="unfinished">Unable to delete the existing Robustness_scenarios.json file, the file is occupied by other processes</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="755"/>
        <source>创建Robustness_scenarios.json文件失败</source>
        <translation type="unfinished">Failed to create Robustness_scenarios.json file</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="779"/>
        <source>获取Body信息失败：</source>
        <translation type="unfinished">Failed to obtain Body information:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="796"/>
        <source>图像dcm_image.nii文件不存在</source>
        <translation type="unfinished">The image dcm_image.nii file does not exist</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="802"/>
        <source>图像dcm_info.json文件不存在</source>
        <translation type="unfinished">Image dcm_info.json file does not exist</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="818"/>
        <source>删除已存在的RtStruct文件夹失败</source>
        <translation type="unfinished">Failed to delete the existing RtStruct folder</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="825"/>
        <source>删除已存在的tempRT.dcm文件夹失败</source>
        <translation type="unfinished">Failed to delete the existing tempRT.dcm folder</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="838"/>
        <source>获取勾画基本信息失败：</source>
        <translation type="unfinished">Failed to obtain basic information of the outline:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="845"/>
        <source>生成StructNii文件夹失败：</source>
        <translation type="unfinished">Failed to generate StructNii folder:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="863"/>
        <source>未检索到Body.nii文件：</source>
        <translation type="unfinished">Body.nii file not found:</translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="870"/>
        <source>生成tempRT.dcm失败：</source>
        <translation type="unfinished">Generate tempRT.dcm failed:</translation>
    </message>
</context>
<context>
    <name>QObject</name>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="449"/>
        <source>ShanzhongTR3.txt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="454"/>
        <source>ShanzhongTR3_</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <location filename="../../Business/ProtonDoseCalculationManager.cpp" line="456"/>
        <source>.txt</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
