﻿#include "AccuComponentUi\Header\UnitUIComponent\QCustMtComboBox.h"
#include "ui_QCustMtComboBox.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include <QMouseEvent>
#include <QPainter>
#include "CMtCoreDefine.h"
#include "MtComboBox.h"



/*********************单元组件的Param*********************************/
QCustMtComboBoxParam::QCustMtComboBoxParam()
{
    _cellWidgetType = DELEAGATE_QCustMtComboBox;
}

QCustMtComboBoxParam::~QCustMtComboBoxParam()
{
}
QWidget* QCustMtComboBoxParam::CreateUIModule(QWidget* parent)
{
    QCustMtComboBox* comboBox = new QCustMtComboBox(parent, _bEnabaleDrawSquare, m_newStyle);
    comboBox->SetupCellWidget(*this);
    return comboBox;
}
/*****************************************************************/


/********************单元组件****************************/
QCustMtComboBox::QCustMtComboBox(QWidget* parent, bool bEnabaleDrawSquare/* = false*/, bool bNewStyle/* = false*/)
    : QWidget(parent)
{
    ui = new Ui::QCustMtComboBox;
    ui->setupUi(this);

    if (false == bEnabaleDrawSquare)
    {
        m_comboBox = new MtComboBox(this);
    }
    else
    {
        m_comboBox = new MtComboBoxDrawSquareColor(this);
    }

    if (bNewStyle)
    {
        //delete m_comboBox;
        //m_comboBox = new NoIconComboBox(this);
        //m_comboBox->setMtType(MtComboBox::combobox2_1);
        m_delegate = new QCustMtComboBoxDelegate(m_comboBox);
        m_comboBox->setItemDelegate(m_delegate);
    }

    if (nullptr != m_delegate)
    {
        connect(m_comboBox, &MtComboBox::SigPopup, [&]()
        {
            QStringList itemStrList = GetAllItemStrList();
            int index = itemStrList.indexOf(GetCurText());

            if (nullptr != m_delegate)
            {
                m_delegate->setLastSelectedIndex(index);
            }
        });
    }

    ui->widget_lineBK->layout()->addWidget(m_comboBox);
    m_comboBox->setMtType(MtComboBox::MtType::combobox1);
    m_comboBox->setElideMode(Qt::ElideRight);
    m_comboBox->setViewTextElideMode(Qt::ElideRight);
    m_comboBox->setEnabledWheelEvent(false);
    m_comboBox->installEventFilter(this);

    if (bNewStyle)
    {
        m_comboBox->setMtType(MtComboBox::MtType::combobox2_1);
    }
}

QCustMtComboBox::~QCustMtComboBox()
{
    disconnect(m_comboBox, &MtComboBox::currentTextChanged, this, &QCustMtComboBox::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
    disconnect(this, &QCustMtComboBox::sigUIUpdated, m_delegate, &QCustMtComboBoxDelegate::SlotCheckIndexChanged);
    MT_DELETE(m_comboBox);
    MT_DELETE(ui);
}

void QCustMtComboBox::SetupCellWidget(QCustMtComboBoxParam& cellWidgetParam)
{
    AddItems(cellWidgetParam._textList, cellWidgetParam._userDataList);

    //为Max和Min选项添加蓝绿icon
    for (int i = 0; i < cellWidgetParam.listMaxMin.size(); ++i)
    {
        if (cellWidgetParam.listMaxMin[i] == 0)
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/icon_min.png"), Qt::DecorationRole);
        }
        else if (cellWidgetParam.listMaxMin[i] == 1)
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/icon_max.png"), Qt::DecorationRole);
        }
        else
        {
            m_comboBox->setItemData(i, QIcon(":/AccuUIComponentImage/images/icon_blank.png"), Qt::DecorationRole);
        }
    }

    disconnect(m_comboBox, &MtComboBox::currentTextChanged, this, &QCustMtComboBox::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));

    if (cellWidgetParam._comboBoxIndex >= 0)
    {
        m_comboBox->setCurrentIndex(cellWidgetParam._comboBoxIndex);
    }
    else if (cellWidgetParam._text.size() >= 0 && cellWidgetParam._textList.indexOf(cellWidgetParam._text) >= 0)
    {
        m_comboBox->setCurrentText(cellWidgetParam._text);
    }
    else
    {
        m_comboBox->setCurrentIndex(-1);
    }

    connect(m_comboBox, &MtComboBox::currentTextChanged, this, &QCustMtComboBox::slotCurrentTextChanged);
    connect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));

    if (cellWidgetParam.m_newStyle)
    {
        QString styleStr = "QComboBox QAbstractItemView::item:hover { background-color: rgba(@colorA2,1); }";
        CMtCoreWidgetUtil::formatStyleSheet(styleStr);
        m_comboBox->setStyleSheet(styleStr);

        if (nullptr != m_delegate)
        {
            m_delegate->setLastSelectedIndex(m_comboBox->currentIndex());
        }
    }
}

bool QCustMtComboBox::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        QString preText = m_comboBox->currentText();
        m_comboBox->blockSignals(true);

        if (text != preText)    //防止多次抛出信号
        {
            QStringList itemStrList = GetAllItemStrList();

            if (itemStrList.indexOf(text) < 0)
            {
                m_comboBox->setCurrentIndex(-1);
            }
            else
            {
                m_comboBox->setCurrentText(text);

                if (nullptr != m_delegate)
                {
                    int index = itemStrList.indexOf(text);
                    m_delegate->setLastSelectedIndex(index);
                    emit sigUIUpdated(index);
                }
            }
        }

        m_comboBox->blockSignals(false);
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        m_comboBox->setEnabled(bEditEnable);
        return true;
    }
    else if (QMetaType::Int == userType)
    {
        bool index = updateData.toInt();
        m_comboBox->setCurrentIndex(index);

        if (nullptr != m_delegate)
        {
            m_delegate->setLastSelectedIndex(index);
        }

        return true;
    }

    return false;
}

QString QCustMtComboBox::GetCurText()
{
    return m_comboBox->currentText();
}

QString QCustMtComboBox::currentText()
{
    return m_comboBox->currentText();
}

void QCustMtComboBox::SetEnableEdit(bool bEdit)
{
    m_comboBox->setEnabled(bEdit);
}

void QCustMtComboBox::setCurrentIndex(int index)
{
    m_comboBox->setCurrentIndex(index);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(index);
    }
}

void QCustMtComboBox::setCurrentText(const QString& text)
{
    m_comboBox->setCurrentText(text);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(m_comboBox->currentIndex());
    }
}

QStringList QCustMtComboBox::GetAllItemStrList()
{
    QStringList itemStrList;

    for (int idx = 0; idx < m_comboBox->count(); idx++)
    {
        QString itemName = m_comboBox->itemText(idx);
        itemStrList << itemName;
    }

    return itemStrList;
}

void QCustMtComboBox::AddItem(const QString& itemStr, const QVariant& auserData)
{
    m_comboBox->addItem(itemStr, auserData);
}

void QCustMtComboBox::AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList/* = QList<QVariant>()*/)
{
    if (0 == userDataList.size() || itemStrList.size() != userDataList.size())
    {
        m_comboBox->addItems(itemStrList);
    }
    else if (itemStrList.size() == userDataList.size())
    {
        for (int i = 0; i < itemStrList.size(); ++i)
        {
            m_comboBox->addItem(itemStrList[i], userDataList[i]);
        }
    }
}

void QCustMtComboBox::RemoveItem(const QString& itemStr)
{
    QStringList itemStrList = GetAllItemStrList();
    int index = itemStrList.indexOf(itemStr);
    m_comboBox->removeItem(index);
}

void QCustMtComboBox::ClearItems()
{
    m_comboBox->clear();
}

void QCustMtComboBox::RegisterSquareColor(int index, const QColor& color)
{
    MtComboBoxDrawSquareColor* drawComboBox = qobject_cast<MtComboBoxDrawSquareColor*>(m_comboBox);

    if (nullptr == drawComboBox)
    {
        return;
    }

    drawComboBox->RegisterSquareColor(index, color);
}

void QCustMtComboBox::UnRegisterSquareColor(int index)
{
    MtComboBoxDrawSquareColor* drawComboBox = qobject_cast<MtComboBoxDrawSquareColor*>(m_comboBox);

    if (nullptr == drawComboBox)
    {
        return;
    }

    drawComboBox->UnRegisterSquareColor(index);
}

MtComboBox* QCustMtComboBox::GetMtComboBox()
{
    return m_comboBox;
}

bool QCustMtComboBox::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (m_comboBox == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
        }
    }

    return QWidget::eventFilter(obj, evt);
}

void QCustMtComboBox::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void QCustMtComboBox::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit sigClicked(1);
    }

    QWidget::mousePressEvent(event);
}

void QCustMtComboBox::slotCurrentTextChanged(const QString& text)
{
    emit currentTextChanged(text);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(GetAllItemStrList().indexOf(text));
    }
}

void QCustMtComboBox::slotCurrentIndexChanged(int index)
{
    emit currentIndexChanged(index);

    if (nullptr != m_delegate)
    {
        m_delegate->setLastSelectedIndex(index);
    }
}

MtComboBoxDrawSquareColor::MtComboBoxDrawSquareColor(QWidget* parent /*= Q_NULLPTR*/)
    : MtComboBox(parent)
{
}

MtComboBoxDrawSquareColor::~MtComboBoxDrawSquareColor()
{
}

void MtComboBoxDrawSquareColor::RegisterSquareColor(int index, const QColor& color)
{
    m_indexSquareColorMap.insert(index, color);
}

void MtComboBoxDrawSquareColor::UnRegisterSquareColor(int index)
{
    m_indexSquareColorMap.remove(index);
}

void MtComboBoxDrawSquareColor::paintEvent(QPaintEvent* event)
{
    int index = this->currentIndex();

    if (index < 0 || false == m_indexSquareColorMap.contains(index))
    {
        MtComboBox::paintEvent(event);
        return;
    }

    MtComboBox::paintEvent(event);
    DrawSquareAppendText(event);
}

void MtComboBoxDrawSquareColor::DrawSquareAppendText(QPaintEvent* event)
{
    int index = this->currentIndex();

    if (false == m_indexSquareColorMap.contains(index))
    {
        return;
    }

    QFontMetrics fm(qApp->font());
    int itemWidth = 10;

    //查找所有下拉框的长度
    do
    {
        for (int idx = 0; idx < this->count(); idx++)
        {
            QString itemName = this->itemText(idx);
            int tmpItemWidth = fm.width(itemName);

            if (tmpItemWidth > itemWidth)
            {
                itemWidth = tmpItemWidth;
            }
        }
    }
    while (0);

    QSize size = this->size();
    QPainter painter(this);
    painter.save();
    QColor color = m_indexSquareColorMap.value(index);
    painter.setPen(Qt::NoPen);

    if (false == this->isEnabled())
    {
        color.setAlpha(153);
    }
    else
    {
    }

    painter.setBrush(color);
    int width = 10;
    int height = 10;
    int x = itemWidth + 24;

    if (x > (size.width() - width))
    {
        x = size.width() / 2 - width / 2;
    }

    int y = size.height() / 2 - height / 2;
    QRectF rectangle(x, y, width, height);
    painter.drawRoundedRect(rectangle, 2, 2);
    painter.restore();
}





