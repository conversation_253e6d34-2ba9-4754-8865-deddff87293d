﻿#include "RoiSettingDialog.h"
#include <QAbstractButton>
#include "CMtLanguageUtil.h"


RoiSettingDialog::RoiSettingDialog(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //
    this->setMainLayout(ui.horizontalLayout);
    this->setTitle(tr("批量设置ROI信息"));
    this->setFixedSize(466, 340);
    this->getButton(ButtonIndex::BtnRight1)->setText(tr("确定"));
    this->getButton(ButtonIndex::BtnRight2)->setText(tr("取消"));
    this->getButton(ButtonIndex::BtnRight1)->setEnabled(false);
    this->setAllowDrag(true);
    //
    ui.comboBox_group->setMtType(MtComboBox::MtType::combobox1);
    ui.comboBox_group->setElideMode(Qt::ElideRight);
    ui.comboBox_group->setViewTextElideMode(Qt::ElideRight);
    ui.comboBox_group->setEnabledWheelEvent(false);
    ui.comboBox_group->installEventFilter(this);
    ui.comboBox_group->addItem("NONE");

    for (const auto& item : allGroupList)
    {
        ui.comboBox_group->addItem(item.name);
    }

    ui.comboBox_group->setCurrentIndex(0);
    //
    ui.mtFrameEx_2->setEnabled(false);
    ui.comboBox_group->setEnabled(false);
    ui.edit_desc->setEnabled(false);
    //
    connect(ui.chkBox_enableROIName, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotEnableROINameStateChanged);
    connect(ui.chkBox_enableGroup, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotEnableGroupStateChanged);
    connect(ui.chkBox_enableDesc, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotEnableDescriptionStateChanged);
    connect(ui.chkBox_label, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotSettingChanged);
    connect(ui.chkBox_roiType, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotSettingChanged);
    connect(ui.chkBox_color, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotSettingChanged);
    connect(ui.chkBox_chName, &QCheckBox::stateChanged, this, &RoiSettingDialog::slotSettingChanged);
    connect(ui.comboBox_group, SIGNAL(sigSelectionChange(int, int, const QString&)), this, SLOT(slotGroupChanged(int, int, const QString&)));

    if (CMtLanguageUtil::type == english)
    {
        ui.chkBox_chName->hide();
    }
}

bool RoiSettingDialog::isEnableLabel()
{
    if (ui.chkBox_enableROIName->isChecked())
    {
        return ui.chkBox_label->isChecked();
    }

    return false;
}

bool RoiSettingDialog::isEnableROIType()
{
    if (ui.chkBox_enableROIName->isEnabled())
    {
        return ui.chkBox_roiType->isChecked();
    }

    return false;
}

bool RoiSettingDialog::isEnableColor()
{
    if (ui.chkBox_enableROIName->isEnabled())
    {
        return ui.chkBox_color->isChecked();
    }

    return false;
}

bool RoiSettingDialog::isEnableChineseName()
{
    if (ui.chkBox_enableROIName->isEnabled())
    {
        return ui.chkBox_chName->isChecked();
    }

    return false;
}

bool RoiSettingDialog::isEnableDescription()
{
    return ui.edit_desc->isEnabled();
}

QStringList RoiSettingDialog::getGroupList()
{
    if (ui.comboBox_group->isEnabled())
    {
        return ui.comboBox_group->currentText();
    }

    return QStringList();
}

QString RoiSettingDialog::getDescription()
{
    return ui.edit_desc->text();
}

void RoiSettingDialog::onBtnCloseClicked()
{
    this->reject();
}

void RoiSettingDialog::onBtnRight2Clicked()
{
    this->reject();
}

void RoiSettingDialog::onBtnRight1Clicked()
{
    this->accept();
}

void RoiSettingDialog::slotEnableROINameStateChanged(int state)
{
    ui.mtFrameEx_2->setEnabled(Qt::Checked == state);
    slotSettingChanged(state);
}

void RoiSettingDialog::slotEnableGroupStateChanged(int state)
{
    ui.comboBox_group->setEnabled(Qt::Checked == state);
    slotSettingChanged(state);
}

void RoiSettingDialog::slotEnableDescriptionStateChanged(int state)
{
    ui.edit_desc->setEnabled(Qt::Checked == state);
    slotSettingChanged(state);
}

void RoiSettingDialog::slotSettingChanged(int state)
{
    if (ui.chkBox_enableROIName->isChecked() && (ui.chkBox_label->isChecked() || ui.chkBox_roiType->isChecked() || ui.chkBox_color->isChecked() || ui.chkBox_chName->isChecked())
        || ui.comboBox_group->isEnabled() || ui.edit_desc->isEnabled())
    {
        this->getButton(ButtonIndex::BtnRight1)->setEnabled(true);
    }
    else
    {
        this->getButton(ButtonIndex::BtnRight1)->setEnabled(false);
    }
}

void RoiSettingDialog::slotGroupChanged(int nIndex, int state, const QString& itemText)
{
    if (state == Qt::Checked)
    {
        if (tr("空勾画") == itemText || "NONE" == itemText)
        {
            ui.comboBox_group->setCurrentIndex(nIndex);
        }
        else
        {
            ui.comboBox_group->setItemsState(QStringList() << "NONE" << tr("空勾画"), Qt::Unchecked);
        }
    }
}