﻿#include "MTRoiModelDialog.h"
#include "ui_MTRoiModelDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MTRoiModelDialog\ModelSub\ModelSettingWidget.h"
#include "CommonUtil.h"
#include "CMtLanguageUtil.h"


namespace n_mtautodelineationdialog
{

MTRoiModelDialog::MTRoiModelDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTRoiModelDialogClass;
    ui->setupUi(this);
    //基本属性
    this->setMainLayout(ui->verticalLayout);            //设置布局
    this->setDialogWidthAndContentHeight(CMtLanguageUtil::type == english ? 1222 : 1352, 600); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("勾画模型设置"));                     //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    getWidgetButton()->hide();                          //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTRoiModelDialog, " << errMsg.toStdString();
        }
    }
}

MTRoiModelDialog::~MTRoiModelDialog()
{
}

QDialog::DialogCode MTRoiModelDialog::showModelSettingDlg(const QStringList& allRoiTypeList, const QStringList& allLabelList
                                                          , const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList
                                                          , const QList<ST_Organ>& stOrganList
                                                          , const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
                                                          , const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList
                                                          , QList<ST_Organ>& outStOrganList
                                                          , QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& outModelInfoMap
                                                          , const ST_CallBack_ROIModelSetting& cb)
{
    //模型设置
    ModelSettingWidget* modelSettingWidget = new ModelSettingWidget(this);
    modelSettingWidget->setImagePathHash(m_imagePathHash);
    modelSettingWidget->init(allRoiTypeList.isEmpty() == true ? CommonUtil::getRoiTypeList() : allRoiTypeList, allLabelList, allGroupList, stOrganList, modelInfoMap, modelCollectionInfoList);
    ui->verticalLayout_2->addWidget(modelSettingWidget);
    //连接窗口信号
    connect(this, &MTRoiModelDialog::sigModelImportProgress, modelSettingWidget, &ModelSettingWidget::sigModelImportProgress);
    connect(this, &MTRoiModelDialog::sigModelImportFinish, modelSettingWidget, &ModelSettingWidget::sigModelImportFinish);
    connect(this, &MTRoiModelDialog::sigModelImportResult, modelSettingWidget, &ModelSettingWidget::sigModelImportResult);
    connect(this, &MTRoiModelDialog::sigModelDeleteResult, modelSettingWidget, &ModelSettingWidget::sigModelDeleteResult);
    connect(modelSettingWidget, &ModelSettingWidget::sigGetLabelLibraryInfo, this, [&](QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)
    {
        emit sigGetLabelLibraryInfo(stRoiLabelInfoVec);

        if (nullptr != cb.cbGetLabelLibraryInfo)
        {
            stRoiLabelInfoVec.clear();
            cb.cbGetLabelLibraryInfo(stRoiLabelInfoVec);
        }
    });
    connect(modelSettingWidget, &ModelSettingWidget::sigGetOrganDefaultInfo, this, [&](QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList)
    {
        emit sigGetOrganDefaultInfo(stOrganDefaultList);

        if (nullptr != cb.cbGetOrganDefaultInfo)
        {
            stOrganDefaultList.clear();
            cb.cbGetOrganDefaultInfo(stOrganDefaultList);
        }
    });
    connect(modelSettingWidget, &ModelSettingWidget::sigModelImport, this, [&](const QString& modelPath)
    {
        emit sigModelImport(modelPath);

        if (nullptr != cb.cbModelImport)
        {
            cb.cbModelImport(modelPath);
        }
    });
    connect(modelSettingWidget, &ModelSettingWidget::sigDeleteModel, this, [&](const QString& modelId, const QString& modelName)
    {
        emit sigDeleteModel(modelId, modelName);

        if (nullptr != cb.cbDeleteModel)
        {
            cb.cbDeleteModel(modelId, modelName);
        }
    });
    connect(modelSettingWidget, &ModelSettingWidget::sigSaveModelInfoResult, this, [&](const QString& modelId, const QString& modelName, int result)
    {
        emit sigSaveModelInfoResult(modelId, modelName, result);

        if (nullptr != cb.cbSaveModelInfoResult)
        {
            cb.cbSaveModelInfoResult(modelId, modelName, result);
        }
    });
    //连接列表组取消信号
    connect(modelSettingWidget->getTableWidget(), &ModelRoiTable::sigRemoveRoiRelatedGroup, this, [&](int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int, QString>& refModelInfoMap)
    {
        emit sigRemoveRoiRelatedGroup(roiID, roiName, groupID, groupName, refModelInfoMap);

        if (nullptr != cb.cbRemoveRoiRelatedGroup)
        {
            cb.cbRemoveRoiRelatedGroup(roiID, roiName, groupID, groupName, refModelInfoMap);
        }
    });
    int ret = this->exec();
    //保存最后一次修改
    modelSettingWidget->saveLastChange();
    //获取信息
    outStOrganList = modelSettingWidget->getAllOrganInfo();
    outModelInfoMap = modelSettingWidget->getModelInfo();

    if (modelSettingWidget->isNeedSave2File())
    {
        ret = QDialog::Accepted;
    }

    return QDialog::DialogCode(ret);
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTRoiModelDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

}
