﻿#pragma once

#include <QCheckBox>
#include <QListWidget>

#include "AccuComponentUi/Header/QMTAbsTableWidgetItem.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtCheckBox.h"

//QMTCheckBox 参数
class  QMTCheckBoxParam : public ICellWidgetParam
{
public:
    enum EM_AlignmentType
    {
        Type_LeftJustifying = 0,
        Type_Center,

    };
public:
    int _width = -1;
    int _height = -1;
    /// <summary>
    /// 居中格式，默认是靠左
    /// </summary>
    int showType = Type_LeftJustifying;

    QMTCheckBoxParam();
    ~QMTCheckBoxParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTCheckBoxParam)

class  QMTCheckBox : public MtCheckBox, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    explicit QMTCheckBox(QWidget* parent = Q_NULLPTR);
    ~QMTCheckBox();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口

    //设置是否允许编辑
    virtual void SetEnableEdit(bool bEdit);

signals:
    void sigClicked(int);           //点击了单元组件

protected:
    //virtual void mousePressEvent(QMouseEvent* event) override;
    virtual bool eventFilter(QObject* obj, QEvent* evt) override;
private:

};
