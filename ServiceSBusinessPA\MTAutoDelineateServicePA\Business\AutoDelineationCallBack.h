﻿// *********************************************************************************
// <remarks>
// FileName    : AutoDelineationDataOpt
// Author      : zlw
// CreateTime  : 2024-02-08
// Description : 自动勾画组件-回调交互
// </remarks>
// **********************************************************************************
#pragma once

#include "AutoDelineationDataOpt.h"


class AutoDelineationCallBack : public QObject
{
    Q_OBJECT

public:
    AutoDelineationCallBack(QObject* parent = Q_NULLPTR);
    ~AutoDelineationCallBack();

    /// <summary>
    /// 创建自动勾画回调对象
    /// </summary>
    /// <returns>自动勾画回调对象</returns>
    static n_mtautodelineationdialog::ST_CallBack_AutoSketch createAutoSketchCallBack();

    /// <summary>
    /// 创建无人值守回调对象
    /// </summary>
    /// <returns>无人值守回调对象</returns>
    static n_mtautodelineationdialog::ST_CallBack_Unattended creteUnattendedCallBack();

    /// <summary>
    /// 创建模型和ROI设置回调对象
    /// </summary>
    /// <returns>模型和ROI设置回调对象</returns>
    static n_mtautodelineationdialog::ST_CallBack_ROILibrarySetting createModelRoiSettingCallback();

private:

};