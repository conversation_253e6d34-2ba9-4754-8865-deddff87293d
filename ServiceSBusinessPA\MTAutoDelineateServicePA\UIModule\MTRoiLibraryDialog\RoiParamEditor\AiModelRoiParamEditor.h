﻿#pragma once

#include "MtTemplateDialog.h"
#include "ui_AiModelRoiParamEditor.h"
#include <QJsonObject>

class AiModelRoiParamEditor : public MtTemplateDialog
{
    Q_OBJECT

public:
    AiModelRoiParamEditor(int modelId, const QString& roiParam, QWidget* parent = 0);
    QString getParamInfo();
    void SetFilterParamEditable(bool editable = true);

protected:
    void init(const QString& roiParam);

    virtual void onBtnCloseClicked();
    virtual void onBtnRight1Clicked();
    virtual void onBtnRight2Clicked();

private:
    Ui::AiModelRoiParamEditor ui;

    QJsonObject _roiParamObj;
};
