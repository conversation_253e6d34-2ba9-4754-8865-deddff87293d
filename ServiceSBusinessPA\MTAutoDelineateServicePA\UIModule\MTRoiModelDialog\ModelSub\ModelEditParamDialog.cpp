﻿#include "ModelEditParamDialog.h"
#include <QJsonObject>
#include "MtMessageBox.h"
#include "MtToolButton.h"
#include "CommonUtil.h"
#include "DataDefine/InnerStruct.h"


ModelEditParamDialog::ModelEditParamDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout_4);         //设置布局
    this->setDialogWidthAndContentHeight(466, 380); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("参数详情"));                     //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //格式限制
    QRegExp regExp1(RegExp_Number3); //正整数
    QValidator* validator1 = new QRegExpValidator(regExp1, this);
    QRegExp regExp2(RegExp_Number4); //正数带两位小数
    QValidator* validator2 = new QRegExpValidator(regExp2, this);
    ui.mtLineEdit_MaxNumCC3D->setValidator(validator1);
    ui.mtLineEdit_MinNumSlice->setValidator(validator1);
    ui.mtLineEdit_MinDiameter->setValidator(validator1);
    ui.mtLineEdit_MinVolume->setValidator(validator1);
    ui.mtLineEdit_ClearX->setValidator(validator1);
    ui.mtLineEdit_ClearY->setValidator(validator1);
    ui.mtLineEdit_DilateDist->setValidator(validator2);
}

ModelEditParamDialog::~ModelEditParamDialog()
{
}

/// <summary>
/// 初始化
/// json: https://manteiatech.yuque.com/manteia/mgmrmw/chsd5gmg4u4tamuh
/// </summary>
/// <param name="paramJson">[IN]后处理参数json</param>
void ModelEditParamDialog::init(const QString& paramJson)
{
    ui.mtComboBox_type->setCurrentIndex(0);

    if (paramJson.isEmpty() == true)
        return;

    QJsonObject jsonObject = CommonUtil::qStringToqJsonObject(paramJson);

    if (jsonObject.isEmpty() == true)
        return;

    if (jsonObject.contains("MaxNumCC3D") == true)
    {
        int MaxNumCC3D = jsonObject.value("MaxNumCC3D").toInt();
        ui.mtLineEdit_MaxNumCC3D->setText(QString::number(MaxNumCC3D));
    }

    if (jsonObject.contains("MinNumSlice") == true)
    {
        int MinNumSlice = jsonObject.value("MinNumSlice").toInt();
        ui.mtLineEdit_MinNumSlice->setText(QString::number(MinNumSlice));
    }

    if (jsonObject.contains("MinDiameter") == true)
    {
        int MinDiameter = jsonObject.value("MinDiameter").toInt();
        ui.mtLineEdit_MinDiameter->setText(QString::number(MinDiameter));
    }

    if (jsonObject.contains("MinVolume") == true)
    {
        int MinVolume = jsonObject.value("MinVolume").toInt();
        ui.mtLineEdit_MinVolume->setText(QString::number(MinVolume));
    }

    if (jsonObject.contains("ClearX") == true)
    {
        int ClearX = jsonObject.value("ClearX").toInt();
        ui.mtLineEdit_ClearX->setText(QString::number(ClearX));
    }

    if (jsonObject.contains("ClearY") == true)
    {
        int ClearY = jsonObject.value("ClearY").toInt();
        ui.mtLineEdit_ClearY->setText(QString::number(ClearY));
    }

    if (jsonObject.contains("ErodeDist") == true) //内缩
    {
        double ErodeDist = jsonObject.value("ErodeDist").toDouble();
        ui.mtLineEdit_DilateDist->setText(QString::number(ErodeDist));
        ui.mtComboBox_type->setCurrentIndex(0);
    }
    else if (jsonObject.contains("DilateDist") == true) //外扩
    {
        double DilateDist = jsonObject.value("DilateDist").toInt();
        ui.mtLineEdit_DilateDist->setText(QString::number(DilateDist));
        ui.mtComboBox_type->setCurrentIndex(1);
    }
}

/// <summary>
/// 获取roi后处理参数json字符串
/// </summary>
/// <returns>roi后处理参数json字符串</returns>
QString ModelEditParamDialog::getParamJson()
{
    QJsonObject jsonObject;

    if (ui.mtLineEdit_MaxNumCC3D->text().isEmpty() == false)
        jsonObject["MaxNumCC3D"] = ui.mtLineEdit_MaxNumCC3D->text().toInt();

    if (ui.mtLineEdit_MinNumSlice->text().isEmpty() == false)
        jsonObject["MinNumSlice"] = ui.mtLineEdit_MinNumSlice->text().toInt();

    if (ui.mtLineEdit_MinDiameter->text().isEmpty() == false)
        jsonObject["MinDiameter"] = ui.mtLineEdit_MinDiameter->text().toInt();

    if (ui.mtLineEdit_MinVolume->text().isEmpty() == false)
        jsonObject["MinVolume"] = ui.mtLineEdit_MinVolume->text().toInt();

    if (ui.mtLineEdit_ClearX->text().isEmpty() == false)
        jsonObject["ClearX"] = ui.mtLineEdit_ClearX->text().toInt();

    if (ui.mtLineEdit_ClearY->text().isEmpty() == false)
        jsonObject["ClearY"] = ui.mtLineEdit_ClearY->text().toInt();

    if (ui.mtLineEdit_DilateDist->text().isEmpty() == false)
    {
        if (ui.mtComboBox_type->currentIndex() == 0)
            jsonObject["ErodeDist"] = ui.mtLineEdit_MaxNumCC3D->text().toDouble();
        else if (ui.mtComboBox_type->currentIndex() == 1)
            jsonObject["DilateDist"] = ui.mtLineEdit_MaxNumCC3D->text().toDouble();
    }

    if (jsonObject.isEmpty() == true)
        return QString();

    return CommonUtil::qJsonObjectToqString(jsonObject);
}

/// <summary>
/// 关闭按钮
/// </summary>
void ModelEditParamDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void ModelEditParamDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void ModelEditParamDialog::onBtnRight1Clicked()
{
    //隔层删除要填需两个都填
    if ((ui.mtLineEdit_ClearX->text().isEmpty() == true && ui.mtLineEdit_ClearY->text().isEmpty() == false) ||
        (ui.mtLineEdit_ClearX->text().isEmpty() == false && ui.mtLineEdit_ClearY->text().isEmpty() == true))
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("隔层删除不允许只配置其中一项"));
        return;
    }

    this->accept();
}
