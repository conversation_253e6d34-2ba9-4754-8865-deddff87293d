﻿#include <QFileDialog>
#include <QtConcurrent/QtConcurrent>
#include <QFontMetrics>

#include "ModelSettingWidget.h"
#include "MTRoiTemplateDialog\ItemStyle\ModelOrganWidget.h"
#include "DataDefine/InnerStruct.h"
#include "MtMessageBox.h"
#include "RoiSettingDialog.h"
#include "CMtWidgetManager.h"
#include "Skin/CMtSkinManager.h"

ModelSettingWidget::ModelSettingWidget(QWidget* parent)
    : QWidget(parent)
    , m_parentDialog(parent)
    , m_bInfoChanged(false)
    , m_bNeedSave2File(false)
{
    ui.setupUi(this);
    //ui.mtPushButton_save->setEnabled(false);
    //ui.mtPushButton_cancel->setEnabled(false);
    //信号槽
    connect(ui.widget_roiTable, &ModelRoiTable::sigTableInfoChanged, this, &ModelSettingWidget::slotTableInfoChanged);   //ROI表格信息发生了变化
    connect(ui.widget_roiTable, &ModelRoiTable::sigTableInitialized, this, &ModelSettingWidget::slotTableInitialized);
    connect(ui.widget_modelTable, &ModelNameView::sigModelTableChanged, this, &ModelSettingWidget::slotModelTableChanged);
    connect(ui.mtLineEdit_name, &QLineEdit::textChanged, this, &ModelSettingWidget::slotModelInfoChanged);
    connect(ui.mtLineEdit_desc, &QLineEdit::textChanged, this, &ModelSettingWidget::slotModelInfoChanged);
    connect(this, &ModelSettingWidget::sigModelImportProgress, this, &ModelSettingWidget::slotModelImportProgress);
    connect(this, &ModelSettingWidget::sigModelImportFinish, this, &ModelSettingWidget::slotModelImportFinish);
    connect(this, &ModelSettingWidget::sigModelImportResult, this, &ModelSettingWidget::slotModelImportResult);
    connect(this, &ModelSettingWidget::sigModelDeleteResult, this, &ModelSettingWidget::slotModelDeleteResult);
    connect(this, &ModelSettingWidget::sigRefreshROITableAsync, this, [&](const QString& modelId)
    {
        ui.widget_roiTable->refreshTable(modelId);
    }, Qt::QueuedConnection);
    //
    ui.mtPushButton_save->installEventFilter(this);
    ui.mtPushButton_cancel->installEventFilter(this);
    MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_parentDialog);

    if (nullptr != parentDialog)
    {
        parentDialog->getButton(MtTemplateDialog::BtnClose)->installEventFilter(this);
    }

    // 加载皮肤
    QString errMsg;
    CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

    if (bool ret = skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
    {
        std::cout << "ModelSettingWidget: LoadDomainWidgetSkinStyle failed" << errMsg.toStdString();
    }

    // 将加载组件移出布局，以便设置居中
    ui.mtFrameEx_5->layout()->removeWidget(ui.mtFrameEx_6);

    if (CMtLanguageUtil::type == english)
    {
        ui.mtPushButton_reset->setToolTipText(tr("恢复默认的ROI名称、标签、颜色、ROI类型、描述"));
    }
}

ModelSettingWidget::~ModelSettingWidget()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void ModelSettingWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    ui.widget_roiTable->setGroupColumnBtnImage(imagePathHash["icon_table_group_help"]);
    ui.label_rotate->setUrl(imagePathHash["icon_loading"]);
    ui.mtToolButton_import->setPixmapFilename(imagePathHash["import"]);
    ui.mtToolButton_del->setPixmapFilename(imagePathHash["icon_del2_green"]);
}

void ModelSettingWidget::showLoadingState(bool bShow)
{
    if (bShow)
    {
        ui.label_rotate->setFixedSize(14, 14);
        ui.label_rotate->setClockwise(true);
        ui.label_rotate->setInterval(5);
        ui.label_rotate->show();
        ui.label_data_loading->show();
    }
    else
    {
        ui.label_rotate->hide();
        ui.label_data_loading->hide();
    }
}

ModelRoiTable* ModelSettingWidget::getTableWidget()
{
    return ui.widget_roiTable;
}

void ModelSettingWidget::init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
                              const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
                              const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
                              const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
                              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    //保存模型信息
    m_modelInfoMap = modelInfoMap;
    //保存组信息
    m_allGroupList = allGroupList;
    //保存模板信息
    m_modelCollectionInfoList = modelCollectionInfoList;
    //初始化列表
    ui.widget_roiTable->init(allRoiTypeList, allLabelList, allGroupList, stOrganList, modelCollectionInfoList);
    ui.widget_modelTable->init(m_modelInfoMap.values());
}

QList<n_mtautodelineationdialog::ST_Organ> ModelSettingWidget::getAllOrganInfo()
{
    return ui.widget_roiTable->getAllOrganInfo();
}

QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel> ModelSettingWidget::getModelInfo()
{
    return m_modelInfoMap;
}

QList<n_mtautodelineationdialog::ST_OrganGroupInfo> ModelSettingWidget::getAllOrganGroupInfo()
{
    return ui.widget_roiTable->getAllOrganGroupInfo();
}

void ModelSettingWidget::setTableChangedStatus()
{
    MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_parentDialog);

    if (nullptr != parentDialog)
    {
        parentDialog->getButton(MtTemplateDialog::BtnRight1)->setEnabled(true);
    }

    m_bInfoChanged = true;
    ui.mtPushButton_save->setEnabled(true);
    ui.mtPushButton_cancel->setEnabled(true);
}

void ModelSettingWidget::saveLastChange()
{
    if (/*ui.mtPushButton_save->isEnabled()*/m_bInfoChanged)
    {
        if (QMessageBox::StandardButton::Yes == MtMessageBox::NoIcon::question(this, tr("是否保存本次修改？")))
        {
            on_mtPushButton_save_clicked();
        }
        else
        {
            on_mtPushButton_cancel_clicked();
        }
    }
}

bool ModelSettingWidget::isNeedSave2File()
{
    return m_bNeedSave2File;
}

QString ModelSettingWidget::formatDateTimeStr(QString dateTimeStr)
{
    bool isOK = false;
    QString dataAndTimeStr;
    QDateTime dateTime;
    dateTimeStr = dateTimeStr.remove(QRegExp("[\\s:/]")); //去除所有空格

    if (dateTimeStr.size() == 14)
    {
        dataAndTimeStr = QDateTime::fromString(dateTimeStr, "yyyyMMddhhmmss").toString("yyyy-MM-dd hh:mm:ss");
    }
    else if (dataAndTimeStr.size() == 8)
    {
        dataAndTimeStr = QDateTime::fromString(dateTimeStr, "yyyyMMdd").toString("yyyy-MM-dd");
    }
    else
    {
        dataAndTimeStr = "-";
    }

    return dataAndTimeStr;
}

bool ModelSettingWidget::eventFilter(QObject* obj, QEvent* event)
{
    //点击保存和取消按钮时，让焦点设置到列表上，使列表输入框失去焦点而触发编辑完成事件
    MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_parentDialog);

    if (obj == ui.mtPushButton_cancel || obj == ui.mtPushButton_save || (nullptr != parentDialog && obj == parentDialog->getButton(MtTemplateDialog::BtnClose)))
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent* mouse_event = static_cast<QMouseEvent*>(event);

            if (mouse_event->button() == Qt::LeftButton)
            {
                ui.widget_roiTable->setFocus();
            }
        }
    }

    return QWidget::eventFilter(obj, event);
}

void ModelSettingWidget::showEvent(QShowEvent* e)
{
    static bool isInit = false;

    if (!isInit)
    {
        int dx = (ui.mtFrameEx_5->width() - ui.mtFrameEx_6->width()) / 2;
        ui.mtFrameEx_6->move(dx, ui.mtFrameEx_6->y());
    }
    else
    {
        isInit = true;
    }

    QWidget::showEvent(e);
}

void ModelSettingWidget::on_mtPushButton_settingROIInfo_clicked()
{
    RoiSettingDialog dlg(m_allGroupList, this);

    if (QDialog::Rejected == dlg.exec())
        return;

    //获取批量设置信息
    bool bLabel = dlg.isEnableLabel();
    bool bRoiType = dlg.isEnableROIType();
    bool bColor = dlg.isEnableColor();
    bool bChName = dlg.isEnableChineseName();
    bool bDesc = dlg.isEnableDescription();
    QStringList groupList = dlg.getGroupList();
    QString desc = dlg.getDescription();
    //发送信息获取标签库信息
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> stRoiLabelInfoVec;
    emit sigGetLabelLibraryInfo(stRoiLabelInfoVec);

    //更新roi列表
    if (stRoiLabelInfoVec.size() != 0)
    {
        ui.widget_roiTable->syncLabelLibraryInfo(stRoiLabelInfoVec, bLabel, bRoiType, bColor, bChName, groupList, bDesc, desc);
    }

    //更新保存/取消按钮状态
    setTableChangedStatus();
}

void ModelSettingWidget::on_mtPushButton_reset_clicked()
{
    if (!ui.widget_roiTable->isTableInitialized())
    {
        MtMessageBox::NoIcon::information(this, tr("请等数据加载完成后再进行操作"));
        return;
    }

    //发送信号获取默认模型器官信息
    QList<n_mtautodelineationdialog::ST_Organ> stOrganDefaultList;
    emit sigGetOrganDefaultInfo(stOrganDefaultList);

    if (stOrganDefaultList.size() != 0)
    {
        ui.widget_roiTable->resetDefaultInfo(stOrganDefaultList);
    }

    //更新保存/取消按钮状态
    setTableChangedStatus();
}

void ModelSettingWidget::on_mtPushButton_save_clicked()
{
    //记录是否要保存到数据库
    if (m_bInfoChanged)
    {
        m_bNeedSave2File = true;
    }

    //保存列表信息
    ui.widget_roiTable->saveEdit();
    //保存模型信息
    QString modelName = ui.mtLineEdit_name->text();
    QString desc = ui.mtLineEdit_desc->text();
    QString oldModelName = m_modelInfoMap[m_curModelID.toInt()].modelName;
    m_modelInfoMap[m_curModelID.toInt()].modelName = modelName;
    m_modelInfoMap[m_curModelID.toInt()].modelDesc = desc;
    //设置按钮状态
    m_bInfoChanged = false;
    //ui.mtPushButton_save->setEnabled(false);
    //ui.mtPushButton_cancel->setEnabled(false);
    //更新模型名到左侧列表
    ui.widget_modelTable->UpdateCellWidget(m_curModelID, ModelNameView::COL_ModelName, QVariant::fromValue(modelName));
    //发生保存成功信号
    emit sigSaveModelInfoResult(m_curModelID, oldModelName, 0);
}

void ModelSettingWidget::on_mtPushButton_cancel_clicked()
{
    //丢弃列表编辑的信息
    ui.widget_roiTable->cancelEdit();
    //还原模型信息
    ui.mtLineEdit_name->setText(m_modelInfoMap[m_curModelID.toInt()].modelName);
    ui.mtLineEdit_desc->setText(m_modelInfoMap[m_curModelID.toInt()].modelDesc);
    //设置按钮状态
    m_bInfoChanged = false;
    //ui.mtPushButton_save->setEnabled(false);
    //ui.mtPushButton_cancel->setEnabled(false);
}

void ModelSettingWidget::on_mtToolButton_import_clicked()
{
    QString fileName = QFileDialog::getOpenFileName(this,
                                                    tr("Open"),
                                                    "",
                                                    tr("Pack Files (*.pack)"));

    if (fileName.isNull())
    {
        return;
    }

    /*m_progressDialog = new MtProgressDialog(this);放到服务外部实现
    m_progressDialog->show();
    m_progressDialog->setLabelText(tr("正在导入模型，请稍候..."));
    m_progressDialog->setCancelButtonVisible(false);
    m_progressDialog->setRange(0, 100);*/
    //发送导入模型信号
    emit sigModelImport(fileName);
}

void ModelSettingWidget::on_mtToolButton_del_clicked()
{
    QString curModelId = ui.widget_modelTable->GetCurUniqueValue();
    QString curModelName = ui.widget_modelTable->GetColumnText(curModelId, ModelNameView::COL_ModelName);

    if (curModelId.isEmpty())
    {
        MtMessageBox::information(this, tr("请先选择要删除的模型"));
        return;
    }

    int modelId = curModelId.toInt();
    QStringList modelCollectionNameList;
    QString modelCollStr;

    for (const auto& organItem : m_modelInfoMap[modelId].organList)
    {
        for (const auto& collItem : m_modelCollectionInfoList)
        {
            if (collItem.showGroupIdMap.keys().contains(organItem.id) && !modelCollectionNameList.contains(collItem.templateName))
            {
                modelCollectionNameList.append(collItem.templateName);
                modelCollStr += collItem.templateName + "; ";
            }
        }
    }

    if (modelCollectionNameList.size() > 0)
    {
        modelCollStr.chop(2);

        if (QMessageBox::StandardButton::Yes != MtMessageBox::NoIcon::question_Title(this, tr("下列模板引用了该模型的ROI，删除模型将移除模板中对应的ROI，是否确定删除？"), modelCollStr))
        {
            return;
        }
    }

    QString msg = QString(tr("确定删除[%1]模型吗？").arg(curModelName));
    //MtMessageBox::NoIcon::question_Title(this, msg, tr("删除后将无法恢复!"));
    int ret = MtMessageBox::redWarning(this, msg, tr("删除后将无法恢复!"), tr("删除"));

    if (QMessageBox::Yes == ret)
    {
        //发送删除模型信号
        emit sigDeleteModel(curModelId, curModelName);
    }
}

void ModelSettingWidget::slotTableInfoChanged(const QString& defOrganName, int col, const QString& newText)
{
    setTableChangedStatus();
}

void ModelSettingWidget::slotTableInitialized()
{
    //隐藏加载状态
    showLoadingState(false);
}

void ModelSettingWidget::slotModelTableChanged(const QString& modelID)
{
    //提示保存
    saveLastChange();
    //保存当前显示的模型ID
    m_curModelID = modelID;
    //显示加载状态
    showLoadingState(true);
    //
    const n_mtautodelineationdialog::ST_SketchModel& modelInfo = m_modelInfoMap[modelID.toInt()];
    //刷新列表
    emit sigRefreshROITableAsync(modelID);
    //设置模型名称
    ui.mtLineEdit_name->setText(modelInfo.modelName);
    //导入时间
    ui.mtLineEdit_exportTime->setText(formatDateTimeStr(modelInfo.importTime));
    //模型描述
    ui.mtLineEdit_desc->setText(modelInfo.modelDesc);

    //默认模型不可编辑
    if ("0" == modelID)
    {
        ui.mtLineEdit_name->setReadOnly(true);
        ui.mtLineEdit_desc->setReadOnly(true);
        //可以恢复默认
        ui.mtPushButton_reset->setVisible(true);
        //不可删除
        ui.mtToolButton_del->setEnabled(false);
        //不可批量设置
        ui.mtPushButton_settingROIInfo->setEnabled(false);
    }
    else
    {
        ui.mtLineEdit_name->setReadOnly(false);
        ui.mtLineEdit_desc->setReadOnly(false);
        //不可恢复默认
        ui.mtPushButton_reset->setVisible(false);
        //可删除
        ui.mtToolButton_del->setEnabled(true);
        //可批量设置
        ui.mtPushButton_settingROIInfo->setEnabled(true);
    }

    //禁用保存/取消保存按钮
    m_bInfoChanged = false;
    //ui.mtPushButton_save->setEnabled(false);
    //ui.mtPushButton_cancel->setEnabled(false);
}

void ModelSettingWidget::slotModelInfoChanged(const QString& text)
{
    setTableChangedStatus();
}

void ModelSettingWidget::slotModelImportProgress(int value)
{
    m_progressDialog->setValue(value);
}

void ModelSettingWidget::slotModelImportFinish(bool bSuccess, const QString& errMsg)
{
    m_progressDialog->setValue(100);
    m_progressDialog->close();
    delete m_progressDialog;
    m_progressDialog = nullptr;

    if (!bSuccess)
    {
        MtMessageBox::information(this, errMsg);
    }
}

void ModelSettingWidget::slotModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap)
{
    QMap<int, n_mtautodelineationdialog::ST_SketchModel>::const_iterator itor = modelInfoMap.constBegin();
    int modelId = 0;

    for (; itor != modelInfoMap.constEnd(); ++itor)
    {
        modelId = itor.key();
        const n_mtautodelineationdialog::ST_SketchModel& modelItemInfo = itor.value();

        if (m_modelInfoMap.contains(modelId))
        {
            continue;
        }

        //更新信息
        m_modelInfoMap.insert(modelId, modelItemInfo);
        //添加列表项
        ui.widget_modelTable->addRow(modelItemInfo);
    }

    //说明有新导入的模型，1. 合并新增的模型器官信息；2. 切换到新导入模型，并进行批量设置
    if (modelId != 0)
    {
        ui.widget_roiTable->mergeNewOrganInfo(stOrganInfoVec);
        ui.widget_modelTable->SetCurrentRow(QString::number(modelId), true);

        if (QMessageBox::Yes == MtMessageBox::NoIcon::question_Title(this, tr("模型导入成功"), tr("是否设置模型ROI的标签及分组等信息？")))
        {
            on_mtPushButton_settingROIInfo_clicked();
        }
    }
}

void ModelSettingWidget::slotModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg)
{
    QStringList rowValueList = ui.widget_modelTable->GetAllRowUniqueValueList();
    QString mid = modelId;

    for (const QString& id : rowValueList)
    {
        QString name = ui.widget_modelTable->GetColumnText(id, ModelNameView::COL_ModelName);

        if (name == modelName)
        {
            mid = id;
            break;
        }
    }

    if (bSuccess)
    {
        //移除列表项--ROI
        if (m_modelInfoMap.contains(mid.toInt()))
        {
            for (const auto& organItem : m_modelInfoMap[mid.toInt()].organList)
            {
                ui.widget_roiTable->delRow(QString::number(organItem.id));
            }
        }

        //移除列表项--模型
        QString curModelId = ui.widget_modelTable->GetCurUniqueValue();
        ui.widget_modelTable->delRow(mid);

        if (curModelId == mid)
        {
            curModelId = ui.widget_modelTable->GetRowUniqueValue(0);
            ui.widget_modelTable->SetCurrentRow(curModelId, true);
        }

        //删除缓存信息
        m_modelInfoMap.remove(mid.toInt());
    }
    else
    {
        MtMessageBox::information(this, errMsg);
    }
}