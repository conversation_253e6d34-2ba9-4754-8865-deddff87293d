﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTAbsHorizontalBtns.h"
#include "ui_QMTAbsHorizontalBtns.h"
#include "CMtCoreDefine.h"
#include <QLabel>
#include <QPainter>
#include <qDebug>
#include <QCheckBox>
#include "MtToolButton.h"

QMTAbsHorizontalBtnsParam::QMTAbsHorizontalBtnsParam()
{
    _cellWidgetType = DELEAGATE_QMTAbsHorizontalBtns;
}

QMTAbsHorizontalBtnsParam::~QMTAbsHorizontalBtnsParam()
{
}

QWidget* QMTAbsHorizontalBtnsParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTAbsHorizontalBtns* btns = new QMTAbsHorizontalBtns(parent);
    btns->SetupCellWidget(*this);
    return btns;
}

QMTAbsHorizontalBtns::QMTAbsHorizontalBtns(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTAbsHorizontalBtns;
    ui->setupUi(this);
}

QMTAbsHorizontalBtns::~QMTAbsHorizontalBtns()
{
    DeleteButtons();
    MT_DELETE(ui);
}

bool QMTAbsHorizontalBtns::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        this->SetButtonText(0, text);
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bChecked = updateData.toBool();
        SetChecked(0, bChecked);
        return true;
    }
    else if (QMetaType::QColor == userType)
    {
        QColor color = updateData.value<QColor>();
        SetBtnsBackground(color);
    }
    else if (updateData.canConvert<QMTAbsHorizontalBtns::HorizontalBtnsUpdateInfo>())
    {
        HorizontalBtnsUpdateInfo updateButtonInfo = updateData.value<QMTAbsHorizontalBtns::HorizontalBtnsUpdateInfo>();
        //SetAllButtonEnable(true);
        //HideAllButton(false);     //可能会出现闪动情况
        //SetAllButtonChecked(false);

        //隐藏按键
        //for (int i = 0; i < updateButtonInfo._hideBtnIndexList.size(); ++i)
        for (int i = 0; i < _buttonList.size(); ++i)
        {
            if (updateButtonInfo._hideBtnIndexList.indexOf(i) >= 0)
            {
                HideButton(i, true);
            }
            else
            {
                HideButton(i, false);
            }
        }

        //checked状态
        //for (int i = 0; i < updateButtonInfo._checkedBtnList.size(); ++i)
        for (int i = 0; i < _buttonList.size(); ++i)
        {
            //int btnIndex = updateButtonInfo._checkedBtnList[i];
            if (updateButtonInfo._checkedBtnList.indexOf(i) >= 0)
            {
                SetChecked(i, false);
            }
            else
            {
                SetChecked(i, true);
            }
        }

        //使能按键
        // for (int i = 0; i < updateButtonInfo._disableBtnList.size(); ++i)
        for (int i = 0; i < _buttonList.size(); ++i)
        {
            if (updateButtonInfo._disableBtnList.indexOf(i) >= 0)
            {
                SetButtonEnable(i, false);
            }
            else
            {
                SetButtonEnable(i, true);
            }
        }
    }
    else if (updateData.canConvert<QMTAbsHorizontalBtns::OneButtonUpdateInfo>())
    {
        QMTAbsHorizontalBtns::OneButtonUpdateInfo oneBtnUpdateInfo = updateData.value<QMTAbsHorizontalBtns::OneButtonUpdateInfo>();
        HideButton(oneBtnUpdateInfo.btnIndex, oneBtnUpdateInfo.bHide);
        SetChecked(oneBtnUpdateInfo.btnIndex, oneBtnUpdateInfo.bChecked);
        SetButtonEnable(oneBtnUpdateInfo.btnIndex, oneBtnUpdateInfo.bEnable);
        return true;
    }

    return false;
}

void QMTAbsHorizontalBtns::SetEnableEdit(bool bEdit)
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        _buttonList[i]->setEnabled(bEdit);
    }
}

void QMTAbsHorizontalBtns::SetupCellWidget(QMTAbsHorizontalBtnsParam& param)
{
    _btnsWidgetParam = param;
    int count = 0;

    if (_btnsWidgetParam._btnCount < 1)
    {
        count = param._pixPathList.size();
    }
    else
    {
        count = _btnsWidgetParam._btnCount;
    }

    DeleteButtons();

    if (0 == param._hoverPixPathList.size() || param._hoverPixPathList.size() != param._pixPathList.size())
    {
        param._hoverPixPathList = param._pixPathList;
    }

    //创建ui
    if (param._pixPathList.size() == 0)//没有图片,只显示背景色按键
    {
        MtToolButton* button = new MtToolButton(ui->widget_btnsBK);
        ui->widget_btnsBK->layout()->addWidget(button);
        button->setFixedSize(_btnsWidgetParam._width, _btnsWidgetParam._height);
        _buttonList.push_back(button);
        SetBtnsBackground(_btnsWidgetParam._btnBackgroundColor);
        connect(button, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
    }
    else
    {
        for (int i = 0; i < count; ++i)
        {
            if (true == _btnsWidgetParam._checkable)
            {
                QCheckBox* checkBox = new QCheckBox(ui->widget_btnsBK);
                ui->widget_btnsBK->layout()->addWidget(checkBox);
                //checkBox->setFixedSize(_btnsWidgetParam._width, _btnsWidgetParam._height);
                checkBox->setMaximumSize(_btnsWidgetParam._width, _btnsWidgetParam._height);
                QString sheetStr = GetCheckBoxSheetStr(i);

                if (sheetStr.size() > 0)
                {
                    checkBox->setStyleSheet(sheetStr);
                }

                checkBox->setCheckable(_btnsWidgetParam._checkable);
                checkBox->setChecked(_btnsWidgetParam._isChecked);
                connect(checkBox, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
                _buttonList.push_back(checkBox);
            }
            else
            {
                MtToolButton* button = new MtToolButton(ui->widget_btnsBK);
                ui->widget_btnsBK->layout()->addWidget(button);
                button->setMaximumSize(_btnsWidgetParam._width, _btnsWidgetParam._height);
                QString sheetStr = GetBtnSheetStr(i);

                if (sheetStr.size() > 0)
                {
                    button->setStyleSheet(sheetStr);
                }
                else
                {
                    QString sheetStr = "QToolButton{"
                        "border:none;}"
                        "QToolButton:pressed{padding-top: 2px;}";
                    button->setStyleSheet(sheetStr);
                    QString imagePath = _btnsWidgetParam._pixPathList.at(i);

                    if (imagePath.size() > 0)
                    {
                        QPixmap pixmap(imagePath);
                        //button->setIcon(QIcon(pixmap));
                    }
                }

                //button->setText(QString::number(i));
                //button->setCheckable(_btnsWidgetParam._checkable);
                connect(button, SIGNAL(clicked(bool)), this, SLOT(slotButtonClicked(bool)));
                _buttonList.push_back(button);
            }
        }
    }

    ((QHBoxLayout*)ui->widget_btnsBK->layout())->addStretch();
    SetPaddingLeft(_btnsWidgetParam._paddingLeft);
    //初始化隐藏和使能按键

    //隐藏按键
    for (int i = 0; i < _btnsWidgetParam._hideBtnIndexList.size(); ++i)
    {
        int btnIndex = _btnsWidgetParam._hideBtnIndexList[i];
        HideButton(btnIndex, true);
    }

    ////checked状态
    //for (int i = 0; i < updateButtonInfo._checkedBtnList.size(); ++i)
    //{
    //    int btnIndex = updateButtonInfo._checkedBtnList[i];
    //    SetChecked(btnIndex, true);
    //}

    //使能按键
    for (int i = 0; i < _btnsWidgetParam._disableBtnList.size(); ++i)
    {
        int btnIndex = _btnsWidgetParam._disableBtnList[i];
        SetButtonEnable(btnIndex, false);
    }
}


void QMTAbsHorizontalBtns::SetCustStyleSheet(QString& sheetStr)
{
    this->setStyleSheet(sheetStr);
}

void QMTAbsHorizontalBtns::SetButtonText(int index, QString& text)
{
    if (index < _buttonList.size())
    {
        QWidget* widget = _buttonList.at(index)
            ;
        QString className = widget->metaObject()->className();

        if (className == "QPushButton")
        {
            QPushButton* btn = qobject_cast<QPushButton*>(widget);
            btn->setText(text);
        }
        else if (className == "QToolButton")
        {
            QToolButton* btn = qobject_cast<QToolButton*>(widget);
            btn->setText(text);
        }
        else if (className == "MtToolButton")
        {
            MtToolButton* btn = qobject_cast<MtToolButton*>(widget);
            btn->setText(text);
        }
        else if (className == "QCheckBox")
        {
            QCheckBox* checkBox = qobject_cast<QCheckBox*>(widget);
            checkBox->setText(text);
        }
        else
        {
        }
    }
}


void QMTAbsHorizontalBtns::SetBackgroundColor(QColor& color)
{
    int rgb[3];
    QString backColor;
    QString sheetStr;
    rgb[0] = color.red();
    rgb[1] = color.green();
    rgb[2] = color.blue();
    backColor = "background-color:rgb(" + QString::number(rgb[0]) + "," + QString::number(rgb[1]) + "," + QString::number(rgb[2]) + ");";
    backColor += "border:none";
    sheetStr = "QWidget{" + backColor + ";}";
    sheetStr = "QToolButton:pressed{padding-top: 2px;}";
    this->setStyleSheet(sheetStr);
}

void QMTAbsHorizontalBtns::SetBtnsBackground(QColor& color)
{
    if (true == color.isValid())
    {
        int rgb[3];
        QString backColor;
        QString sheetStr;
        rgb[0] = color.red();
        rgb[1] = color.green();
        rgb[2] = color.blue();
        int alpha = color.alpha();
        backColor = "background-color:rgb(" + QString::number(rgb[0]) + "," + QString::number(rgb[1]) + "," + QString::number(rgb[2]) + "," + QString::number(alpha) + ");";
        backColor += "border:none";
        sheetStr = "QToolButton{" + backColor + ";}";

        for (int i = 0; i < _buttonList.size(); ++i)
        {
            QWidget* widget = _buttonList.at(i);
            widget->setStyleSheet(sheetStr);
            widget->show();
        }
    }
    else
    {
        for (int i = 0; i < _buttonList.size(); ++i)
        {
            QWidget* widget = _buttonList.at(i);
            widget->hide();
        }
    }
}

void QMTAbsHorizontalBtns::DeleteButtons()
{
    for (int i = 0; i < _buttonList.size(); ++i)
    {
        QWidget* widget = _buttonList.at(i);
        delete widget;
    }

    _buttonList.clear();
}

QString QMTAbsHorizontalBtns::GetBtnSheetStr(int index)
{
    QString retSheet;

    if (index < 0 || index >= _btnsWidgetParam._pixPathList.size())
        return retSheet;

    //unchecked状态图标
    retSheet += QString("QToolButton{background-image:url(%1);border:1px;}").arg(_btnsWidgetParam._pixPathList[index]);
    QString pixPath;

    //unchecked状态hover的时候图标
    if (index < _btnsWidgetParam._hoverPixPathList.size())
    {
        pixPath = _btnsWidgetParam._hoverPixPathList[index];

        if (pixPath.size() > 0)
            retSheet += QString("QToolButton:hover{background-image:url(%1);}").arg(pixPath);
    }

    //disable的时候的图标
    if (index < _btnsWidgetParam._disablePixPathList.size())
    {
        pixPath = _btnsWidgetParam._disablePixPathList[index];

        if (pixPath.size() > 0)
            retSheet += QString("QToolButton:!enabled{background-image:url(%1);}").arg(pixPath);
    }

    if (false == _btnsWidgetParam._checkable)
        goto _END;

    do//checkable为true的时候的样式
    {
        if (0 == _btnsWidgetParam._checkedPixPathList.size() ||
            false == _btnsWidgetParam._checkable)
            break;

        if (index < _btnsWidgetParam._checkedPixPathList.size())
        {
            pixPath = _btnsWidgetParam._hoverCheckedPixPathList[index];

            if (pixPath.size() > 0)
                retSheet += QString("QToolButton:checked{background-image:url(%1);}").arg(pixPath);
        }

        if (index < _btnsWidgetParam._hoverCheckedPixPathList.size())
        {
            pixPath = _btnsWidgetParam._hoverCheckedPixPathList[index];

            if (pixPath.size() > 0)
                retSheet += QString("QToolButton:checked:hover{background-image:url(%1);}").arg(pixPath);
        }
    }
    while (0);

_END:
    retSheet += QString("QToolButton:pressed{padding-top: 2px;}");
    return retSheet;
}

QString QMTAbsHorizontalBtns::GetCheckBoxSheetStr(int index)
{
    QString retSheet;

    if (index < 0 || index >= _btnsWidgetParam._pixPathList.size())
        return retSheet;

    retSheet += QString("QCheckBox::indicator:unchecked{image:url(%1);}").arg(_btnsWidgetParam._pixPathList[index]);
    QString pixPath = _btnsWidgetParam._hoverPixPathList[index];

    if (pixPath.size() > 0)
        retSheet += QString("QCheckBox::indicator:unchecked:hover{image:url(%1);}").arg(pixPath);

    retSheet += QString("QCheckBox::indicator:unchecked:pressed{image:url(%1);}").arg(_btnsWidgetParam._pixPathList[index]);

    do//checkable为true的时候的样式
    {
        if (0 == _btnsWidgetParam._checkedPixPathList.size() ||
            false == _btnsWidgetParam._checkable)
            break;

        retSheet += QString("QCheckBox::indicator:checked{image:url(%1);}").arg(_btnsWidgetParam._checkedPixPathList[index]);
        pixPath = _btnsWidgetParam._hoverCheckedPixPathList[index];

        if (pixPath.size() > 0)
            retSheet += QString("QCheckBox::indicator:checked:hover{image:url(%1);}").arg(pixPath);

        retSheet += QString("QCheckBox::indicator:checked:pressed{image:url(%1);}").arg(_btnsWidgetParam._checkedPixPathList[index]);
    }
    while (0);

    retSheet += QString("QCheckBox::indicator:indeterminate:hover{image:url(%1);}").arg(_btnsWidgetParam._hoverCheckedPixPathList[index]);
    retSheet += QString("QCheckBox::indicator:indeterminate:pressed{image:url(%1);}").arg(_btnsWidgetParam._checkedPixPathList[index]);
    return retSheet;
}

QWidget* QMTAbsHorizontalBtns::GetButtonWidget(int index)
{
    if (index < 0 || index >= _buttonList.size())
        return nullptr;

    return _buttonList[index];
}

void QMTAbsHorizontalBtns::SetButtonSize(int index, QSize size)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setFixedSize(size);
    }
}

void QMTAbsHorizontalBtns::HideButton(int index, bool bHide)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setHidden(bHide);
    }
    else
    {
    }
}

void QMTAbsHorizontalBtns::SetButtonEnable(int index, bool enable)
{
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setEnabled(enable);
    }
    else
    {
    }
}


bool QMTAbsHorizontalBtns::GetCellChecked(int index)
{
    QWidget* btnWidget = GetButtonWidget(index);
    QAbstractButton* btn = qobject_cast<QAbstractButton*>(btnWidget);

    if (nullptr == btn)
        return false;

    bool bChecked = btn->isChecked();
    return bChecked;
}

void QMTAbsHorizontalBtns::SetButtonStyle(int index, QString style)
{
    //上层应用qrc中有图片资源即可，组件中可以不存在
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        _buttonList[index]->setStyleSheet(style);
    }
    else
    {
    }
}

void QMTAbsHorizontalBtns::SetButtonIcon(int index, QString imagePath)
{
    //上层应用qrc中有图片资源即可，组件中可以不存在
    if (_buttonList.size() > 0 && index < _buttonList.size())
    {
        MtToolButton* toolButton = qobject_cast<MtToolButton*>(_buttonList[index]);

        if (toolButton)
        {
            QPixmap pixmap(imagePath);
            toolButton->setIcon(QIcon(pixmap));
        }
    }
    else
    {
    }
}


void QMTAbsHorizontalBtns::SetChecked(int btnIndex, bool isChecked)
{
    QWidget* widget = GetButtonWidget(btnIndex);

    if (nullptr == widget)
        return;

    QString className = widget->metaObject()->className();

    if (className == "QCheckBox")
    {
        QCheckBox* checkBox = qobject_cast<QCheckBox*>(widget);
        checkBox->setChecked(isChecked);
    }
    else if ("QToolButton" == className)
    {
        QToolButton* toolBtn = qobject_cast<QToolButton*>(widget);
        toolBtn->setChecked(isChecked);
    }
    else if ("MtToolButton" == className)
    {
        MtToolButton* toolBtn = qobject_cast<MtToolButton*>(widget);
        toolBtn->setChecked(isChecked);
    }
}

int QMTAbsHorizontalBtns::GetButtonCount()
{
    return _buttonList.size();
}

void QMTAbsHorizontalBtns::SetPaddingLeft(int size)
{
    if (size > 0)
    {
        QMargins margs = this->contentsMargins();
        margs.setLeft(size);
        this->setContentsMargins(margs);
    }
}

void QMTAbsHorizontalBtns::HideAllButton(bool bHide)
{
    int count = GetButtonCount();

    for (int i = 0; i < count; ++i)
    {
        HideButton(i, bHide);
    }
}

void QMTAbsHorizontalBtns::SetAllButtonEnable(bool bEnable)
{
    int count = GetButtonCount();

    for (int i = 0; i < count; ++i)
    {
        SetButtonEnable(i, bEnable);
    }
}

void QMTAbsHorizontalBtns::SetAllButtonChecked(bool bChecked)
{
    int count = GetButtonCount();

    for (int i = 0; i < count; ++i)
    {
        SetChecked(i, bChecked);
    }
}

void QMTAbsHorizontalBtns::slotButtonClicked(bool isChecked)
{
    QObject* senderObj = this->sender();
    QString className = senderObj->metaObject()->className();
    QWidget* sender = nullptr;

    if (className == "QPushButton")
    {
        sender = qobject_cast<QPushButton*>(senderObj);
    }
    else if (className == "QToolButton")
    {
        sender = qobject_cast<QToolButton*>(senderObj);
    }
    else if (className == "MtToolButton")
    {
        sender = qobject_cast<MtToolButton*>(senderObj);
    }
    else if (className == "QCheckBox")
    {
        sender = qobject_cast<QCheckBox*>(senderObj);
    }

    int index = -1;

    for (int i = 0; i < _buttonList.size(); ++i)
    {
        QWidget* tempButton = _buttonList.at(i);

        if (tempButton == sender)
        {
            index = i;
            break;
        }
    }

    if (index >= 0)
    {
        emit sigButtonClicked(index, isChecked);
    }
}