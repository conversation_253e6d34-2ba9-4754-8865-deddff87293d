﻿// *********************************************************************************
// <remarks>
// FileName    : ModelSettingTable
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : 模型设置列表(适用于: ModelSettingWidget 模型设置)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class ModelSettingTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    ModelSettingTable(QWidget* parent = nullptr);
    ~ModelSettingTable();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="sketchModelList">[IN]模型集合</param>
    void init(const QList<n_mtautodelineationdialog::ST_SketchModel>& sketchModelList);

    /// <summary>
    /// 添加新的模型
    /// <param name="sketchModel">[IN]模型信息</param>
    /// </summary>
    void addNewSketchModel(const n_mtautodelineationdialog::ST_SketchModel& sketchModel);

    /// <summary>
    /// 删除模型
    /// </summary>
    /// <param name="rowValueList">[IN]行唯一标识集合(modelName.id)</param>
    void delSketchModel(const QStringList& rowValueList);

    /// <summary>
    /// 获取勾选的模型唯一标识集合
    /// </summary>
    /// <returns>勾选的模型唯一标识集合(modelName.id)</returns>
    QStringList getCheckedSketchModel();

signals:
    /// <summary>
    /// 编辑按钮点击
    /// <param name="rowValue">[IN]行唯一标识(modelName.id)</param>
    /// </summary>
    void sigEditButtonClicked(const QString rowValue);

    /// <summary>
    /// 行点击
    /// <param name="rowValue">[IN]行唯一标识(modelName.id)</param>
    /// </summary>
    void sigRowClicekd(const QString rowValue);

protected slots:
    /// <summary>
    /// 某一行点击了
    /// </summary>
    void slotRowItemClicked(const QString& rowValue);

    /// <summary>
    /// 某个按键点击了
    /// </summary>
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);


protected:
    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="stSketchModel">[IN]模型信息</param>
    void addRow(const n_mtautodelineationdialog::ST_SketchModel& stSketchModel);

private:
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)
};