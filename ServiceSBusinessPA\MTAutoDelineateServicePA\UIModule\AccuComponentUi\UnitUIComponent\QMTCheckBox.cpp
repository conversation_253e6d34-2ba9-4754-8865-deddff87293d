﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTCheckBox.h"
#include <QHelpEvent>
#include <QToolTip>
#include <QPainter>
#include <QTextLayout>
#include <QTextDocument>
#include <QTextBlock>
#include <QDebug>
#include <QHBoxLayout>


QMTCheckBoxParam::QMTCheckBoxParam()
{
    _cellWidgetType = DELEAGATE_QMTCheckBox;
}

QMTCheckBoxParam::~QMTCheckBoxParam()
{
}

QWidget* QMTCheckBoxParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    if (showType == QMTCheckBoxParam::EM_AlignmentType::Type_LeftJustifying)
    {
        QMTCheckBox* checkBox = new QMTCheckBox(parent);

        if (_mtType > 0)
        {
            checkBox->setMtType((MtCheckBox::MtType)_mtType);
        }

        checkBox->setCheckState((Qt::CheckState)_state);

        if (_width > 0)
            checkBox->setFixedWidth(_width);

        if (_height > 0)
            checkBox->setFixedHeight(_height);

        if (_text.size() > 0)
        {
            checkBox->setText(_text);
        }

        return checkBox;
    }
    else if (showType == QMTCheckBoxParam::EM_AlignmentType::Type_Center)
    {
        QWidget* checkboxWidget = new QWidget(parent);
        QHBoxLayout* AlignmentLayout = new QHBoxLayout(parent);
        AlignmentLayout->setContentsMargins(18, 0, 18, 0);
        AlignmentLayout->setAlignment(Qt::AlignCenter);
        QMTCheckBox* checkBox = new QMTCheckBox(parent);

        if (_mtType > 0)
        {
            checkBox->setMtType((MtCheckBox::MtType)_mtType);
        }

        checkBox->setCheckState((Qt::CheckState)_state);

        if (_width > 0)
            checkBox->setFixedWidth(_width);

        if (_height > 0)
            checkBox->setFixedHeight(_height);

        if (_text.size() > 0)
        {
            checkBox->setText(_text);
        }

        AlignmentLayout->addWidget(checkBox);
        checkboxWidget->setLayout(AlignmentLayout);
        return checkboxWidget;
    }
}

/*********************************************************/
QMTCheckBox::QMTCheckBox(QWidget* parent /*= Q_NULLPTR*/)
    : MtCheckBox(parent)
{
    setMtType(MtCheckBox::checkbox1);
    this->installEventFilter(this);
}

QMTCheckBox::~QMTCheckBox()
{
}

bool QMTCheckBox::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::Int == userType)
    {
        int state = updateData.toInt();
        setCheckState((Qt::CheckState)state);
        return true;
    }

    return false;
}

void QMTCheckBox::SetEnableEdit(bool bEdit)
{
    setEnabled(bEdit);
}

bool QMTCheckBox::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (this == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
        }
    }

    return MtCheckBox::eventFilter(obj, evt);
}

//void QMTCheckBox::mousePressEvent(QMouseEvent* event)
//{
//    if (event->button() == Qt::LeftButton)
//    {
//        emit sigClicked(1);
//    }
//
//    MtCheckBox::mousePressEvent(event);
//}
