<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>AiModelRoiParamEditor</class>
 <widget class="QWidget" name="AiModelRoiParamEditor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>275</width>
    <height>429</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>AiModelRoiParamEditor</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtLineLabel" name="mtLineLabel">
        <property name="text">
         <string>过滤参数</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QGridLayout" name="gridLayout">
        <property name="spacing">
         <number>16</number>
        </property>
        <item row="3" column="1">
         <widget class="MtLineEdit" name="mtLineEdit_4">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>mm³</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="MtLineEdit" name="mtLineEdit_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>层</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="MtLineEdit" name="mtLineEdit">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>个</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="MtLineEdit" name="mtLineEdit_3">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>mm</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="MtLabel" name="lab_minVolume">
          <property name="text">
           <string>连通域最小体积</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="MtLabel" name="lab_minSlice">
          <property name="text">
           <string>连通域内最小层数</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="MtLabel" name="lab_minDiameter">
          <property name="text">
           <string>连通域最小直径</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="MtLabel" name="lab_maxCC3D">
          <property name="text">
           <string>最大连通域数量</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="MtLineLabel" name="mtLineLabel_2">
        <property name="text">
         <string>运算参数</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_4">
        <item>
         <widget class="MtLabel" name="lab_delCpm">
          <property name="text">
           <string>隔层删除</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <property name="spacing">
         <number>16</number>
        </property>
        <item>
         <widget class="MtLabel" name="lab_cpm">
          <property name="text">
           <string>每隔</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtLineEdit" name="mtLineEdit_5">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>层</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtLabel" name="lab_delete">
          <property name="text">
           <string>删除</string>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtLineEdit" name="mtLineEdit_6">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>层</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_5">
        <item>
         <widget class="MtLabel" name="lab_distance">
          <property name="text">
           <string>内缩/外扩</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1</enum>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="spacing">
         <number>16</number>
        </property>
        <item>
         <widget class="MtComboBox" name="cmb_type">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtComboBox::combobox2</enum>
          </property>
          <item>
           <property name="text">
            <string>内缩</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>外扩</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <widget class="MtLineEdit" name="mtLineEdit_7">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="trailingText">
           <string>mm</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLineEdit::lineedit1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <spacer name="verticalSpacer">
        <property name="orientation">
         <enum>Qt::Vertical</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>20</width>
          <height>3</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineLabel</class>
   <extends>QWidget</extends>
   <header>MtLineLabel.h</header>
  </customwidget>
 </customwidgets>
 <tabstops>
  <tabstop>mtLineEdit</tabstop>
  <tabstop>mtLineEdit_2</tabstop>
  <tabstop>mtLineEdit_3</tabstop>
  <tabstop>mtLineEdit_4</tabstop>
  <tabstop>mtLineEdit_5</tabstop>
  <tabstop>mtLineEdit_6</tabstop>
  <tabstop>cmb_type</tabstop>
  <tabstop>mtLineEdit_7</tabstop>
 </tabstops>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
