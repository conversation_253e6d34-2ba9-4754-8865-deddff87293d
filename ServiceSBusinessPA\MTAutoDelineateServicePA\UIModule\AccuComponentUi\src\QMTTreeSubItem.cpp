﻿#include <QColorDialog>
#include <QStandardItemModel>
#include <qDebug>
#include "AccuComponentUi\Header\QMTTreeSubItem.h"
#include "ui_QMTTreeSubItem.h"
#include "AccuComponentUi\Header\QMTInputDialog.h"
#include "CMtCoreDefine.h"

QMTTreeSubItem::QMTTreeSubItem(QWidget* tip, QWidget* parent)
    : QWidget(parent), _TipWidget(tip), _TipWidgetShow(false), ui(nullptr)
{
    ui = new Ui::QMTTreeSubItem;
    ui->setupUi(this);
    _isSelected = false;
    _isCheck = true;
    _isNoData = false;
    _bfill = false;
    ui->textName->SetRegExpStr(QString("[a-zA-Z0-9]+$"));
    _zebraPic = new QLabel(this);
    _zebraPic->setVisible(false);
    _zebraPic->setStyleSheet("background-image:url(:/AccuUIComponentImage/images/zebra.png);");
    _zebraPic->setWindowFlags(Qt::WindowStaysOnBottomHint);
    _backgroundColor = new QLabel(this);
    _backgroundColor->setVisible(false);
    _backgroundColor->setStyleSheet("background-color:rgba(56,67,85,125);");
    _backgroundColor->setWindowFlags(Qt::WindowStaysOnBottomHint);
    //设置样式
    {
        QString styleSheet;
        QString textColor;
        textColor = "color:rgba(219, 226, 241, 1);";
        textColor += QString("font-size:12px;");
        textColor += "font-family: \"Microsoft Yahei\",Arial;";
        styleSheet = "QLabel{" + textColor + ";}";
        styleSheet += "QLineEdit{border-radius:4px;border-style:solid;border-width:1px;background:rgba(37, 41, 48,1);border-color:rgba(37, 41, 48,1);color:rgba(219,226,241,1);}";
        ui->textName->SetMyStyleSheet(styleSheet);
    }
    QString lineStyleStr = "background:rgba(@colorA0,1)";
    CMtCoreWidgetUtil::formatStyleSheet(lineStyleStr);
    ui->line->setStyleSheet(lineStyleStr);
    SetComboBoxSelectStyle(QString());
    SetMenuSelectVisiable(false);
    SetEyeBtnVisible(true);
    setGenerationAlgorithm(QString());
    SetEnableChangeName(false);
    SetEnableChangeColor(false);
    //ui->comboBox_select->hide(); //comboBox使用ui->toolButton_select代替了
    connect(ui->toolButton_color, SIGNAL(clicked()), this, SLOT(slotButtonColorClick()));
    connect(ui->checkBox_show, SIGNAL(clicked()), this, SLOT(slotCheckBoxShowClicked()));
    connect(ui->textName, SIGNAL(sigEditFinish(QString, QString)), this, SLOT(slotTextNameEditFinish(QString, QString)));
    connect(ui->toolButton_select, SIGNAL(clicked()), this, SLOT(slotToolButtonSelectClicked()));
    InitDerivedRoiStyle();
}

QMTTreeSubItem::~QMTTreeSubItem()
{
    //qDebug() << "QMTTreeSubItem::~QMTTreeSubItem()";
    if (m_derivedROIMenu)
    {
        if (nullptr != m_derivedROIMenu->GetMenu())
        {
            m_derivedROIMenu->GetMenu()->aboutToHide();
        }

        delete m_derivedROIMenu;
        m_derivedROIMenu = nullptr;
    }

    MT_DELETE(ui);
}
void QMTTreeSubItem::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (!_subItemProperty._isEnableChangeName)
    {
        QWidget::mouseDoubleClickEvent(event);
    }
    else
    {
        ui->textName->setReadOnly(false);
    }
}
void QMTTreeSubItem::SetCurName(QString& name)
{
    ui->textName->setText(name);
}
void QMTTreeSubItem::ConnectComboBoxSignlas()
{
#if 0
    connect(ui->comboBox_select, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotComboBoxItemTextChange(const QString&)));
    connect(ui->comboBox_select, SIGNAL(currentIndexChanged(int)), this, SLOT(slotComboBoxItemIndexChange(int)));
    connect(ui->comboBox_select, SIGNAL(clicked(int)), this, SLOT(slotComboBoxClicked(int)));
#endif
}
void QMTTreeSubItem::DisConnectComboBoxSignlas()
{
#if 0
    disconnect(ui->comboBox_select, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotComboBoxItemTextChange(const QString&)));
    disconnect(ui->comboBox_select, SIGNAL(currentIndexChanged(int)), this, SLOT(slotComboBoxItemIndexChange(int)));
    disconnect(ui->comboBox_select, SIGNAL(clicked(int)), this, SLOT(slotComboBoxClicked(int)));
#endif
}

void QMTTreeSubItem::SlotClickedDerivedEditMenu()
{
    bClickedMenu = true;
    EM_DerivedROIMenu index = EM_DerivedROIMenu::EditDerivedROIMenu;
    emit SigClickedDerivedMenu(_parentValue, _uniqueValue, index);
}
void QMTTreeSubItem::SlotClickedDerivedUpdateMenu()
{
    bClickedMenu = true;
    EM_DerivedROIMenu index = EM_DerivedROIMenu::UpdateDerivedROIMenu;
    emit SigClickedDerivedMenu(_parentValue, _uniqueValue, index);
}
void QMTTreeSubItem::SlotClickedDerivedCancelMenu()
{
    bClickedMenu = true;
    EM_DerivedROIMenu index = EM_DerivedROIMenu::CancelDerivedROIMenu;
    emit SigClickedDerivedMenu(_parentValue, _uniqueValue, index);
}
void QMTTreeSubItem::SetIsEnabled(bool bEnabled)
{
    m_isEnabled = bEnabled;

    for (int start = EM_DerivedROIMenu::EditDerivedROIMenu; start <= EM_DerivedROIMenu::CancelDerivedROIMenu; start++)
    {
        if (m_isEnabled)
        {
            //可用状态下   最新状态时，更新menu不可用
            if (start == EM_DerivedROIMenu::UpdateDerivedROIMenu && m_roiStatus == DerivedROILatestStatus)
            {
                continue;
            }
        }

        if (m_derivedROIMenu)
        {
            m_derivedROIMenu->SetActionEnable(start, bEnabled);
        }
    }
}
void QMTTreeSubItem::SetDerivedToolTips(const QString& tips)
{
    ui->derivedBtnStatus->SetIconToolTipsFirst(tips);
}
void QMTTreeSubItem::SetDerivedToolTipsEx(const QString& tips)
{
    ui->derivedBtnStatus->SetIconToolTipsSecond(tips);
}


void QMTTreeSubItem::SetTextFirstHidden(bool bHide)
{
    ui->derivedBtnStatus->SetTextFirstHidden(bHide);
}

void QMTTreeSubItem::SetTextSecondHidden(bool bHide)
{
    ui->derivedBtnStatus->SetTextSecondHidden(bHide);
}

void QMTTreeSubItem::InitDerivedRoiStyle()
{
    SetDerivedROIBtnStatus(m_roiStatus);
    ui->derivedBtnStatus->setToolTipMaxWidth(400);
}

QString QMTTreeSubItem::GetPoiType()
{
    return m_poiType;
}

void QMTTreeSubItem::SetDerivedROIBtnStatus(QString derivedRoiBtnStatus)
{
    if (derivedRoiBtnStatus == "")
    {
        ui->derivedBtnStatus->hide();
        ui->DerivedROIBtn->hide();
        return;
    }

    ui->DerivedROIBtn->setPixmapFilename(":/AccuUIComponentImage/images/DerivedROIStatusRowItem.png");
    ui->DerivedROIBtn->setFixedSize(10, 10);

    if (nullptr == m_derivedROIMenu)
    {
        m_derivedROIMenu = new QMTAbstractMenu(this);
        ui->derivedBtnStatus->SetIconToolTips(true);
        connect(ui->DerivedROIBtn, &MtToolButton::clicked, this, &QMTTreeSubItem::SlotMenuClicked);
        m_derivedROIMenu->AddAction(tr("编辑"), MAKE_MENU_CALLBACK(QMTTreeSubItem::SlotClickedDerivedEditMenu));
        m_derivedROIMenu->AddAction(tr("更新"), MAKE_MENU_CALLBACK(QMTTreeSubItem::SlotClickedDerivedUpdateMenu));
        m_derivedROIMenu->AddAction(tr("取消衍生逻辑"), MAKE_MENU_CALLBACK(QMTTreeSubItem::SlotClickedDerivedCancelMenu));
    }

    ui->DerivedROIBtn->show();
    ui->derivedBtnStatus->show();
    m_roiStatus = derivedRoiBtnStatus;

    //不同状态引起的图标变化
    if (m_roiStatus == DerivedROILatestStatus)
    {
        ui->derivedBtnStatus->SetIconToolTipsStatus(tr("最新"));
        ui->derivedBtnStatus->SetIconToolTipsIcon(":/AccuUIComponentImage/images/MtToolBtnTipsIconDerivedLatestStatus.png");
        ui->derivedBtnStatus->setPixmapFilename(":/AccuUIComponentImage/images/MtToolBtnTipsIconDerivedLatestStatus.png");
        ui->derivedBtnStatus->update();

        if (m_isEnabled)
        {
            //最新状态时，菜单：更新置灰
            SetDerivedMenuEnabled(EM_DerivedROIMenu::UpdateDerivedROIMenu, false);
        }
    }
    else if (m_roiStatus == DerivedROINeedUpdateStatus)
    {
        ui->derivedBtnStatus->SetIconToolTipsStatus(tr("需要更新"));
        ui->derivedBtnStatus->SetIconToolTipsIcon(":/AccuUIComponentImage/images/MtToolBtnTipsIconDerivedNeedUpdateStatus.png");
        ui->derivedBtnStatus->setPixmapFilename(":/AccuUIComponentImage/images/MtToolBtnTipsIconDerivedNeedUpdateStatus.png");
        ui->derivedBtnStatus->update();

        if (m_isEnabled)
        {
            //恢复置灰
            SetDerivedMenuEnabled(EM_DerivedROIMenu::UpdateDerivedROIMenu, true);
        }
    }
    else if (m_roiStatus == DerivedROIManualOverlayStatus)
    {
        ui->derivedBtnStatus->SetIconToolTipsStatus(tr("手动覆盖"));
        ui->derivedBtnStatus->SetIconToolTipsIcon(":/AccuUIComponentImage/images/MtToolBtnTipsIconDerivedManualOverLayStatus.png");
        ui->derivedBtnStatus->setPixmapFilename(":/AccuUIComponentImage/images/MtToolBtnTipsIconDerivedManualOverLayStatus.png");
        ui->derivedBtnStatus->update();

        if (m_isEnabled)
        {
            //恢复置灰
            SetDerivedMenuEnabled(EM_DerivedROIMenu::UpdateDerivedROIMenu, true);
        }
    }

    if (!m_isEnabled)
    {
        SetIsEnabled(false);
    }
}

void QMTTreeSubItem::SlotMenuClicked()
{
    if (m_derivedROIMenu)
    {
        m_derivedROIMenu->PopMenu();

        if (bClickedMenu)
        {
            emit SigMenuPopFinished();
            bClickedMenu = false;
        }
    }
}

void QMTTreeSubItem::SetDerivedMenuEnabled(EM_DerivedROIMenu derivedMenuIndex, bool bEnabled)
{
    //外部设置了不可用，则一直不可用
    if (!m_isEnabled)
    {
        bEnabled = false;
    }

    if (m_derivedROIMenu)
    {
        m_derivedROIMenu->SetActionEnable(derivedMenuIndex, bEnabled);
    }
}
void QMTTreeSubItem::slotButtonColorClick()
{
    if (false == _subItemProperty._isEnableChangeColor)
        return;

    //对颜色进行修改
    QColorDialog colorDialog;
    colorDialog.setCurrentColor(_color);
    //colorDialog.setWindowFlags(Qt::Tool | Qt::WindowStaysOnTopHint);//这样设置会导致弹窗样式发生变化
    colorDialog.setWindowIcon(QIcon(":/AccuUIComponentImage/images/logo.png"));
    // colorDialog.setWindowFlags(Qt::FramelessWindowHint);
    colorDialog.setStyleSheet("*{color:#000000}");

    if (colorDialog.exec() == QDialog::Accepted)
    {
        QColor newColor = colorDialog.selectedColor();
        emit sigCurrentColorChange(_parentValue, _uniqueValue, _color, newColor);
        _color = newColor;
        setCheckColor();
    }
}
void QMTTreeSubItem::slotTextNameEditFinish(QString oldText, QString newText)
{
    emit sigCurrentNameChange(_parentValue, oldText, newText);
}
void QMTTreeSubItem::slotComboBoxItemTextChange(const QString& text)
{
    emit sigComboBoxTextChange(_parentValue, _uniqueValue, text);
}
void QMTTreeSubItem::slotComboBoxItemIndexChange(int index)
{
    emit sigComboBoxIndexChange(_uniqueValue, _uniqueValue, index);
}
void QMTTreeSubItem::slotComboBoxClicked(int)
{
}
void QMTTreeSubItem::slotToolButtonSelectClicked()
{
    if (!_onlyMenuHide)
        emit sigSelectButtonClicked(_parentValue, _uniqueValue);
    else
        emit sigSelectButtonClickedButNotShowMenu(_parentValue, _uniqueValue);
}
void QMTTreeSubItem::slotCheckBoxShowClicked()
{
    _isCheck = ui->checkBox_show->isChecked();
    QStringList strList;
    strList.append(_uniqueValue);
    ui->toolButton_color->setEnabled(_isCheck);
    setCheckColor();
    emit sigIsShowItemList(_parentValue, strList, _isCheck);//是否展示
}
void QMTTreeSubItem::setCheckColor()
{
    if (!_isCheck)
    {
        QString strColor;
        strColor = "background-color:rgba(@colorA3, 0.3);";
        strColor += "border:none;";
        QString sheet = "";
        sheet += "QToolButton {"
            + strColor + ";}";
        CMtCoreWidgetUtil::formatStyleSheet(sheet);
        ui->toolButton_color->setStyleSheet(sheet);
    }
    else
    {
        QString strColor;

        if (_isNoData)
        {
            strColor = "background: qlineargradient(x1:0.5, y1:0.5, x2:1, y2:1,stop:0 rgba(@colorA3,0.3), stop:0.1 rgb(" + QString::number(_color.red()) + "," + QString::number(_color.green()) + "," + QString::number(_color.blue())
                + "), stop:1 rgb(" + QString::number(_color.red()) + "," + QString::number(_color.green()) + "," + QString::number(_color.blue()) + "));";
        }
        else
        {
            strColor = "background-color:rgb(" + QString::number(_color.red()) + "," + QString::number(_color.green()) + "," + QString::number(_color.blue()) + ");";
        }

        strColor += "border:none;";
        QString sheet = "";
        sheet += "QToolButton {"
            + strColor + ";}";
        CMtCoreWidgetUtil::formatStyleSheet(sheet);
        ui->toolButton_color->setStyleSheet(sheet);
    }

    UpdateTextNameStyle();
    this->update();
}
void QMTTreeSubItem::setTipWidget(QWidget* pTipWidget)
{
    _TipWidget = pTipWidget;
}
void QMTTreeSubItem::setSelected(bool select)
{
    _isSelected = select;
    setCheckColor();
    setGenerationAlgorithm(_algorithm);
    SetPoiItemIconType(_algorithm);
    UpdateTextNameStyle();
}
bool QMTTreeSubItem::isChecked()
{
    return _isCheck;
    //return ui->checkBox->isChecked();
}
bool QMTTreeSubItem::isNoData()
{
    return _isNoData;
}
bool QMTTreeSubItem::getFill()
{
    return _bfill;
}
QColor QMTTreeSubItem::getColor()
{
    return _color;
}
int QMTTreeSubItem::GetMenuSelectIndex()
{
    return _selectIndex;
}
QString QMTTreeSubItem::GetUserData(int userIndex)
{
    return _userDataMap.value(userIndex);
}
void QMTTreeSubItem::SetName(const QString& str)
{
    ui->textName->setText(str);
}
void QMTTreeSubItem::SetOnlyMenuHide()
{
    _onlyMenuHide = true;
}

void QMTTreeSubItem::hideUI()
{
    ui->checkBox_show->hide();
    ui->label_AI->hide();
    ui->toolButton_color->hide();
    ui->toolButton_select->hide();
    SetEnableEdit(false);
}

void QMTTreeSubItem::setMapString(QString parentValue, QString uniqueValue, QString name, QColor color, QString type)
{
    _parentValue = parentValue;
    _uniqueValue = uniqueValue;
    //_name = name;
    _color = color;
    _type = type;
    //ui->sliderTextWidget->setTextElided(name);
    ui->textName->setText(name);
    QString strColor;

    if (_isNoData)
    {
        strColor = "background: qlineargradient(x1:0.5, y1:0.5, x2:1, y2:1,stop:0 rgba(41,44,53,1), stop:0.1 rgb(" + QString::number(color.red()) + "," + QString::number(color.green()) + "," + QString::number(color.blue())
            + "), stop:1 rgb(" + QString::number(color.red()) + "," + QString::number(color.green()) + "," + QString::number(color.blue()) + "));";
        strColor += "border:1px solid rgba(67,75,87,1)";
    }
    else
    {
        strColor = "background-color:rgb(" + QString::number(_color.red()) + "," + QString::number(_color.green()) + "," + QString::number(_color.blue()) + ");";
        strColor += "border:0px";
    }

    QString sheet = "";
    sheet += "QToolButton {"
        + strColor + ";}";
    ui->toolButton_color->setStyleSheet(sheet);
}
void QMTTreeSubItem::setMapString(QString parentValue, QString uniqueValue, QString name, QColor color, QString type, QString label)
{
    _parentValue = parentValue;
    _uniqueValue = uniqueValue;
    //_name = name;
    _color = color;
    _type = type;
    _label = label;
    //ui->sliderTextWidget->setTextElided(name);
    ui->textName->setText(name);
    QString strColor;

    if (_isNoData)
    {
        strColor = "background: qlineargradient(x1:0.5, y1:0.5, x2:1, y2:1,stop:0 rgba(41,44,53,1), stop:0.1 rgb(" + QString::number(color.red()) + "," + QString::number(color.green()) + "," + QString::number(color.blue())
            + "), stop:1 rgb(" + QString::number(color.red()) + "," + QString::number(color.green()) + "," + QString::number(color.blue()) + "));";
        strColor += "border:1px solid rgba(67,75,87,1)";
    }
    else
    {
        strColor = "background-color:rgb(" + QString::number(_color.red()) + "," + QString::number(_color.green()) + "," + QString::number(_color.blue()) + ");";
        strColor += "border:0px";
    }

    QString sheet = "";
    sheet += "QToolButton {"
        + strColor + ";}";
    ui->toolButton_color->setStyleSheet(sheet);
}
void QMTTreeSubItem::setMapString(const TreeSubItemAddUIInfo& subItemUIInfo)
{
    QColor color = QColor(subItemUIInfo._rgb[0], subItemUIInfo._rgb[1], subItemUIInfo._rgb[2]);
    setNoData(subItemUIInfo._isNoData);
    setMapString(subItemUIInfo._groupKey, subItemUIInfo._uniqueValue, subItemUIInfo._showText, color);
    setGenerationAlgorithm(subItemUIInfo._algorithm);
    SetShowState(subItemUIInfo._isShow);
    SetMenuSelectIndex(subItemUIInfo._comBoxIndex);
    QMap<int, QString>::const_iterator itr = subItemUIInfo._userDataMap.begin();

    for (itr; itr != subItemUIInfo._userDataMap.end(); itr++)
    {
        SetUserData(itr.key(), itr.value());
    }

    //setSelected(subItemUIInfo._isSelect);//这个设置效果初始化的时候暂时无效
}
void QMTTreeSubItem::UpdateUIInfo(const TreeSubItemUpdateInfo& info)
{
    switch (info._optType)
    {
        case TreeSubItemOptType::Opt_IsShow:
        {
            bool isShow = info._uiInfo._isShow;
            setChecked(isShow);
        }
        break;

        case TreeSubItemOptType::Opt_ChangeColor:
        {
            UpdateToolBtnColor(info._uiInfo._rgb);
        }
        break;

        case TreeSubItemOptType::Opt_ColorData:          //ROI是否有数据
        {
            setNoData(info._uiInfo._isNoData);
        }
        break;

        case TreeSubItemOptType::Opt_ChangeName:         //修改名称
        {
            UpdateShowText(info._uiInfo._showText);
        }
        break;

        case TreeSubItemOptType::Opt_ChangeAlgorithm:    //修改AI类型
        {
            setGenerationAlgorithm(info._uiInfo._algorithm);
        }
        break;

        case TreeSubItemOptType::Opt_ComboBoxChange:     //修改下拉框下标
        {
        }
        break;

        default:
            break;
    }
}
void QMTTreeSubItem::UpdateItemColor(QColor& color)
{
    _color = color;
    setCheckColor();
}
void QMTTreeSubItem::SetSubItemProperty(TreeSubItemProperty& itemProperty)
{
    _subItemProperty = itemProperty;
    SetAIiconShow(_subItemProperty._isAIShow);
    SetMenuSelectVisiable(_subItemProperty._isComboBoxVisiable);
    SetEyeBtnVisible(_subItemProperty._isEyeBtnVisiable);
    SetEnableChangeName(_subItemProperty._isEnableChangeName);
    SetEnableChangeColor(_subItemProperty._isEnableChangeColor);
}
void QMTTreeSubItem::SetParentUniqueValue(QString value)
{
    _parentValue = value;
}
void QMTTreeSubItem::setChecked(bool state)
{
    _isCheck = state;
    ui->toolButton_color->setEnabled(state);
    setCheckColor();
    //ui->checkBox->setChecked(state);
}
void QMTTreeSubItem::setNoData(bool state)
{
    _isNoData = state;
    setCheckColor();
}
void QMTTreeSubItem::setFill(bool bfill)
{
    _bfill = bfill;
}
void QMTTreeSubItem::setType(QString type)
{
    _type = type;
}
void QMTTreeSubItem::setLabel(const QString& label)
{
    _label = label;
}
QString QMTTreeSubItem::getType()
{
    return _type;
}
QString QMTTreeSubItem::getLabel()
{
    return _label;
}
bool QMTTreeSubItem::getSelected()
{
    return _isSelected;
}
QString QMTTreeSubItem::getName()
{
    return ui->textName->getText();
}
bool QMTTreeSubItem::getIsShow()
{
    return ui->checkBox_show->isChecked();
}
QString QMTTreeSubItem::getUniqueValue()
{
    return _uniqueValue;
}
QString QMTTreeSubItem::getParentUniqueValue()
{
    return _parentValue;
}
void QMTTreeSubItem::setGenerationAlgorithm(QString algorithm)
{
    _algorithm = algorithm;

    if (true == ui->label_AI->isHidden())
        return;

    if (algorithm == "AUTOMATIC")
    {
        if (_isSelected)
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_AI_selected.png"));
        }
        else
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_AI.png"));
        }
    }
    else if ("MANUAL" == algorithm)
    {
        if (_isSelected)
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_NoAI_selected.png"));
        }
        else
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_NoAI.png"));
        }
    }
    else if ("ISOCENTER" == algorithm)
    {
        if (_isSelected)
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_iso.png"));
        }
        else
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_iso_selected"));
        }
    }
    else if ("RIGID" == algorithm)
    {
        if (_isSelected)
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_regidRegister_selected.png"));
        }
        else
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_regidRegister.png"));
        }
    }
    else if ("DEFORMBLE" == algorithm)
    {
        if (_isSelected)
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_deformable_selected.png"));
        }
        else
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_deformable.png"));
        }
    }
    else
    {
        if (_isSelected)
        {
            if (_selectPixmap.isEmpty())
                _selectPixmap = ":/AccuUIComponentImage/images/icon_NoAI_selected.png";

            ui->label_AI->setPixmap(QPixmap(_selectPixmap));
        }
        else
        {
            if (_unselectPixmap.isEmpty())
                _unselectPixmap = ":/AccuUIComponentImage/images/icon_NoAI.png";

            ui->label_AI->setPixmap(QPixmap(_unselectPixmap));
        }
    }

    update();
}
QString QMTTreeSubItem::getGenerationAlgorithm()
{
    return _algorithm;
}
bool QMTTreeSubItem::GetEnableEdit()
{
    return _subItemProperty._isEnableRightMenu;
}
void QMTTreeSubItem::SetShowState(bool state, bool isEmit)
{
    ui->checkBox_show->setChecked(state);
    _isCheck = state;
    setCheckColor();
}
void QMTTreeSubItem::SetSelectBtnTipString(const QString& tips, const QString& tipsEx)
{
    if (tipsEx.isEmpty())
    {
        ui->toolButton_select->setToolTipText(tips);
    }
    else
    {
        ui->toolButton_select->setToolTipText(tips, tipsEx);
    }
}
void QMTTreeSubItem::SetShowHideBtnTipString(const QString& tips, const QString& tipsEx)
{
    if (tipsEx.isEmpty())
    {
        ui->checkBox_show->setToolTipText(tips);
    }
    else
    {
        ui->checkBox_show->setToolTipText(tips, tipsEx);
    }
}
void QMTTreeSubItem::SetEnableChangeName(bool enable)
{
    _subItemProperty._isEnableChangeName = enable;
    ui->textName->SetEnableEdit(enable);
}
void QMTTreeSubItem::SetEnableChangeColor(bool enable)
{
    _subItemProperty._isEnableChangeColor = enable;
}
void QMTTreeSubItem::SetItemValidator(QValidator* validator)
{
    // ui->lineEdit->setValidator(validator);
    ui->textName->SetItemValidator(validator);
}
void QMTTreeSubItem::SetAIiconShow(bool isShow)
{
    if (isShow)
    {
        ui->label_AI->show();
    }
    else
    {
        ui->label_AI->hide();
    }

    ui->label_AI->setHidden(!isShow);
    update();
}
void QMTTreeSubItem::SetEnableClickeShowBtn(bool enable)
{
    ui->checkBox_show->setEnabled(enable);
}
void QMTTreeSubItem::SetEnableEdit(bool enable)
{
    _subItemProperty._isEnableRightMenu = enable;
    SetEnableClickeShowBtn(enable);
}

void QMTTreeSubItem::SetEyeBtnVisible(bool isShow)
{
    if (false == isShow)
    {
        ui->checkBox_show->hide();
        ui->horizontalSpacer_3->changeSize(0, 20);
    }
    else
    {
        ui->checkBox_show->show();
        ui->horizontalSpacer_3->changeSize(10, 20);
    }
}

void QMTTreeSubItem::SetMenuSelectVisiable(bool isShow)
{
    if (false == isShow)
    {
        ui->toolButton_select->hide();
    }
    else
    {
        ui->toolButton_select->show();
    }
}
void QMTTreeSubItem::SetMenuSelectEnable(bool enable)
{
    //ui->comboBox_select->setEnabled(enable);
    ui->toolButton_select->setEnabled(enable);
}
void QMTTreeSubItem::SetMenuSelectItemEnable(int index, bool enable)
{
    // ui->comboBox_select->SetItemEnable(index, enable);
    if (enable)
    {
        _menuDisableIndexList.removeOne(index);
    }
    else
    {
        if (_menuDisableIndexList.indexOf(index) < 0)
            _menuDisableIndexList.append(index);
    }
}
QList<int> QMTTreeSubItem::GetMenuDisableIndexList()
{
    return _menuDisableIndexList;
}
void QMTTreeSubItem::UpdateToolBtnColor(const int* rgb)
{
    _color = QColor(rgb[0], rgb[1], rgb[2]);
    setCheckColor();
}
void QMTTreeSubItem::UpdateShowText(const QString& text)
{
    ui->textName->setText(text);
}
void QMTTreeSubItem::SetComboBoxSelectStyle(QString styleSheetStr)
{
    if (styleSheetStr.isEmpty())
    {
        QString textColor = "background-color: rgba(37, 41, 48, 1);color: rgb(219, 226, 241);padding-left: 5px;border: 0px solid rgba(72, 83, 100, 1);border - radius: 0px;font - family: \"Microsoft Yahei\", Arial;";
        textColor += QString("font-size:12px;");
        textColor += "font-family: \"Microsoft Yahei\",Arial;";
        styleSheetStr += "QComboBox{" + textColor + ";}";
        styleSheetStr += "QComboBox::drop-down {border: 0;padding-right: 10px;padding-left: 10px;}";
        styleSheetStr += "QComboBox::down-arrow {image:url(:/AccuUIComponentImage/images/icon_pull-down.png);}";
        styleSheetStr += "QComboBox QAbstractItemView {background-color: rgba(45,52,64,1);color: rgb(219,226,241);selection-background-color: #4E9CD5;border:0px solid rgba(72,83,100,1);border-radius:0px;font-size:14px;outline: none;padding-left: 5px;}";
        styleSheetStr += "QComboBox QAbstractItemView::Item {min-height: 24px;font-family: \"Microsoft Yahei\",Arial;}";
    }

    // ui->comboBox_select->setStyleSheet(styleSheetStr);
}
void QMTTreeSubItem::SetAILabelPixmap(QString unselectPix, QString selectPix, QString algorithm /*= ""*/)
{
    if (ui->toolButton_select->isHidden())
        return;

    _unselectPixmap = unselectPix;
    _selectPixmap = selectPix;
    setGenerationAlgorithm(algorithm);
}
void QMTTreeSubItem::SetAILabelTipString(const QString& tip)
{
    if (ui->label_AI->isVisible())
    {
        ui->label_AI->setToolTip(tip);
    }
}


void QMTTreeSubItem::SetPoiItemIconType(const QString& type)
{
    m_poiType = type;

    if (type == "ISOCENTER")
    {
        if (_isSelected)
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_iso_selected.png"));
        }
        else
        {
            ui->label_AI->setPixmap(QPixmap(":/AccuUIComponentImage/images/icon_iso.png"));
        }
    }
    else
    {
        if (_isSelected)
        {
            if (_selectPixmap.isEmpty())
                _selectPixmap = ":/AccuUIComponentImage/images/icon_NoAI_selected.png";

            ui->label_AI->setPixmap(QPixmap(_selectPixmap));
        }
        else
        {
            if (_unselectPixmap.isEmpty())
                _unselectPixmap = ":/AccuUIComponentImage/images/icon_NoAI.png";

            ui->label_AI->setPixmap(QPixmap(_unselectPixmap));
        }
    }
}
void QMTTreeSubItem::SetMenuSelectIndex(int index)
{
    if (index < 0)
    {
        return;
    }

    _selectIndex = index;

    if (_selectIndex < _subItemProperty._menuUnselectIconPathList.size() &&
        _selectIndex < _subItemProperty._menuSelectIconPathList.size())
    {
        QString menuUnselectIconPath = _subItemProperty._menuUnselectIconPathList.at(_selectIndex);
        QString menuSelectIconPath = _subItemProperty._menuSelectIconPathList.at(_selectIndex);
        SetAILabelPixmap(menuUnselectIconPath, menuSelectIconPath);
    }
}
void QMTTreeSubItem::UpdateTextNameStyle()
{
    QString styleSheet;
    QString textColor;

    if (ui->checkBox_show->isChecked() || _isSelected)
    {
        textColor = "color:rgba(219, 226, 241, 1);";
    }
    else
    {
        textColor = "color:rgba(157,165,180,0.6);";
    }

    textColor += QString("font-size:12px;");
    textColor += "font-family: \"Microsoft Yahei\",Arial;";
    styleSheet = "QLabel{" + textColor + ";}";
    styleSheet += "QLineEdit{border-radius:4px;border-style:solid;border-width:1px;background:rgba(37, 41, 48,1);border-color:rgba(37, 41, 48,1);color:rgba(219,226,241,1);}";
    ui->textName->SetMyStyleSheet(styleSheet);
}
void QMTTreeSubItem::SetSchedule(int value)
{
    if (value < 0 || value > 100)
    {
        return;
    }
    else if (value == 100)
    {
        _zebraPic->setVisible(false);
        _backgroundColor->setVisible(false);
    }
    else
    {
        QRect Spacer3 = ui->horizontalSpacer_3->geometry();
        //int width = ui->sliderTextWidget->geometry().right() - Spacer3.left();
        int width = ui->textName->geometry().right() - Spacer3.left();
        int offset = value / 100.0 * width;
        _zebraPic->setGeometry(Spacer3.x(), Spacer3.y() + (Spacer3.height() - 18) / 2, offset, 18);
        _zebraPic->setVisible(true);
        _zebraPic->lower();
        _backgroundColor->setGeometry(Spacer3.x() + offset, Spacer3.y() + (Spacer3.height() - 18) / 2, width - offset, 18);
        _backgroundColor->setVisible(true);
        _backgroundColor->lower();
    }
}
void QMTTreeSubItem::SetUserData(int userIndex, QString data)
{
    _userDataMap.insert(userIndex, data);
}