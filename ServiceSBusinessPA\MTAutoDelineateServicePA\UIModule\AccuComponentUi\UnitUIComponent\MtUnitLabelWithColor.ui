<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MtUnitLabelWithColor</class>
 <widget class="QWidget" name="MtUnitLabelWithColor">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>218</width>
    <height>41</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MtUnitLabelWithColor</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_labelBK" native="true">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>6</number>
      </property>
      <property name="leftMargin">
       <number>1</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtLabel" name="mtLabel_color">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtLabel" name="mtLabel_text">
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
