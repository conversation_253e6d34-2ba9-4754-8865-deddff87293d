<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTTextWidget</class>
 <widget class="QWidget" name="QMTTextWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>233</width>
    <height>43</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTTextWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>1</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>1</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_background" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QLabel_Dot" name="label_text">
        <property name="text">
         <string>RTst</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>QLabel_Dot</class>
   <extends>QLabel</extends>
   <header>AccuComponentUi\Header\qlabel_dot.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
