﻿#pragma once

#include <QObject>
#include <QJsonObject>
#include <QMap>
#include <QColor>
#include <QMetaType>
#include "Language.h"
#include "QMTEnumDef.h"

/*
UI常用的数据结构体
*/

namespace mtuiData
{
/***************一级列表**********************/
//排序状态结构体
typedef struct ST_TableOrderParam
{
    QString _orderKey;                                  //排序的标识
    int _orderState = ButtonOrderState::State_normal;   //0:默认排序，1:升序，2:降序，参照ButtonOrderState

    void Init()
    {
        _orderKey.clear();
        _orderState = 0;
    }
} TableOrderParam;

//一级列表行信息结构体
typedef struct ST_TableWidgetItemIndex
{
    QString _parentValue;           //父唯一标识，适用于二级列表。因为二级列表也是由两个一级列表组成
    QString _uniqueValue;           //行唯一标识
    int _type = 0;                  //行类型，一级列表通过SetRowItemType设置
    int _column = -1;               //列标号
    bool operator == (const ST_TableWidgetItemIndex& data) const
    {
        if (this == &data)
        {
            return true;
        }

        if (this->_parentValue != data._parentValue ||
            this->_uniqueValue != data._uniqueValue ||
            this->_type != data._type)
        {
            return false;
        }

        return true;
    }
    bool operator != (const ST_TableWidgetItemIndex& data) const
    {
        if (this == &data)
        {
            return false;
        }

        return true;
    }
    ST_TableWidgetItemIndex& operator=(const ST_TableWidgetItemIndex& obj)
    {
        // 避免自赋值
        if (this != &obj)
        {
            this->_parentValue = obj._parentValue;
            this->_uniqueValue = obj._uniqueValue;
            this->_type = obj._type;
            this->_column = obj._column;
        }

        return *this;
    }

} TableWidgetItemIndex;
Q_DECLARE_METATYPE(TableWidgetItemIndex);
}

