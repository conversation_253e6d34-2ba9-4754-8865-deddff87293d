﻿// ************************************************************
// <remarks>
// Author      : LiuHualin
// CreateTime  : 2025-09-27
// Description : 自动勾画操作服务接口类（解决多个模块调用自动勾画重复代码的问题）
// </remarks>
// ************************************************************
#pragma once

#include <QObject>
#include <QString>
#include <functional>

#include "MTAlgorithmService/IMTAlgorithmService.h"

#include "MTAutoDelineateServiceDataDefine.h"


/// <summary>
/// 自动勾画操作服务接口类
/// </summary>
class IMTAutoDelineateService
{
public:
    /*********************自动勾画操作**********************/
    /// <summary>
    /// 获取运行自动勾画参数信息
    /// </summary>
    /// <param name="stInputParam">输入参数</param>
    /// <param name="outBusinessParam">[OUT]自动勾画信息QJsonObject</param>
    /// <param name="sketchOrganVec">[OUT]选择的勾画器官信息</param>
    /// <param name="trainOrganVec">[OUT]要勾画的训练器官信息</param>
    /// <param name="emptyOrganVec">[OUT]要勾画的空器官信息</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>执行状态</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual mt_algo::EM_AlgoExecRetType GetDelineateParam(const ST_InputAutoDelineate& stInputParam
                                                          , ST_SketchBusinessInfo& outBusinessParam
                                                          , std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec
                                                          , std::vector<ST_REQ_AutoSketchSuper_Organ>& trainOrganVec
                                                          , std::vector<ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec
                                                          , QString& errMsg) = 0;

    /*********************自动勾画设置窗口**********************/
    /// <summary>
    /// 注册自动勾画状态处理接口，所有的勾画状态（包括取消状态）将返回到该接口处理
    /// </summary>
    /// <param name="moduleId">模块id，用于区别不同模块，不同模块的处理方式可能不一样</param>
    /// <param name="statusCallback">状态回调函数，要使用服务同生命周期函数，调用者在回调中通过参数判断是否进一步处理</param>
    /// <returns>成功返回true</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool RegisterDelineateStatusHandler(const QString& moduleId, std::function<void(const ST_OutputAutoDelineate&)> statusCallback) = 0;

    /// <summary>
    /// 获取勾画模板设置窗口（注意：每次调用都会新建一个窗口）
    /// </summary>
    /// <param name="stInputParam">输入参数</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>模板设置窗口</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual QWidget* GetDelineateTemplateSettingWidget(const ST_InputDelineateTemplateSetting& stInputParam, QString& errMsg) = 0;
    /// <summary>
    /// 初始化勾画模板设置窗口
    /// </summary>
    /// <param name="templateWidget">GetDelineateTemplateSettingWidget接口返回的窗口对象</param>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual void InitDelineateTemplateSettingWidget(QWidget* templateWidget) = 0;
    /// <summary>
    /// 处理勾画模板设置窗口结果（关闭窗口时调用）
    /// </summary>
    /// <param name="templateWidget">GetDelineateTemplateSettingWidget接口返回的窗口对象</param>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual void DealDelineteTemplateWidgetResult(QWidget* templateWidget) = 0;
    /// <summary>
    /// 勾画模板设置窗口是否处于编辑状态
    /// </summary>
    /// <param name="templateWidget">GetDelineateTemplateSettingWidget接口返回的窗口对象</param>
    /// <param name="showErrDlg">是否内部显示弹窗提示</param>
    /// <returns>true:处于编辑状态</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool IsDelineateTemplateWidgetEditState(QWidget* templateWidget, bool showErrDlg = false) = 0;

    /*********************模型ROI设置窗口**********************/

    /// <summary>
    /// 获取模型ROI设置窗口（注意：每次调用都会新建一个窗口）
    /// </summary>
    /// <param name="parentWdgt">父窗口</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>设置窗口</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual QWidget* GetRoiLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg) = 0;
    /// <summary>
    /// 窗口数据是否发送了修改
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:数据修改了</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool IsInfoChanged(QWidget* roiLibraryWidget) = 0;
    /// <summary>
    /// 初始化设置窗口
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <param name="organDefaultConfigInfoPath">器官默认设置配置文件路径</param>
    /// <returns>true:操作成功</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool InitRoiLibraryWidget(QWidget* roiLibraryWidget, const QString& organDefaultConfigInfoPath) = 0;
    /// <summary>
    /// 设置窗口即将销毁（因为销毁窗口时，数据可能还在加载，该接口通知服务停止加载数据）
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual void SetRoiLibraryWidgetDestroying(QWidget* roiLibraryWidget) = 0;
    /// <summary>
    /// 保存设置数据
    /// </summary>
    /// <param name="roiLibraryWidget">GetRoiLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:修改成功</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool SavRoiLibrarySettingData(QWidget* roiLibraryWidget) = 0;

    /*********************标签库设置窗口**********************/

    /// <summary>
    /// 获取标签库设置窗口（注意：每次调用都会新建一个窗口）
    /// </summary>
    /// <param name="parentWdgt">父窗口</param>
    /// <param name="errMsg">错误信息</param>
    /// <returns>设置窗口</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual QWidget* GetLabelLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg) = 0;
    /// <summary>
    /// 窗口数据是否发送了修改
    /// </summary>
    /// <param name="labelLibraryWidget">GetLabelLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:数据修改了</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool IsLabelInfoChanged(QWidget* labelLibraryWidget) = 0;
    /// <summary>
    /// 初始化设置窗口
    /// </summary>
    /// <param name="labelLibraryWidget">GetLabelLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:操作成功</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool InitLabelLibraryWidget(QWidget* labelLibraryWidget) = 0;
    /// <summary>
    /// 保存设置数据
    /// </summary>
    /// <param name="labelLibraryWidget">GetLabelLibrarySettingWidget接口返回的窗口对象</param>
    /// <returns>true:修改成功</returns>
    /// <remarks>[Version]:1.0.1.0 Change: </remarks>
    virtual bool SavLabelLibrarySettingData(QWidget* labelLibraryWidget) = 0;
};
Q_DECLARE_INTERFACE(IMTAutoDelineateService, "org.manteia.IMTAutoDelineateService")
