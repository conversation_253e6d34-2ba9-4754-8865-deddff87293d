﻿#include "AccuComponentUi\Header\UnitUIComponent\QCustMtLineEdit.h"
#include "ui_QCustMtLineEdit.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "CMtCoreDefine.h"
#include <qDebug>

QCustMtLineEditParam::QCustMtLineEditParam()
{
    _cellWidgetType = DELEAGATE_QCustMtLineEdit;
}

QCustMtLineEditParam::~QCustMtLineEditParam()
{
}

QWidget* QCustMtLineEditParam::CreateUIModule(QWidget* parent)
{
    QCustMtLineEdit* lineEdit = new QCustMtLineEdit(parent);
    lineEdit->SetupCellWidget(*this);
    return lineEdit;
}

/*****************************************************************/

QCustMtLineEdit::QCustMtLineEdit(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QCustMtLineEdit;
    ui->setupUi(this);
    ui->lineEdit->setContextMenuPolicy(Qt::NoContextMenu);
    ui->lineEdit->setMtType(MtLineEdit::MtType::lineedit2);
    ui->lineEdit->installEventFilter(this);
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

QCustMtLineEdit::~QCustMtLineEdit()
{
    disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
    MT_DELETE(_validator);
    MT_DELETE(ui);
}

void QCustMtLineEdit::SetupCellWidget(QCustMtLineEditParam& cellWidgetParam)
{
    this->SetRegExpStr(cellWidgetParam._regExpStr);
    this->setText(cellWidgetParam._text);
    _oldText = cellWidgetParam._text;

    if (cellWidgetParam._maxLength > 0)
    {
        ui->lineEdit->setMaxLength(cellWidgetParam._maxLength);
    }

    if (cellWidgetParam._placeholderText.size() > 0)
    {
        ui->lineEdit->setPlaceholderText(cellWidgetParam._placeholderText);
    }
}

bool QCustMtLineEdit::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString str = updateData.toString();
        disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        ui->lineEdit->setText(str);
        _oldText = str;
        connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        return true;
    }
    else if (updateData.canConvert<QCustMtLineEditParam>())
    {
        QCustMtLineEditParam editParam = updateData.value<QCustMtLineEditParam>();
        MtRangeLineEdit::RangeParamInfo inputRange;
        inputRange.minValue = editParam.minValue;
        inputRange.maxValue = editParam.maxValue;
        inputRange.initValue = editParam.initValue;
        inputRange.decimals = editParam.decimals;
        inputRange.bContainMin = editParam.bContainMin;
        inputRange.bContainMax = editParam.bContainMax;
        inputRange.seperateLeftValue = editParam.seperateLeftValue;
        inputRange.seperateRightValue = editParam.seperateRightValue;
        inputRange.unit = editParam.unit;
        ui->lineEdit->SetEditRange(inputRange);
    }
    else if (updateData.canConvert<MtRangeLineEdit::RangeParamInfo>())
    {
        MtRangeLineEdit::RangeParamInfo inputRange = updateData.value<MtRangeLineEdit::RangeParamInfo>();
        inputRange.minValue = inputRange.minValue;
        inputRange.maxValue = inputRange.maxValue;
        inputRange.initValue = inputRange.initValue;
        inputRange.decimals = inputRange.decimals;
        inputRange.bContainMin = inputRange.bContainMin;
        inputRange.bContainMax = inputRange.bContainMax;
        inputRange.seperateLeftValue = inputRange.seperateLeftValue;
        inputRange.seperateRightValue = inputRange.seperateRightValue;
        inputRange.unit = inputRange.unit;
        ui->lineEdit->SetEditRange(inputRange);
    }

    return false;
}

QString QCustMtLineEdit::GetCurText()
{
    return ui->lineEdit->text();
}

void QCustMtLineEdit::SetEnableEdit(bool bEdit)
{
    ui->lineEdit->setEnabled(bEdit);
}

MtRangeLineEdit* QCustMtLineEdit::GetLineEdit()
{
    return ui->lineEdit;
}

void QCustMtLineEdit::SetEditRange(const QCustMtLineEditParam& inputInfo)
{
    MtRangeLineEdit::RangeParamInfo rangeInfo;
    rangeInfo.decimals = inputInfo.decimals;
    rangeInfo.minValue = inputInfo.minValue;
    rangeInfo.maxValue = inputInfo.maxValue;
    rangeInfo.initValue = inputInfo.initValue;
    rangeInfo.bContainMax = inputInfo.bContainMax;
    rangeInfo.bContainMin = inputInfo.bContainMin;
    rangeInfo.disableRangeList = inputInfo.disableRangeList;
    ui->lineEdit->SetEditRange(rangeInfo);
}

void QCustMtLineEdit::SetRegExpStr(QString& regExpStr)
{
    if (0 == regExpStr.size())
        return;

    if (nullptr != _validator)
    {
        delete _validator;
        _validator = nullptr;
    }

    QRegExp regExp(regExpStr);
    _validator = new QRegExpValidator(regExp, this);
    ui->lineEdit->setValidator(_validator);
}

void QCustMtLineEdit::SetItemValidator(QValidator* regExp)
{
    //这边不能对_validator赋值,外面delete后，析构会引起奔溃
    ui->lineEdit->setValidator(_validator);
}

void QCustMtLineEdit::setText(const QString& text)
{
    UpdateLineEditText(text);
}

void QCustMtLineEdit::SetMyStyleSheet(QString& sheetStr)
{
    ui->lineEdit->setStyleSheet(sheetStr);
}

QString QCustMtLineEdit::getText()
{
    return ui->lineEdit->text();
}

bool QCustMtLineEdit::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (ui->lineEdit == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
            bool bEnable = ui->lineEdit->isEnabled();

            if (true == bEnable)
            {
                ui->lineEdit->setFocus();
            }
        }
    }

    return QWidget::eventFilter(obj, evt);
}

//void QCustMtLineEdit::resizeEvent(QResizeEvent* event)
//{
//    int width = this->width();
//    int height = this->height();
//    ui->lineEdit->setFixedWidth(width - 6);
//
//    if (height > 30)
//    {
//        height = 30;
//    }
//
//    ui->lineEdit->setFixedHeight(height - 6);
//    QWidget::resizeEvent(event);
//}

void QCustMtLineEdit::mousePressEvent(QMouseEvent* event)
{
    _oldText = ui->lineEdit->text();
    emit sigClicked(0);
    QWidget::mousePressEvent(event);
}

void QCustMtLineEdit::UpdateLineEditText(const QString& text)
{
    disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
    ui->lineEdit->setText(text);
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

void QCustMtLineEdit::slotLineEditingFinished()
{
    QString newText = ui->lineEdit->text();

    if (_oldText != newText)
    {
        _oldText = newText;
        setFocus(Qt::FocusReason::ActiveWindowFocusReason);     //外部编辑后，可能会把自己杀死，所以这边的赋值必须都是在抛送信号之前。bug 19559
        emit currentTextChanged(newText);
    }
}