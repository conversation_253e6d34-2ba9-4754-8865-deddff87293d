﻿#include "AccuComponentUi\Header\QMTAbstractListView.h"
#include "ui_QMTAbstractListView.h"
#include "CMtCoreDefine.h"

QMTAbstractListView::QMTAbstractListView(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTAbstractListView;
    ui->setupUi(this);
    _contentParent = ui->scrollAreaWidgetContents;
    _headerWidgetList.append(ui->label);
    _headerWidgetList.append(ui->label_2);
    _headerWidgetList.append(ui->label_3);
    _headerWidgetList.append(ui->label_4);
    _headerWidgetList.append(ui->label_5);
    _headerWidgetList.append(ui->label_6);
    _headerWidgetList.append(ui->label_7);

    for (int i = 0; i < _headerWidgetList.size(); ++i)
    {
        QWidget* widget = _headerWidgetList.at(i);
        widget->hide();
    }
}

QMTAbstractListView::~QMTAbstractListView()
{
    MT_DELETE(ui);
}

void QMTAbstractListView::InitListViewParam(QMTPerRowItemWidgetParam& param)
{
    _listViewParam = param;
    SetColumnCount(_listViewParam._defaultColumn);

    for (int i = 0; i < _listViewParam._defaultColumn; ++i)
    {
        int width = _listViewParam._columnWidthMap.value(i);
        SetColumnWidth(i, width);
    }
}

void QMTAbstractListView::SetColumnCount(int columns)
{
    if (columns < 0 || columns >= _headerWidgetList.size())
        return;

    for (int i = 0; i < columns; ++i)
    {
        QWidget* widget = _headerWidgetList.at(i);
        widget->show();
    }
}

void QMTAbstractListView::SetColumnWidth(int column, int width)
{
    if (column < 0 || column >= _headerWidgetList.size())
        return;

    if (width > 0)
    {
        QWidget* widget = _headerWidgetList.at(column);
        widget->setFixedWidth(width);
    }
}

void QMTAbstractListView::SetHorizontalScrollBarPolicy(Qt::ScrollBarPolicy policy)
{
    ui->scrollArea->setHorizontalScrollBarPolicy(policy);
}

bool QMTAbstractListView::SetHorizontalHeaderList(QStringList strList)
{
    if (strList.size() > _headerWidgetList.size())
    {
        return false;
    }

    for (int i = 0; i < strList.size(); ++i)
    {
        QString headStr = strList.at(i);
        _headerWidgetList.at(i)->setText(headStr);
    }

    return true;
}


void QMTAbstractListView::AddRowItem(QJsonObject jsonObj)
{
    bool isExist = false;
    QString mainKey = _listViewParam._mainKey;

    if (mainKey.size() > 0)
    {
        for (int i = 0; i < _widgetUiHashList.size(); ++i)
        {
            QVariantHash tmpHash = _widgetUiHashList.at(i);
            QString value = jsonObj.value(mainKey).toString();
            QString tmpvalue = tmpHash.value(mainKey).toString();

            if (value == tmpvalue)
            {
                isExist = true;
                break;
            }
        }
    }

    if (!isExist)
    {
        QMTAbstractRowItemWithBorder* rowWidget = AddRowItemWidget(jsonObj);
    }
}

void QMTAbstractListView::SetPerRowWidgetSignals(QWidget* widget)
{
    connect(widget, SIGNAL(sigCurrentIndexChanged(int, QString)), this, SLOT(slotCurrentIndexChanged(int, QString)));
}

QMTAbstractRowItemWithBorder* QMTAbstractListView::AddRowItemWidget(QJsonObject jsonObj)
{
    //delete strech
    int count = ((QVBoxLayout*)_contentParent->layout())->count();
    QLayoutItem* child = _contentParent->layout()->itemAt(count - 1);
    _contentParent->layout()->removeItem(child);
    ///add widget
    QVariantHash hashtemp = jsonObj.toVariantHash();
    QMTAbstractRowItemWithBorder* perRowItemWidget = new QMTAbstractRowItemWithBorder(_contentParent);
    SetPerRowWidgetSignals(perRowItemWidget);
    _contentParent->layout()->addWidget(perRowItemWidget);
    perRowItemWidget->InitPerRowWidgetParam(_listViewParam);
    perRowItemWidget->CreateWidgetItem(jsonObj);
    //add widgetUi hash
    QVariantHash widgetUiHash = jsonObj.toVariantHash();
    QVariant widgetUi = QVariant::fromValue(perRowItemWidget);
    widgetUiHash.insert(TOString(widgetUi), widgetUi);
    _widgetUiHashList.append(widgetUiHash);
    //add strech
    ((QVBoxLayout*)_contentParent->layout())->addStretch();
    return perRowItemWidget;
}

void QMTAbstractListView::ResetColumnItemString(int index, int column, QString defalutStr)
{
    QWidget* widget = GetColumnItemWidget(index, column);
    QComboBox* comboBox = qobject_cast<QComboBox*>(widget);

    if (comboBox)
    {
        if (defalutStr.size() > 0)
        {
            comboBox->setCurrentText(defalutStr);
        }
        else
        {
            comboBox->setCurrentText("NONE");
        }
    }
}

QWidget* QMTAbstractListView::GetRowItemWidget(int index)
{
    int row = index;

    if (row >= _widgetUiHashList.size() || index < 0)
        return NULL;

    QVariantHash record = _widgetUiHashList.at(row);
    QMTAbstractRowItemWithBorder* widget = record.value(TOString(widgetUi)).value<QMTAbstractRowItemWithBorder*>();
    return widget;
}

QWidget* QMTAbstractListView::GetColumnItemWidget(int index, int column)
{
    QWidget* widget = GetRowItemWidget(index);
    QMTAbstractRowItemWithBorder* rowItemWidget = qobject_cast<QMTAbstractRowItemWithBorder*>(widget);

    if (rowItemWidget)
    {
        widget = rowItemWidget->GetColumnWidget(column);
    }
    else
    {
        widget = nullptr;
    }

    return widget;
}

QString QMTAbstractListView::GetColumnItemString(int index, int column)
{
    QWidget* widget = GetColumnItemWidget(index, column);
    int type = _listViewParam._colDelegateTypeMap.value(column);
    QString text;

    if (Type_Label == type)
    {
        QLabel* label = qobject_cast<QLabel*>(widget);

        if (label)
        {
            text = label->text();
        }
    }
    else if (Type_ComboBox == type)
    {
        QComboBox* comboBox = qobject_cast<QComboBox*>(widget);

        if (comboBox)
        {
            text = comboBox->currentText();
        }
    }

    return text;
}

int QMTAbstractListView::GetRowCount()
{
    return _widgetUiHashList.size();
}

int QMTAbstractListView::GetColumnCount()
{
    return _listViewParam._defaultColumn;
}

QStringList QMTAbstractListView::GetHeadList()
{
    QStringList ret;

    for (int i = 0; i < _listViewParam._defaultColumn; ++i)
    {
        QWidget* headWidget = _headerWidgetList.at(i);
        QLabel* headLabel = qobject_cast<QLabel*>(headWidget);
        QString headStr = headLabel->text();
        ret.append(headStr);
    }

    return ret;
}

void QMTAbstractListView::slotCurrentIndexChanged(int column, QString text)
{
}