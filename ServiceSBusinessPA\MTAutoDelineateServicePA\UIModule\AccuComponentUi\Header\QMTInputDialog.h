﻿#ifndef QMTInputDialog_H
#define QMTInputDialog_H

#include <QDialog>
#include "Language.h"
namespace Ui
{
class QMTInputDialog;
};
class  QMTInputDialog : public QDialog
{
    Q_OBJECT

public:
    QMTInputDialog(QWidget* parent = 0);
    ~QMTInputDialog();
    void EnableChangeColor(bool);
    void SetValidator(QString);//正则表达式
    void SetValidator(const QValidator*);
    void SetDialogTitle(QString);//title text
    void SetNameText(QString);//name text
    void SetTextValue(const QString& value); //lineedit value
    QString NameValue();
    QColor GetColor();
    void setRoi(QString name, int* color);
    QString _name;
    int _rgb[3];
    bool _ok;
    bool _bExec = false;

public slots:
    void on_pushButton_edit_clicked();
    void on_pushButton_color_clicked();
    void on_pushButton_cancel_clicked();
    int exec();

private:
    Ui::QMTInputDialog* ui;
    int _ROINameFormat;
};

#endif // QMTInputDialog_H
