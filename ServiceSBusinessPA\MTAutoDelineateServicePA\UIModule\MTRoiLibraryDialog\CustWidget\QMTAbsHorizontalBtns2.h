﻿#pragma once

#include <QWidget>
#include <QPushButton>
#include <QToolButton>
#include <QMap>
#include "AccuComponentUi\Header\QMTUIModuleParam.h"

namespace Ui
{
class QMTAbsHorizontalBtns2;
}

//QMTAbsHorizontalBtns2 参数
class QMTAbsHorizontalBtns2Param : public ICellWidgetParam
{
public:
    bool _checkable = false;                //是否有checked状态
    //bool _isChecked = false;
    int _btnCount = 1;                      //按键个数
    QStringList _pixPathList;               //unchecked状态图标路径
    QStringList _hoverPixPathList;          //unchecked状态悬浮的时候图标路径
    QStringList _disablePixPathList;        //设置成disable的时候的图标
    QStringList _checkedPixPathList;        //check状态图标路径
    QStringList _hoverCheckedPixPathList;   //checked状态悬浮的时候图标路径

    QColor _btnBackgroundColor = QColor(255, 255, 255);
    int _layoutSpacing = 8;
    //int _paddingLeft = 7;
    int _width = 24;
    int _height = 24;
    QList<int> _hideBtnIndexList;                    //隐藏的按键
    QList<int> _disableBtnList;                      //disable的按键集合

    QMTAbsHorizontalBtns2Param();
    ~QMTAbsHorizontalBtns2Param();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTAbsHorizontalBtns2Param)

/*水平方向按键集合*/
class QMTAbsHorizontalBtns2 : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    typedef struct ST_HorizontalBtnsUpdateInfo
    {
        QList<int> _hideBtnIndexList;                    //隐藏的按键
        QList<int> _disableBtnList;                      //disable的按键集合
        QList<int> _checkedBtnList;                      //checked的按键集合
    } HorizontalBtnsUpdateInfo;

    typedef struct ST_OneButtonUpdateInfo
    {
        int btnIndex = 0;
        bool bHide = false;         //隐藏/显示
        bool bEnable = true;       //使能
        bool bChecked = false;      //checked状态
    } OneButtonUpdateInfo;

    QMTAbsHorizontalBtns2(QWidget* parent = Q_NULLPTR);
    ~QMTAbsHorizontalBtns2();

    /*单元格公共接口*/
    //更新界面接口
    virtual bool UpdateUi(const QVariant& updateData);
    //按键使能设置
    virtual void SetButtonEnable(int btnIndex/**/, bool bEnable);
    void SetEnableEdit(bool bEdit);
    //获取checked
    virtual bool GetCellChecked(int index);
    void SetupCellWidget(QMTAbsHorizontalBtns2Param&);


    /******************ui************************/
    void SetCustStyleSheet(QString& styleStr);          //设置自定义样式
    void SetBtnsBackground(QColor&);                    //设置widget背景色
    void SetBackgroundColor(QColor& color);             //设置按键背景色
    void SetButtonSize(int btnIndex, QSize size);       //设置按键大小
    void SetButtonStyle(int btnIndex, QString styleStr); //设置某个按键样式
    void SetButtonIcon(int btnIndex, QString iconPath);  //设置按键的Icon
    void SetPaddingLeft(int leftPadding);

    /******************set************************/
    void HideAllButton(bool bHide);             //是否隐藏所有按键
    void SetAllButtonEnable(bool bEnable);      //设置所有按键是否使能
    void SetAllButtonChecked(bool bChecked);    //设置所有按键checked状态

    void SetButtonText(int btnIndex, QString& text);    //设置某个按键的文案
    void HideButton(int btnIndex, bool bHide);          //是否隐藏按键
    //void SetButtonEnable(int btnIndex, bool bEnable);   //设置所有按键是否使能(使用SetButtonEnable)
    void SetChecked(int btnIndex, bool isChecked);      //设置按键checked状态

    /*********************get**************************/
    int GetButtonCount();       //获取按键个数
    QWidget* GetButtonWidget(int index);

signals:
    void sigButtonClicked(int/*btnIndex*/, bool/*ischecked*/);  //某个按键点击了

protected:
    /*******************delete*************************/
    void DeleteButtons();
    QString GetBtnSheetStr(int index);              //获取按键的样式
    QString GetCheckBoxSheetStr(int index);         //获取checkbox的样式

private slots:
    void slotButtonClicked(bool);
private:
    Ui::QMTAbsHorizontalBtns2* ui;
    QMTAbsHorizontalBtns2Param _btnsWidgetParam;
    QList<QWidget*> _buttonList;
    // int _count = 0;
};

Q_DECLARE_METATYPE(QMTAbsHorizontalBtns2::HorizontalBtnsUpdateInfo);
Q_DECLARE_METATYPE(QMTAbsHorizontalBtns2::OneButtonUpdateInfo);