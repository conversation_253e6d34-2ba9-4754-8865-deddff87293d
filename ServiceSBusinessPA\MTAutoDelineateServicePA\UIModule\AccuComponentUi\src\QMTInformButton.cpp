﻿#include "AccuComponentUi\Header\QMTInformButton.h"
#include "ui_QMTInformButton.h"
#include "CMtCoreDefine.h"

QMTInformButton::QMTInformButton(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTInformButton;
    ui->setupUi(this);
    ResetValue();
    connect(ui->pushButton, SIGNAL(clicked()), this, SLOT(slotButtonClicked()));
}

QMTInformButton::~QMTInformButton()
{
    MT_DELETE(ui);
}

void QMTInformButton::SetValue(int value)
{
    bool isvisiable = ui->widget_bubble->isVisible();

    if (value > 0 && !isvisiable)
    {
        ui->widget_bubble->show();
    }

    ui->widget_bubble->setValue(value);
    //ui->widget_bubble->update();
}

void QMTInformButton::AddOneValue(bool isWarn)
{
    int value = ui->widget_bubble->GetValue();

    if (isWarn)
    {
        _isWarn = isWarn;
        QColor color(217, 0, 27);
        SetBackColor(color);
    }
    else if (false == _isWarn)
    {
        QColor color(0, 0, 255);
        SetBackColor(color);
    }

    SetValue(++value);
}

void QMTInformButton::DeleteOneValue()
{
    int value = ui->widget_bubble->GetValue();
    value--;

    if (value >= 0)
    {
        SetValue(value);
    }
}

void QMTInformButton::ResetValue()
{
    _isWarn = false;
    ui->widget_bubble->setValue(0);
    ui->widget_bubble->hide();
}

void QMTInformButton::SetBackColor(QColor color)
{
    ui->widget_bubble->SetArcColor(color);
}

void QMTInformButton::mousePressEvent(QMouseEvent* event)
{
    emit sigInformClicked();
}

void QMTInformButton::slotButtonClicked()
{
#if 0
    static int index = 3;
    ++index;

    if (6 == index)
    {
        AddOneValue(true);
    }
    else
    {
        AddOneValue();
    }

#endif
    emit sigInformClicked();
}