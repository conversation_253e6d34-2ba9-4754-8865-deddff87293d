<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GroupItemClass</class>
 <widget class="QWidget" name="GroupItemClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>42</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>GroupItem</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="MtStackedWidget" name="mtStackedWidget">
     <widget class="QWidget" name="page_label">
      <layout class="QVBoxLayout" name="verticalLayout_pageLabel">
       <property name="spacing">
        <number>16</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>16</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QGridLayout" name="gridLayout_pageLabel">
         <property name="sizeConstraint">
          <enum>QLayout::SetDefaultConstraint</enum>
         </property>
         <property name="leftMargin">
          <number>9</number>
         </property>
         <property name="spacing">
          <number>0</number>
         </property>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_check">
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="spacing">
        <number>16</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>16</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QGridLayout" name="gridLayout_pageCheck">
         <property name="leftMargin">
          <number>21</number>
         </property>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtStackedWidget</class>
   <extends>QStackedWidget</extends>
   <header>MtStackedWidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
