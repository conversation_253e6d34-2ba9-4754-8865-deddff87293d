﻿#include "QCustMtLineEdit2.h"
#include "ui_QCustMtLineEdit2.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "CMtCoreDefine.h"
#include <qDebug>

QCustMtLineEdit2Param::QCustMtLineEdit2Param()
{
    _cellWidgetType = DELEAGATE_QCustMtLineEdit;
}

QCustMtLineEdit2Param::~QCustMtLineEdit2Param()
{
}

QWidget* QCustMtLineEdit2Param::CreateUIModule(QWidget* parent)
{
    QCustMtLineEdit2* lineEdit = new QCustMtLineEdit2(parent);
    lineEdit->SetupCellWidget(*this);
    return lineEdit;
}

/*****************************************************************/

QCustMtLineEdit2::QCustMtLineEdit2(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QCustMtLineEdit2;
    ui->setupUi(this);
    ui->lineEdit->setContextMenuPolicy(Qt::NoContextMenu);
    ui->lineEdit->setMtType(MtLineEdit::MtType::lineedit2);
    ui->lineEdit->installEventFilter(this);
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

QCustMtLineEdit2::~QCustMtLineEdit2()
{
    disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
    MT_DELETE(_validator);
    MT_DELETE(ui);
}

void QCustMtLineEdit2::SetupCellWidget(QCustMtLineEdit2Param& cellWidgetParam)
{
    this->SetRegExpStr(cellWidgetParam._regExpStr);
    this->setText(cellWidgetParam._text);
    _oldText = cellWidgetParam._text;

    if (cellWidgetParam._maxLength > 0)
    {
        ui->lineEdit->setMaxLength(cellWidgetParam._maxLength);
    }

    if (cellWidgetParam._placeholderText.size() > 0)
    {
        ui->lineEdit->setPlaceholderText(cellWidgetParam._placeholderText);
    }
}

bool QCustMtLineEdit2::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString str = updateData.toString();
        disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        ui->lineEdit->setText(str);
        _oldText = str;
        connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        return true;
    }
    else if (updateData.canConvert<QCustMtLineEdit2Param>())
    {
        QCustMtLineEdit2Param editParam = updateData.value<QCustMtLineEdit2Param>();
        MtRangeLineEdit::RangeParamInfo inputRange;
        inputRange.minValue = editParam.minValue;
        inputRange.maxValue = editParam.maxValue;
        inputRange.initValue = editParam.initValue;
        inputRange.decimals = editParam.decimals;
        inputRange.bContainMin = editParam.bContainMin;
        inputRange.bContainMax = editParam.bContainMax;
        inputRange.seperateLeftValue = editParam.seperateLeftValue;
        inputRange.seperateRightValue = editParam.seperateRightValue;
        inputRange.unit = editParam.unit;
        ui->lineEdit->SetEditRange(inputRange);
    }
    else if (updateData.canConvert<MtRangeLineEdit::RangeParamInfo>())
    {
        MtRangeLineEdit::RangeParamInfo inputRange = updateData.value<MtRangeLineEdit::RangeParamInfo>();
        inputRange.minValue = inputRange.minValue;
        inputRange.maxValue = inputRange.maxValue;
        inputRange.initValue = inputRange.initValue;
        inputRange.decimals = inputRange.decimals;
        inputRange.bContainMin = inputRange.bContainMin;
        inputRange.bContainMax = inputRange.bContainMax;
        inputRange.seperateLeftValue = inputRange.seperateLeftValue;
        inputRange.seperateRightValue = inputRange.seperateRightValue;
        inputRange.unit = inputRange.unit;
        ui->lineEdit->SetEditRange(inputRange);
    }

    return false;
}

QString QCustMtLineEdit2::GetCurText()
{
    return ui->lineEdit->text();
}

void QCustMtLineEdit2::SetEnableEdit(bool bEdit)
{
    ui->lineEdit->setEnabled(bEdit);
}

MtRangeLineEdit* QCustMtLineEdit2::GetLineEdit()
{
    return ui->lineEdit;
}

void QCustMtLineEdit2::SetEditRange(const QCustMtLineEdit2Param& inputInfo)
{
    MtRangeLineEdit::RangeParamInfo rangeInfo;
    rangeInfo.decimals = inputInfo.decimals;
    rangeInfo.minValue = inputInfo.minValue;
    rangeInfo.maxValue = inputInfo.maxValue;
    rangeInfo.initValue = inputInfo.initValue;
    rangeInfo.bContainMax = inputInfo.bContainMax;
    rangeInfo.bContainMin = inputInfo.bContainMin;
    rangeInfo.disableRangeList = inputInfo.disableRangeList;
    ui->lineEdit->SetEditRange(rangeInfo);
}

void QCustMtLineEdit2::SetRegExpStr(QString& regExpStr)
{
    if (0 == regExpStr.size())
        return;

    if (nullptr != _validator)
    {
        delete _validator;
        _validator = nullptr;
    }

    QRegExp regExp(regExpStr);
    _validator = new QRegExpValidator(regExp, this);
    ui->lineEdit->setValidator(_validator);
}

void QCustMtLineEdit2::SetItemValidator(QValidator* regExp)
{
    //这边不能对_validator赋值,外面delete后，析构会引起奔溃
    ui->lineEdit->setValidator(_validator);
}

void QCustMtLineEdit2::setText(const QString& text)
{
    UpdateLineEditText(text);
}

void QCustMtLineEdit2::SetMyStyleSheet(QString& sheetStr)
{
    ui->lineEdit->setStyleSheet(sheetStr);
}

QString QCustMtLineEdit2::getText()
{
    return ui->lineEdit->text();
}

bool QCustMtLineEdit2::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (ui->lineEdit == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
            bool bEnable = ui->lineEdit->isEnabled();

            if (true == bEnable)
            {
                ui->lineEdit->setFocus();
            }
        }
    }

    return QWidget::eventFilter(obj, evt);
}

//void QCustMtLineEdit2::resizeEvent(QResizeEvent* event)
//{
//    int width = this->width();
//    int height = this->height();
//    ui->lineEdit->setFixedWidth(width - 6);
//
//    if (height > 30)
//    {
//        height = 30;
//    }
//
//    ui->lineEdit->setFixedHeight(height - 6);
//    QWidget::resizeEvent(event);
//}

void QCustMtLineEdit2::mousePressEvent(QMouseEvent* event)
{
    _oldText = ui->lineEdit->text();
    emit sigClicked(0);
    QWidget::mousePressEvent(event);
}

void QCustMtLineEdit2::UpdateLineEditText(const QString& text)
{
    disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
    ui->lineEdit->setText(text);
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

void QCustMtLineEdit2::slotLineEditingFinished()
{
    QString newText = ui->lineEdit->text();

    if (_oldText != newText)
    {
        _oldText = newText;
        setFocus(Qt::FocusReason::ActiveWindowFocusReason);     //外部编辑后，可能会把自己杀死，所以这边的赋值必须都是在抛送信号之前。bug 19559
        emit currentTextChanged(newText);
    }
}