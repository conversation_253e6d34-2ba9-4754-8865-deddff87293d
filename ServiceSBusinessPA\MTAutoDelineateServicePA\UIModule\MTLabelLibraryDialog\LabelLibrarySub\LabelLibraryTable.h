﻿// *********************************************************************************
// <remarks>
// FileName    : LabelLibraryTable
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : 标签库设置列表(适用于: LabelLibraryWidget 标签库设置)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class LabelLibraryTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum
    {
        Col_Lable = 0,
        Col_ROIName,
        Col_ROIChName,
        Col_ROIType,
        Col_Color,
        Col_AliasName,
        Col_Operate
    };
    /// <summary>
    /// 构造函数
    /// </summary>
    LabelLibraryTable(QWidget* parent = nullptr);
    ~LabelLibraryTable();

    /// <summary>
    /// 列表是否初始化完成
    /// </summary>
    bool isTableInitialized();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="roiTypeList">[IN]Roi类型集合</param>
    /// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
    void init(const QStringList& roiTypeList, const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 添加新的manteiaRoiLabel
    /// </summary>
    void addNewManteiaRoiLabel();

    /// <summary>
    /// 更新/新增manteiaRoiLabel
    /// </summary>
    /// <param name="info">[IN]待更新数据</param>
    /// <param name="outUpdateNum">[IN]更新了几个</param>
    /// <param name="outNewNum">[IN]新增了几个</param>
    void updateManteiaRoiLabel(const n_mtautodelineationdialog::ST_RoiLabelInfo& info, int& outUpdateNum, int& outNewNum);

    /// <summary>
    /// 删除当前行
    /// </summary>
    void delCurrentRow();

    /// <summary>
    /// 隐藏或展示行
    /// </summary>
    /// <param name="labelOrName">[IN]manteiaRoiLabel或roiName</param>
    void hideShowRow(const QString& labelOrName);

    /// <summary>
    /// 隐藏标签列表中的几列.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="columnIndexVec">要隐藏的列索引，从0开始</param>
    /// <param name="bHide">是否隐藏</param>
    void hideLabelListColumn(const QVector<int>& columnIndexVec, bool bHide = true);

    /// <summary>
    /// 设置列是否可以编辑
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="columnIndexVec">要设置的列索引，从0开始</param>
    /// <param name="bEnable">是否可编辑</param>
    void enableLabelListColumn(const QVector<int> columnIndexVec, bool bEnable = true);

    /// <summary>
    /// 获取所有manteiaRoiLabel信息
    /// </summary>
    /// <returns>所有manteiaRoiLabel信息</returns>
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> getAllRoiLabelInfo();

signals:
    /// <summary>
    /// 列表信息发生了改变
    /// </summary>
    void sigTableInfoChanged();

    /// <summary>
    /// 创建列表Item信号
    /// </summary>
    void sigCreateTableRowItem(const n_mtautodelineationdialog::ST_RoiLabelInfo& organInfo, bool bLast);

    /// <summary>
    /// 列表初始化完成信号
    /// </summary>
    void sigTableInitialized();

protected slots:
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);

    void slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText);

    /// <summary>
    /// 添加一条记录
    /// </summary>
    void slotCreateTableRowItem(const n_mtautodelineationdialog::ST_RoiLabelInfo& labelInfo, bool bLast);

protected:
    /// <summary>
    /// 重载单元格创建完成后回调
    /// </summary>
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget);

    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <param name="headList">[IN]表头文本集合</param>
    /// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="stEmptyOrgan">[IN]manteiaRoiLabel信息</param>
    void addRow(const n_mtautodelineationdialog::ST_RoiLabelInfo& stRoiLabelInfo);

private:
    QStringList m_roiTypeList;      //Roi类型集合
    QStringList m_manteiaRoiLabelListSort; //manteiaRoiLabel集合(用于排序,只增不减)
    QMap<QString, n_mtautodelineationdialog::ST_RoiLabelInfo> m_allRoiLibraryMap;   //全部manteiaRoiLabel信息集合(key-manteiaRoiLabel-toLower())

    bool m_bInitialized;    //指示列表是否初始化完成
    bool m_bDestroyed;      //指示列表是否被销毁
};