<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QCustColorButton</class>
 <widget class="QWidget" name="QCustColorButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>24</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QCustColorButton</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>16</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>16</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
