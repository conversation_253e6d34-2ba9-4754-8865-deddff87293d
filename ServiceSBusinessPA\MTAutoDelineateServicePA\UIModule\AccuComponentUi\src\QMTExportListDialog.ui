<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTExportListDialog</class>
 <widget class="QWidget" name="QMTExportListDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>349</width>
    <height>813</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QMTExportListDialog</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::frameEx2</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_5">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget" native="true">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>90</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>14</number>
         </property>
         <property name="rightMargin">
          <number>11</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="widget_3" native="true">
           <property name="maximumSize">
            <size>
             <width>16777215</width>
             <height>16777215</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>13</number>
            </property>
            <property name="topMargin">
             <number>5</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_4">
              <property name="spacing">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>20</number>
              </property>
              <item>
               <widget class="MtLabel" name="label_title">
                <property name="minimumSize">
                 <size>
                  <width>98</width>
                  <height>22</height>
                 </size>
                </property>
                <property name="text">
                 <string>title</string>
                </property>
                <property name="elideMode">
                 <enum>Qt::ElideRight</enum>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtLabel::myLabel3</enum>
                </property>
               </widget>
              </item>
              <item>
               <spacer name="horizontalSpacer_2">
                <property name="orientation">
                 <enum>Qt::Horizontal</enum>
                </property>
                <property name="sizeHint" stdset="0">
                 <size>
                  <width>30</width>
                  <height>16</height>
                 </size>
                </property>
               </spacer>
              </item>
              <item>
               <widget class="MtToolButton" name="btn_export_2">
                <property name="minimumSize">
                 <size>
                  <width>16</width>
                  <height>16</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16</width>
                  <height>16</height>
                 </size>
                </property>
                <property name="cursor">
                 <cursorShape>PointingHandCursor</cursorShape>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="pixmapFilename">
                 <string notr="true">:/AccuUIComponentImage/images/export.png</string>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtToolButton::toolbutton5</enum>
                </property>
               </widget>
              </item>
             </layout>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QWidget" name="widget_right" native="true">
           <layout class="QVBoxLayout" name="verticalLayout_3">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>8</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <layout class="QHBoxLayout" name="horizontalLayout_2">
              <property name="spacing">
               <number>27</number>
              </property>
              <property name="leftMargin">
               <number>7</number>
              </property>
              <item>
               <widget class="MtToolButton" name="btn_showAndHide">
                <property name="minimumSize">
                 <size>
                  <width>16</width>
                  <height>16</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16</width>
                  <height>16</height>
                 </size>
                </property>
                <property name="cursor">
                 <cursorShape>PointingHandCursor</cursorShape>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="pixmapFilename">
                 <string notr="true">:/AccuUIComponentImage/images/reduce.png</string>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtToolButton::toolbutton5</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="MtToolButton" name="btn_close">
                <property name="minimumSize">
                 <size>
                  <width>16</width>
                  <height>16</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16</width>
                  <height>16</height>
                 </size>
                </property>
                <property name="cursor">
                 <cursorShape>PointingHandCursor</cursorShape>
                </property>
                <property name="text">
                 <string/>
                </property>
                <property name="pixmapFilename">
                 <string notr="true">:/Manteia.Domain.Widget/images/dialog_close.png</string>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtToolButton::toolbutton5</enum>
                </property>
               </widget>
              </item>
             </layout>
            </item>
            <item>
             <widget class="QWidget" name="widget_Image" native="true">
              <layout class="QVBoxLayout" name="verticalLayout_4">
               <property name="spacing">
                <number>0</number>
               </property>
               <property name="leftMargin">
                <number>7</number>
               </property>
               <property name="topMargin">
                <number>0</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="MtLabel" name="label_image">
                 <property name="minimumSize">
                  <size>
                   <width>78</width>
                   <height>40</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>Image</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="elideMode">
                  <enum>Qt::ElideRight</enum>
                 </property>
                 <property name="_mtType" stdset="0">
                  <enum>MtLabel::myLabel1</enum>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="MtLabel" name="label_title2">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>20</height>
                  </size>
                 </property>
                 <property name="text">
                  <string>title 2</string>
                 </property>
                 <property name="alignment">
                  <set>Qt::AlignCenter</set>
                 </property>
                 <property name="elideMode">
                  <enum>Qt::ElideRight</enum>
                 </property>
                 <property name="_mtType" stdset="0">
                  <enum>MtLabel::myLabel1</enum>
                 </property>
                </widget>
               </item>
              </layout>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="background" native="true">
        <layout class="QVBoxLayout" name="verticalLayout_2">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>13</number>
         </property>
         <property name="topMargin">
          <number>14</number>
         </property>
         <property name="rightMargin">
          <number>11</number>
         </property>
         <property name="bottomMargin">
          <number>15</number>
         </property>
         <item>
          <widget class="QMTAbstractTableView" name="perlistView" native="true"/>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>QMTAbstractTableView</class>
   <extends>QWidget</extends>
   <header>AccuComponentUi\Header\qmtabstracttableview.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
