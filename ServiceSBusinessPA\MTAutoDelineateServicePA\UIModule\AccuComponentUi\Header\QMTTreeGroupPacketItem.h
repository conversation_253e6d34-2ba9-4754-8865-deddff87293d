﻿// ************************************************************
// <remarks>
// Author      : Chenguanhua
// CreateTime  : 2024-11-22
// Description : ROI列表中的小分组，如靶区和非靶区
// </remarks>
// ************************************************************
#pragma once

#include <QWidget>

#include "QMTTreeSubItem.h"

namespace Ui
{
class QMTTreeGroupPacketItem;
}

/// <summary>
/// Class
/// </summary>
class  QMTTreeGroupPacketItem : public QWidget
{
    Q_OBJECT

public:
    QMTTreeGroupPacketItem(QString uniqueValue, QWidget* parent = 0);
    /// <summary>
    /// Finalizes an instance of the <see cref="QMTTreeGroupPacketItem"/> class.
    /// </summary>
    ~QMTTreeGroupPacketItem();
    /// <summary>
    /// Initializes the tree group packet item.
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void InitTreeGroupPacketItem();
    /// <summary>
    /// 设置显示名称
    /// </summary>
    /// <param name="name">The name.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void setName(QString name);
    /// <summary>
    /// 获取显示的名称
    /// </summary>
    /// <returns>QString.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QString getName();
    /// <summary>
    /// 获取是否展开/收起
    /// </summary>
    /// <returns>bool.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool GetGroupExpand();
    /// <summary>
    /// 设置该节点下的数量
    /// </summary>
    /// <param name="ItemNum">The item number.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetItemNum(int ItemNum);
    /// <summary>
    /// 获取该节点下的数量
    /// </summary>
    /// <returns>int.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    int GetItemNum();
    /// <summary>
    /// 设置类型
    /// </summary>
    /// <param name="type"> type 为 EM_ROICellFormat.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void setGroupType(int type);
    /// <summary>
    /// 返回设置的类型
    /// </summary>
    /// <returns> EM_ROICellFormat类型的参数.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    int getGroupType();         //返回 EM_ROICellFormat
    /// <summary>
    /// 设置唯一标识.
    /// </summary>
    /// <param name="uniqueValue">The unique value.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void setUniqueValue(const QString& uniqueValue);
    /// <summary>
    /// 获取唯一标识.
    /// </summary>
    /// <returns>QString.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QString getUniqueValue();
    /// <summary>
    /// 改变展开/收起的状态
    /// </summary>
    /// <remarks>[Version]:******* Change: </remarks>
    void changeExpandState();
    /// <summary>
    /// 设置展开/收起状态
    /// </summary>
    /// <param name="isExpand">The is expand.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void setExpandState(bool isExpand);
    /// <summary>
    /// 设置子节点为0的时候是否需要隐藏.
    /// </summary>
    /// <param name="bHideWhenItemNumIsEzro">子节点为0的时候是否需要隐藏..</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetBHideWhenItemNumIsEzro(bool bHideWhenItemNumIsEzro);
    /// <summary>
    /// 获取子节点为0的时候是否需要隐藏.
    /// </summary>
    /// <returns>子节点为0的时候是否需要隐藏..</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    bool GetBHideWhenItemNumIsEzro();
public slots:
    void on_actExpandChanged();
signals:
    void sigIsExpandGroup(QString, QString, bool); //展开/收束
protected:
    /// <summary>
    /// Enters the event.
    /// </summary>
    /// <param name="">The .</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void enterEvent(QEvent*);
    /// <summary>
    /// Leaves the event.
    /// </summary>
    /// <param name="">The .</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void leaveEvent(QEvent*);
private:
    /// <summary>
    /// The UI
    /// </summary>
    Ui::QMTTreeGroupPacketItem* ui;
    /// <summary>
    /// 名称
    /// </summary>
    QString _name;
    /// <summary>
    /// 子节点数量
    /// </summary>
    int _ItemNum;
    /// <summary>
    /// 是否展开
    /// </summary>
    bool _isExpand;
    /// <summary>
    /// 唯一标识
    /// </summary>
    QString _uniqueValue;
    /// <summary>
    /// 类型
    /// </summary>
    int _groupType = -1;    //EM_ROICellFormat枚举值
    /// <summary>
    /// 当子节点数量为0的时候是否隐藏
    /// </summary>
    bool m_bHideWhenItemNumIsEzro = false;


};

