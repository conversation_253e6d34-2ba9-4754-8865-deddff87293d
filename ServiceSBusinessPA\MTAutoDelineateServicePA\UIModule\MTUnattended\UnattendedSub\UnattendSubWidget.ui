<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>UnattendSubWidgetClass</class>
 <widget class="QWidget" name="UnattendSubWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1307</width>
    <height>816</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>UnattendSubWidget</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>24</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>8</number>
   </property>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout">
     <item>
      <widget class="QWidget" name="widget" native="true">
       <property name="minimumSize">
        <size>
         <width>190</width>
         <height>0</height>
        </size>
       </property>
       <property name="maximumSize">
        <size>
         <width>190</width>
         <height>16777215</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_3">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>26</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>26</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_2">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>12</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtLabel" name="mtLabel">
             <property name="text">
              <string>影像来源</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtLabel::myLabel1_2</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_4">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>1</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>1</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtListWidget" name="mtListWidget_left">
          <property name="scrollType">
           <enum>MtScrollBar::scrollbar1</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtListWidget::listwidget3</enum>
          </property>
          <property name="_mtItemType" stdset="0">
           <enum>MtListWidget::listitem1_4</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_3">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>1</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>1</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::frameEx3</enum>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_2">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>34</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>34</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout_3">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>12</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtToolButton" name="mtToolButton_add">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>新增</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_copy">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>拷贝</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="mtToolButton_del">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="iconSize">
              <size>
               <width>22</width>
               <height>22</height>
              </size>
             </property>
             <property name="toolTipText">
              <string>删除</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <property name="leftMargin">
      <number>0</number>
     </property>
     <property name="bottomMargin">
      <number>0</number>
     </property>
     <item>
      <widget class="QWidget" name="widget_4" native="true">
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>50</height>
        </size>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_11">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="leftMargin">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="rightMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="QWidget" name="widget_right" native="true">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>0</height>
           </size>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_6" stretch="0,0,1">
           <property name="spacing">
            <number>24</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_4">
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_5">
               <property name="spacing">
                <number>24</number>
               </property>
               <property name="rightMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="MtLabel" name="mtLabel_title">
                 <property name="sizePolicy">
                  <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
                   <horstretch>0</horstretch>
                   <verstretch>0</verstretch>
                  </sizepolicy>
                 </property>
                 <property name="text">
                  <string>MtTextLabel</string>
                 </property>
                 <property name="elideMode">
                  <enum>Qt::ElideRight</enum>
                 </property>
                 <property name="_mtType" stdset="0">
                  <enum>MtLabel::myLabel3</enum>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="MtFrameEx" name="mtFrameEx_5">
                 <property name="minimumSize">
                  <size>
                   <width>1</width>
                   <height>0</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>1</width>
                   <height>20</height>
                  </size>
                 </property>
                 <property name="_mtType" stdset="0">
                  <enum>MtFrameEx::default_type</enum>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="MtSwitchButton" name="mtSwitchButton_title">
                 <property name="minimumSize">
                  <size>
                   <width>42</width>
                   <height>20</height>
                  </size>
                 </property>
                 <property name="maximumSize">
                  <size>
                   <width>42</width>
                   <height>20</height>
                  </size>
                 </property>
                </widget>
               </item>
               <item>
                <widget class="MtStackedWidget" name="mtStackedWidget_title">
                 <property name="maximumSize">
                  <size>
                   <width>16777215</width>
                   <height>30</height>
                  </size>
                 </property>
                 <property name="currentIndex">
                  <number>1</number>
                 </property>
                 <widget class="QWidget" name="page_show_title">
                  <layout class="QHBoxLayout" name="horizontalLayout_6">
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="MtLabel" name="mtLabel_2">
                     <property name="text">
                      <string>开启状态不可编辑或删除</string>
                     </property>
                     <property name="elideMode">
                      <enum>Qt::ElideRight</enum>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtLabel::myLabel1_2</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_2">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>551</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="MtPushButton" name="mtPushButton_edit_title">
                     <property name="minimumSize">
                      <size>
                       <width>80</width>
                       <height>26</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>26</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>编辑规则</string>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtPushButton::pushbutton1</enum>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                 <widget class="QWidget" name="page_edit_title">
                  <layout class="QHBoxLayout" name="horizontalLayout_7">
                   <property name="spacing">
                    <number>12</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <spacer name="horizontalSpacer_3">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>591</width>
                       <height>20</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                   <item>
                    <widget class="MtPushButton" name="mtPushButton_save_title">
                     <property name="minimumSize">
                      <size>
                       <width>80</width>
                       <height>26</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>26</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>保存规则</string>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtPushButton::pushbutton1</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="MtPushButton" name="mtPushButton_cancel_title">
                     <property name="minimumSize">
                      <size>
                       <width>80</width>
                       <height>26</height>
                      </size>
                     </property>
                     <property name="maximumSize">
                      <size>
                       <width>16777215</width>
                       <height>26</height>
                      </size>
                     </property>
                     <property name="text">
                      <string>取消编辑</string>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtPushButton::pushbutton10</enum>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </widget>
               </item>
              </layout>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QFormLayout" name="formLayout">
             <property name="labelAlignment">
              <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
             </property>
             <property name="formAlignment">
              <set>Qt::AlignBottom|Qt::AlignLeading|Qt::AlignLeft</set>
             </property>
             <property name="horizontalSpacing">
              <number>8</number>
             </property>
             <property name="verticalSpacing">
              <number>24</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item row="0" column="0">
              <widget class="MtLabel" name="mtLabel_3">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>54</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>54</height>
                </size>
               </property>
               <property name="text">
                <string>1、影像接收</string>
               </property>
               <property name="elideMode">
                <enum>Qt::ElideRight</enum>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLabel::myLabel2</enum>
               </property>
              </widget>
             </item>
             <item row="0" column="1">
              <widget class="MtFrameEx" name="mtFrameEx_6">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>54</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>54</height>
                </size>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtFrameEx::default_type</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_11">
                <property name="spacing">
                 <number>6</number>
                </property>
                <property name="leftMargin">
                 <number>22</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtLabel" name="mtLabel_8">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="text">
                   <string>接收节点：</string>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLabel::myLabel1_1</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtStackedWidget" name="mtStackedWidget_source">
                  <property name="currentIndex">
                   <number>0</number>
                  </property>
                  <widget class="QWidget" name="page_edit_source">
                   <layout class="QHBoxLayout" name="horizontalLayout_12">
                    <property name="spacing">
                     <number>12</number>
                    </property>
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <property name="rightMargin">
                     <number>0</number>
                    </property>
                    <property name="bottomMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="MtComboBox" name="mtComboBox_server_source">
                      <property name="minimumSize">
                       <size>
                        <width>317</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>317</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="elideMode">
                       <enum>Qt::ElideRight</enum>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtComboBox::combobox1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtToolButton" name="mtToolButton_search_source">
                      <property name="minimumSize">
                       <size>
                        <width>22</width>
                        <height>22</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>22</width>
                        <height>22</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>...</string>
                      </property>
                      <property name="iconSize">
                       <size>
                        <width>18</width>
                        <height>18</height>
                       </size>
                      </property>
                      <property name="toolTipText">
                       <string>新增节点</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtToolButton::toolbutton2</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <spacer name="horizontalSpacer_7">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                  <widget class="QWidget" name="page_show_source">
                   <layout class="QHBoxLayout" name="horizontalLayout_13">
                    <property name="leftMargin">
                     <number>0</number>
                    </property>
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <property name="rightMargin">
                     <number>0</number>
                    </property>
                    <property name="bottomMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="MtLabel" name="mtLabel_server_source">
                      <property name="text">
                       <string>MtTextLabel</string>
                      </property>
                      <property name="elideMode">
                       <enum>Qt::ElideRight</enum>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <spacer name="horizontalSpacer_8">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>323</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
                <item>
                 <spacer name="horizontalSpacer_6">
                  <property name="orientation">
                   <enum>Qt::Horizontal</enum>
                  </property>
                  <property name="sizeHint" stdset="0">
                   <size>
                    <width>392</width>
                    <height>20</height>
                   </size>
                  </property>
                 </spacer>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="0">
              <widget class="QWidget" name="widget_2" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <property name="spacing">
                 <number>4</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtLabel" name="mtLabel_4">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>54</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>54</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>2、影像过滤</string>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLabel::myLabel2</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtToolButton" name="mtToolButton_question_filter">
                  <property name="minimumSize">
                   <size>
                    <width>16</width>
                    <height>16</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16</width>
                    <height>16</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>...</string>
                  </property>
                  <property name="iconSize">
                   <size>
                    <width>14</width>
                    <height>14</height>
                   </size>
                  </property>
                  <property name="toolTipText">
                   <string>符合条件的图像仅支持导出，不进行自动勾画</string>
                  </property>
                  <property name="toolTipTextEx">
                   <string/>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtToolButton::toolbutton2</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item row="1" column="1">
              <widget class="MtFrameEx" name="mtFrameEx_7">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>54</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>16777215</width>
                 <height>54</height>
                </size>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtFrameEx::default_type</enum>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_14">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>2</number>
                </property>
                <property name="topMargin">
                 <number>2</number>
                </property>
                <property name="rightMargin">
                 <number>2</number>
                </property>
                <property name="bottomMargin">
                 <number>2</number>
                </property>
                <item>
                 <widget class="MtStackedWidget" name="mtStackedWidget_filter">
                  <property name="currentIndex">
                   <number>0</number>
                  </property>
                  <widget class="QWidget" name="page_edit_filter">
                   <layout class="QHBoxLayout" name="horizontalLayout_15">
                    <property name="leftMargin">
                     <number>22</number>
                    </property>
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <property name="bottomMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="MtLabel" name="mtLabel_9">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="text">
                       <string>(1)</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1_3</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtCheckBox" name="mtCheckBox_imageNum_filter">
                      <property name="text">
                       <string>影像层数少于</string>
                      </property>
                      <property name="elideMode">
                       <enum>Qt::ElideRight</enum>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtCheckBox::checkbox1_2</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtRangeLineEdit" name="lineEdit_imageNum_filter">
                      <property name="minimumSize">
                       <size>
                        <width>80</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>80</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="placeholderText">
                       <string>2-99</string>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_10">
                      <property name="text">
                       <string>层</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <spacer name="horizontalSpacer_9">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeType">
                       <enum>QSizePolicy::Fixed</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_11">
                      <property name="text">
                       <string>(2)</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1_3</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtCheckBox" name="mtCheckBox_word_filter">
                      <property name="text">
                       <string>DICOM文件的序列描述(0008,103E)字段，含有</string>
                      </property>
                      <property name="elideMode">
                       <enum>Qt::ElideRight</enum>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtCheckBox::checkbox1_2</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtRangeLineEdit" name="lineEdit_word_filter">
                      <property name="minimumSize">
                       <size>
                        <width>0</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>16777215</width>
                        <height>30</height>
                       </size>
                      </property>
                      <property name="maxLength">
                       <number>64</number>
                      </property>
                      <property name="placeholderText">
                       <string>用/隔开多个识别词</string>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                  <widget class="QWidget" name="page_show_filter">
                   <layout class="QHBoxLayout" name="horizontalLayout_16">
                    <property name="spacing">
                     <number>20</number>
                    </property>
                    <property name="leftMargin">
                     <number>22</number>
                    </property>
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <property name="bottomMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="MtLabel" name="mtLabel_imageNum_filter">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="text">
                       <string>MtTextLabel</string>
                      </property>
                      <property name="elideMode">
                       <enum>Qt::ElideRight</enum>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_word_filter">
                      <property name="sizePolicy">
                       <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
                        <horstretch>0</horstretch>
                        <verstretch>0</verstretch>
                       </sizepolicy>
                      </property>
                      <property name="text">
                       <string>MtTextLabel</string>
                      </property>
                      <property name="elideMode">
                       <enum>Qt::ElideRight</enum>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <spacer name="horizontalSpacer_10">
                      <property name="orientation">
                       <enum>Qt::Horizontal</enum>
                      </property>
                      <property name="sizeType">
                       <enum>QSizePolicy::Expanding</enum>
                      </property>
                      <property name="sizeHint" stdset="0">
                       <size>
                        <width>40</width>
                        <height>20</height>
                       </size>
                      </property>
                     </spacer>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QVBoxLayout" name="verticalLayout_5">
             <property name="spacing">
              <number>16</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <layout class="QHBoxLayout" name="horizontalLayout_8">
               <property name="spacing">
                <number>6</number>
               </property>
               <property name="bottomMargin">
                <number>0</number>
               </property>
               <item>
                <widget class="MtLabel" name="mtLabel_5">
                 <property name="text">
                  <string>3、影像识别、勾画与导出</string>
                 </property>
                 <property name="_mtType" stdset="0">
                  <enum>MtLabel::myLabel2</enum>
                 </property>
                </widget>
               </item>
               <item>
                <spacer name="horizontalSpacer_4">
                 <property name="orientation">
                  <enum>Qt::Horizontal</enum>
                 </property>
                 <property name="sizeHint" stdset="0">
                  <size>
                   <width>40</width>
                   <height>20</height>
                  </size>
                 </property>
                </spacer>
               </item>
               <item>
                <widget class="QWidget" name="widget_sketch" native="true">
                 <property name="minimumSize">
                  <size>
                   <width>0</width>
                   <height>26</height>
                  </size>
                 </property>
                 <layout class="QHBoxLayout" name="horizontalLayout_9">
                  <property name="spacing">
                   <number>12</number>
                  </property>
                  <property name="leftMargin">
                   <number>0</number>
                  </property>
                  <property name="topMargin">
                   <number>0</number>
                  </property>
                  <property name="rightMargin">
                   <number>0</number>
                  </property>
                  <property name="bottomMargin">
                   <number>0</number>
                  </property>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_add_sketch">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>新增</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton10</enum>
                    </property>
                   </widget>
                  </item>
                  <item>
                   <widget class="MtPushButton" name="mtPushButton_del_sketch">
                    <property name="minimumSize">
                     <size>
                      <width>80</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="maximumSize">
                     <size>
                      <width>16777215</width>
                      <height>26</height>
                     </size>
                    </property>
                    <property name="text">
                     <string>删除</string>
                    </property>
                    <property name="_mtType" stdset="0">
                     <enum>MtPushButton::pushbutton2</enum>
                    </property>
                   </widget>
                  </item>
                 </layout>
                </widget>
               </item>
              </layout>
             </item>
             <item>
              <widget class="QWidget" name="widget_3" native="true">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QVBoxLayout" name="verticalLayout_7">
                <property name="spacing">
                 <number>0</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="QScrollArea" name="scrollArea">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>30</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>30</height>
                   </size>
                  </property>
                  <property name="widgetResizable">
                   <bool>true</bool>
                  </property>
                  <widget class="QWidget" name="scrollAreaWidgetContents">
                   <property name="geometry">
                    <rect>
                     <x>0</x>
                     <y>0</y>
                     <width>993</width>
                     <height>28</height>
                    </rect>
                   </property>
                   <layout class="QHBoxLayout" name="horizontalLayout_18">
                    <property name="spacing">
                     <number>8</number>
                    </property>
                    <property name="leftMargin">
                     <number>7</number>
                    </property>
                    <property name="topMargin">
                     <number>0</number>
                    </property>
                    <property name="rightMargin">
                     <number>8</number>
                    </property>
                    <property name="bottomMargin">
                     <number>0</number>
                    </property>
                    <item>
                     <widget class="MtCheckBox" name="mtCheckBox_all_sketch">
                      <property name="minimumSize">
                       <size>
                        <width>13</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>13</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="text">
                       <string/>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtCheckBox::checkbox1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtFrameEx" name="mtFrameEx_10">
                      <property name="minimumSize">
                       <size>
                        <width>1</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>1</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtFrameEx::frameEx3</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_12">
                      <property name="minimumSize">
                       <size>
                        <width>64</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>64</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>影像模态</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1_1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtFrameEx" name="mtFrameEx_11">
                      <property name="minimumSize">
                       <size>
                        <width>1</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>1</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtFrameEx::frameEx3</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_13">
                      <property name="minimumSize">
                       <size>
                        <width>427</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>427</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>影像识别方式</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1_1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtFrameEx" name="mtFrameEx_12">
                      <property name="minimumSize">
                       <size>
                        <width>1</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>1</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtFrameEx::frameEx3</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_14">
                      <property name="minimumSize">
                       <size>
                        <width>188</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>188</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="text">
                       <string>勾画模板</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1_1</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtFrameEx" name="mtFrameEx_13">
                      <property name="minimumSize">
                       <size>
                        <width>1</width>
                        <height>0</height>
                       </size>
                      </property>
                      <property name="maximumSize">
                       <size>
                        <width>1</width>
                        <height>16777215</height>
                       </size>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtFrameEx::frameEx3</enum>
                      </property>
                     </widget>
                    </item>
                    <item>
                     <widget class="MtLabel" name="mtLabel_15">
                      <property name="text">
                       <string>导出规则</string>
                      </property>
                      <property name="_mtType" stdset="0">
                       <enum>MtLabel::myLabel1_1</enum>
                      </property>
                     </widget>
                    </item>
                   </layout>
                  </widget>
                 </widget>
                </item>
                <item>
                 <widget class="QWidget" name="widget_table_sketch" native="true">
                  <property name="sizePolicy">
                   <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
                    <horstretch>0</horstretch>
                    <verstretch>0</verstretch>
                   </sizepolicy>
                  </property>
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>0</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>16777215</height>
                   </size>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_20">
                   <property name="spacing">
                    <number>0</number>
                   </property>
                   <property name="leftMargin">
                    <number>0</number>
                   </property>
                   <property name="topMargin">
                    <number>0</number>
                   </property>
                   <property name="rightMargin">
                    <number>0</number>
                   </property>
                   <property name="bottomMargin">
                    <number>0</number>
                   </property>
                   <item>
                    <widget class="MtListWidget" name="mtListWidget_right">
                     <property name="minimumSize">
                      <size>
                       <width>0</width>
                       <height>0</height>
                      </size>
                     </property>
                     <property name="scrollType">
                      <enum>MtScrollBar::scrollbar1</enum>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtListWidget::listwidget3</enum>
                     </property>
                     <property name="_mtItemType" stdset="0">
                      <enum>MtListWidget::listitem1_4</enum>
                     </property>
                    </widget>
                   </item>
                  </layout>
                 </widget>
                </item>
                <item>
                 <widget class="MtFrameEx" name="mtFrameEx_8">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>1</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>1</height>
                   </size>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtFrameEx::frameEx3</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtFrameEx" name="mtFrameEx_9">
                  <property name="minimumSize">
                   <size>
                    <width>0</width>
                    <height>34</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>16777215</width>
                    <height>34</height>
                   </size>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtFrameEx::default_type</enum>
                  </property>
                  <layout class="QHBoxLayout" name="horizontalLayout_10">
                   <property name="leftMargin">
                    <number>16</number>
                   </property>
                   <item>
                    <widget class="MtLabel" name="mtLabel_6">
                     <property name="text">
                      <string>*</string>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtLabel::myLabel1_5</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <widget class="MtLabel" name="mtLabel_7">
                     <property name="text">
                      <string>注：影像符合多条规则时，优先使用Dicom字段识别结果进行勾画</string>
                     </property>
                     <property name="_mtType" stdset="0">
                      <enum>MtLabel::myLabel1_2</enum>
                     </property>
                    </widget>
                   </item>
                   <item>
                    <spacer name="horizontalSpacer_5">
                     <property name="orientation">
                      <enum>Qt::Horizontal</enum>
                     </property>
                     <property name="sizeHint" stdset="0">
                      <size>
                       <width>604</width>
                       <height>13</height>
                      </size>
                     </property>
                    </spacer>
                   </item>
                  </layout>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtListWidget</class>
   <extends>QListWidget</extends>
   <header>MtListWidget.h</header>
  </customwidget>
  <customwidget>
   <class>MtStackedWidget</class>
   <extends>QStackedWidget</extends>
   <header>MtStackedWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtSwitchButton</class>
   <extends>QWidget</extends>
   <header>MtSwitchButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtRangeLineEdit</class>
   <extends>QLineEdit</extends>
   <header>mtrangelineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
