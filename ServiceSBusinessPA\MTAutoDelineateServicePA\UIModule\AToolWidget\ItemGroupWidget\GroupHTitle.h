﻿// *********************************************************************************
// <remarks>
// FileName    : GroupHTitle
// Author      : zlw
// CreateTime  : 2024-01-20
// Description : 数据项标题(内嵌于: GroupItemListWidget listWidget窗体)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_GroupHTitle.h"


/// <summary>
/// 数据
/// </summary>
class GroupHTitleData
{
public:
    int groupId = -1;
    Qt::CheckState state = Qt::Unchecked;
    QString value;
};

class GroupHTitle : public QWidget
{
    Q_OBJECT

public:
    enum EM_PageType
    {
        Page_Label,
        Page_Check
    };
    GroupHTitle(QWidget* parent = nullptr);
    ~GroupHTitle();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化Item
    /// </summary>
    /// <param name="pageTypeEnum">[IN]页面类型</param>
    /// <param name="data">[IN]数据</param>
    /// <param name="topMargin">[IN]上边距</param>
    /// <param name="bottomMargin">[IN]下边距</param>
    /// <returns>项高度</returns>
    int init(const EM_PageType pageTypeEnum, const GroupHTitleData& data, int topMargin = 10, int bottomMargin = 6);

    /// <summary>
    /// 设置复选框状态
    /// </summary>
    void setCheckState(const Qt::CheckState state);

    /// <summary>
    /// 设置复选框状态
    /// </summary>
    void setCheckState(const bool isCheck);

    /// <summary>
    /// 获取展开状态
    /// </summary>
    bool getExpandButtonState();

    /// <summary>
    /// 设置展开按钮状态
    /// </summary>
    /// <param name="expand">[IN]true展开</param>
    void setExpandButtonState(const bool expand);

signals: //发送到父窗口
    void sigGroupHTitleCheckToGroupItemListWidget(const int groupId, const bool checked);   //标题复选框是否勾选
    void sigGroupHTitleExpandToGroupItemListWidget(const EM_PageType pageTypeEnum, const int groupId, const bool expanded);  //标题展开/收起

protected slots:
    /// <summary>
    /// checkbox勾选
    /// </summary>
    void onCheckBoxStateChanged(int state);

    /// <summary>
    /// 展开/收起按钮MtLabelLabel
    /// </summary>
    void onMtToolButton_pageTitle();

    /// <summary>
    /// 展开/收起按钮MtLabelCheck
    /// </summary>
    void onMtToolButton_pageCheck();

private:
    Ui::GroupHTitleClass ui;
    int m_groupId; //组id
    EM_PageType m_pageTypeEnum; //title类型
    QHash<QString, QString> m_imagePathHash; //图标map(key-name value-图片相对路径)
};
