<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModelImportSuccessDlg</class>
 <widget class="QWidget" name="ModelImportSuccessDlg">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>492</width>
    <height>211</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <property name="styleSheet">
      <string notr="true">#label, #label_2 {background: rgba(36, 155, 217, 1)}</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>8</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout" stretch="0,1">
        <item>
         <widget class="MtLabel" name="mtLabel_successIcon">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtLabel" name="mtLabel">
          <property name="text">
           <string>模型导入成功！</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel2_1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <property name="leftMargin">
         <number>30</number>
        </property>
        <property name="bottomMargin">
         <number>12</number>
        </property>
        <item>
         <widget class="MtLabel" name="mtLabel_2">
          <property name="text">
           <string>系统已自动将该模型的ROI添加进“临时分组”这个分组中</string>
          </property>
          <property name="wordWrap">
           <bool>true</bool>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_2</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <property name="leftMargin">
         <number>30</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtLabel" name="mtLabel_5">
          <property name="text">
           <string>操作提示</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_2</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <property name="leftMargin">
         <number>30</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_2">
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <property name="leftMargin">
            <number>16</number>
           </property>
           <property name="topMargin">
            <number>7</number>
           </property>
           <property name="rightMargin">
            <number>16</number>
           </property>
           <property name="bottomMargin">
            <number>7</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_4">
             <property name="topMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="label_2">
               <property name="minimumSize">
                <size>
                 <width>4</width>
                 <height>4</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>4</width>
                 <height>4</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <widget class="MtLabel" name="mtLabel_4">
               <property name="text">
                <string>您在发起自动勾画或者管理模板时可以通过分组信息快速找到模型的ROI</string>
               </property>
               <property name="wordWrap">
                <bool>true</bool>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLabel::myLabel1</enum>
               </property>
              </widget>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_5">
             <property name="topMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="QLabel" name="label">
               <property name="minimumSize">
                <size>
                 <width>4</width>
                 <height>4</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>4</width>
                 <height>4</height>
                </size>
               </property>
               <property name="text">
                <string/>
               </property>
              </widget>
             </item>
             <item>
              <widget class="MtLabel" name="mtLabel_3">
               <property name="text">
                <string>您也可以在模型和ROI设置页面对ROI的名称、颜色、分组等信息进行调整</string>
               </property>
               <property name="wordWrap">
                <bool>true</bool>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtLabel::myLabel1</enum>
               </property>
              </widget>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
