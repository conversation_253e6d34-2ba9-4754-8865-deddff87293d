﻿// *********************************************************************************
// <remarks>
// FileName    : UnattendRuleExportDialog
// Author      : zlw
// CreateTime  : 2024-02-04
// Description : 影像过滤规则配置界面
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_UnattendRuleExportDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class UnattendRuleExportDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    UnattendRuleExportDialog(QWidget* parent = nullptr);
    ~UnattendRuleExportDialog();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="isAdd">[IN]true新增</param>
    /// <param name="stAddrInfo">[IN]导出地址</param>
    /// <param name="usedExportScpMap">[IN]已被使用的地址信息(key-customId)</param>
    /// <param name="allExportScpInfoList">[IN]所有导出到远程服务器信息</param>
    void init(
        const bool isAdd,
        const n_mtautodelineationdialog::ST_AddrSimple& stAddrInfo,
        const QMap<QString, n_mtautodelineationdialog::ST_AddrSimple>& usedExportInfoMap,
        const QList<n_mtautodelineationdialog::ST_AddrSimple>& allExportScpInfoList
    );

    /// <summary>
    /// 获取最新的导出地址信息
    /// </summary>
    /// <param name="outIsMod">[OUT]是否发生修改</param>
    /// <param name="outAddrInfo">[OUT]导出地址信息</param>
    void getNewAddrInfo(bool& outIsMod, n_mtautodelineationdialog::ST_AddrSimple& outAddrInfo);

protected slots:
    /// <summary>
    /// 远程节点选中
    /// </summary>
    void onMtRadioButton_scp(bool checked);

    /// <summary>
    /// 远程节点下拉变化
    /// </summary>
    void onMtComboBox_scp(const QString& text);

    /// <summary>
    /// 选择共享目录
    /// </summary>
    void onMtPushButton_dir();

    /// <summary>
    /// 使用默认导出规则
    /// </summary>
    void onMtPushButton_def();

protected:
    /// <summary>
    /// 显示导出格式的选中内容
    /// </summary>
    /// <param name="isSelectScpRadio">[IN]是否选中SCP服务器RadioButton</param>
    /// <param name="scpServerName">[IN]远程SCP名称</param>
    void showExportFormatCombox(const bool isSelectScpRadio, const QString& scpServerName);

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::UnattendRuleExportDialogClass ui;
    bool m_isAdd = false; //新增
    n_mtautodelineationdialog::ST_AddrSimple m_oldAddrInfo; //原始导出地址
    QSet<QString> m_dirPathSet; //文件夹路径集合
};
