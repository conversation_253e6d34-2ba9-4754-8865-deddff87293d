<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>CWidgetButton</class>
 <widget class="QWidget" name="CWidgetButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>305</width>
    <height>47</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>16777215</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>CWidgetButton</string>
  </property>
  <property name="windowIcon">
   <iconset>
    <normaloff>:/images/images/other.png</normaloff>:/images/images/other.png</iconset>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::default_type</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="layoutWidget" native="true">
        <property name="styleSheet">
         <string notr="true"/>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>8</number>
         </property>
         <property name="leftMargin">
          <number>16</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>16</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="MtLabel" name="mtLabel_name">
           <property name="text">
            <string>MtTextLabel</string>
           </property>
           <property name="elideMode">
            <enum>Qt::ElideRight</enum>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtLabel::myLabel1</enum>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="MtPushButton" name="listBtnImportModel">
           <property name="text">
            <string>导入</string>
           </property>
           <property name="toolTipText">
            <string>导入自定义模型</string>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtPushButton::default_type</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
