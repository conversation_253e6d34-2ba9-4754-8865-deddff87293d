﻿#pragma once

#include <QWidget>
#include <QPushButton>
#include <QToolButton>
#include <QMap>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtToolButton.h"


namespace Ui
{
class MtUnitLabelWithColor;
}
class MtToolButton;

//MtUnitLabelWithColor 参数
class  MtUnitLabelWithColorParam : public ICellWidgetParam
{
public:
    QColor _preColor;
    int _preColorWidth = 2;     //颜色宽度

    MtUnitLabelWithColorParam();
    ~MtUnitLabelWithColorParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(MtUnitLabelWithColorParam)

/*
MtToolButton集合
*/

class MtUnitLabelWithColor :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT
public:

    MtUnitLabelWithColor(QWidget* parent = Q_NULLPTR);
    ~MtUnitLabelWithColor();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);  //更新界面接口
    virtual QString GetCurText();                      //获取当前界面展示文案

    /******************ui************************/
    void SetupCellWidget(MtUnitLabelWithColorParam& param);
    void SetPreBackgroundColor(const QColor& bkColor);              //设置前缀背景色

private:
    Ui::MtUnitLabelWithColor* ui;

};
