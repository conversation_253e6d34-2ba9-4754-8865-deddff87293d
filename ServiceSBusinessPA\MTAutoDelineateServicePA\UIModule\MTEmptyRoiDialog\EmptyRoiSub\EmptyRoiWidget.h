﻿// *********************************************************************************
// <remarks>
// FileName    : EmptyRoiWidget
// Author      : zlw
// CreateTime  : 2023-10-30
// Description : 空勾画页签(内嵌EmptyRoiTable)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_EmptyRoiWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class EmptyRoiWidget : public QWidget
{
    Q_OBJECT

public:
    EmptyRoiWidget(QWidget* parent = nullptr);
    ~EmptyRoiWidget();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    void init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
              const QList<n_mtautodelineationdialog::ST_Organ>& stEmptyOrganList,
              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 获取所有空勾画信息
    /// </summary>
    /// <returns>所有空勾画信息</returns>
    QList<n_mtautodelineationdialog::ST_Organ> getAllEmptyOrganInfo();

    void removeFocusFromTable();

    bool isNeedSave2File();

public slots:
    /// <summary>
    /// 添加按钮
    /// </summary>
    void onMtToolButton_add();

    /// <summary>
    /// 删除按钮
    /// </summary>
    void onMtToolButton_del();

    /// <summary>
    /// 列表信息发生变化
    /// </summary>
    void slotTableInfoChanged(const QString& defOrganName, int col, const QString& newText);

signals:
    void sigRemoveRoi(int roiID, const QString& roiName);

protected:
    void setTableChangedStatus();

private:
    Ui::EmptyRoiWidgetClass ui;
    int m_newRoiId = -1;
    QWidget* m_parentDialog = nullptr;

    bool m_bNeedSave2File;  //是否需要保存到数据库
};
