<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModelOrganWidgetClass</class>
 <widget class="QWidget" name="ModelOrganWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ModelOrganWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_modelName" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>30</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>16</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>16</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtLabel" name="mtLabel_modelName">
        <property name="text">
         <string>MtTextLabel</string>
        </property>
        <property name="elideMode">
         <enum>Qt::ElideRight</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="MtListWidget" name="mtListWidget_roi">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>0</height>
      </size>
     </property>
     <property name="scrollType">
      <enum>MtScrollBar::scrollbar2</enum>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtListWidget::listwidget2</enum>
     </property>
     <property name="mtItemType" stdset="0">
      <string notr="true">default_item_type</string>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtListWidget</class>
   <extends>QListWidget</extends>
   <header>MtListWidget.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
