﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QValidator>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtRangeLineEdit.h"
#include "AccuComponentUi\Header\MtWarningLineEdit.h"



namespace Ui
{
class QCustPairLineEdit;
}
//QCustPairLineEdit参数
class  QCustPairLineEditParam : public ICellWidgetParam
{
public:
    QString _text1;//文本1
    QString _text2;//文本2

    QString _regExpStr;         //正则表达式
    int _maxLength = -1;        //允许输入最大长度
    QString _placeholderText;   //占位字符串

    double minValue;                //最小值
    double maxValue;                //最大值
    double initValue;               //初始值
    int decimals = 2;               //小数点位数
    bool bContainMin = true;        //是否允许包含最小值。true:表示"[min,";false:表示"(min"
    bool bContainMax = true;        //是否允许包含最大值。true:表示"max]";false:表示"max)"

    double seperateLeftValue = 360.00;    //分节值左侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
    double seperateRightValue = 0.00;     //分节值右侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
    QString unit;                         //单位

    QCustPairLineEditParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustPairLineEditParam)

class  QCustPairLineEdit : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustPairLineEdit(QWidget* parent = Q_NULLPTR);
    ~QCustPairLineEdit();
    void SetupCellWidget(QCustPairLineEditParam& cellWidgetParam); //初始化单元格界面

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);      //更新界面接口

    /// <summary>
    /// 设置输入范围规则
    /// </summary>
    /// <param name="inputInfo"></param>
    void SetEditRange(const QCustPairLineEditParam& inputInfo);

    /// <summary>
    /// 设置正则表达式
    /// </summary>
    void SetRegExpStr(QString&);

    /// <summary>
    /// 设置输入的规则
    /// </summary>
    void SetItemValidator(QValidator*);

    /// <summary>
    /// 设置样式
    /// </summary>
    void SetMyStyleSheet(QString&);

    /// <summary>
    /// 清除警告样式
    /// </summary>
    void ClearWarning();

    /// <summary>
    /// 设置警告文本
    /// </summary>
    /// <param name="indexLst"></param>
    /// <param name="tips"></param>
    /// <param name="bClear"></param>
    void SetWarning(QList<int> indexLst, const QString& tips, bool bClear = true);

    /// <summary>
    /// 设置文本内容
    /// </summary>
    /// <param name="text1"></param>
    /// <param name="text2"></param>
    void SetText(QString& text1, QString& text2);

    /// <summary>
    /// 获取输入框文本
    /// </summary>
    /// <param name="text1"></param>
    /// <param name="text2"></param>
    void GetText(QString& text1, QString& text2);

    /// <summary>
    /// 获取输入框提示文本
    /// </summary>
    /// <returns></returns>
    QStringList GetTips();
    /// <summary>
    /// 获取左边输入框
    /// </summary>
    /// <returns></returns>
    MtWarningLineEdit* GetLeftEdit();
    /// <summary>
    /// 获取右边输入框
    /// </summary>
    /// <returns></returns>
    MtWarningLineEdit* GetRightEdit();
    /// <summary>
    /// 设置输入框之间的文本是否显示
    /// </summary>
    /// <param name="isVisible"></param>
    void SetMidLabelVisible(bool isVisible);

    /// <summary>
    /// 设置输入框之间的文本
    /// </summary>
    /// <param name="text"></param>
    void SetMidLabelText(const QString& text);

    /// <summary>
    /// 设置margin
    /// </summary>
    /// <param name="left"></param>
    /// <param name="top"></param>
    /// <param name="right"></param>
    /// <param name="bottom"></param>
    void SetMargins(int left, int top, int right, int bottom);

    /// <summary>
    /// 设置是否触发光标进入tips消失
    /// </summary>
    void SetIsFocusInEventEnabled(bool bEnabled);

    /// <summary>
    /// 设置是否显示背景文字
    /// </summary>
    /// <param name="bIsShowPlaceholderText"></param>
    void SetShowPlaceholderText(bool bIsShowPlaceholderText);

signals:
    void sigEditFinish(QString newText, QString oldText);
    void sigPairTextChange(const QString& newText1, const QString& newText2);
    void sigPairEditFinish(const QString& text1, const QString& text2);
    /// <summary>
    /// 输入框被点击
    /// </summary>
    void SigEditClicked();
    /// <summary>
    /// 向外发送向左移动单元格
    /// </summary>
    void SigLeftChangeCell();
    /// <summary>
    /// 向外发送向右移动单元格
    /// </summary>
    void SigRightChangeCell();
protected:
    /// <summary>
    /// 鼠标tab/shift+tab键生效
    /// </summary>
    /// <param name="event">The event.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    virtual void keyReleaseEvent(QKeyEvent* event)override;
    /// <summary>
    /// 鼠标点击事件
    /// </summary>
    /// <param name="event">The event.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    virtual void mousePressEvent(QMouseEvent* event)override;
private slots:
    void slotTextChanged(const QString& text);
    void slotLineEditingFinished();

private:
    Ui::QCustPairLineEdit* ui = nullptr;
    QValidator* _validator = nullptr;
};
