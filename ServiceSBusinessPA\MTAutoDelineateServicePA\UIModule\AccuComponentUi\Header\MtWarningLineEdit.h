﻿#pragma once

#include "MtLineEdit.h"
#include <QRegExpValidator>
#include "MtToolTip.h"


class MtRangeValidator;
/*
输入框组件，实现以下功能
1.支持浮点型或者整数两种类型的限制。
2.接口可以设置允许输入的最大值、最小值，支持的范围可以是[min,max],(min,max], [min,max),(min,max)四种情况
3.提供校验当前输入值是否在范围内的功能。
4.用户在输入的过程中，实时校验是否符合范围，不符合范围回到默认值
5.如果是DoubleData输入类型，那么可以指定小数点位数
*/

class  MtWarningLineEdit : public MtLineEdit
{
    Q_OBJECT

public:
    struct RangeParamInfo
    {
        double minValue = 0.0;                //最小值
        double maxValue = 0.0;                //最大值
        double initValue = -999999999999;     //初始值
        double seperateLeftValue = 360.00;    //分节值左侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
        double seperateRightValue = 0.00;     //分节值右侧值。如果最大值小于最小值，使用该值。比如minValue=280,maxValue=50,那么提示信息为"正确范围为[280-360]or[0-50]"
        int decimals = 0;                     //小数点位数
        bool bContainMin = true;              //是否允许包含最小值。true:表示"[min,";false:表示"(min"
        bool bContainMax = true;              //是否允许包含最大值。true:表示"max]";false:表示"max)"
        QString unit;
        void SetWarningStatus(bool isShowWarning);
        //单位
    };

public:
    MtWarningLineEdit(QWidget* parent = Q_NULLPTR);
    /// <summary>
    /// 设置编辑框输入范围
    /// </summary>
    /// <param name="inputInfo"></param>
    void SetEditRange(const RangeParamInfo& inputInfo);

    /// <summary>
    /// 设置是否显示背景文字
    /// </summary>
    /// <param name="bIsShowPlaceholderText"></param>
    void SetShowPlaceholderText(bool bIsShowPlaceholderText);

    /// <summary>
    /// 设置tips
    /// </summary>
    /// <param name="text"></param>
    void setTips(const QString& text);
    QString getTips();

    /// <summary>
    /// 将边框设置为(默认红色)并且弹出提示框
    /// </summary>
    /// <param isAdaptText="">是否自适应提示内容长度，如果true:只会显示一行，文字多长提示框就多长,用于解决lineEdit太短,但是提示内容长显示异常的场景</param>
    /// <param name="color">边框颜色</param>
    void setWarningBorderStatus(QString tipText, bool isAdaptText = false, QColor color = QColor(177, 89, 89, 1));

    /// <summary>
    /// 设置正则表达式限制。设置了正则表达式就不能设置SetEditRange，否则两边冲突
    /// </summary>
    /// <param name="regExp">正则字符串，可参考TpsUIRegExp.h</param>
    void setRegExpression(QString regExp);

    /// <summary>
    /// 设置边框显示颜色
    /// </summary>
    /// <param name="isNormal">true:正常蓝湖边框， false:</param>
    void SetBoderShowStatus(bool isNormal);

    /// <summary>
    /// 是否要显示tips
    /// </summary>
    void SetWarningStatus(bool isShowWarning);

    /// <summary>
    /// 设置是否要当光标移进时tips消失
    /// </summary>
    void SetIsFocusInClearWarning(bool bSet);

protected:
    /// <summary>
    /// 校验当前的输入值是否在范围内
    /// </summary>
    bool CheckInputDataIsValid();

    /// <summary>
    /// 获取最终调整的数据，可能默认值不在范围里,那么默认恢复到最小值
    /// </summary>
    /// <returns></returns>
    QString GetFinalFitNumber();

    /// <summary>
    /// 判断是否在范围内
    /// </summary>
    /// <param name="curDouble"></param>
    /// <param name="minValue"></param>
    /// <param name="bContainMin"></param>
    /// <param name="maxValue"></param>
    /// <param name="bContainMax"></param>
    /// <returns></returns>
    bool IsContansValue(double curDouble, double minValue, bool bContainMin, double maxValue, bool bContainMax);

    /// <summary>
    /// 获取范围提示文案
    /// </summary>
    /// <returns></returns>
    QString GetRangeTipString();

protected:
    void focusOutEvent(QFocusEvent* e);
    void focusInEvent(QFocusEvent* e);
    void mouseMoveEvent(QMouseEvent* e);

private Q_SLOTS:
    /// <summary>
    /// 编辑框文字发现变动时，实时校验槽
    /// </summary>
    void slotTextChanged();
    /// <summary>
    /// 编辑框文字发现变动时，隐藏Tips
    /// </summary>
    void SlotTextChangedHideTips();
signals:
    void sigFocusOut();
    /// <summary>
    /// 焦点再输入框中
    /// </summary>
    void SigFocusIn();

private:
    RangeParamInfo    m_rangeInfo;//范围校验数据
    MtRangeValidator* m_lineEditValidator = nullptr;
    bool  m_isShowWarning = true;//是否要显示范围
    bool m_isInitRange = false;
    bool m_isWarningBorder = false;
    //MtToolTip*     m_tip;//悬浮提示窗口
    QString m_regExpression;
    QRegExpValidator* m_regExp = nullptr;
    QString _tips = "";
    bool m_bIsFocusInClearWarning = false;
    bool m_bIsShowPlaceholderText = false;
};
Q_DECLARE_METATYPE(MtWarningLineEdit::RangeParamInfo);