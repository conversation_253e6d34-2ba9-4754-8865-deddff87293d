﻿#pragma once

/*!
 * \def GETINSTANCE_STRING_DECLARE(classname)
 * \brief   1. 静态getInstance，便于只生成一个实例，需要手动实现initialize(.cpp)
 *          2. _DECLARE 声明，在类里面(.h)
 *          3. _DEFINE  定义，在类外面(.cpp)
 * \param   classname   类名
*/
#define GETINSTANCE_STRING_DECLARE(classname)\
    public: \
        static classname *getInstance(QString str = "");\
        void initialize(QString str = "");\
    protected:\
        classname(QString str);
#define GETINSTANCE_STRING_DEFINE(classname)\
    classname *classname::getInstance(QString str)\
    {\
        static bool isInit = false;\
        static classname tmp(str);\
        if(isInit)\
        {\
            if(!str.isEmpty())\
                tmp.initialize(str);\
        }\
        isInit = true;\
        return &tmp;\
    }\
    classname::classname(QString str)\
    {\
        initialize(str);\
    }

#define GETINSTANCE(classname)\
    static classname *getInstance()\
    {\
        static classname tmp;\
        return &tmp;\
    }
