﻿// *********************************************************************************
// <remarks>
// FileName    : SketchTemplateSaveAsDialog
// Author      : zlw
// CreateTime  : 2024-01-24
// Description : 勾画模板另存
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_SketchTemplateSaveAsDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class SketchTemplateSaveAsDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    SketchTemplateSaveAsDialog(const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& sketchCollectionList, QWidget* parent = nullptr);
    ~SketchTemplateSaveAsDialog();

    /// <summary>
    /// 获取新的模板名称
    /// </summary>
    /// <returns>模板名称</returns>
    QString getNewTemplateName();

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::SketchTemplateSaveAsDialogClass ui;
    QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection> m_sketchCollectionMap; //模板集合(kwy-templateId)
};
