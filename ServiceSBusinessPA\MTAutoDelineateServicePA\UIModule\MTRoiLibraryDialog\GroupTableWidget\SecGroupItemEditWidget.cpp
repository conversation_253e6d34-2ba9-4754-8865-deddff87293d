﻿#include "SecGroupItemEditWidget.h"
#include <QAbstractButton>
#include "DataDefine/InnerStruct.h"

SecGroupItemEditWidget::SecGroupItemEditWidget(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList
        , const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap
        , int wndType, QWidget* parent)
    : MtTemplateDialog(parent)
    , m_organInfoList(organInfoList)
    , m_modelInfoMap(modelInfoMap)
{
    ui.setupUi(this);
    this->setMainLayout(ui.verticalLayout);             //设置布局
    this->setDialogWidthAndContentHeight(428, 98);     //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(wndType == SecGroupItemEditWidget::Add ? tr("添加分组") : tr("编辑分组"));                //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    this->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //
    QRegularExpression regex(/*RegExp_NotSemicolon*/R"([^;]+)");
    QRegularExpressionValidator* validator = new QRegularExpressionValidator(regex, this);
    ui.mtLineEditg_groupName->setValidator(validator);
    //
    initRefRoiList();
}

SecGroupItemEditWidget::~SecGroupItemEditWidget()
{
}

void SecGroupItemEditWidget::initRefRoiList()
{
    //器官是否为亚结构分组
    auto funcCanBeMainStructure = [&](const n_mtautodelineationdialog::ST_Organ & organItem) -> bool
    {
        if (m_modelInfoMap[organItem.modelId].modelType != 1)//必须是内置默认roi
        {
            return false;
        }

        QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo>::const_iterator itor = organItem.organGroupInfoMap.constBegin();

        for (; itor != organItem.organGroupInfoMap.constEnd(); ++itor)
        {
            if (itor.value().type == 3)//已经作为亚结构的器官不能作为主结构
            {
                return false;
            }
        }

        return true;
    };
    //器官按字母排序
    QMap<QString, n_mtautodelineationdialog::ST_Organ> organInfoMap;

    for (int i = 0; i < m_organInfoList.size(); ++i)
    {
        const auto&  organItem = m_organInfoList[i];

        if (funcCanBeMainStructure(organItem))
        {
            organInfoMap[organItem.customOrganName.toLower()] = organItem;
        }
    }

    for (QMap<QString, n_mtautodelineationdialog::ST_Organ>::const_iterator itor = organInfoMap.constBegin(); itor != organInfoMap.constEnd(); ++itor)
    {
        ui.mtComboBox_refROI->addItem(itor.value().customOrganName, QVariant::fromValue(itor.value().id));
        //ui.mtComboBox_refROI->setItemData(i, QVariant::fromValue(itor.value().id), Qt::UserRole);
    }
}

void SecGroupItemEditWidget::onBtnRight1Clicked()
{
    MtTemplateDialog::onBtnRight1Clicked();
}

void SecGroupItemEditWidget::setGroupInfo(const QString& groupName, int organId)
{
    for (int i = 0; i < ui.mtComboBox_refROI->count(); ++i)
    {
        int itemOrganId = ui.mtComboBox_refROI->itemData(i).toInt();

        if (itemOrganId == organId)
        {
            ui.mtComboBox_refROI->setCurrentIndex(i);
            break;
        }
    }

    ui.mtLineEditg_groupName->setText(groupName);
}

void SecGroupItemEditWidget::getGroupInfo(QString& groupName, int& organId)
{
    organId = ui.mtComboBox_refROI->currentData().toInt();
    groupName = ui.mtLineEditg_groupName->text();
}