﻿// *********************************************************************************
// <remarks>
// FileName    : MTLabelLibraryDialog
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : 标签库设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTLabelLibraryDialogClass;
}

namespace n_mtautodelineationdialog
{

class MTLabelLibraryDialog : public MtTemplateDialog, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="nWidth">[IN]窗口宽度，-1时，使用默认宽度</param>
    /// <param name="nHeight">[IN]窗口高度，-1时，使用默认高度</param>
    /// <param name="parent">[IN]父窗口</param>
    MTLabelLibraryDialog(int nWidth = -1, int nHeight = -1, QWidget* parent = nullptr);
    ~MTLabelLibraryDialog();

    /// <summary>
    /// 设置隐藏的列，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="hideColIndexVec">要隐藏的列索引，从0开始</param>
    void setHiddenColumn(const QVector<int>& hideColIndexVec);

    /// <summary>
    /// 设置禁用编辑的列，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="editDisableColVec">不可编辑的列索引，从0开始</param>
    void setDisableColumn(const QVector<int>& editDisableColVec);

    /// <summary>
    /// 隐藏导入按钮，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void setImportButtonHidden();

    /// <summary>
    /// 隐藏优先使用复选按钮，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void setUseROIFirstButtonHidden();

    /// <summary>
    /// 显示标签库设置弹窗
    /// </summary>
    /// <param name="roiTypeList">[IN]Roi类型集合(如果为空将采用内置的类型集合)</param>
    /// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
    /// <param name="outStEmptyOrganVec">[OUT]输出manteiaRoiLabel信息集合(根据optTypeEnum判断增删改)</param>
    /// <returns>QDialog::DialogCode</returns>
    QDialog::DialogCode showLabelLibrarySettingDlg(const QStringList& roiTypeList, const QList<ST_RoiLabelInfo>& stRoiLabelInfoVec, QList<ST_RoiLabelInfo>& outStRoiLabelInfoVec);

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

protected:
    virtual void onBtnCloseClicked() override;  //关闭按钮
    virtual void onBtnRight2Clicked() override; //取消按钮
    virtual void onBtnRight1Clicked() override; //确认按钮
    virtual bool eventFilter(QObject* obj, QEvent* event) override;

private:
    Ui::MTLabelLibraryDialogClass* ui;
    QHash<QString, QString> m_imagePathHash;     //图标map(key-name value-图片相对路径)
    QVector<int> m_hiddenColVec;                    //设置的隐藏列索引
    QVector<int> m_disableColVec;                   //设置的禁用编辑的列索引
    bool m_bImportBtnHidden;                        //是否隐藏导入按钮
    bool m_bUseROIBtnHidden;                        //是否隐藏默认使用ROI库复选按钮
};

}
