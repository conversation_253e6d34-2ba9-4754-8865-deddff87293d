﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTButtonWithOrder.h"
#include "ui_QMTButtonWithOrder.h"
#include "CMtCoreDefine.h"
#include <QToolButton>
#include "AccuComponentUi\Header\Language.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"


using namespace mtuiData;

QMTButtonWithOrderParam::QMTButtonWithOrderParam()
{
    _cellWidgetType = DELEAGATE_QMTButtonWithOrder;
}

QWidget* QMTButtonWithOrderParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTButtonWithOrder* btnOrder = new QMTButtonWithOrder(parent, _text, _orderKey, _enable, _bDefaultOrder);

    if (_styleSheetStr.size() > 0)
    {
        btnOrder->setStyleSheet(_styleSheetStr);
    }

    btnOrder->SetupCellWidget(*this);
    return btnOrder;
}


QMTButtonWithOrder::QMTButtonWithOrder(QWidget* parent, QString text, QString key, bool enable, bool bDefaultOrder)
    : QWidget(parent)
{
    ui = new Ui::QMTButtonWithOrder;
    ui->setupUi(this);
    _normalPixPath = ":/AccuUIComponentImage/images/btn_order.png";
    _normalHoverPixPath = ":/AccuUIComponentImage/images/btn_order_hover.png";
    _ascPixPath = ":/AccuUIComponentImage/images/btn_asc.png";
    _ascHoverPixPath = ":/AccuUIComponentImage/images/btn_asc.png";
    _descPixPath = ":/AccuUIComponentImage/images/btn_desc.png";
    _descHoverPixPath = ":/AccuUIComponentImage/images/btn_desc.png";
    /*InitTemplateStyle();*/
    SetButtonText(text);
    SetButtonKey(key);
    EnableOrder(enable);
    _bDefaultOrder = bDefaultOrder;
    /*if (_bDefaultOrder)
    {
        SetDefaultOrderState();
    }*/
    connect(ui->pushButton_order, SIGNAL(clicked()), this, SLOT(slotUpdateOrder()));
}

QMTButtonWithOrder::~QMTButtonWithOrder()
{
    MT_DELETE(ui);
}

bool QMTButtonWithOrder::UpdateUi(const QVariant& updateData)
{
    return false;
}

void QMTButtonWithOrder::SetupCellWidget(QMTButtonWithOrderParam& cellParam)
{
    if (cellParam._normalPixPath.size() > 0)
    {
        _normalPixPath = cellParam._normalPixPath;
    }

    if (cellParam._normalHoverPixPath.size() > 0)
    {
        _normalHoverPixPath = cellParam._normalHoverPixPath;
    }

    if (cellParam._ascPixPath.size() > 0)
    {
        _ascPixPath = cellParam._ascPixPath;
    }

    if (cellParam._ascHoverPixPath.size() > 0)
    {
        _ascHoverPixPath = cellParam._ascHoverPixPath;
    }

    if (cellParam._descPixPath.size() > 0)
    {
        _descPixPath = cellParam._descPixPath;
    }

    if (cellParam._descHoverPixPath.size() > 0)
    {
        _descHoverPixPath = cellParam._descHoverPixPath;
    }

    InitTemplateStyle();

    if (_bDefaultOrder)
    {
        SetDefaultOrderState(State_desc);
    }
    else
    {
    }
}

void QMTButtonWithOrder::SetButtonText(QString text)
{
    ui->label->setText(text);
}

void QMTButtonWithOrder::SetTextLabelStyle(const QString& styleStr)
{
    ui->label->setStyleSheet(styleStr);
}

void QMTButtonWithOrder::EnableOrder(bool enable)
{
    ui->pushButton_order->setVisible(enable);
}

void QMTButtonWithOrder::SetButtonKey(QString text)
{
    _key = text;
}

QString QMTButtonWithOrder::GetButtonKey()
{
    return _key;
}

int QMTButtonWithOrder::GetButtonState()
{
    return _state;
}

void QMTButtonWithOrder::ResetButton()
{
    QString buttonStyle = GetStyleSheetStr(State_normal);
    ui->pushButton_order->setStyleSheet(buttonStyle);
    _state = 0;
}

void QMTButtonWithOrder::SetDefaultOrderState(int state /*= mtuiData::ButtonOrderState::State_desc*/)
{
    QString buttonStyle = GetStyleSheetStr(state);
    ui->pushButton_order->setStyleSheet(buttonStyle);
    _state = state;
}

bool QMTButtonWithOrder::IsDefaultOrder()
{
    return _bDefaultOrder;
}

QString QMTButtonWithOrder::GetStyleSheetStr(int state)
{
    QString key = _key;
    QString buttonStyle;

    if (State_asc == state)
    {
        buttonStyle = QString("#pushButton_order{"
                              "background-image:url(%1);"
                              "border:none;}"
                              "#pushButton_order:hover{"
                              "background-image:url(%2);}").arg(_ascPixPath).arg(_ascHoverPixPath);
    }
    else if (State_desc == state)
    {
        buttonStyle = QString("#pushButton_order{"
                              "background-image:url(%1);"
                              "border:none;}"
                              "#pushButton_order:hover{"
                              "background-image:url(%2);}").arg(_descPixPath).arg(_descHoverPixPath);
    }
    else
    {
        buttonStyle = QString("#pushButton_order{"
                              "background-image:url(%1);"
                              "border:none;}"
                              "#pushButton_order:hover{"
                              "background-image:url(%2);}").arg(_normalPixPath).arg(_normalHoverPixPath);
    }

    return buttonStyle;
}

void QMTButtonWithOrder::InitTemplateStyle()
{
    QString styleStr;
    QString textColor;
    int fontSize = 14;

    if (Language::type == English)
    {
        fontSize = 12;
    }

    ui->label->setProperty(QssPropertyKey, QssPropertyLabelSecondTitle);
    QString buttonStyle = GetStyleSheetStr(State_normal);
    ui->pushButton_order->setStyleSheet(buttonStyle);
}

void QMTButtonWithOrder::slotUpdateOrder()
{
    _state++;
    _state = _state % State_number;

    //点击排序按钮不能回到默认状态，只能reset到默认状态
    if (State_normal == _state)
    {
        _state++;
    }

    if (State_normal == _state)
    {
    }
    else if (State_asc == _state)
    {
        QString buttonStyle = GetStyleSheetStr(_state);
        ui->pushButton_order->setStyleSheet(buttonStyle);
    }
    else if (State_desc == _state)
    {
        QString buttonStyle = GetStyleSheetStr(_state);
        ui->pushButton_order->setStyleSheet(buttonStyle);
    }
    else
    {
        _state = State_normal;
        QString buttonStyle = GetStyleSheetStr(_state);
        ui->pushButton_order->setStyleSheet(buttonStyle);
    }

    emit sigOrderStateChange(_key, _state);
}

