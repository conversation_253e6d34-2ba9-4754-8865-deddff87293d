<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GroupTableWidget</class>
 <widget class="QWidget" name="GroupTableWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>511</width>
    <height>328</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>GroupTableWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtTabWidget" name="mtTabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtTabWidget::tabwidget1</enum>
     </property>
     <widget class="QWidget" name="tab1">
      <attribute name="title">
       <string>常规分组</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <item>
        <widget class="GroupTableView" name="tableView"/>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx_line">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>1</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>1</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::frameEx3</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>34</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::default_type</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout">
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="MtToolButton" name="mtToolButton_add">
            <property name="minimumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="iconSize">
             <size>
              <width>22</width>
              <height>22</height>
             </size>
            </property>
            <property name="toolTipText">
             <string>新增分组</string>
            </property>
            <property name="pixmapFilename">
             <string notr="true">:/images/images/icon_add_green.png</string>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtToolButton::toolbutton2</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tab2">
      <attribute name="title">
       <string>亚组</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_3">
       <property name="spacing">
        <number>0</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <item>
        <widget class="SecGroupTableView" name="tableView2"/>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx_line2">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>1</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>1</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::frameEx3</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx_2">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>34</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>34</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::default_type</enum>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="MtToolButton" name="mtToolButton_add2">
            <property name="minimumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="text">
             <string/>
            </property>
            <property name="toolTipText">
             <string>新增分组</string>
            </property>
            <property name="pixmapFilename">
             <string notr="true">:/images/images/icon_add_green.png</string>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtToolButton::toolbutton2</enum>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtTabWidget</class>
   <extends>QTabWidget</extends>
   <header>MtTabWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>GroupTableView</class>
   <extends>QTableView</extends>
   <header>GroupTableView.h</header>
  </customwidget>
  <customwidget>
   <class>SecGroupTableView</class>
   <extends>QTableView</extends>
   <header>secgrouptableview.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
