<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ExportRuleEditWidgetClass</class>
 <widget class="QWidget" name="ExportRuleEditWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>26</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>ExportRuleEditWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>12</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtStackedWidget" name="mtStackedWidget">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>26</height>
      </size>
     </property>
     <property name="currentIndex">
      <number>1</number>
     </property>
     <widget class="QWidget" name="page_1">
      <layout class="QHBoxLayout" name="horizontalLayout">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="MtPushButton" name="mtPushButton_add">
         <property name="text">
          <string>新增规则</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtPushButton::pushbutton5</enum>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>541</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_2">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>0</height>
       </size>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <property name="spacing">
        <number>12</number>
       </property>
       <property name="leftMargin">
        <number>6</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="MtLabel" name="mtLabel_range">
         <property name="minimumSize">
          <size>
           <width>72</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>72</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>内容：仅勾画</string>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx_4">
         <property name="minimumSize">
          <size>
           <width>1</width>
           <height>12</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>1</width>
           <height>12</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::default_type</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtLabel" name="mtLabel_format">
         <property name="minimumSize">
          <size>
           <width>110</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>110</width>
           <height>16777215</height>
          </size>
         </property>
         <property name="text">
          <string>格式：Eclipse 15+</string>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx_5">
         <property name="minimumSize">
          <size>
           <width>1</width>
           <height>12</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>1</width>
           <height>12</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::default_type</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="MtLabel" name="mtLabel_addr">
         <property name="text">
          <string>地址：远程服节点-MANTEIA</string>
         </property>
         <property name="elideMode">
          <enum>Qt::ElideRight</enum>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtLabel::myLabel1</enum>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QWidget" name="widget_opt" native="true">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>0</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>96</width>
           <height>16777215</height>
          </size>
         </property>
         <layout class="QHBoxLayout" name="horizontalLayout_3">
          <property name="spacing">
           <number>8</number>
          </property>
          <property name="leftMargin">
           <number>8</number>
          </property>
          <property name="topMargin">
           <number>0</number>
          </property>
          <property name="rightMargin">
           <number>8</number>
          </property>
          <property name="bottomMargin">
           <number>0</number>
          </property>
          <item>
           <widget class="MtToolButton" name="mtToolButton_mod">
            <property name="minimumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="text">
             <string>...</string>
            </property>
            <property name="iconSize">
             <size>
              <width>22</width>
              <height>22</height>
             </size>
            </property>
            <property name="toolTipText">
             <string>编辑</string>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtToolButton::toolbutton2</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MtToolButton" name="mtToolButton_del">
            <property name="minimumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="text">
             <string>...</string>
            </property>
            <property name="iconSize">
             <size>
              <width>22</width>
              <height>22</height>
             </size>
            </property>
            <property name="toolTipText">
             <string>删除</string>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtToolButton::toolbutton2</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MtToolButton" name="mtToolButton_def">
            <property name="minimumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>26</width>
              <height>26</height>
             </size>
            </property>
            <property name="text">
             <string>...</string>
            </property>
            <property name="iconSize">
             <size>
              <width>22</width>
              <height>22</height>
             </size>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtToolButton::toolbutton2</enum>
            </property>
           </widget>
          </item>
         </layout>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtStackedWidget</class>
   <extends>QStackedWidget</extends>
   <header>MtStackedWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
