﻿#include "MTAutoDelineateService.h"

#include <QDebug>
#include <QUuid>
#include <QTextCodec>

//服务头文件
#include "MTServiceAccessMgr.h"

//内部头文件
#include "CommonUtil.h"


#pragma execution_character_set("utf-8")


MTAutoDelineateService::MTAutoDelineateService(mtcore::IMTContextBase* context)
    :m_autoDelineateManager(context)
{
    m_context = context;
}

MTAutoDelineateService::~MTAutoDelineateService()
{
    m_autoDelineateManager.UnRegisterTopic();
}

bool MTAutoDelineateService::RegisterDelineateStatusHandler(const QString& moduleId, std::function<void(const ST_OutputAutoDelineate&)> statusCallback)
{
    m_autoDelineateManager.RegisterTopics();
    return m_autoDelineateManager.RegisterDelineateStatus<PERSON>and<PERSON>(moduleId, statusCallback);
}

mt_algo::EM_AlgoExecRetType MTAutoDelineateService::GetDelineateParam(const ST_InputAutoDelineate& stInputParam
                                                                      , ST_SketchBusinessInfo& outBusinessParam
                                                                      , std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec
                                                                      , std::vector< ST_REQ_AutoSketchSuper_Organ>& trainOrganVec
                                                                      , std::vector< ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec
                                                                      , QString& errMsg)
{
    std::string err;
    //m_startAlgoMsgUid = QUuid::createUuid().toString().remove("{").remove("}").toStdString();
    mt_algo::EM_AlgoExecRetType algoRetType = m_autoDelineateManager.GetDelineateParam(stInputParam, outBusinessParam, sketchOrganVec, trainOrganVec, emptyOrganVec, err);
    errMsg = err.c_str();
    return algoRetType;
}

QWidget* MTAutoDelineateService::GetDelineateTemplateSettingWidget(const ST_InputDelineateTemplateSetting& stInputParam, QString& errMsg)
{
    return m_autoDelineateManager.GetDelineateTemplateSettingWidget(stInputParam.bShowRoiGroupList
                                                                    , stInputParam.bShowUnattendUsedTip
                                                                    , stInputParam.templateNameListWidth
                                                                    , stInputParam.parentWidget
                                                                    , stInputParam.clientConfigPath
                                                                    , errMsg);
}

void MTAutoDelineateService::InitDelineateTemplateSettingWidget(QWidget* templateWidget)
{
    m_autoDelineateManager.InitDelineateTemplateSettingWidget(templateWidget);
}

void MTAutoDelineateService::DealDelineteTemplateWidgetResult(QWidget* templateWidget)
{
    m_autoDelineateManager.DealDelineteTemplateWidgetResult(templateWidget);
}

bool MTAutoDelineateService::IsDelineateTemplateWidgetEditState(QWidget* templateWidget, bool showErrDlg)
{
    return m_autoDelineateManager.IsDelineateTemplateWidgetEditState(templateWidget, showErrDlg);
}

QWidget* MTAutoDelineateService::GetRoiLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg)
{
    return m_autoDelineateManager.GetRoiLibrarySettingWidget(parentWdgt, errMsg);
}

bool MTAutoDelineateService::IsInfoChanged(QWidget* roiLibraryWidget)
{
    return m_autoDelineateManager.IsInfoChanged(roiLibraryWidget);
}

bool MTAutoDelineateService::InitRoiLibraryWidget(QWidget* roiLibraryWidget, const QString& organDefaultConfigInfoPath)
{
    return m_autoDelineateManager.InitRoiLibraryWidget(roiLibraryWidget, organDefaultConfigInfoPath);
}

void MTAutoDelineateService::SetRoiLibraryWidgetDestroying(QWidget* roiLibraryWidget)
{
    m_autoDelineateManager.SetRoiLibraryWidgetDestroying(roiLibraryWidget);
}

bool MTAutoDelineateService::SavRoiLibrarySettingData(QWidget* roiLibraryWidget)
{
    return m_autoDelineateManager.SavRoiLibrarySettingData(roiLibraryWidget);
}

QWidget* MTAutoDelineateService::GetLabelLibrarySettingWidget(QWidget* parentWdgt, QString& errMsg)
{
    return m_autoDelineateManager.GetLabelLibrarySettingWidget(parentWdgt, errMsg);
}

bool MTAutoDelineateService::IsLabelInfoChanged(QWidget* labelLibraryWidget)
{
    return m_autoDelineateManager.IsLabelInfoChanged(labelLibraryWidget);
}

bool MTAutoDelineateService::InitLabelLibraryWidget(QWidget* labelLibraryWidget)
{
    return m_autoDelineateManager.InitLabelLibraryWidget(labelLibraryWidget);
}

bool MTAutoDelineateService::SavLabelLibrarySettingData(QWidget* labelLibraryWidget)
{
    return m_autoDelineateManager.SavLabelLibrarySettingData(labelLibraryWidget);
}

