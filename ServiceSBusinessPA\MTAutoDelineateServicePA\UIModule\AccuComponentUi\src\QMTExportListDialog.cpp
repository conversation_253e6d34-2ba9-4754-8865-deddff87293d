﻿#include "AccuComponentUi\Header\QMTExportListDialog.h"
#include <QDesktopWidget>
#include <QRect>
#include <QFileDialog>
#include <QRegExp>
#include "MtMessageBox.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "ui_QMTExportListDialog.h"

#include <windows.h>        //注意头文件
#include <windowsx.h>

QMTExportListDialog::QMTExportListDialog(QWidget* parent, DialogType type)
    : QDialog(parent), ui(nullptr)
{
    ui = new Ui::QMTExportListDialog();
    ui->setupUi(this);
    /// ui->label_hidetitle->hide();
    {
        _dialogType = type;
        _listTableView = ui->perlistView;
        _closeMessage1 = "";
        _closeMessage2 = "";
    }
    //this->setFixedSize(QSize(349, 893));
    InitDialogType(_dialogType);
    ui->widget_Image->hide();
    connect(ui->btn_export_2, SIGNAL(clicked()), this, SLOT(slotExportClicked()));
    connect(ui->btn_showAndHide, SIGNAL(clicked()), this, SLOT(slotShowAndHideClicked()));
    connect(ui->btn_close, SIGNAL(clicked()), this, SLOT(slotCloseClicked()));
    connect(_listTableView, SIGNAL(sigRowItemClicked(QString)), this, SLOT(slotCellClicked(QString)));
    // connect(_listTableView, SIGNAL(sigCurrentCellChanged(QMTListViewModelIndex, QMTListViewModelIndex)), this, SLOT(slotCurrentCellChanged(QMTListViewModelIndex, QMTListViewModelIndex)));
    boundaryWidth = 4;                                    //设置触发resize的宽度
    this->setMinimumSize(349, 813);                        //设置最小尺寸
}

QMTExportListDialog::~QMTExportListDialog()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QMTExportListDialog::InitTableView(QMTAbsRowWidgetItemParam& perItemParam)
{
    _listTableView->InitTableView(perItemParam);
}

QMTAbstractTableView* QMTExportListDialog::GetTableView()
{
    return _listTableView;
}

bool QMTExportListDialog::SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QMap<int/*column*/, ICellWidgetParam*>>& cellWidgetParamMapMap)
{
    return _listTableView->SetDataModel(rowValueList, cellWidgetParamMapMap);
}

bool QMTExportListDialog::SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QStringList/*column string list*/>& rowColumnStrListMap)
{
    return _listTableView->SetDataModel(rowValueList, rowColumnStrListMap);
}

void QMTExportListDialog::InitDialogType(DialogType type)
{
    QMTAbsRowWidgetItemParam& perRowItemParam = _listTableView->GetPerRowItemParam();
    perRowItemParam._rowWidgetHeight = 40;
    _defaultExportName = tr("影像组学分析");

    if (Dialog_DiceValue == type)
    {
        ui->label_title2->setText(tr("Dice 值"));
        //QStringList headList;
#if 0
        _listTableView->SetColumnCount(3);
        _listTableView->SetColumnWidth(0, 104);
        _listTableView->SetColumnWidth(1, 107);
        _listTableView->SetColumnWidth(2, 107);
        //_listTableView->SetHorizontalHeaderList(headList);
        _listTableView->SetMainKey("id");
        QMap<int, QString> columnKeyMap;
        columnKeyMap.insert(0, "ROI");
        columnKeyMap.insert(1, "DICEFirst");
        columnKeyMap.insert(2, "DICESecond");
        _listTableView->SetKeyMap(columnKeyMap);
#endif
        //_listTableView->InitTableWidget();
    }
    else
    {
        ui->label_title2->setText(_dialogTitle);
        ui->btn_export_2->hide();
        ui->btn_showAndHide->hide();
        ui->widget_Image->hide();
    }
}

//set
void QMTExportListDialog::SetColumnCount(int columns)
{
    QMTAbsRowWidgetItemParam& perRowItemParam = _listTableView->GetPerRowItemParam();
    perRowItemParam._headParam._defaultColumn = columns;
}


void QMTExportListDialog::SetColumnWidth(int column, int width)
{
    QMTAbsRowWidgetItemParam& perRowItemParam = _listTableView->GetPerRowItemParam();
    perRowItemParam._headParam._columnWidthMap.insert(column, width);
}

bool QMTExportListDialog::SetHorizontalHeaderList(QStringList value)
{
    QMTAbsRowWidgetItemParam& perRowItemParam = _listTableView->GetPerRowItemParam();
    perRowItemParam._headParam._headStrList = value;
    return true;
}

void QMTExportListDialog::SetHorizontalHeaderVisible(bool isvisible)
{
    // _listTableView->SetHorizontalHeaderVisible(isvisible);
    _isHorizontalHeaderVisible = isvisible;
}

void QMTExportListDialog::SetBtnExportVisible(bool isvisible)
{
    if (isvisible)
    {
        ui->btn_export_2->show();
    }
    else
    {
        ui->btn_export_2->hide();
    }
}
void QMTExportListDialog::SetBtnShowAndHideVisible(bool isvisible)
{
    if (isvisible)
    {
        ui->btn_showAndHide->show();
    }
    else
    {
        ui->btn_showAndHide->hide();
    }
}

void QMTExportListDialog::SetDialogTitle(QString text)
{
    _dialogTitle = text;
    ui->label_title->setText(text);
}

void QMTExportListDialog::SetDialogThumbnail(QString ThumbnailName)
{
    _dialogThumbnail = ThumbnailName;
    ui->label_image->setPixmap(QPixmap(ThumbnailName));
}


void QMTExportListDialog::SetDefaultExpName(QString name)
{
    _defaultExportName = name;
}

void QMTExportListDialog::SetCloseMessage(QString message1, QString message2)
{
    this->_closeMessage1 = message1;
    this->_closeMessage2 = message2;
}

/// <summary>
/// 导出excel
/// </summary>
void QMTExportListDialog::slotExportClicked()
{
    emit sigRequestExportSheetFile();
#if 0
    // 1. 去除文件非法字符
    QString exportName = _defaultExportName;
    exportName.remove(QRegExp("[\\\\/:*?\"<>|]"));
    // 保存
    QString fileName = QFileDialog::getSaveFileName(this,
                                                    tr("导出"),
                                                    exportName,
                                                    tr("Excel (*.xlsx)"));

    if (fileName.isNull())
        return;

    _listTableView->ExportExcel(fileName, "sheet1");
#endif
}

void QMTExportListDialog::slotShowAndHideClicked()
{
    QDesktopWidget* desktopWidget = QApplication::desktop();
    //获取可用桌面大小
    QRect deskRect = desktopWidget->availableGeometry();
#if 0
    //获取设备屏幕大小
    QRect screenRect = desktopWidget->screenGeometry();
    int actScreenX = screenRect.width();
    int actScreenY = screenRect.height();
#endif
    int actScreenX = deskRect.width();
    int actScreenY = deskRect.height();
    QWidget* parent = (QWidget*)this->parent();

    if (_isShow)//缩起
    {
        ui->btn_showAndHide->setFixedSize(12, 12);
        ui->btn_showAndHide->setPixmapFilename(":/AccuUIComponentImage/images/openOut.png");
        ui->btn_close->setFixedSize(12, 12);
        ui->btn_close->setPixmapFilename(":/AccuUIComponentImage/images/small_close.png");
        //other
        ui->background->hide();
        ui->widget_3->hide();
        ui->label_image->setFixedSize(60, 36);
        ui->widget_Image->show();
        _isShow = false;
        this->setFixedSize(QSize(75, 90));
        int heightStep = 92;

        if (Dialog_DiceValue == _dialogType)
        {
            ui->label_image->setPixmap(QPixmap(":/AccuUIComponentImage/images/Dice_shrink.png"));
            this->move(actScreenX - 85, 176 + heightStep);
        }
        else
        {
            ui->label_title2->setText(_dialogTitle);
            this->move(actScreenX - 85, 176 + heightStep * 2);
        }

        ui->horizontalLayout_5->setContentsMargins(0, 0, 8, 0);
        emit sigIsOpenOut(false);
    }
    else
    {
        ui->btn_showAndHide->setFixedSize(16, 16);
        ui->btn_showAndHide->setPixmapFilename(":/AccuUIComponentImage/images/reduce.png");
        ui->btn_close->setFixedSize(16, 16);
        ui->btn_close->setPixmapFilename(":/AccuUIComponentImage/images/close.png");
        //other
        ui->background->show();
        ui->widget_3->show();
        ui->widget_Image->hide();
        ui->horizontalLayout_5->setContentsMargins(0, 14, 11, 0);
        _isShow = true;
        this->setMinimumSize(QSize(349, 813));
        this->setMaximumSize(QSize(3490, 8130));
        this->move(actScreenX - 359, 176);
        emit sigIsOpenOut(true);
    }
}

void QMTExportListDialog::slotCloseClicked()
{
    QString message1;
    QString message2;

    if (Dialog_DiceValue == _dialogType)
    {
        message1 = tr("确认关闭当前DICE值计算结果吗?");
        message2 = tr("关闭后计算结果将被丢弃");
    }
    else if (!_closeMessage1.isEmpty())
    {
        message1 = _closeMessage1;
        message2 = _closeMessage2;
    }
    else
    {
        message1 = tr("确认关闭当前计算结果吗?");
        message2 = tr("关闭后计算结果将被丢弃");
    }

    QMessageBox::StandardButton ret = QMessageBox::Yes;

    if (!ui->btn_export_2->isHidden())
    {
        ret = MtMessageBox::NoIcon::question_Title(this, message1, message2);
    }

    if (ret == QMessageBox::Yes)
    {
        this->hide();
        emit sigClickedClose();
    }
}

#if 0
void QMTExportListDialog::slotCellClicked(QMTListViewModelIndex modelIndex)
{
    QString key = modelIndex._firstValue;
    emit sigCellClicked(key);
}



void QMTExportListDialog::slotCurrentCellChanged(QMTListViewModelIndex modelIndex, QMTListViewModelIndex preModelIndex)
{
    QString key = modelIndex._firstValue;
    QString preKey = preModelIndex._firstValue;
    emit sigCurrentCellChanged(key, preKey);
}
#endif

void QMTExportListDialog::slotCellClicked(QString uniqueValue)
{
    emit sigCellClicked(uniqueValue);
}

bool QMTExportListDialog::nativeEvent(const QByteArray& eventType, void* message, long* result)
{
    MSG* msg = (MSG*)message;

    switch (msg->message)
    {
        case WM_NCHITTEST:
            int xPos = GET_X_LPARAM(msg->lParam) - this->frameGeometry().x();
            int yPos = GET_Y_LPARAM(msg->lParam) - this->frameGeometry().y();

            if (xPos < boundaryWidth && yPos < boundaryWidth)                    //左上角
                *result = HTTOPLEFT;
            else if (xPos >= width() - boundaryWidth && yPos < boundaryWidth)          //右上角
                *result = HTTOPRIGHT;
            else if (xPos < boundaryWidth && yPos >= height() - boundaryWidth)       //左下角
                *result = HTBOTTOMLEFT;
            else if (xPos >= width() - boundaryWidth && yPos >= height() - boundaryWidth)//右下角
                *result = HTBOTTOMRIGHT;
            else if (xPos < boundaryWidth)                                     //左边
                *result = HTLEFT;
            else if (xPos >= width() - boundaryWidth)                              //右边
                *result = HTRIGHT;
            else if (yPos < boundaryWidth)                                       //上边
                *result = HTTOP;
            else if (yPos >= height() - boundaryWidth)                             //下边
                *result = HTBOTTOM;
            else              //其他部分不做处理，返回false，留给其他事件处理器处理
                return false;

            return true;
    }

    return false;         //此处返回false，留给其他事件处理器处理
}


void QMTExportListDialog::mousePressEvent(QMouseEvent* e)
{
    if (e->button() == Qt::LeftButton)
        clickPos = e->pos();
}

void QMTExportListDialog::mouseMoveEvent(QMouseEvent* e)
{
    if (e->buttons() & Qt::LeftButton)
    {
        move(e->pos() + pos() - clickPos);
    }
}

void QMTExportListDialog::resizeEvent(QResizeEvent* size)
{
    if (false == _isPress)
    {
        _isPress = true;
        return;
    }

    int listViewWidth = _listTableView->width();
    QMTAbsRowWidgetItemParam& perRowItemParam = _listTableView->GetPerRowItemParam();
    int column = perRowItemParam._headParam._defaultColumn;
    int averageWidth = listViewWidth / column;
    int addWidth = 0;

    for (int i = 0; i < column; ++i)
    {
        if (i == column - 1)
        {
            int width = listViewWidth - addWidth - 10;
            // _listTableView->ResizeHorizontalSection(i, width);
            _listTableView->horizontalHeader()->resizeSection(i, width);
        }
        else
        {
            //_listTableView->ResizeHorizontalSection(i, averageWidth);
            _listTableView->horizontalHeader()->resizeSection(i, averageWidth);
            addWidth += averageWidth;
        }
    }
}

void QMTExportListDialog::keyPressEvent(QKeyEvent* ev)
{
    if (ev->key() == Qt::Key_Escape)
    {
        if (!ui->btn_close->isHidden())
        {
            slotCloseClicked();
        }

        return;
    }

    QWidget::keyPressEvent(ev);
}