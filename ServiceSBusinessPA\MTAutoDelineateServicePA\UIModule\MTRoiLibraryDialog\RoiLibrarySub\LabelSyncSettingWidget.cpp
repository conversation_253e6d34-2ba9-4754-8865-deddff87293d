﻿#include "LabelSyncSettingWidget.h"
#include "CMtLanguageUtil.h"

LabelSyncSettingWidget::LabelSyncSettingWidget(QWidget* parent /*= Q_NULLPTR*/)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setDialogWidthAndContentHeight(406, 30); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("信息同步设置"));                     //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));

    if (CMtLanguageUtil::type == english)
    {
        ui.mtLabel->setText(tr("勾选后，若ROI库的ROI名称、颜色、类型发生变动，则会自动更新对应的标签信息"));
    }

    connect(ui.mtCheckBox, &MtCheckBox::stateChanged, this, &LabelSyncSettingWidget::slotStateChanged);
}

LabelSyncSettingWidget::~LabelSyncSettingWidget()
{
}

void LabelSyncSettingWidget::slotStateChanged(int state)
{
    m_bSync = Qt::Checked == state;
}

void LabelSyncSettingWidget::setSync(bool bSync)
{
    m_bSync = bSync;
    ui.mtCheckBox->setChecked(m_bSync);
}

bool LabelSyncSettingWidget::getSync()
{
    return m_bSync;
}