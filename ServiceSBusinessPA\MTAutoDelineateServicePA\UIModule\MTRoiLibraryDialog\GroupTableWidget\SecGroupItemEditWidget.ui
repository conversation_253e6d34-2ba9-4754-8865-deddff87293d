<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SecGroupItemEditWidget</class>
 <widget class="QWidget" name="SecGroupItemEditWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>432</width>
    <height>68</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>SecGroupItemEditWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QGridLayout" name="gridLayout">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <property name="spacing">
       <number>16</number>
      </property>
      <item row="0" column="0">
       <widget class="MtLabel" name="mtLabel">
        <property name="text">
         <string>分组名称</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1_1</enum>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="MtLineEdit" name="mtLineEditg_groupName">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>26</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>26</height>
         </size>
        </property>
        <property name="maxLength">
         <number>64</number>
        </property>
        <property name="elideMode">
         <enum>Qt::ElideRight</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLineEdit::lineedit1</enum>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="MtLabel" name="mtLabel_2">
        <property name="text">
         <string>关联ROI</string>
        </property>
        <property name="alignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1_1</enum>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="MtComboBox" name="mtComboBox_refROI">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>26</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>26</height>
         </size>
        </property>
        <property name="viewTextElideMode">
         <enum>Qt::ElideRight</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtComboBox::combobox1</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
