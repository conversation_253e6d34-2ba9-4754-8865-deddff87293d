﻿#pragma once

#include <QListWidget>
#include "QMTParamItemWidget.h"
//每一行都是QMTParamItemWidget


class  QMTAbstractParamListWidget : public QListWidget
{
    Q_OBJECT

public:
    QMTAbstractParamListWidget(QWidget* parent);
    ~QMTAbstractParamListWidget();
    //add
    void AddRowItem(QList<ItemParam>&);
    void AddRowItem(QString, QString, QString key2 = "", QString value2 = "", QString key3 = "", QString value3 = "");
    //delete
    void ClearDataModel();
};
