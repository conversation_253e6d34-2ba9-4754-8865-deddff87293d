﻿# 目标名 -------------------------------------------------------------------------
set(TARGET_NAME MTAutoDelineateServicePA)
# 设置目标类型：executable | service | business （可执行|服务插件|业务插件）
# set(TARGET_TYPE unknown)

# 添加DEFINE --------------------------------------------------------------------
set(DEBUG_DEFINE   MTAutoDelineateServicePA_LIB)
set(RELEASE_DEFINE MTAutoDelineateServicePA_LIB)

# 设置使用到的Qt模块 ------------------------------------------------------------------
set(QT_MODULES Core Network Gui Widgets Xml)

find_package(Qt5 COMPONENTS ${QT_MODULES} REQUIRED)

# 所有源文件
file(GLOB_RECURSE SRC_FILE "*.cpp" "*.cxx" "*.cc" "*.C" "*.c++" "*.h" "*.hpp" "*.H" "*.hxx" "*.ui" "*.qrc" "*.ts")

# 排除生成的moc、qrc和ui文件
foreach(file ${SRC_FILE})
    list(APPEND TARGET_SRC ${file})
endforeach(file ${SRC_FILE})
list(FILTER TARGET_SRC EXCLUDE REGEX "moc_|qrc_|ui_")

# 排除文件 ------------------------------------------------------------------------
# list(FILTER TARGET_SRC EXCLUDE REGEX "UIModule/ROIToolWidget.cpp")

# 添加VS过滤器
source_group_by_dir(TARGET_SRC)

# packages.config
file(GLOB PACKAGES_CONFIG "packages.config")

add_library(${TARGET_NAME} SHARED 
    ${TARGET_SRC}
    ${PACKAGES_CONFIG}
)

# 添加属性表 -----------------------------------------------------------------------
set(VS_PROPS
    ${PROJECT_SOURCE_DIR}/Props/A_ToolNuget/MTFileOperateModule.props
    ${PROJECT_SOURCE_DIR}/Props/Interaction.props
    ${PROJECT_SOURCE_DIR}/Props/SDKFrameWork/MTComponentFrameLib.props
	${PROJECT_SOURCE_DIR}/Props/SDKFrameWork/MTModuleFrameLib.props
	${PROJECT_SOURCE_DIR}/Props/UI/Manteia.Core.Widget.props
	${PROJECT_SOURCE_DIR}/Props/UI/Manteia.Domain.Widget.props
	${PROJECT_SOURCE_DIR}/Props/Utils/MTJsonConvert.props
    ${PROJECT_SOURCE_DIR}/Props/A_AStructData/MTClusterRpcData.props
    ${PROJECT_SOURCE_DIR}/Props/RpcData/MTCommonRpcData.props
    ${PROJECT_SOURCE_DIR}/Props/RpcData/MTDicomRpcData.props
)

set_target_properties(${TARGET_NAME} PROPERTIES 
    VS_USER_PROPS "${VS_PROPS}"
)

# UI文件所在目录 --------------------------------------------------------------------
# set(UI_DIR 
#     ${CMAKE_CURRENT_SOURCE_DIR}/UIModule/IsoDoseUI
#     ${CMAKE_CURRENT_SOURCE_DIR}/UIModule/PoiUI
#     ${CMAKE_CURRENT_SOURCE_DIR}/UIModule/RegistrationUI
#     ${CMAKE_CURRENT_SOURCE_DIR}/UIModule/RoiUI
# )

set_target_properties(${TARGET_NAME} PROPERTIES 
    AUTOUIC_SEARCH_PATHS "${UI_DIR}"
)

set_target_properties(${TARGET_NAME} PROPERTIES COMPILE_DEFINITIONS
    "$<$<CONFIG:DEBUG>:${DEBUG_DEFINE}>$<$<CONFIG:RELEASE>:${RELEASE_DEFINE}>"
)

add_definitions(-w)

# add_dependencies(${TARGET_NAME} MTRpcTcpServerServicePA)

# 链接使用到的Qt模块
foreach(MODULE ${QT_MODULES})
    target_link_libraries(${TARGET_NAME} Qt5::${MODULE})
endforeach(MODULE ${QT_MODULES})


# 使用到的NuGet包 ------------------------------------------------------------------
install_nuget(${TARGET_NAME} Manteia.Core.MTCore.Qt)
install_nuget(${TARGET_NAME} Manteia.Core.Exception)
install_nuget(${TARGET_NAME} Manteia.Core.DcmParse.Qt)
install_nuget(${TARGET_NAME} Manteia.Core.RPC)
install_nuget(${TARGET_NAME} Manteia.Common.table.Data)
install_nuget(${TARGET_NAME} Manteia.Common.table.Data)
install_nuget(${TARGET_NAME} Manteia.Common.tableBusiness.Data)

# 添加include path --------------------------------------------------------------
target_include_directories(${TARGET_NAME} PUBLIC ExportInterface)
target_include_directories(${TARGET_NAME} PUBLIC Business)
target_include_directories(${TARGET_NAME} PUBLIC Utils)
target_include_directories(${TARGET_NAME} PUBLIC UIModule)
target_include_directories(${TARGET_NAME} PUBLIC Resources)
target_include_directories(${TARGET_NAME} PUBLIC AccuComponentUi)
target_include_directories(${TARGET_NAME} PUBLIC .)

# if(${TARGET_TYPE} STREQUAL service)
#     set_property(GLOBAL APPEND PROPERTY PLUGINS_SERVICES ${TARGET_NAME})
# elseif(${TARGET_TYPE} STREQUAL business)
#     set_property(GLOBAL APPEND PROPERTY PLUGINS_BUSINESS ${TARGET_NAME})
# endif()

# build后Copy事件 ----------------------------------------------------------------
# 1. 路径使用/，不要使用\
# 2. 路径是文件夹的后面要加/
after_build_copy(${TARGET_NAME} 
    ${CMAKE_CURRENT_SOURCE_DIR}/ExportInterface/*
    ${PROJECT_SOURCE_DIR}/Dependencies/Interaction/Service/MTAutoDelineateService/
)
after_build_copy(${TARGET_NAME} 
    ${CMAKE_RUNTIME_OUTPUT_DIRECTORY}/$<$<CONFIG:DEBUG>:Debug>$<$<CONFIG:RELEASE>:Release>/${TARGET_NAME}.dll
    ${PROJECT_SOURCE_DIR}/Dependencies/ServiceSBusinessPA/$(Configuration)/${TARGET_NAME}.dll
)