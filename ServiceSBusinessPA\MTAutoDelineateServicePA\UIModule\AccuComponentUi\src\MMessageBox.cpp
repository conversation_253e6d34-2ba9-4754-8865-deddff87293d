﻿#include "AccuComponentUi\Header\MMessageBox.h"
#include <QHelpEvent>
#include <QToolTip>
#include <QDebug>
#include <QGraphicsDropShadowEffect>
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "ui_MMessageBox.h"

/// <summary>
/// 构造
/// 1. 无边框，工具类窗体
/// </summary>
/// <param name="parent">父窗体</param>
/// <returns></returns>
MMessageBox::MMessageBox(QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint | Qt::Tool | Qt::WindowStaysOnTopHint), ui(nullptr)
{
    ui = new Ui::MMessageBox;
    ui->setupUi(this);    // Create the UI
    ui->messageLabel->setProperty(QssPropertyKey, QssPropertyLabelThirdTitle);
    ui->messageLabel2->setProperty(QssPropertyKey, QssLabelThirdTitleComment);
    QApplication::restoreOverrideCursor();      //恢复鼠标状态
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedHeight(198);
    darkenWidget = new DarkeningWidget();// 不能继承this，否则会显示在最前面
    // 1. 模态对话框
    setModal(true);
    setAttribute(Qt::WA_TranslucentBackground);
    // 2. 默认设置，yes按键获取焦点，设置message pixelSize（由于需要在显示之前对字符串长度进行判断）
    ui->pushButton_yes->setFocus();
    resultButton = QMessageBox::NoButton;
    ui->messageLabel->setWordWrap(true);
    ui->messageLabel2->setWordWrap(true);
    // 3. 信号槽，设置返回值，关闭窗体
    connect(ui->pushButton_cancel, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Cancel; this->close();
    });//this->setResult(QMessageBox::Cancel);
    connect(ui->pushButton_no, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::No; ; this->close();
    });
    connect(ui->pushButton_yes, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Yes;; this->close();
    });
    connect(ui->pushButton_save, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Save; ; this->close();
    });
    // 4. 英文
    setEnglishLayout();
    //////悬浮效果
#if 1
    shadow_effect = new QGraphicsDropShadowEffect(this);
    shadow_effect->setOffset(4, 4);
    shadow_effect->setBlurRadius(20);//目前测试20效果最佳
    shadow_effect->setColor(QColor(0, 0, 0, 40));
    ui->horizontalLayout->setMargin(12);
    ui->frame->setGraphicsEffect(shadow_effect);
#endif
    //this->
}

MMessageBox::~MMessageBox()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }

    if (darkenWidget != NULL)
    {
        darkenWidget->hide();
        delete darkenWidget;
    }

    if (shadow_effect != NULL)
    {
        delete shadow_effect;
        shadow_effect = nullptr;
    }
}
/// <summary>
/// 创建此构造的目的主要是为了测试，不建议直接这么使用，建议还是用Show
/// </summary>
/// <param name="buttons"></param>
/// <param name="buttonText"></param>
/// <param name="message"></param>
/// <param name="newLine"></param>
/// <param name="hideLogo"></param>
/// <param name="parent"></param>
MMessageBox::MMessageBox(QMessageBox::StandardButtons buttons, QList<QString> buttonText, QList<QString> message, bool hideLogo, QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint | Qt::Tool)
{
    ui->setupUi(this);    // Create the UI
    ui->messageLabel->setProperty(QssPropertyKey, QssPropertyLabelThirdTitle);
    ui->messageLabel2->setProperty(QssPropertyKey, QssLabelThirdTitleComment);
    darkenWidget = new DarkeningWidget(this);
    // 1. 模态对话框
    setModal(true);
    setAttribute(Qt::WA_TranslucentBackground);
    Language::setFontSize(this);
    // 2. 默认设置，yes按键获取焦点
    ui->pushButton_yes->setFocus();
    resultButton = QMessageBox::NoButton;
    // 3. 信号槽，设置返回值，关闭窗体
    connect(ui->pushButton_cancel, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Cancel; this->close();
    });//this->setResult(QMessageBox::Cancel);
    connect(ui->pushButton_no, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::No; ; this->close();
    });
    connect(ui->pushButton_yes, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Yes;; this->close();
    });
    connect(ui->pushButton_save, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Save; ; this->close();
    });
    SetButtons(buttons);
    SetButtonText(buttonText);
    SetMessage(message);
    HideLogo(hideLogo);
}
/// <summary>
/// 测试用，不建议直接使用
/// </summary>
/// <returns></returns>
QMessageBox::StandardButton MMessageBox::getResultButton()
{
    return this->resultButton;
}
/// <summary>
/// 显示MMessageBox单独的yes按键
/// </summary>
/// <param name="message">label中需要显示的文字，newLine=true </param>
/// <param name="buttonText">Yes按键字符串</param>
/// <param name="hideLogo">true(默认):隐藏logo。 false: 显示</param>
/// <param name="parent">父窗体</param>
/// <returns></returns>
void MMessageBox::Show(QString message, QString buttonText, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(QMessageBox::Yes);
    messagebox.SetButtonText(QList<QString>() << buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
}
void MMessageBox::ShowCancel(QString message, QString buttonText, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(QMessageBox::Yes | QMessageBox::Cancel);
    messagebox.SetButtonText(QList<QString>() << buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
}

/// <summary>
/// 显示警告
/// </summary>
/// <param name="message"></param>
/// <param name="buttonText"></param>
/// <param name="hideLogo"></param>
/// <param name="hideDarken"></param>
/// <param name="parent"></param>
void MMessageBox::ShowWarning2(QString message, QString buttonText, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(QMessageBox::Yes);
    messagebox.SetButtonText(QList<QString>() << buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(false);
    messagebox.SetIcon("image: url(:/AccuUIComponentImage/images/warning.png);");
    messagebox.exec();
}

void MMessageBox::ShowWarning(QString message, QString message2, QString buttonText, bool hideDarken, bool hideLogo, QWidget* parent)
{
    MMessageBox messagebox(parent);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(QMessageBox::Yes);
    messagebox.SetButtonText(QList<QString>() << buttonText);
    messagebox.SetMessage(QStringList() << message << message2);
    messagebox.HideLogo(hideLogo);
    messagebox.SetIcon("image: url(:/AccuUIComponentImage/images/warning.png);");

    if (message2.size() > 0 && hideLogo)
    {
        messagebox.ui->horizontalSpacer_8->changeSize(50, 20);
        messagebox.ui->messageLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        messagebox.ui->messageLabel2->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    }

    messagebox.exec();
}

QMessageBox::StandardButton MMessageBox::ShowWarning(bool cancel, QString message, QString message2,
                                                     QString noStyle, QString OkStyle, bool hideDarken, bool hideLogo, QWidget* parent)
{
    MMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    QStringList buttonText;
    buttonText << tr("确定") << tr("取消");
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);

    if (noStyle.size())
    {
        messagebox.SetNoStyle(noStyle);
    }

    if (OkStyle.size())
    {
        messagebox.SetYesStyle(OkStyle);
    }

    messagebox.SetButtonText(buttonText);

    if (message2.size() > 0)
    {
        messagebox.SetMessage1(message, false);
        messagebox.SetMessage2(message2);
    }
    else
    {
        messagebox.SetMessage1(message);
    }

    //messagebox.HideLogo(hideLogo);
    messagebox.HideLogo(hideLogo);
    messagebox.SetIcon("image: url(:/AccuUIComponentImage/images/warning.png);");

    if (message2.size() > 0 && hideLogo)
    {
        messagebox.ui->horizontalSpacer_8->changeSize(50, 20);
        messagebox.ui->messageLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        messagebox.ui->messageLabel2->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    }

    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

/// <summary>
/// 显示MMessageBox
/// </summary>
/// <param name="buttons">需要显示的button，为Yes,No,Save,Cancel的组合</param>
/// <param name="buttonText">字符串数量最多为button数量(3个，没有Cancel)，顺序为Yes（No（Save）），可以只设置Yes，或值设置Yes,No</param>
/// <param name="message">label中需要显示的文字</param>
/// <param name="newLine">true(默认):超长启动新行，总共最多两行。false:不启动新行</param>
/// <param name="hideLogo">true(默认):隐藏logo。 false: 显示</param>
/// <param name="parent">父窗体</param>
/// <returns>根据按键按下情况返回 Yes|No|Save|Cancel</returns>
QMessageBox::StandardButton MMessageBox::Show(QMessageBox::StandardButtons buttons, QList<QString> buttonText, QString message, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);
    messagebox.SetButtonText(buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;//.result();
}

/// <summary>
/// 显示MMessageBox
/// </summary>
/// <param name="buttons">需要显示的button，为Yes,No,Save,Cancel的组合</param>
/// <param name="buttonText">字符串数量最多为button数量(3个，没有Cancel)，顺序为Yes（No（Save）），可以只设置Yes，或值设置Yes,No</param>
/// <param name="message">label与label2中需要显示的文字</param>
/// <param name="newLine">newLine.at(0)设置label，newLine.at(1) 设置label2。
/// true(默认):超长启动新行，总共最多两行。false:不启动新行</param>
/// <param name="hideLogo">true(默认):隐藏logo。 false: 显示</param>
/// <param name="parent">父窗体</param>
/// <returns>根据按键按下情况返回 Yes|No|Save|Cancel</returns>
/// <example>
// QMessageBox::StandardButton rt = MMessageBox::Show(QMessageBox::Yes | QMessageBox::Save,
//QList<QString>() << tr("是") << tr("") << tr("取消"),
//QList<QString>() << "message 1 " << "message 2 ",
//QList<bool>() << true << false, false);
///</example>
QMessageBox::StandardButton MMessageBox::Show(QMessageBox::StandardButtons buttons, QList<QString> buttonText, QList<QString> message, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);
    messagebox.SetButtonText(buttonText);
    messagebox.SetMessage(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}


QMessageBox::StandardButton MMessageBox::Show(QList<QString> buttonText, QList<QString> message, bool hideLogo, bool hideDarken, QWidget* parent)
{
    QMessageBox::StandardButtons buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Save | QMessageBox::Cancel;
    MMessageBox messagebox(parent);
    QString yesStyle = "#pushButton_yes{font-size:12px;min-width:80px;min-height:30px;max-width:80px;max-height:30px;background-color: rgb(56,67,85);color: rgba(188,186,186,1);border-style: outset; border-width: 1px;  border-color: rgba(188,186,186,0.34);}"
        "#pushButton_yes:hover{background-color:rgba(216, 216, 216, 0.1);}";
    messagebox.SetYesStyle(yesStyle);
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);
    messagebox.SetButtonText(buttonText);
    messagebox.SetMessage(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}


QMessageBox::StandardButton MMessageBox::ShowOKAndNo(bool cancel, QString message, QString message2,
                                                     QString noStyle, QString OkStyle, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    QStringList buttonText;
    buttonText << tr("确定") << tr("取消");
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);

    if (noStyle.size())
    {
        messagebox.SetNoStyle(noStyle);
    }

    if (OkStyle.size())
    {
        messagebox.SetYesStyle(OkStyle);
    }

    messagebox.SetButtonText(buttonText);

    if (message2.size() > 0)
    {
        messagebox.SetMessage1(message, false);
        messagebox.SetMessage2(message2);
    }
    else
    {
        messagebox.SetMessage1(message);
    }

    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton MMessageBox::ShowDeleteWarning(bool cancel, QString message, QString message2, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;
    QString noStyle = "#pushButton_no{background-color: rgb(78,156,213);color:rgb(255,255,255);border-width:0px;}"
        "#pushButton_no:hover{background-color: rgb(60,143,203);}";
    QString yesStyle = "#pushButton_yes{background-color:rgb(@colorB4);color:rgb(255,255,255);border-width:0px;}"
        "#pushButton_yes:hover{background-color: rgb(194,86,86);}";
    CMtCoreWidgetUtil::formatStyleSheet(yesStyle);

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    QStringList buttonText;
    buttonText << tr("确定") << tr("取消");
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);

    if (noStyle.size())
    {
        messagebox.SetNoStyle(noStyle);
    }

    if (yesStyle.size())
    {
        messagebox.SetYesStyle(yesStyle);
    }

    messagebox.SetButtonText(buttonText);

    if (message2.size() > 0)
    {
        messagebox.SetMessage1(message, false);
        messagebox.SetMessage2(message2);
    }
    else
    {
        messagebox.SetMessage1(message);
    }

    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton MMessageBox::ShowSaveAndNo(bool cancel, QString message, bool hideLogo, bool hideDarken, QWidget* parent)
{
    MMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    QStringList buttonText;
    buttonText << tr("保存") << tr("不保存");
    messagebox.HideDarken(hideDarken);
    messagebox.SetButtons(buttons);
    messagebox.SetButtonText(buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

/// <summary>
/// 隐藏No按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void MMessageBox::HideNoButton(bool value)
{
    if (value)
        ui->pushButton_no->hide();
    else
        ui->pushButton_no->show();
}
/// <summary>
/// 隐藏Yes按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void MMessageBox::HideYesButton(bool value)
{
    if (value)
        ui->pushButton_yes->hide();
    else
        ui->pushButton_yes->show();
}
/// <summary>
/// 隐藏Save按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void MMessageBox::HideSaveButton(bool value)
{
    if (value)
        ui->pushButton_save->hide();
    else
        ui->pushButton_save->show();
}
/// <summary>
/// 隐藏Cancel按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void MMessageBox::HideCancelButton(bool value)
{
    if (value)
        ui->pushButton_cancel->hide();
    else
        ui->pushButton_cancel->show();
}
/// <summary>
/// 隐藏logo图片
/// 1. 隐藏的情况下Message居中，显示的情况下Message居左
/// 2. 隐藏情况下label宽度延申置350（默认305）
/// </summary>
/// <param name="value">true(默认):隐藏logo。 false: 显示</param>
void MMessageBox::HideLogo(bool value)
{
    if (value)
    {
        ui->label_image->hide();
        ui->verticalSpacer_manteia1->changeSize(0, 0);
        ui->verticalSpacer_manteia2->changeSize(0, 0);
        ui->messageLabel->setMinimumWidth(350);
        ui->messageLabel2->setMinimumWidth(350);
        ui->messageLabel->setAlignment(Qt::AlignHCenter | Qt::AlignBottom);
        ui->messageLabel2->setAlignment(Qt::AlignHCenter | Qt::AlignTop);
        ui->verticalSpacer_manteia3->changeSize(0, 0, QSizePolicy::Fixed, QSizePolicy::Fixed);
    }
    else
    {
        ui->label_image->show();
        ui->messageLabel->setAlignment(Qt::AlignLeft | Qt::AlignBottom);
        ui->messageLabel2->setAlignment(Qt::AlignLeft | Qt::AlignTop);
        ui->verticalSpacer_manteia3->changeSize(16, 20, QSizePolicy::Fixed, QSizePolicy::Fixed);
    }
}
/// <summary>
/// 是否显示全屏黑色背景
/// </summary>
/// <param name="value"></param>
void MMessageBox::HideDarken(bool value)
{
    if (value)
        this->darkenWidget->hide();
    else
        this->darkenWidget->show();
}
/// <summary>
/// 设置显示的button
/// </summary>
/// <param name="buttons">
/// Yes: 显示yes按键
/// No: 显示no按键
/// Save: 显示save按键
/// Cancel: 显示cancel按键
/// </param>
void MMessageBox::SetButtons(QMessageBox::StandardButtons buttons)
{
    if ((buttons & QMessageBox::Yes) == QMessageBox::NoButton)
        this->HideYesButton(true);
    else
        this->HideYesButton(false);

    if ((buttons & QMessageBox::No) == QMessageBox::NoButton)
        this->HideNoButton(true);
    else
        this->HideNoButton(false);

    if ((buttons & QMessageBox::Save) == QMessageBox::NoButton)
        this->HideSaveButton(true);
    else
        this->HideSaveButton(false);

    if ((buttons & QMessageBox::Cancel) == QMessageBox::NoButton)
        this->HideCancelButton(true);
    else
        this->HideCancelButton(false);
}

void MMessageBox::SetYesStyle(QString style)
{
    ui->pushButton_yes->setStyleSheet(style);
    ui->pushButton_yes->repaint();
    ui->pushButton_yes->update();
}

void MMessageBox::SetNoStyle(QString style)
{
    ui->pushButton_no->setStyleSheet(style);
    ui->pushButton_no->repaint();
    ui->pushButton_no->update();
}
/// <summary>
/// 设置label的text
/// 1. 文本截断，过长的字符串末尾变成 …
/// 2. 隐藏label2
/// </summary>
/// <param name="message">label中需要显示的文字</param>
/// <param name="newLine">true(默认):超长启动新行，总共最多两行。false:不启动新行</param>
/// <param name="hideLabel2">true(默认）：隐藏label2。false:显示</param>
void MMessageBox::SetMessage1(QString message, bool hideLabel2)
{
    SetMessageWidth(message, ui->messageLabel);

    // 2. 隐藏label2
    if (hideLabel2)
    {
        ui->verticalSpacer_9->changeSize(0, 0, QSizePolicy::Fixed, QSizePolicy::Fixed);
        ui->messageLabel2->hide();
    }
}
/// <summary>
/// 设置label2的text
/// 1. 文本截断，过长的字符串末尾变成 …
/// </summary>
/// <param name="message"></param>
/// <param name="newLine">true(默认):超长启动新行，总共最多两行。false:不启动新行</param>
void MMessageBox::SetMessage2(QString message)
{
    SetMessageWidth(message, ui->messageLabel2);
    ui->verticalSpacer_9->changeSize(10, 10, QSizePolicy::Fixed, QSizePolicy::Fixed);
}
/// <summary>
/// 设置label和label2的text
/// 1. label2 样式为QLabel_Dot，字符串超出自动截断为…
/// </summary>
/// <param name="messages">字符串数量为label数量（2个），顺序为label,label2</param>
void MMessageBox::SetMessage(QList<QString> messages)
{
    if (messages.count() > 0)
    {
        this->SetMessage1(messages.at(0), false);
    }

    if (messages.count() > 1 && !messages.at(1).isEmpty())
    {
        this->SetMessage2(messages.at(1));
    }
    else
        ui->messageLabel2->hide();
}
/// <summary>
/// 设置所有button的text
/// </summary>
/// <param name="texts">字符串数量最多为button数量(3个，没有Cancel)，顺序为Yes（No（Save）），可以只设置Yes，或值设置Yes,No</param>
void MMessageBox::SetButtonText(QList<QString> texts)
{
    if (texts.count() > 0)
        ui->pushButton_yes->setText(texts.at(0));

    if (texts.count() > 1)
        ui->pushButton_no->setText(texts.at(1));

    if (texts.count() > 2)
        ui->pushButton_save->setText(texts.at(2));
}
/// <summary>
/// 设置icon 的图片
/// </summary>
/// <param name="urls"></param>
void MMessageBox::SetIcon(QString urls)
{
    ui->label_image->setStyleSheet(urls);
}

/// <summary>
/// 1. 鼠标悬停显示label whatsThis = > 改为直接由QLabel_Dot管理
/// </summary>
/// <param name="object"></param>
/// <param name="event"></param>
/// <returns></returns>
//bool MMessageBox::eventFilter(QObject *object, QEvent *event)
//{
//    // 1. 显示label whatsThis
//    if (event->type() == QEvent::ToolTip)
//    {
//        QString msg;
//        QHelpEvent* helpEvent = (QHelpEvent*)(event);
//        int x = helpEvent->globalPos().x();
//        int y = helpEvent->globalPos().y();
//
//        if (x >= messageLabel->mapToGlobal(QPoint(0, 0)).x() && x <= messageLabel->mapToGlobal(QPoint(0, 0)).x() + 305
//            && y >= messageLabel->mapToGlobal(QPoint(0, 0)).y() && y <= messageLabel->mapToGlobal(QPoint(0, 0)).y() + 15)
//        {
//            msg = messageLabel->whatsThis();
//        }
//        else if (x >= messageLabel2->mapToGlobal(QPoint(0, 0)).x() && x <= messageLabel2->mapToGlobal(QPoint(0, 0)).x() + 305
//            && y >= messageLabel2->mapToGlobal(QPoint(0, 0)).y() && y <= messageLabel2->mapToGlobal(QPoint(0, 0)).y() + 13)
//        {
//            msg = messageLabel2->whatsThis();
//        }
//
//        if (!msg.isEmpty())
//        {
//            QToolTip::showText(helpEvent->globalPos(), msg);
//        }
//    }
//    return QDialog::eventFilter(object, event);
//}
/// <summary>
/// 设置messsage 的显示宽度
/// 1. 通过字符串对应的字体宽度设置label最小宽度
/// </summary>
/// <param name="message"></param>
/// <param name="label"></param>
void MMessageBox::SetMessageWidth(QString message, QLabel_Dot* label)
{
    QString temp = message;
    //QFontMetrics metrics(label->font());
    //QRect rect = metrics.boundingRect(message);
    //int tmpwidth = rect.width();
    QStringList tmpmessage = message.split("\n");
    int tmpwidth = 0;

    foreach(QString msg, tmpmessage)
    {
        QFontMetrics fontWidth(label->font());
        int tmpwidth1 = fontWidth.width(msg);
        tmpwidth = tmpwidth1 > tmpwidth ? tmpwidth1 : tmpwidth;
    }

    if (tmpwidth > label->maximumWidth())
    {
        label->setMinimumWidth(label->maximumWidth());
    }
    else
    {
        label->setMinimumWidth(tmpwidth + 50);// ??? 单纯tmpwidth的情况下一直会有一个字符被强制换行
    }

    label->setTextElided(temp);
}

/// <summary>
/// 设置英语显示
/// </summary>
void MMessageBox::setEnglishLayout()
{
    if (Language::type == English)
    {
        ui->pushButton_save->setStyleSheet("font-size:12px");
        ui->pushButton_yes->setStyleSheet("font-size:12px");
        ui->pushButton_no->setStyleSheet("font-size:12px");
    }
    else
    {
    }

    ui->messageLabel->setStyleSheet(Language::type == Chinese ? "font-size:14px" : "font-size:12px");
    ui->messageLabel2->setStyleSheet("font-size:12px");
}