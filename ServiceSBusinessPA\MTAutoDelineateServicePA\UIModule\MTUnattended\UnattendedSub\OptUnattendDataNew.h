﻿// *********************************************************************************
// <remarks>
// FileName    : OptUnattendDataNew
// Author      : zlw
// CreateTime  : 2024-05-25
// Description : 无人值守信息
// </remarks>
// **********************************************************************************
#pragma once

#include <QObject>
#include <iostream>
#include "DataDefine/MTAutoDelineationDialogData.h"


class OptUnattendDataNew
{
public: //静态方法
    /// <summary>
    /// 设置默认导出地址
    /// </summary>
    /// <param name="stExportAddr">[IN]默认导出地址</param>
    static void setDefaultExportAddr(const n_mtautodelineationdialog::ST_AddrSimple& stExportAddr);

    /// <summary>
    /// 获取默认导出地址
    /// </summary>
    static n_mtautodelineationdialog::ST_AddrSimple getDefaultExportAddr();

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="unattendedConfigMap">[IN]无人值守信息集合(key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息)</param>
    /// <param name="allTemplateNameMap">[IN]所有勾画模板id-模板名称集合</param>
    /// <param name="allRemoteScpInfoList">[IN]所有远程服务器信息</param>
    /// <param name="allLocalServerNameMap">[IN]所有本地服务器名称集合(key-serverType value-serverName)</param>
    OptUnattendDataNew(
        const QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>& unattendedConfigMap,
        const QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*模板id*/, QString>>& allTemplateNameMap,
        const QList<n_mtautodelineationdialog::ST_AddrSimple> allRemoteScpInfoList,
        const QMap<int, QStringList>& allLocalServerNameMap
    );
    ~OptUnattendDataNew();

    /// <summary>
    /// 添加被使用的本地服务器
    /// </summary>
    /// <param name="serverType">[IN]服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)</param>
    /// <param name="serverName">[IN]服务器名</param>
    void addUsedLocalServerNameMap(const int serverType, const QString& serverName);

    /// <summary>
    /// 删除被使用的本地服务器
    /// </summary>
    /// <param name="serverType">[IN]服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器)</param>
    /// <param name="serverName">[IN]服务器名</param>
    void delUsedLocalServerNameMap(const int serverType, const QString& serverName);

    /// <summary>
    /// 替换最新的本地服务器名集合(根据服务器类型有就换)
    /// </summary>
    /// <param name="newLocalServerNameMap">[IN]key-服务器类型(1:共享文件夹 2:FTP服务器 3:加速器log(没用到) 4:SCP服务器) value-serverName</param>
    void replaceLocalServerNameMap(const QMap<int/*serverType*/, QStringList>& newLocalServerNameMap);

    /// <summary>
    /// 获取所有本地服务器名集合(包括空闲和不空闲)
    /// </summary>
    /// <returns>key-serverType(1:共享文件夹 2:FTP服务器 4:SCP服务器) value-serverName</returns>
    QMap<int/*serverType*/, QStringList> getAllLocalServerMap();

    /// <summary>
    /// 获取空闲的本地服务器名集合
    /// </summary>
    /// <returns>key-serverType(1:共享文件夹 2:FTP服务器 4:SCP服务器) value-serverName</returns>
    QMap<int/*serverType*/, QStringList> getAvailLocalServerMap();

    /// <summary>
    /// 获取空闲的本地服务器名集合
    /// </summary>
    /// <param name="notRemoveKey">[IN]不用去除某个服务器(serverType.serverName)</param>
    /// <returns>key-serverType(1:共享文件夹 2:FTP服务器 4:SCP服务器) value-serverName</returns>
    QMap<int/*serverType*/, QStringList> getAvailLocalServerMap(const QString& notRemoveKey);

    /// <summary>
    /// 获取所有远程服务器导出地址集合
    /// </summary>
    /// <returns>所有远程服务器导出地址集合</returns>
    QList<n_mtautodelineationdialog::ST_AddrSimple> getAllRemoteServerList();

    /// <summary>
    /// 获取无人值守信息集合
    /// </summary>
    /// <returns>key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息</returns>
    QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig> getUnattendedConfigMap();

    /// <summary>
    /// 获取无人值守信息
    /// </summary>
    /// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
    /// <returns>无人值守信息(其实就一条)</returns>
    QList<n_mtautodelineationdialog::ST_UnattendedConfig> getUnattendedConfig(const QString& customId);

    /// <summary>
    /// 新增无人值守信息
    /// </summary>
    /// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
    /// <param name="stUnattendedConfig">无人值守信息</param>
    void addUnattendedConfig(const QString& customId, const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig);

    /// <summary>
    /// 删除无人值守信息
    /// </summary>
    /// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
    /// <param name="stUnattendedConfig">无人值守信息</param>
    void delUnattendedConfig(const QString& customId);

    /// <summary>
    /// 更新无人值守信息
    /// </summary>
    /// <param name="customId">[IN]唯一标识(创建时间:yyyyMMddhhmmss)</param>
    /// <param name="stUnattendedConfig">无人值守信息</param>
    void updateUnattendedConfig(const QString& customId, const n_mtautodelineationdialog::ST_UnattendedConfig& stUnattendedConfig);

    /// <summary>
    /// 获取所有模板名称集合
    /// </summary>
    /// <returns>key-模态 value(key-templateId value-templateName)</returns>
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>> getAllTempateNameMap();

protected:

private:
    QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig> m_unattendedConfigMap; //无人值守全部规则(key-创建时间(yyyyMMddhhmmss 数据库中存在customId栏) value-配置信息)
    QMap<int/*serverType*/, QStringList> m_allLocalServerNameMap;           //全部本地服务器名地址(key-serverType value:serverName 1:共享文件夹 2:FTP服务器 4:SCP服务器)
    QSet<QString/*serverType.serverName*/> m_usedLocalServerNameUpperSet;   //已经被使用的本地服务器名信息toUpper
    QList<n_mtautodelineationdialog::ST_AddrSimple> m_allRemoteScpInfoList; //全部远程服务器信息
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*tempateId*/, QString>> m_allTemplateNameMap; //所有模板id-名称集合


};