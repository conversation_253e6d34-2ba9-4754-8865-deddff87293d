﻿#pragma once
#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif

#include <QWidget>
#include "AccuComponentUi/Header/QMTButtonEnum.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "AccuComponentUi/Header/QMTAbsTableWidgetItem.h"


/*病人界面自动勾画、重新勾画、取消、打开图像等按键控件*/

//QMTButtonsUi参数,index参照ButtonIndex
class  QMTButtonsUiParam : public ICellWidgetParam
{
public:
    QList<int> _showBtnIndexList;                    //显示的按键,参照类型QMTButtonsUi::ButtonIndex
    QList<int> _disableBtnList;                      //disable的按键集合
    QMap<int/*ButtonIndex*/, QString> _styleStrMap;  //样式map
    QMap<int, QString> _btnTextMap;                  //按键的文案

    QMTButtonsUiParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
namespace Ui
{
class QMTButtonsUi;
}
/**/
class  QMTButtonsUi : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    //按键下标
    enum ButtonIndex
    {
        Btn_Delinete,
        Btn_Cancel,
        Btn_Redo,
        Btn_Open,
        Btn_Max,
    };

    //更新UI结构体
    typedef struct ST_OneButtonsUiUpdateInfo
    {
        int btnIndex = 0;           //默认是第0个按键
        QString text = "";          //如果赋值了那么会更改，否则不更改
        bool bHide = false;         //隐藏/显示
        bool bEnable = true;        //使能
    } OneButtonsUiUpdateInfo;

    QMTButtonsUi(QWidget* parent = Q_NULLPTR);
    ~QMTButtonsUi();

    /*单元格公共接口*/
    //更新界面接口
    virtual bool UpdateUi(const QVariant& updateData);
    //按键使能设置
    virtual void SetButtonEnable(int btnIndex/**/, bool bEnable);

    //更新样式
    void SetupCellWidget(QMTButtonsUiParam& cellWidgetParam);   //根据传入结构体更新样式

    void SetPushButtonText(ButtonIndex index, QString text);    //设置显示的文案
    void SetPushButtonName(ButtonIndex index, QString name);    //设置ObjectName

    void HideAllButtons(bool bHide);                            //隐藏/显示所有的按键
    void HideButton(ButtonIndex index, bool bHide);             //隐藏某个button

    void SetButtonStyle(ButtonIndex index, QString style);      //设置某个button的样式
    void SetButtonEnable(ButtonIndex index, bool enable);       //设置某个按键是否可用
    void SetMode(QJsonObject&);
    void SetButtonStyle(ButtonType type, bool isVisial, bool enable = true, QString style = QString(), QString property = QString(), QString text = QString());
    void SetButtonEnable(ButtonType type, bool enable = true);

protected:
    void InitTemplateStyle();
    //获取按键样式
    QString GetButtonStyle(ButtonStyle);
    QString GetRtButtonStyle(ButtonType type, bool enable = true);
signals:
    void sigButtonClicked(QWidget*, int);
    void sigButtonClicked(int, bool);
    void sigRead();
    void sigButtonClicked(mtuiData::TableWidgetItemIndex&, int);//某一个按键点击了
private slots:
    void BtnClicked(int);
private:
    Ui::QMTButtonsUi* ui;

    QButtonGroup* _btnGroup = nullptr;

    QString _openStyle;             //打开样式
    QString _delineteStyle;         //自动勾画样式
    QString _reDelineteStyle;       //重新勾画样式
    bool _cancelEnable = true;      //是否允许取消
    int _needUpdata = 0;
};

Q_DECLARE_METATYPE(QMTButtonsUi::OneButtonsUiUpdateInfo);