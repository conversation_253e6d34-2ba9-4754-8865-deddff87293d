﻿#include "AccuComponentUi\Header\WidgetProcessingStateFlag.h"

WidgetProcessingStateFlag::WidgetProcessingStateFlag(WidgetProcessingStateFlag::WidgetProcessState* processStateObj, WidgetProcessState initState)
    : m_processStateObj(processStateObj)
{
    *m_processStateObj = initState;
}

WidgetProcessingStateFlag::WidgetProcessingStateFlag(bool* lockFalg)
    : m_lockFalg(lockFalg)
{
    *m_lockFalg = true;
}

WidgetProcessingStateFlag::~WidgetProcessingStateFlag()
{
    if (m_processStateObj)
    {
        *m_processStateObj = WidgetProcessingStateFlag::WidgetProcessState::State_IDLE;
    }

    if (m_lockFalg)
    {
        *m_lockFalg = false;
    }
}
