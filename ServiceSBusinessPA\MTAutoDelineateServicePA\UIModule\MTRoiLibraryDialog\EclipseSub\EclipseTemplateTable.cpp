﻿#include "EclipseTemplateTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTCheckBox.h"
#include "MtMessageBox.h"


/// <summary>
/// 构造函数
/// </summary>
EclipseTemplateTable::EclipseTemplateTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    //初始化列表
    initTableView(
        {
            tr("Template ID"), tr("Approval"), tr("Users"), tr("Description")
        },
        { 260, 224, 224, 240 });
}

EclipseTemplateTable::~EclipseTemplateTable()
{
}

/// <summary>
/// 添加Eclipse模板
/// </summary>
/// <param name="eclipseTemplateVec">[IN]Eclipse模板集合</param>
void EclipseTemplateTable::addEclipseTemplate(const QVector<ST_EclipseTemplate>& eclipseTemplateVec)
{
    ClearDataModel();

    for (int i = 0; i < eclipseTemplateVec.size(); i++)
    {
        addRow(i, eclipseTemplateVec[i]);
    }
}

/// <summary>
/// 全选
/// </summary>
/// <param name="isCheck">[IN]true全选</param>
void EclipseTemplateTable::checkAll(const bool isCheck)
{
    int rowNum = this->GetRowCount();

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(rowValue, 0);

        if (widget == nullptr)
            continue;

        ((QMTCheckBox*)widget)->setChecked(isCheck);
    }
}

/// <summary>
/// 获取最新的Eclipse-ROI信息
/// </summary>
/// <returns>key-roiCode value-ST_EclipseRoi</returns>
QMap<QString, ST_EclipseRoi> EclipseTemplateTable::getNewEclipseRoiInfo()
{
    int rowNum = this->GetRowCount();
    QMap<QString, ST_EclipseRoi> stNewEclipseRoiMap;

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(rowValue, 0);

        if (widget == nullptr)
            continue;

        bool isCheck = ((QMTCheckBox*)widget)->isChecked();

        if (isCheck == true)
        {
            QString templateID = ((QMTCheckBox*)widget)->text();

            if (m_allEclipseTemplateMap.contains(templateID) == true)
            {
                ST_EclipseTemplate tempEclipseTemplate = m_allEclipseTemplateMap[templateID];

                for (QMap<QString/*roiCode*/, ST_EclipseRoi>::iterator it = tempEclipseTemplate.roiInfoMap.begin(); it != tempEclipseTemplate.roiInfoMap.end(); it++)
                {
                    stNewEclipseRoiMap.insert(it.key(), it.value());
                }
            }
        }
    }

    return stNewEclipseRoiMap;
}

QMap<QString/*templateId*/, ST_EclipseTemplate> EclipseTemplateTable::getNewEclipseTemplate()
{
    int rowNum = this->GetRowCount();
    QMap<QString, ST_EclipseTemplate> stNewEclipseTemplateMap;

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(rowValue, 0);

        if (widget == nullptr)
            continue;

        bool isCheck = ((QMTCheckBox*)widget)->isChecked();

        if (isCheck == true)
        {
            QString templateID = ((QMTCheckBox*)widget)->text();

            if (m_allEclipseTemplateMap.contains(templateID) == true)
            {
                stNewEclipseTemplateMap.insert(templateID, m_allEclipseTemplateMap[templateID]);
            }
        }
    }

    return stNewEclipseTemplateMap;
}

/// <summary>
/// 初始化表格
/// </summary>
/// <param name="headList">[IN]表头文本集合</param>
/// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
void EclipseTemplateTable::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="num">[IN]自定义编号1,2,3... 作为列表行唯一值</param>
/// <param name="stEclipseTemplate">[IN]Eclipse模板</param>
void EclipseTemplateTable::addRow(const int num, const ST_EclipseTemplate& stEclipseTemplate)
{
    m_allEclipseTemplateMap.insert(stEclipseTemplate.templateID, stEclipseTemplate);
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;
    //templateID
    QMTCheckBoxParam* checkBoxParam = new QMTCheckBoxParam();
    checkBoxParam->_text = stEclipseTemplate.templateID;
    cellWidgetParamMap.insert(0, checkBoxParam);
    //approval
    QCustMtLabelParam* labelApprovalParam = new QCustMtLabelParam();
    labelApprovalParam->_text = stEclipseTemplate.approval;
    cellWidgetParamMap.insert(1, labelApprovalParam);
    //user
    QCustMtLabelParam* labelUserParam = new QCustMtLabelParam();
    labelUserParam->_text = stEclipseTemplate.user;
    cellWidgetParamMap.insert(2, labelUserParam);
    //des
    QCustMtLabelParam* labelDescParam = new QCustMtLabelParam();
    labelDescParam->_text = stEclipseTemplate.desc;
    cellWidgetParamMap.insert(3, labelDescParam);
    this->AddRowItem(QString::number(num), cellWidgetParamMap);
}

