<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RoiLabelSettingDialogClass</class>
 <widget class="QDialog" name="RoiLabelSettingDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>405</width>
    <height>154</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RoiLabelSettingDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrame" name="mtFrame">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>10</number>
      </property>
      <item>
       <layout class="QFormLayout" name="formLayout">
        <property name="labelAlignment">
         <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
        </property>
        <property name="horizontalSpacing">
         <number>16</number>
        </property>
        <property name="verticalSpacing">
         <number>8</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item row="0" column="0">
         <widget class="MtLabel" name="mtLabel_2">
          <property name="text">
           <string>标签</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_1</enum>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="MtRangeLineEdit" name="lineEdit_label">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="maxLength">
           <number>64</number>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="MtLabel" name="mtLabel">
          <property name="text">
           <string>匹配字段</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_1</enum>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="MtRangeLineEdit" name="lineEdit_roiAlias">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="maxLength">
           <number>64</number>
          </property>
         </widget>
        </item>
        <item row="2" column="0">
         <widget class="MtLabel" name="mtLabel_13">
          <property name="text">
           <string>颜色</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_1</enum>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="MtFrameEx" name="mtFrameEx">
          <property name="minimumSize">
           <size>
            <width>68</width>
            <height>28</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>68</width>
            <height>28</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::frameEx3</enum>
          </property>
          <layout class="QVBoxLayout" name="verticalLayout_3">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>16</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtColorButton" name="mtColorButton">
             <property name="maximumSize">
              <size>
               <width>36</width>
               <height>16777215</height>
              </size>
             </property>
             <property name="text">
              <string/>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtColorButton::colorbutton1</enum>
             </property>
             <property name="indicatorWidth">
              <number>36</number>
             </property>
             <property name="indicatorHeight">
              <number>16</number>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
        <item row="3" column="0">
         <widget class="MtLabel" name="mtLabel_12">
          <property name="text">
           <string>ROI类型</string>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtLabel::myLabel1_1</enum>
          </property>
         </widget>
        </item>
        <item row="3" column="1">
         <widget class="MtComboBox" name="mtComboBox_roiType">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>30</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>30</height>
           </size>
          </property>
          <property name="elideMode">
           <enum>Qt::ElideRight</enum>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtComboBox::combobox1</enum>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrame</class>
   <extends>QFrame</extends>
   <header>MtFrame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>MtColorButton</class>
   <extends>QCheckBox</extends>
   <header>MtColorButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtRangeLineEdit</class>
   <extends>QLineEdit</extends>
   <header>mtrangelineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
