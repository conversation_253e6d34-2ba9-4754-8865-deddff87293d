﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QValidator>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtRangeLineEdit.h"

#include "MtRangeLineEdit.h"

/*******由QLabel和MtRangeLineEdit组成
//只读：显示QLabel,隐藏MtRangeLineEdit,反之相反
//双击后允许修改内容
*********/

//QCustDoubleClickLineEdit参数
class  QCustDoubleClickLineEditParam : public ICellWidgetParam
{
public:
    bool _isReadOnly = true;    //初始是否只读，双击后可以修改
    QString _regExpStr;         //正则表达式
    int _maxLength = -1;        //允许输入最大长度
    QString _placeholderText;   //占位字符串

    double minValue;                //最小值
    double maxValue;                //最大值
    double initValue;               //初始值
    int decimals = 2;               //小数点位数
    bool bContainMin = true;        //是否允许包含最小值。true:表示"[min,";false:表示"(min"
    bool bContainMax = true;        //是否允许包含最大值。true:表示"max]";false:表示"max)"

    QCustDoubleClickLineEditParam();
    ~QCustDoubleClickLineEditParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustDoubleClickLineEditParam)

namespace Ui
{
class QCustDoubleClickLineEdit;
}
class  QCustDoubleClickLineEdit : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustDoubleClickLineEdit(QWidget* parent = Q_NULLPTR);
    ~QCustDoubleClickLineEdit();
    void SetupCellWidget(QCustDoubleClickLineEditParam& cellWidgetParam); //初始化单元格界面

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);      //更新界面接口
    virtual QString GetCurText();                     //获取当前界面展示文案
    virtual void SetEnableEdit(bool bEdit);           //设置是否允许编辑


    QLabel_Dot* GetLable();                 //获取显示的label
    MtRangeLineEdit* GetLineEdit();               //获取输入框对象

    void SetRegExpStr(QString&);            //设置正则表达式
    void SetItemValidator(QValidator*);     //设置输入的规则
    void setText(const QString&);           //设置字符串
    void setReadOnly(bool);                 //是否只读
    void SetMyStyleSheet(QString&);         //设置样式


    QString getText();                      //获取当前text
signals:
    void sigClicked(int);
    void currentTextChanged(const QString& newText);         //文案改变了
    void sigEditFinish(QString newText, QString oldText);

protected:
    void resizeEvent(QResizeEvent* event);          //保证...
    void mousePressEvent(QMouseEvent* event);
    void mouseDoubleClickEvent(QMouseEvent* event);
    virtual void enterEvent(QEvent* e);
    virtual void leaveEvent(QEvent* e);

    void UpdateLineEditText(const QString& text);     //只更新编辑框文案
private slots:
    void slotLineEditingFinished();

private:
    Ui::QCustDoubleClickLineEdit* ui = nullptr;
    bool _isDClickEdit = true;//默认双击可以修改
    QValidator* _validator = nullptr;
    QString _oldText; //上一次数据内容

    bool m_bIsEnterInThisWgt = false;   //是否进入此widget
    bool m_bIsInEditing = false;        //是否处于编辑中
};
