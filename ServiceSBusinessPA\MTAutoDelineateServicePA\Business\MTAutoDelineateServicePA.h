﻿// ************************************************************
// <remarks>
// Author      : LiuHualin
// CreateTime  : 2025-09-27
// Description : 自动勾画操作服务
// </remarks>
// ************************************************************
#pragma once

#include "IMTContextBase.h"
#include "IMTPluginBase.h"
#include "Service/IServiceInitialize.h"
#include "Skin/CMtSkinEnum.h"


class MTAutoDelineateService;
class MTAutoDelineateServicePA :
    public QObject,
    public mtcore::IMTPluginBase,
    public IServiceInitialize
{
    Q_OBJECT
        Q_INTERFACES(mtcore::IMTPluginBase)
        Q_PLUGIN_METADATA(IID IMTPlugin_id FILE "MTAutoDelineateServicePA.json")

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    MTAutoDelineateServicePA();
    /// <summary>
    /// 析构函数
    /// </summary>
    ~MTAutoDelineateServicePA();

    /// <summary>
    /// 初始化服务
    /// </summary>
    /// <param name="lstBusinessPluginPath">服务需要使用的业务插件的全路径集合</param>
    /// <param name="configDir">配置文件文件夹路径（如：c:\config\）</param>
    /// <param name="errMsg">错误信息</errMsg>
    /// <return>0:成功</return>
    /// <remarks>[Version]:1.0.1.0  ChangeInfo:</remarks>
    virtual int InitService(const QStringList& lstBusinessPluginPath, const QString& configDir, QString& errMsg) override;
    /// <summary>
    /// 启动服务
    /// </summary>
    /// <param name="context">上下文</param>
    /// <remarks>[Version]:1.0.1.0  ChangeInfo:</remarks>
    virtual void Start(mtcore::IMTContextBase* context) override;
    /// <summary>
    /// 停止服务
    /// </summary>
    /// <param name="context">上下文</param>
    /// <remarks>[Version]:1.0.1.0  ChangeInfo:</remarks>
    virtual void Stop(mtcore::IMTContextBase* context) override;

protected:
    /// <summary>
    /// 注册服务
    /// </summary>
    /// <remarks>[Version]:1.0.1.0  ChangeInfo:</remarks>
    void RegisterService();

    /// <summary>
    /// 注销服务
    /// </summary>
    /// <remarks>[Version]:1.0.1.0  ChangeInfo:</remarks>
    void UnRegisterService();

    /// <summary>
    /// 加载皮肤
    /// </summary>
    /// <param name="skinStyle">皮肤风格</param>
    /// <param name="qssPath">皮肤样式路径</param>
    /// <param name="qssSettingPath">qssSettting路径</param>
    /// <remarks>[Version]:1.0.1.0  ChangeInfo:</remarks>
    void AddSkinQssPath_DoMain(mtSkin::AppSkinStyle skinStyle, const QString& qssPath, const QString& qssSettingPath);

private:
    /// <summary>
    /// 自动勾画服务实现类
    /// </summary>
    MTAutoDelineateService* m_delineateServices = nullptr;
    /// <summary>
    /// 组件上下文
    /// </summary>
    mtcore::IMTContextBase* m_context = nullptr;
    /// <summary>
    /// 服务秘钥
    /// </summary>
    QString m_servicePassword = "C2987F4E-F694-4497-BA68-937320969196";
};