﻿<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTParamItemWidget</class>
 <widget class="QWidget" name="QMTParamItemWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>2134</width>
    <height>43</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QMTParamItemWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true">*{
     background:rgba(52,59,73,1);
    font-size:14px;
}

QLabel{
	font-size:13px;
	color:rgb(255,255,255);
	border:none;
}

#leValue,#leValue_2,#leValue_3{
	 background-color: rgba(37,41,48,0.56);
}

QLineEdit{
	font-size:13px;
	color:rgb(219,226,241);
	background-color: rgba(37,41,48,0.56);
	border:none;
}



QComboBox {
	background-color: rgba(37,41,48,0.56);
	color: rgb(219,226,241);
	padding-left: 5px;
	border: 1px solid rgba(72,83,100,1);
	border-radius: 4px;
    font-size:14px;
}

QComboBox::drop-down {
    width: 20px;
	height:36px;
	border: 0;
	padding-right: 10px;
	padding-left: 10px;
}

QComboBox::QlineEdit {
	background-color: rgba(37,41,48,0.56);
	color: rgb(219,226,241);
    font-size:14px;
}

QComboBox::down-arrow {
	image:url(:/AccuUIComponentImage/images/icon_pull-down.png);
}

QComboBox QAbstractItemView {
	background-color: rgba(45,52,64,1);
	color: rgb(219,226,241);
	selection-background-color: #4E9CD5;
	max-height: 120px;
	border:1px solid rgba(72,83,100,1);
    border-radius:4px;
	font-size:14px;
    outline: none;
}
QComboBox QAbstractItemView::Item {
	min-height: 24px;
	height: 24px;
	font-size:14px;
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>3</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_7" native="true">
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>40</height>
      </size>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_8">
      <property name="spacing">
       <number>90</number>
      </property>
      <property name="leftMargin">
       <number>37</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>37</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QWidget" name="widget_4" native="true">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_5">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="widget" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="labelKey">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>参数名</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="leValue">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="cbValue">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_5" native="true">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_6">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="widget_2" native="true">
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>0</height>
            </size>
           </property>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="labelKey_2">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>参数名</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="leValue_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="cbValue_2">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <widget class="QWidget" name="widget_6" native="true">
        <property name="minimumSize">
         <size>
          <width>100</width>
          <height>0</height>
         </size>
        </property>
        <layout class="QHBoxLayout" name="horizontalLayout_7">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="QWidget" name="widget_3" native="true">
           <layout class="QHBoxLayout" name="horizontalLayout_4">
            <property name="spacing">
             <number>0</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="topMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <property name="bottomMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="QLabel" name="labelKey_3">
              <property name="minimumSize">
               <size>
                <width>120</width>
                <height>0</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>120</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="text">
               <string>参数名</string>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QLineEdit" name="leValue_3">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
             </widget>
            </item>
            <item>
             <widget class="QComboBox" name="cbValue_3">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="minimumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
              <property name="maximumSize">
               <size>
                <width>250</width>
                <height>34</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="../Resources/AccuUIComponent.qrc"/>
 </resources>
 <connections/>
</ui>
