﻿#include "AccuComponentUi\Header\QMTTreeGroupPacketItem.h"
#include "ui_QMTTreeGroupPacketItem.h"
#include "CMtCoreDefine.h"
#include <QColorDialog>
#include <qDebug>
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "CMtCoreWidgetUtil.h"
#ifndef MANTEIA_UTF_8  // 如果编译器已经定义了 /utf-8 ，那么不需要 execution_character_set("utf-8")
#pragma execution_character_set("utf-8")
#endif
QMTTreeGroupPacketItem::QMTTreeGroupPacketItem(QString uniqueValue, QWidget* parent /*= 0*/)
    : QWidget(parent)
{
    ui = new Ui::QMTTreeGroupPacketItem;
    ui->setupUi(this);
    _isExpand = true;
    _uniqueValue = uniqueValue;
    connect(ui->checkBox_Expand, SIGNAL(clicked()), this, SLOT(on_actExpandChanged()));
    //hideUI();
    /*字体加粗*/
    QFont font = ui->sliderTextWidget->font();
    font.setBold(true); // 设置字体为加粗
    ui->sliderTextWidget->setFont(font);
    QFont font2 = ui->label_Num->font();
    font2.setBold(true); // 设置字体为加粗
    ui->label_Num->setFont(font);
}

QMTTreeGroupPacketItem::~QMTTreeGroupPacketItem()
{
    //qDebug() << "QMTTreeGroupPacketItem::~QMTTreeGroupPacketItem()";
    MT_DELETE(ui);
}

void QMTTreeGroupPacketItem::InitTreeGroupPacketItem()
{
    ui->checkBox_Expand->setChecked(_isExpand);
}

void QMTTreeGroupPacketItem::setName(QString name)
{
    ui->sliderTextWidget->setText(name);
    _name = name;
}

QString QMTTreeGroupPacketItem::getName()
{
    return _name;
}

bool QMTTreeGroupPacketItem::GetGroupExpand()
{
    return _isExpand;
}

void QMTTreeGroupPacketItem::SetItemNum(int ItemNum)
{
    QString strItemNum = QString("(%1)").arg(ItemNum);
    ui->label_Num->setText(strItemNum);
    _ItemNum = ItemNum;
}

int QMTTreeGroupPacketItem::GetItemNum()
{
    return _ItemNum;
}

void QMTTreeGroupPacketItem::setGroupType(int type)
{
    _groupType = type;
}

int QMTTreeGroupPacketItem::getGroupType()
{
    return _groupType;
}

void QMTTreeGroupPacketItem::setUniqueValue(const QString& uniqueValue)
{
    _uniqueValue = uniqueValue;
}

QString QMTTreeGroupPacketItem::getUniqueValue()
{
    return _uniqueValue;
}

void QMTTreeGroupPacketItem::changeExpandState()
{
    bool Checkedstate = ui->checkBox_Expand->checkState();
    ui->checkBox_Expand->setChecked(!Checkedstate);
}

void QMTTreeGroupPacketItem::setExpandState(bool isExpand)
{
    ui->checkBox_Expand->setChecked(isExpand);
}

void QMTTreeGroupPacketItem::SetBHideWhenItemNumIsEzro(bool bHideWhenItemNumIsEzro)
{
    m_bHideWhenItemNumIsEzro = bHideWhenItemNumIsEzro;
}

bool QMTTreeGroupPacketItem::GetBHideWhenItemNumIsEzro()
{
    return m_bHideWhenItemNumIsEzro;
}

void QMTTreeGroupPacketItem::on_actExpandChanged()
{
    _isExpand = ui->checkBox_Expand->isChecked();
    emit sigIsExpandGroup(_uniqueValue, _name, _isExpand);
}

void QMTTreeGroupPacketItem::enterEvent(QEvent* e)
{
    QWidget::enterEvent(e);
}

void QMTTreeGroupPacketItem::leaveEvent(QEvent* e)
{
    QWidget::leaveEvent(e);
}
