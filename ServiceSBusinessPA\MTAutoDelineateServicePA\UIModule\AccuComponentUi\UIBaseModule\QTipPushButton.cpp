﻿#include "AccuComponentUi\Header\QTipPushButton.h"
#include<QCoreApplication>
#include <QTime>
#include <windows.h>

QTipPushButtonParam::QTipPushButtonParam()
{
    _cellWidgetType = DELEAGATE_QTipPushButton;
}

QWidget* QTipPushButtonParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QTipPushButton* btn = new QTipPushButton(parent);
    btn->setTitleDisplayStatus(true);
    btn->setText(_text);

    if (_width > 0)
        btn->setFixedWidth(_width);

    if (_height > 0)
        btn->setFixedHeight(_height);

    if (_styleSheetStr.size() > 0)
    {
        btn->setStyleSheet(_styleSheetStr);
    }

    return btn;
}

QTipPushButton::QTipPushButton(QWidget* parent)
    : QPushButton(parent)
{
    setMouseTracking(true);
    m_title = "default_title";
    m_info = "default_info";
    m_timer = new QTimer(this);
    connect(m_timer, &QTimer::timeout, this, &QTipPushButton::slotShowTips);
    m_toolTip = new CToolTip;
    show();
}

void QTipPushButton::slotShowTips()
{
    POINT point;
    GetCursorPos(&point); // 获取鼠标指针位置（屏幕坐标）
    m_point.setX(point.x);
    m_point.setY(point.y);
    m_toolTip->showMessage(m_title, m_info, m_point);
    m_toolTip->showMessage(m_title, m_info, m_point);
    m_toolTip->show();
}

QTipPushButton::~QTipPushButton()
{
    delete m_toolTip;
    m_toolTip = nullptr;
}

bool QTipPushButton::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString str = updateData.toString();
        this->setText(str);
        return true;
    }

    return false;
}

QString QTipPushButton::GetCurText()
{
    return this->text();
}

void QTipPushButton::setTipText(QString title, QString info)
{
    m_title = title;
    setTitleDisplayStatus(false);
    m_info = info;
}

void QTipPushButton::setTipText(QString info)
{
    m_info = info;
    setTitleDisplayStatus(true);
}

void QTipPushButton::setTitleDisplayStatus(bool isHidden)
{
    _bTipHide = isHidden;
    m_toolTip->setTitleDisplayStatus(isHidden);
}



void QTipPushButton::setTipTitleText(QString text)
{
    m_title = text;
}


void QTipPushButton::setTipInfoText(QString text)
{
    m_info = text;
}


void QTipPushButton::setDelay(int msec)
{
    m_showDelay = msec;
}
void QTipPushButton::setTipMaxWidth(int width)
{
    m_toolTip->setMaxWidth(width);
}

void QTipPushButton::enterEvent(QEvent* event)
{
    if (true == _bTipHide)
    {
        QPushButton::enterEvent(event);
        return;
    }

    m_timer->start(m_showDelay);
}

void QTipPushButton::leaveEvent(QEvent* event)
{
    if (true == _bTipHide)
    {
        QPushButton::leaveEvent(event);
        return;
    }

    m_timer->stop();
    m_toolTip->showStop();
}

