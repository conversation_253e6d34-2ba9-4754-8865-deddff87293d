﻿// ************************************************************
// <remarks>
// Author      : chenguanhua
// CreateTime  : 2025-02-21
// Description : 自定义按钮标签
// </remarks>
// ************************************************************
#pragma once

#include <QWidget>
#include <QMetaType>
#include <QValidator>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtRangeLineEdit.h"
#include "AccuComponentUi\Header\MtWarningLineEdit.h"





/// <summary>
/// QCustLabelWithButton参数
/// </summary>
class  QCustLabelWithButtonParam : public ICellWidgetParam
{
public:
    /// <summary>
    /// 文本
    /// </summary>
    QString labelText;
    /// <summary>
    /// 按键图片路径
    /// </summary>
    QString btnIconFile;

    /// <summary>
    /// Initializes a new instance of the <see cref="QCustLabelWithButtonParam"/> class.
    /// </summary>
    QCustLabelWithButtonParam();
    /// <summary>
    /// 创建UI接口
    /// </summary>
    /// <param name="parent">The parent.</param>
    /// <returns>QWidget *.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustLabelWithButtonParam)

namespace Ui
{
class QCustLabelWithButton;
}
/// <summary>
/// Class
/// </summary>
class  QCustLabelWithButton : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustLabelWithButton(QWidget* parent = Q_NULLPTR);
    /// <summary>
    /// Finalizes an instance of the <see cref="QCustLabelWithButton"/> class.
    /// </summary>
    ~QCustLabelWithButton();
    /// <summary>
    /// 初始化单元格界面
    /// </summary>
    /// <param name="cellWidgetParam">The cell widget parameter.</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SetupCellWidget(QCustLabelWithButtonParam& cellWidgetParam);

    /// <summary>
    /// 获取文本
    /// </summary>
    /// <returns>QString.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    QString GetText();

    /// <summary>
    /// 更新界面接口
    /// </summary>
    /// <param name="updateData">The update data.</param>
    /// <returns>bool.</returns>
    /// <remarks>[Version]:******* Change: </remarks>
    virtual bool UpdateUi(const QVariant& updateData);

private slots:
    void SlotButtonClicked();
signals:
    void SigClicked(int);
    /// <summary>
    /// Sigs the customer button clicked.
    /// </summary>
    /// <param name="">The .</param>
    /// <param name="">The .</param>
    /// <remarks>[Version]:******* Change: </remarks>
    void SigCustButtonClicked(int, bool);

private:
    Ui::QCustLabelWithButton* ui;
};
