﻿#include "ModelSettingTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/MtUnitToolButtonGroup.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTCheckBox.h"
#include "MtMessageBox.h"
#include "DataDefine/InnerStruct.h"


/// <summary>
/// 构造函数
/// </summary>
ModelSettingTable::ModelSettingTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
    //信号槽
    connect(this, SIGNAL(sigRowItemClicked(const QString&)), this, SLOT(slotRowItemClicked(const QString&))); //某一行点击了
    connect(this, &ModelSettingTable::sigCellWidgetButtonClicked, this, &ModelSettingTable::slotCellWidgetButtonClicked); //某个按键点击了
}

ModelSettingTable::~ModelSettingTable()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void ModelSettingTable::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="sketchModelList">[IN]模型集合</param>
void ModelSettingTable::init(const QList<n_mtautodelineationdialog::ST_SketchModel>& sketchModelList)
{
    //初始化表头
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    QStringList titleList = { "", tr("模型名称"), tr("模型类型"), tr("导入时间"), tr("操作") };
    firstViewParam._headParam._headStrList = titleList;
    firstViewParam._headParam._defaultColumn = titleList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;
    QMTCheckBoxParam* checkBoxParam = new QMTCheckBoxParam();
    //checkBoxParam->_paddingLeft = 30;//为啥没生效
    firstViewParam._headParam._headCellParamMap.insert(0, checkBoxParam);
    firstViewParam._headParam._columnWidthMap.insert(0, 25);
    firstViewParam._headParam._columnWidthMap.insert(1, 188);
    firstViewParam._headParam._columnWidthMap.insert(2, 188);
    firstViewParam._headParam._columnWidthMap.insert(3, 188);
    firstViewParam._headParam._columnWidthMap.insert(4, 56);
    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);

    //初始化item
    for (int i = 0; i < sketchModelList.size(); i++)
    {
        addRow(sketchModelList[i]);
    }
}

/// <summary>
/// 添加新的模型
/// <param name="sketchModel">[IN]模型信息</param>
/// </summary>
void ModelSettingTable::addNewSketchModel(const n_mtautodelineationdialog::ST_SketchModel& sketchModel)
{
    addRow(sketchModel);
}

/// <summary>
/// 删除模型
/// </summary>
/// <param name="rowValueList">[IN]行唯一标识(modelName.id)</param>
void ModelSettingTable::delSketchModel(const QStringList& rowValueList)
{
    for (int i = 0; i < rowValueList.size(); i++)
    {
        this->DeleteRowItem(rowValueList[i]);
    }
}

/// <summary>
/// 获取勾选的模型唯一标识集合
/// </summary>
/// <returns>勾选的模型唯一标识集合(modelName.id)</returns>
QStringList ModelSettingTable::getCheckedSketchModel()
{
    int rowNum = this->GetRowCount();
    QStringList rowValueList;
    rowValueList.reserve(rowNum + 1);

    for (int i = 0; i < rowNum; i++)
    {
        QString rowValue = this->GetRowUniqueValue(i);
        QWidget* widget = this->GetCellWidget(rowValue, 0);

        if (widget != nullptr)
        {
            QMTCheckBox* checkBox = qobject_cast<QMTCheckBox*>(widget);

            if (checkBox != nullptr && checkBox->isChecked())
            {
                rowValueList.push_back(rowValue);
            }
        }
    }

    return rowValueList;
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="stSketchModel">[IN]模型信息</param>
void ModelSettingTable::addRow(const n_mtautodelineationdialog::ST_SketchModel& stSketchModel)
{
    //填充列表
    QMap<int, ICellWidgetParam*> cellWidgetParamMap;

    //复选框
    //默认模型不允许导出和输出，故隐藏checkbox
    if (stSketchModel.id > 0)
    {
        QMTCheckBoxParam* checkBoxParam = new QMTCheckBoxParam();
        cellWidgetParamMap.insert(0, checkBoxParam);
    }

    //模型名称
    QCustMtLabelParam* labelNameParam = new QCustMtLabelParam();
    labelNameParam->_text = stSketchModel.modelName;
    cellWidgetParamMap.insert(1, labelNameParam);
    //模型类型
    QCustMtLabelParam* labelTypeParam = new QCustMtLabelParam();
    labelTypeParam->_text = stSketchModel.modality.toUpper();
    cellWidgetParamMap.insert(2, labelTypeParam);
    //创建时间
    QCustMtLabelParam* labelTimeParam = new QCustMtLabelParam();
    labelTimeParam->_text = "2024-01-01";
    cellWidgetParamMap.insert(3, labelTimeParam);
    //编辑按钮
    MtUnitToolButtonGroupParam* btnsToolParam = new MtUnitToolButtonGroupParam();
    btnsToolParam->_btnWidth = 22;
    btnsToolParam->_btnHeight = 22;
    btnsToolParam->_btnIndexMtTypeMap.insert(0, 2);
    btnsToolParam->_btnIconPathList = QStringList({ m_imagePathHash["icon_edit"] });
    cellWidgetParamMap.insert(4, btnsToolParam);
    QString uniqueKey = stSketchModel.modelName + Def_Separator + QString::number(stSketchModel.id);
    this->AddRowItem(uniqueKey, cellWidgetParamMap);
}

/// <summary>
/// 某一行点击了
/// <param name="rowValue">[IN]行唯一标识(modelName.id)</param>
/// </summary>
void ModelSettingTable::slotRowItemClicked(const QString& rowValue)
{
    emit this->sigRowClicekd(rowValue);
}

/// <summary>
/// 某个按键点击了
/// <param name="rowValue">[IN]行唯一标识(modelName.id)</param>
/// </summary>
void ModelSettingTable::slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked)
{
    if (cellItemIndex._column == 4) //编辑按钮
    {
        emit this->sigEditButtonClicked(cellItemIndex._uniqueValue);
    }
}

