﻿#include "GroupTableWidget.h"
#include <QAbstractButton>
#include "MtMessageBox.h"

GroupTableWidget::GroupTableWidget(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList,
                                   const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allOrganGroupList,
                                   const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
                                   QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    this->setMainLayout(ui.verticalLayout);             //设置布局
    this->setDialogWidthAndContentHeight(600, 416);     //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("分组管理"));                     //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    this->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //初始化列表
    ui.tableView->initTableList(organInfoList, allOrganGroupList);
    ui.tableView2->initTableList(organInfoList, allOrganGroupList, modelInfoMap);
}

GroupTableWidget::~GroupTableWidget()
{
}

QList<n_mtautodelineationdialog::ST_OrganGroupInfo> GroupTableWidget::getTableListInfo()
{
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoList = ui.tableView->getTableList();
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoList2 = ui.tableView2->getTableList();
    return QList<n_mtautodelineationdialog::ST_OrganGroupInfo>() << groupInfoList << groupInfoList2;
}

void GroupTableWidget::on_mtToolButton_add_clicked()
{
    ui.tableView->addNew(m_newGroupId--);
}

void GroupTableWidget::on_mtToolButton_add2_clicked()
{
    if (!ui.tableView2->addNew(m_newGroupId--))
    {
        m_newGroupId++;
    }
}

void GroupTableWidget::onBtnRight1Clicked()
{
    //检查组名是否有相同
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoList = getTableListInfo();
    QStringList nameList;
    QList<int>  secGroupRefOrganIdList; //亚组关联的ROI id

    for (const auto& gItem : groupInfoList)
    {
        if (gItem.name.isEmpty())
        {
            MtMessageBox::NoIcon::information_Title(this, tr("分组名不能为空"));
            return;
        }

        if (nameList.indexOf(gItem.name) != -1)
        {
            MtMessageBox::NoIcon::information_Title(this, tr("存在多个组名为“") + gItem.name + tr("”的分组"));
            return;
        }

        if (gItem.type == 3)//亚组
        {
            if (secGroupRefOrganIdList.contains(gItem.refOrganId))
            {
                MtMessageBox::NoIcon::information_Title(this, tr("不同分组的关联ROI不能为同一个。"));
                return;
            }
            else
            {
                secGroupRefOrganIdList.append(gItem.refOrganId);
            }
        }

        nameList.append(gItem.name);
    }

    MtTemplateDialog::onBtnRight1Clicked();
}