﻿#pragma once

#include "ui_LabelSyncSettingWidget.h"
#include "MtTemplateDialog.h"


class LabelSyncSettingWidget : public MtTemplateDialog
{
    Q_OBJECT

public:
    explicit LabelSyncSettingWidget(QWidget* parent = Q_NULLPTR);
    ~LabelSyncSettingWidget();

    void setSync(bool bSync);
    bool getSync();

protected slots:
    void slotStateChanged(int state);

private:
    Ui::LabelSyncSettingWidget ui;

    bool m_bSync = true;
};
