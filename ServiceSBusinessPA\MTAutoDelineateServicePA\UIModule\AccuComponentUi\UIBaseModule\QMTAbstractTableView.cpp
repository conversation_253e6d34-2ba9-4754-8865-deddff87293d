﻿#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include <QHeaderView>
#include <QScrollBar>
#include <QAbstractScrollArea>
#include <qDebug>
#include <QPainter>

#include "CMtLanguageUtil.h"

#include "AccuComponentUi\Header\QMTEnumDef.h"
#include "AccuComponentUi\Header\QMTTools.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "MtMessageBox.h"
#include "AccuComponentUi\UIBaseModule\QMTAbsTableViewDelegate.h"
#include "AccuComponentUi\Header\UnitUIComponent\QCustMtLabel.h"

#include "AccuComponentUi\Header\WidgetProcessingStateFlag.h"

using namespace mtuiData;

//#define DELETELATER_ENABLE  1   //此宏定义打开表示删除行使用deleteLater

//界面是否正在处理
WidgetProcessingStateFlag::WidgetProcessState s_tableProcessState = WidgetProcessingStateFlag::WidgetProcessState::State_IDLE;

/*************************表头**************************************/
QMTAbsTableViewHeaderView::QMTAbsTableViewHeaderView(Qt::Orientation orientation, QWidget* parent /*= 0*/)
    : QHeaderView(orientation, parent)
{
}


QMTAbsTableViewHeaderView::~QMTAbsTableViewHeaderView()
{
    _bPaintSection = false;
    QList<QWidget*> headWidgetList;
    QMap<int, QWidget*>::iterator iter;

    for (iter = _headWidgetMap.begin(); iter != _headWidgetMap.end(); ++iter)
    {
        int columnTmp = iter.key();
        QWidget* widget = iter.value();
        headWidgetList.append(widget);
    }

    _headWidgetMap.clear();

    for (int i = 0; i < headWidgetList.size(); ++i)
    {
        delete headWidgetList[i];
        headWidgetList[i] = nullptr;
    }

    headWidgetList.clear();
}

void QMTAbsTableViewHeaderView::SetHorScrollValue(int value)
{
    if (_horScrollValue != value)
    {
        QList<int> columnList = _headWidgetMap.keys();

        for (int i = 0; i < columnList.size(); ++i)
        {
            int column = columnList[i];
            QWidget* widget = _headWidgetMap.value(column);
            widget->hide();
        }
    }

    _horScrollValue = value;
}

void QMTAbsTableViewHeaderView::HideColumn(int column, bool bHide)
{
    _columnHideMap.insert(column, bHide);
    QWidget* widget = _headWidgetMap.value(column);

    if (widget)
    {
        if (true == bHide)
        {
            widget->hide();
        }
        else
        {
            widget->show();
        }
    }
}

void QMTAbsTableViewHeaderView::SetHeadWidgetMap(QMap<int, QWidget*>& headWidgetMap)
{
    _headWidgetMap = headWidgetMap;
    SetHorScrollValue(0);
}

QMap<int, QWidget*> QMTAbsTableViewHeaderView::GetHeadWidgetMap()
{
    return _headWidgetMap;
}

QWidget* QMTAbsTableViewHeaderView::GetHeadCellWidget(int column)
{
    QWidget* widget = _headWidgetMap.value(column);
    return widget;
}

int QMTAbsTableViewHeaderView::GetHeadWidgetIndex(QWidget* headWidget)
{
    int column = -1;

    if (nullptr == headWidget)
        return column;

    QMap<int, QWidget*>::iterator iter;

    for (iter = _headWidgetMap.begin(); iter != _headWidgetMap.end(); ++iter)
    {
        int columnTmp = iter.key();
        QWidget* widget = iter.value();

        if (headWidget == widget)
        {
            column = columnTmp;
            break;
        }
    }

    return column;
}

void QMTAbsTableViewHeaderView::paintSection(QPainter* painter, const QRect& rect, int logicalIndex) const
{
    if (false == _bPaintSection)
    {
        goto _END;
    }

    if (logicalIndex < _horScrollValue)
    {
        QWidget* widget = _headWidgetMap.value(logicalIndex);

        if (widget)
        {
            widget->hide();
        }
    }
    else
    {
        QList<int> columnList = _headWidgetMap.keys();

        for (int i = 0; i < columnList.size(); ++i)
        {
            int column = columnList[i];
            QWidget* widget = _headWidgetMap.value(column);

            if (column < _horScrollValue)
            {
                widget->hide();
            }
        }

        QWidget* widget = _headWidgetMap.value(logicalIndex);

        if (widget)
        {
            widget->setGeometry(rect);
            widget->setCursor(QCursor());
            widget->show();
        }
    }

_END:
    QHeaderView::paintSection(painter, rect, logicalIndex);//使用自带的样式
}

/*************************表格**************************************/
QMTAbstractTableView::QMTAbstractTableView(QWidget* parent)
    : QTableWidget(parent)
{
    _viewDelegate = new QMTAbsTableViewDelegate();
    this->setItemDelegate(_viewDelegate);       //不显示虚线
    SetInitPageRowCount(50);
    this->setWordWrap(false);
    ConnectScrollSignals();
    ConnectTableViewSignlas();
    //监听自身事件，目前有用于屏蔽tab
    this->installEventFilter(this);
    //this->setMouseTracking(true);     //一定不能加入这个，否则会引发bug12758
}

QMTAbstractTableView::~QMTAbstractTableView()
{
    ClearDataModel();
}

QString QMTAbstractTableView::GetHeadWidgetSheetCallBack(int column, int delegateType)
{
    QString retStyleSheet;

    if (DELEAGATE_QMTCheckBox == delegateType)
    {
        int paddingLeft = 5;

        if (_perRowItemParam._headParam._paddingLeft > 0)
        {
            paddingLeft = _perRowItemParam._headParam._paddingLeft - 3;
        }

        QString textColor = QString("padding-left: %1px;").arg(paddingLeft);
        retStyleSheet = "QCheckBox{" + textColor + ";}";
    }

    return retStyleSheet;
}

QString QMTAbstractTableView::GetColumnWidgetSheetCallBack(int column, int delegateType)
{
    QString retStyleSheet;

    /* if (DELEAGATE_TYPE_STRING == delegateType || DELEAGATE_QLabel == delegateType)
     {
         int paddingLeft = 5;

         if (_perRowItemParam._headParam._paddingLeft > 0)
         {
             paddingLeft = _perRowItemParam._headParam._paddingLeft - 3;
         }

         QString textColor = QString("padding-left: %1px;").arg(paddingLeft);
         retStyleSheet = "QLabel{" + textColor + ";}";
     }

     else */if (DELEAGATE_QMTCheckBox == delegateType)
     {
         int paddingLeft = 5;

         if (_perRowItemParam._headParam._paddingLeft > 0)
         {
             paddingLeft = _perRowItemParam._headParam._paddingLeft - 3;
         }

         QString textColor = QString("padding-left: %1px;").arg(paddingLeft);
         retStyleSheet = "QCheckBox{" + textColor + ";}";
     }

     return retStyleSheet;
}

QString QMTAbstractTableView::GetColumnWidgetStylePropertyCallBack(int column, int delegateType)
{
    QString propertyValue;
    return propertyValue;
}

void QMTAbstractTableView::CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget)
{
}

void QMTAbstractTableView::CreateRowWidgetFinishCallBack(const QString& rowValue, int rowItemType)
{
}

void QMTAbstractTableView::InitTableView(QMTAbsRowWidgetItemParam& param)
{
    _perRowItemParam = param;
    //设置允许编辑的列
    QList<int> columnList;

    for (int i = 0; i < param._headParam._defaultColumn; ++i)
    {
        columnList << i;
    }

    SetCanEditColumnList(columnList);
    /***************************创建表头************************************/
    CreateHeadView();
    /***********************设置样式******************************/
    this->horizontalHeader()->setFixedHeight(_perRowItemParam._headParam._headHeight);                //设置表头的高度
    this->horizontalHeader()->setHighlightSections(false);
    this->horizontalHeader()->setHighlightSections(false);
    //this->horizontalHeader()->setDefaultSectionSize(_perRowItemParam._rowWidgetHeight);//设置列宽
    this->horizontalHeader()->setSectionsClickable(false); //设置表头不可点击（默认点击后进行排序）
    //设置表头字体加粗
    //QFont font = _tableWidget->horizontalHeader()->font();
    //font.setBold(true);
    //_tableWidget->horizontalHeader()->setFont(font);
    this->horizontalHeader()->setStretchLastSection(true); //设置充满表宽度
    this->horizontalHeader()->setDefaultAlignment(Qt::AlignLeft | Qt::AlignVCenter);//表头文案对齐方式
    //this->horizontalHeader()->setSectionResizeMode(QHeaderView::Stretch);         //设置等宽但是不能调节列宽了
    // this->verticalHeader()->setSectionResizeMode(QHeaderView::ResizeToContents);     //这个设置了，调用setRowHeight无效
    this->verticalHeader()->setDefaultSectionSize(_perRowItemParam._rowWidgetHeight); //设置行高
    //_tableWidget->setFrameShape(QFrame::NoFrame);              //设置无边框
    this->setShowGrid(_perRowItemParam._showGrid);                //设置是否显示格子线
    this->verticalHeader()->setVisible(false);                   //设置垂直头不可见
    this->setSelectionMode(QAbstractItemView::SingleSelection);  //可多选（Ctrl、Shift、  Ctrl+A都能够）
    this->setSelectionBehavior(QAbstractItemView::SelectRows);   //设置选择行为时每次选择一行
    //this->setEditTriggers(QAbstractItemView::NoEditTriggers);    //设置不可编辑
    this->setEditTriggers(QAbstractItemView::DoubleClicked);    //设置不可编辑
    setHorizontalScrollBarPolicy(Qt::ScrollBarAsNeeded);
    bool bContentGridShow = true;
    //设置样式
    {
        QString sheetStr = GetHeadHorizontalStyleStr();
        QHeaderView* horizontalView = this->horizontalHeader();

        if (horizontalView)
        {
            horizontalView->setStyleSheet(sheetStr);
        }

        sheetStr = GetHeadVerticalStyleStr();
        QHeaderView* verticalView = this->verticalHeader();

        if (verticalView)
            verticalView->setStyleSheet(sheetStr);

        sheetStr = GetTableWidgetStyleStr(bContentGridShow);
        this->setStyleSheet(sheetStr);
    }

    //设置代理类的颜色值
    if (_viewDelegate)
    {
        if (false == _perRowItemParam._borderColor.isValid())
        {
            _viewDelegate->setDrawHoverRowEnabled(false);
        }
        else
        {
            _viewDelegate->setDrawHoverRowEnabled(_perRowItemParam._isEnableHoverChangeBorderColor);
        }

        _viewDelegate->setContentGridShow(bContentGridShow);

        if (bContentGridShow)
        {
            /*
            1.因为未选中的颜色有透明度，但是通过ViewDelegate绘制透明度不生效，所以这边暂时取最相近的颜色
            2.显示grid的时候，横向的会偏大，所以这边这样处理
            */
            QColor unselectRowBackColor = _perRowItemParam._unselectBackColor;
            unselectRowBackColor.setRed(unselectRowBackColor.red() + 14);
            unselectRowBackColor.setGreen(unselectRowBackColor.green() + 14);
            unselectRowBackColor.setBlue(unselectRowBackColor.blue() + 14);
            _viewDelegate->setUnselectRowBackColor(unselectRowBackColor);
        }
        else
        {
            _viewDelegate->setUnselectRowBackColor(_perRowItemParam._unselectBackColor);
        }

        _viewDelegate->setDrawSelectRowEnabled(_perRowItemParam._isEnableSelectChangeStyle);
        _viewDelegate->setSelectRowBorderColor(_perRowItemParam._selectBorderColor);
        _viewDelegate->setHoveredRowBorderColor(_perRowItemParam._borderColor);
    }

    //设置水平、垂直滚动栏样式
    QScrollBar* horScrollBar = this->horizontalScrollBar();
    horScrollBar->setProperty("mtType", "scrollbar1");
    this->style()->unpolish(horScrollBar);
    this->style()->polish(horScrollBar);
    QScrollBar* verScrollBar = this->verticalScrollBar();
    verScrollBar->setProperty("mtType", "scrollbar1");
    this->style()->unpolish(verScrollBar);
    this->style()->polish(verScrollBar);
    //点击表时不正确表头行光亮（获取焦点）
    this->horizontalHeader()->setHighlightSections(false);
}



QMTAbsRowWidgetItemParam& QMTAbstractTableView::GetPerRowItemParam()
{
    return _perRowItemParam;
}


QMTAbsTableViewHeaderView* QMTAbstractTableView::GetTableViewHeaderView()
{
    return _headView;
}
void QMTAbstractTableView::SetEnableDynamicCreateUi(bool enable)
{
    _enableDynamicCreate = enable;
}

void QMTAbstractTableView::SetInitPageRowCount(int count)
{
    _InitPageRowCnt = count;
}

void QMTAbstractTableView::SetPerPageRowCount(int count)
{
    _perPageRowCnt = count;
}

int QMTAbstractTableView::GetPerPageRowCount()
{
    return _perPageRowCnt;
}

bool QMTAbstractTableView::SetDataModel(const QStringList& rowValueList, QMap<QString, QMap<int, ICellWidgetParam*>>& cellWidgetParamMapMap)
{
    if (0 == rowValueList.size())
    {
        return true;
    }

    if (WidgetProcessingStateFlag::WidgetProcessState::State_IDLE != s_tableProcessState)
    {
        qWarning() << "[QMTAbstractTableView::SetDataModel] true == WidgetProcessingStateFlag::WidgetProcessState::State_IDLE != s_tableProcessState";
        QString errMsg = tr("UI数据处理中(SetDataModel)，请勿重复调用，处理状态标识位 ：%1").arg(s_tableProcessState);
        qWarning() << errMsg;
        MtMessageBox::warning(nullptr, errMsg);
        return false;
    }

    WidgetProcessingStateFlag tableProcessStateFlag(&s_tableProcessState, WidgetProcessingStateFlag::WidgetProcessState::State_Init);
    SetViewDelegateHoverRow(-1);
    SetViewDelegateSelectRow(-1);

    if (rowValueList.size() != cellWidgetParamMapMap.size())
    {
        QString errMsg = QString("QMTAbstractTableView::SetDataModel err:%1, %2").arg(rowValueList.size()).arg(cellWidgetParamMapMap.size());
        MtMessageBox::warning(nullptr, errMsg);
        return false;
    }

    DisConnectScrollSignals();

    for (int i = 0; i < rowValueList.size(); ++i)
    {
        bool isExist = false;
        QString rowValue = rowValueList[i];

        if (IsExistRowItem(rowValue))
        {
            continue;
        }

        QMap<int, ICellWidgetParam*> cellWidgetParam;

        if (true == cellWidgetParamMapMap.contains(rowValue))
        {
            cellWidgetParam = cellWidgetParamMapMap.value(rowValue);
        }
        else
        {
            qWarning() << " QMTAbstractTableWidget::SetDataModel err!";
            continue;
        }

        if (true == _enableDynamicCreate && i >= _InitPageRowCnt)
        {
            break;
        }

        if (0 == i)//第一行
        {
        }

        AddRowItem(rowValue, cellWidgetParam);
    }

    SetCurRowValueList(rowValueList);
    _rowCellWidgetParamMapMap = cellWidgetParamMapMap;
    _cachRowValueList = rowValueList;
    ConnectScrollSignals();
    return true;
}


bool QMTAbstractTableView::SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QStringList/*column string list*/>& rowColumnStrListMap)
{
    if (rowValueList.size() != rowColumnStrListMap.size())
    {
        QString errMsg = QString("QMTAbstractTableView::SetDataModel err:%1, %2").arg(rowValueList.size()).arg(rowColumnStrListMap.size());
        MtMessageBox::warning(nullptr, errMsg);
        return false;
    }

    if (0 == rowValueList.size())
        return true;

    QMap<QString, QMap<int, ICellWidgetParam*>> cellWidgetParamMapMap;
    QMap<QString/*rowValue*/, QStringList/*column string list*/>::iterator iter = rowColumnStrListMap.begin();

    for (iter = rowColumnStrListMap.begin(); iter != rowColumnStrListMap.end(); ++iter)
    {
        QString rowValue = iter.key();
        QStringList columnStrList = iter.value();
        QMap<int, ICellWidgetParam*> cellWidgetParamMap;

        for (int i = 0; i < columnStrList.size(); ++i)
        {
            QCustMtLabelParam* labelParam = new QCustMtLabelParam();
            labelParam->_text = columnStrList[i];
            labelParam->_mtType = GetMtLabelTypeWithFontSize(_perRowItemParam._fontsize);
            cellWidgetParamMap.insert(i, labelParam);
        }

        cellWidgetParamMapMap.insert(rowValue, cellWidgetParamMap);
    }

    SetDataModel(rowValueList, cellWidgetParamMapMap);
    return true;
}

void QMTAbstractTableView::AddRowItem(const QString& rowValue, QMap<int, ICellWidgetParam*>& cellWidgetParamMap)
{
    if (true == IsExistRowItem(rowValue))      //判断是否已经存在
    {
        DeleteCellParamMap(cellWidgetParamMap);
        return;
    }

    //数据层添加
    QMap<int, ICellWidgetParam*>::iterator iter;

    for (iter = cellWidgetParamMap.begin(); iter != cellWidgetParamMap.end(); ++iter)
    {
        int column = iter.key();

        if (nullptr != cellWidgetParamMap[column])
        {
            cellWidgetParamMap[column]->_rowValue = rowValue;
        }
        else
        {
            qWarning() << "[QMTAbstractTableView::AddRowItem] faild, nullptr == cellWidgetParamMap[column] : " << column;
            return;
        }
    }

    if (_rowValueList.indexOf(rowValue) < 0)
    {
        _rowValueList.append(rowValue);
    }
    else
    {
        qWarning() << "_rowValueList : " << _rowValueList << ",contains rowValue : " << rowValue;
    }

    if (_cachRowValueList.indexOf(rowValue) < 0)
    {
        InsertRowCellWidgetParamMap(rowValue, cellWidgetParamMap);
        _cachRowValueList.append(rowValue);
    }
    else
    {
        qWarning() << "_cachRowValueList : " << _cachRowValueList << ",contains rowValue : " << rowValue;
    }

    //界面新增一行
    if (IsNeedCreateWidget())
    {
        int row_count = this->rowCount(); //获取表单行数
        //这边防止处方滚动条滚动
        DisConnectScrollSignals();
        //为了解决滑动条滑到最后的时候，添加一行界面错乱问题
        int maxValue = this->verticalScrollBar()->maximum();
        int curValue = this->verticalScrollBar()->value();
        bool bBottom = (maxValue == curValue) ? true : false;

        if (bBottom)
        {
            this->verticalScrollBar()->setValue(maxValue - 1);
        }

        this->insertRow(row_count);       //插入新行
        CreateRowItemWidget(rowValue);

        if (bBottom)
        {
            this->verticalScrollBar()->setValue(maxValue);
        }

        ConnectScrollSignals();
        DoHorizontalScrollBarValueChangeProc();
        //防止页面错乱
        QSize size = this->size();
        this->resize(size.width() - 1, size.height() - 1);
        this->resize(size.width(), size.height());
        this->show();
    }
    else
    {
    }
}


void QMTAbstractTableView::InsertRowItem(int row, const QString& rowValue, QMap<int, ICellWidgetParam*>& cellWidgetParamMap)
{
    bool isExist = false;

    if (row < 0)
        return;

    if (0 == _rowCellWidgetParamMapMap.size() || row >= _rowCellWidgetParamMapMap.size())
    {
        AddRowItem(rowValue, cellWidgetParamMap);
        return;
    }

    if (true == IsExistRowItem(rowValue))
    {
        DeleteCellParamMap(cellWidgetParamMap);     //释放内存
        return;
    }

    //数据层插入

    if (_rowValueList.indexOf(rowValue) < 0)
    {
        _rowValueList.insert(row, rowValue);
    }
    else
    {
        qWarning() << "_rowValueList : " << _rowValueList << ",contains rowValue : " << rowValue;
    }

    if (_cachRowValueList.indexOf(rowValue) < 0)
    {
        InsertRowCellWidgetParamMap(rowValue, cellWidgetParamMap);
        _cachRowValueList.insert(row, rowValue);
    }
    else
    {
        qWarning() << "_cachRowValueList : " << _cachRowValueList << ",contains rowValue : " << rowValue;
    }

    //界面层插入
    if (row < this->rowCount())
    {
        //这边防止处方滚动条滚动
        DisConnectScrollSignals();
        //为了解决滑动条滑到最后的时候，添加一行界面错乱问题
        int maxValue = this->verticalScrollBar()->maximum();
        int curValue = this->verticalScrollBar()->value();
        bool bBottom = (maxValue == curValue) ? true : false;

        if (bBottom)
        {
            this->verticalScrollBar()->setValue(maxValue - 1);
        }

        this->insertRow(row); //插入新行
        CreateRowItemWidget(rowValue);

        if (bBottom)
        {
            this->verticalScrollBar()->setValue(maxValue);
        }

        ConnectScrollSignals();
        DoHorizontalScrollBarValueChangeProc();
        this->show();               //防止页面错乱
    }
}

void QMTAbstractTableView::InsertPerRowItem(int row, const QString& uniqueValue, const QStringList& columnStrList)
{
    if (columnStrList.size() != _perRowItemParam._headParam._defaultColumn ||
        columnStrList.size() == 0)
        return;

    QMap<int, ICellWidgetParam*> cellWidgetParamMap;

    for (int i = 0; i < columnStrList.size(); ++i)
    {
        QCustMtLabelParam* labelParam = new QCustMtLabelParam();
        labelParam->_text = columnStrList[i];
        labelParam->_mtType = GetMtLabelTypeWithFontSize(_perRowItemParam._fontsize);
        cellWidgetParamMap.insert(i, labelParam);
    }

    InsertRowItem(row, uniqueValue, cellWidgetParamMap);
}
void QMTAbstractTableView::AddPerRowItem(const QString& uniqueValue, const QStringList& columnStrList)
{
    if (columnStrList.size() != _perRowItemParam._headParam._defaultColumn ||
        columnStrList.size() == 0)
        return;

    QMap<int, ICellWidgetParam*> cellWidgetParamMap;

    for (int i = 0; i < columnStrList.size(); ++i)
    {
        QCustMtLabelParam* labelParam = new QCustMtLabelParam();
        labelParam->_text = columnStrList[i];
        labelParam->_mtType = GetMtLabelTypeWithFontSize(_perRowItemParam._headParam._fontSize);
        cellWidgetParamMap.insert(i, labelParam);
    }

    AddRowItem(uniqueValue, cellWidgetParamMap);
}


void QMTAbstractTableView::SetSpanText(const QString& rowValue, int column, int rowSpanCount, int columnSpanCount, const QString& text, const QString& styleStr)
{
    int row = GetRowIndex(rowValue);
    this->setSpan(row, column, rowSpanCount, columnSpanCount);
    QLabel* label = new QLabel(this);
    label->setText(text);
    this->setCellWidget(row, column, label);

    if (styleStr.size() > 0)
    {
        label->setStyleSheet(styleStr);
    }
}

void QMTAbstractTableView::SetSpanRow(const QString& rowValue, const QString& text, const QString& styleStr /*= ""*/)
{
    SetSpanText(rowValue, 0, 1, _perRowItemParam._headParam._defaultColumn, text, styleStr);
}

void QMTAbstractTableView::DeleteCurRow()
{
    int row = this->currentRow();
    QString rowValue = GetRowUniqueValue(row);
    DeleteRowItem(rowValue);
    SetViewDelegateHoverRow(-1);
    SetViewDelegateSelectRow(-1);
}

void QMTAbstractTableView::DeleteRowItem(const QString& rowValue)
{
    if (WidgetProcessingStateFlag::WidgetProcessState::State_IDLE != s_tableProcessState)
    {
        qWarning() << "[QMTAbstractTableView::DeleteRowItem] true == WidgetProcessingStateFlag::WidgetProcessState::State_IDLE != s_tableProcessState";
        QString errMsg = tr("UI数据处理中(DeleteRowItem)，请勿重复调用，处理状态标识位 ：%1").arg(s_tableProcessState);
        qWarning() << errMsg;
        MtMessageBox::warning(nullptr, errMsg);
    }

    WidgetProcessingStateFlag tableProcessStateFlag(&s_tableProcessState, WidgetProcessingStateFlag::WidgetProcessState::State_DeleteRow);
    int row = GetRowIndex(rowValue);
    QMTTableDataCache::DeleteRowItem(rowValue);

    if (row >= 0)
    {
        DisConnectScrollSignals();      //删除界面前，先屏蔽滚动条信号
        int verScrollValue = this->verticalScrollBar()->value();
        int column = this->columnCount();

        for (int j = 0; j < column; ++j)
        {
            QWidget* cellWidget = this->cellWidget(row, j);

            if (nullptr != cellWidget)
            {
#ifdef DELETELATER_ENABLE
                cellWidget->deleteLater();
                cellWidget = nullptr;
#else
                QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(cellWidget);

                if (nullptr != cellWidgetBase)
                {
                    delete cellWidgetBase;
                    cellWidgetBase = nullptr;
                }
                else
                {
                    //cellWidget->deleteLater();
                    delete cellWidget;
                    cellWidget = nullptr;
                }

#endif
            }
        }

        this->removeRow(row);       //delete单元格的界面对象后必须调用removeRow
        ConnectScrollSignals();     //删除界面后，打开滚动条信号
        DoHorizontalScrollBarValueChangeProc();
        //防止页面错乱
        QSize size = this->size();
        this->resize(size.width() - 1, size.height() - 1);
        this->resize(size.width(), size.height());
        this->show();
    }
}

void QMTAbstractTableView::DeleteCellWidgt(int row, int column)
{
    QTableWidgetItem* widgetItem = this->takeItem(row, column);

    if (widgetItem)
    {
        delete widgetItem;
        widgetItem = nullptr;
    }
    else
    {
        QWidget* widget = this->cellWidget(row, column);

        if (widget)
        {
            this->removeCellWidget(row, column);
            delete widget;
            widget = nullptr;
        }
    }
}

void QMTAbstractTableView::ClearDataModel()
{
    if (WidgetProcessingStateFlag::WidgetProcessState::State_IDLE != s_tableProcessState)
    {
        qWarning() << "[QMTAbstractTableView::ClearDataModel] WidgetProcessingStateFlag::WidgetProcessState::State_IDLE != s_tableProcessState";
        QString errMsg = tr("UI数据处理中(ClearDataModel)，请勿重复调用，处理状态标识位 ：%1").arg(s_tableProcessState);
        qWarning() << errMsg;
        MtMessageBox::warning(nullptr, errMsg);
        return;
    }

    WidgetProcessingStateFlag tableProcessStateFlag(&s_tableProcessState, WidgetProcessingStateFlag::WidgetProcessState::State_ClearView);
    int row = this->rowCount(); //获取界面的行
    int column = this->columnCount();
    QList<QWidget*> widgetList;
    QList<QTableWidgetItem*> widgetItemList;
    //防止界面错乱。Qt的bug
    {
        int maxValue = this->verticalScrollBar()->maximum();
        int curValue = this->verticalScrollBar()->value();
        bool bBottom = (curValue == maxValue || curValue > 0) ? true : false;

        if (bBottom)
        {
            this->verticalScrollBar()->setValue(0);
            QSize size = this->size();
            this->resize(size.width() - 1, size.height() - 1);
            this->resize(size.width(), size.height());
        }
    }
    DisConnectScrollSignals();
    DisConnectAllCellWidgetSignals();
#ifdef DELETELATER_ENABLE
    /*
    只清空表格内容，不清空表头
    1.直接调用clearContents会自动析构单元格，这边不需要自己手动析构。
    2.必须使用这种方式让qt自动析构，否则容易出现偶发性bug，如bug 12513
    */
    this->clearContents();
    row = this->rowCount(); //获取界面的行
#else
    /*
    手动delete的方式
    */
    row = this->rowCount(); //获取界面的行

    for (int i = 0; i < row; ++i)
    {
        for (int j = 0; j < column; ++j)
        {
            //QTableWidgetItem* widgetItem = this->item(i, j);
            //if (nullptr != widgetItem)
            //{
            //    widgetItemList << widgetItem;
            //}
            QWidget* cellWidget = this->cellWidget(i, j);

            if (nullptr != cellWidget)
            {
                widgetList << cellWidget;
            }
        }
    }

    DeleteWidgetList(widgetList);
    DeleteTableWidgetItemList(widgetItemList);
    this->clearContents();
#endif

    for (int i = row - 1; i >= 0; --i)
    {
        this->removeRow(i);
    }

    QMTTableDataCache::ClearDataModel();
    ConnectScrollSignals();
    DoHorizontalScrollBarValueChangeProc();
    row = GetRowCount();
    SetViewDelegateHoverRow(-1);
    SetViewDelegateSelectRow(-1);
}

void QMTAbstractTableView::DeleteWidgetList(QList<QWidget*>& widgetList)
{
    for (int i = 0; i < widgetList.size(); ++i)
    {
        QWidget* widget = widgetList.at(i);
#ifdef DELETELATER_ENABLE
        widget->deleteLater();          //防止单元格内部如果发送信号然后去清空界面，导致奔溃，所以使用deleteLater
#else
        QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(widget);

        if (nullptr != cellWidgetBase)
        {
            //获取单元界面信息
            QString uniqueValue = cellWidgetBase->GetCellData(QMTAbstractCellWidget::Role_MainValue);
            QString parentValue = cellWidgetBase->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
            int column = cellWidgetBase->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
            int rowType = cellWidgetBase->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
            //delete基类对象
            delete cellWidgetBase;
            cellWidgetBase = nullptr;
        }
        else
        {
            QString objectName = widget->objectName();
            delete widget;
            widget = nullptr;
        }

#endif
        widgetList[i] = nullptr;
    }

    widgetList.clear();
}

void QMTAbstractTableView::DeleteTableWidgetItemList(QList<QTableWidgetItem*>& widgetItemList)
{
    for (int i = 0; i < widgetItemList.size(); ++i)
    {
        QTableWidgetItem* widgetItem = widgetItemList.at(i);
        delete widgetItem;
        widgetItemList[i] = nullptr;
    }

    widgetItemList.clear();
}

bool QMTAbstractTableView::UpdateCellWidget(const QString& rowValue, int column, const QVariant& updateData)
{
    int row = GetRowIndex(rowValue);

    if (rowValue.isEmpty() || row < 0)
    {
        qWarning() << QString("[QMTAbstractTableView::UpdateCellWidget], rowValue.isEmpty() || row < 0, rowValue = %1").arg(rowValue);
        return false;
    }

    if (false == _rowCellWidgetParamMapMap.contains(rowValue))
    {
        qWarning() << QString("[QMTAbstractTableView::UpdateCellWidget], false == _rowCellWidgetParamMapMap.contains(rowValue), rowValue = %1").arg(rowValue);
        return false;
    }

    QMap<int/*column*/, ICellWidgetParam*> cellParamMap = _rowCellWidgetParamMapMap.value(rowValue);

    if (false == cellParamMap.contains(column))
    {
        qWarning() << QString("[QMTAbstractTableView::UpdateCellWidget], false == cellParamMap.contains(column), rowValue = %1, column = %2").arg(rowValue).arg(column);
        return false;
    }

    //更新数据层
    _rowCellWidgetParamMapMap[rowValue][column]->_bUpdate = true;
    _rowCellWidgetParamMapMap[rowValue][column]->_updateData = updateData;
    //更新界面层
    QWidget* cellWidget = this->cellWidget(row, column);

    if (nullptr == cellWidget)
    {
        qWarning() << QString("[QMTAbstractTableView::UpdateCellWidget], nullptr == cellWidget, row = %1, column = %2").arg(row).arg(column);
        return false;
    }

    QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(cellWidget);

    if (nullptr == cellWidgetBase)
    {
        qWarning() << QString("[QMTAbstractTableView::UpdateCellWidget], nullptr == cellWidgetBase, row = %1, column = %2").arg(row).arg(column);
        return false;
    }

    if (cellWidgetBase)
    {
        cellWidgetBase->UpdateUi(updateData);
    }

    return true;
}

void QMTAbstractTableView::UpdateTextMap(const QString& rowValue, QMap<int, QString>& updateTextMap)
{
    QMap<int, QString>::iterator iter = updateTextMap.begin();

    for (iter = updateTextMap.begin(); iter != updateTextMap.end(); ++iter)
    {
        int column = iter.key();
        QString updateText = iter.value();
        UpdateCellWidget(rowValue, column, QVariant::fromValue(updateText));
    }
}

void QMTAbstractTableView::UpdateHeadText(int column, const QString& updateStr)
{
    if (column < 0 || column >= _perRowItemParam._headParam._headStrList.size())
        return;

    _perRowItemParam._headParam._headStrList[column] = updateStr;
    this->setHorizontalHeaderLabels(_perRowItemParam._headParam._headStrList);
}


bool QMTAbstractTableView::UpdateHeadCellWidget(int column, QVariant& updateData)
{
    if (nullptr == _headView)
        return false;

    QWidget* cellWidget = _headView->GetHeadCellWidget(column);

    if (cellWidget)
    {
        QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(cellWidget);

        if (cellWidgetBase)
        {
            cellWidgetBase->UpdateUi(updateData);
            return true;
        }
    }

    return false;
}

void QMTAbstractTableView::UpdateRowData(const QString& rowValue, const QStringList& columnStrList)
{
    for (int column = 0; column < columnStrList.size(); ++column)
    {
        QString updateText = columnStrList[column];
        UpdateCellWidget(rowValue, column, QVariant::fromValue(updateText));
    }
}

void QMTAbstractTableView::UpdateAllColumnText(int column, const QString& value)
{
    if (column < 0 || column >= _perRowItemParam._headParam._defaultColumn)
        return;

    int count = _rowValueList.size();

    for (int row = 0; row < count; ++row)
    {
        QString rowValue = _rowValueList[row];
        UpdateCellWidget(rowValue, column, QVariant::fromValue(value));
    }
}

void QMTAbstractTableView::UpdateRowMainKey(const QString& oldMainValue, const QString& newMainValue)
{
    if (oldMainValue == newMainValue)
    {
        //如果新旧是一样的，那么直接返回即可
        return;
    }

    int row = _rowValueList.indexOf(oldMainValue);
    int index = _cachRowValueList.indexOf(oldMainValue);

    if (row < 0 || false == _rowCellWidgetParamMapMap.contains(oldMainValue))
    {
        qWarning() << "[QMTAbstractTableView::UpdateRowMainKey] row < 0 || false == _rowCellWidgetParamMapMap.contains(oldMainValue)";
        return;
    }

    if (index < 0)
    {
        qWarning() << "[QMTAbstractTableView::UpdateRowMainKey] index < 0";
        return;
    }

    _rowValueList[row] = newMainValue;
    //处理cache数据
    QMap<int/*column*/, ICellWidgetParam*> cellParamMap = _rowCellWidgetParamMapMap.value(oldMainValue);
    _rowCellWidgetParamMapMap.remove(oldMainValue);
    InsertRowCellWidgetParamMap(newMainValue, cellParamMap);
    _cachRowValueList[index] = newMainValue;

    //是否创建界面
    if (true == _rowItemWidgetFlagMap.contains(oldMainValue))
    {
        _rowItemWidgetFlagMap.remove(oldMainValue);
        _rowItemWidgetFlagMap.insert(newMainValue, true);
    }

    if (_curRowItemValue == oldMainValue)
    {
        _curRowItemValue = newMainValue;
    }

    //界面map修改
    for (int i = 0; i < _perRowItemParam._headParam._defaultColumn; ++i)
    {
        int column = i;
        QWidget* widget = this->cellWidget(row, column);
        QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);

        if (cellWidget)
        {
            cellWidget->SetCellData(QMTAbstractCellWidget::Role_MainValue, newMainValue);
        }
    }
}

void QMTAbstractTableView::SortRowValueList(const QStringList& newRowValueList)
{
    //如果完全相等，没必要执行排序，直接返回
    if (IsAllRowValueEquel(newRowValueList))
    {
        return;
    }

    QStringList oldRowValueList = GetAllRowUniqueValueList();

    if (false == IsAllRowValueContains(newRowValueList, true))
    {
        QString errMsg = QString("[QMTAbstractTableView::SortRowValueList]false == IsAllRowValueContains(newRowValueList, true)");
        qWarning() << errMsg;
        qWarning() << "QMTAbstractTableView Ui rowValueList : " << GetAllRowUniqueValueList();
        qWarning() << "newRowValueList : " << newRowValueList;
        MtMessageBox::yellowWarning(this, errMsg);
        return;
    }

    SetCurRowValueList(newRowValueList);
    _cachRowValueList = newRowValueList;

    //界面map修改
    for (int row = 0; row < newRowValueList.size(); ++row)
    {
        QString oldRowValue = oldRowValueList[row];
        QString newMainValue = GetRowUniqueValue(row);

        if (newMainValue.isEmpty())
        {
            QString errMsg = QString("[QMTAbstractTableView::SortRowValueList]newMainValue.isEmpty()");
            qWarning() << errMsg;
            MtMessageBox::yellowWarning(this, errMsg);
            continue;
        }

        for (int column = 0; column < _perRowItemParam._headParam._defaultColumn; ++column)
        {
            QWidget* widget = this->cellWidget(row, column);
            QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);

            if (cellWidget)
            {
                cellWidget->SetCellData(QMTAbstractCellWidget::Role_MainValue, newMainValue);
            }
        }
    }
}

void QMTAbstractTableView::HideRowItem(const QString& rowValue, bool bHide)
{
    int row = GetRowIndex(rowValue);
    this->setRowHidden(row, bHide);
}

void QMTAbstractTableView::UpdateAllColumnBtnChecked(int column, bool bChecked)
{
    if (column < 0 || column >= _perRowItemParam._headParam._defaultColumn)
        return;

    int count = _rowValueList.size();

    for (int row = 0; row < count; ++row)
    {
        QString rowValue = _rowValueList[row];
        UpdateCellWidget(rowValue, column, QVariant::fromValue(bChecked));
    }
}

void QMTAbstractTableView::HideColumnCellWidget(int row, int column, bool hide)
{
    QWidget* widget = this->cellWidget(row, column);

    if (widget)
    {
        if (hide)
        {
            widget->setMaximumSize(1, 1);
            widget->hide();
        }
        else
        {
            widget->setMaximumSize(4096, 4096);
            bool bColumnHide = this->isColumnHidden(column);

            if (false == bColumnHide)//如果整列都隐藏了，那么就不显示该cellwidget了
                widget->show();
        }
    }
}

void QMTAbstractTableView::HideColumn(int column, bool bHide/* = true*/)
{
    if (true == bHide)
    {
        this->hideColumn(column);
    }
    else
    {
        this->showColumn(column);
    }

    if (_headView)
    {
        _headView->HideColumn(column, bHide);
    }
}

void QMTAbstractTableView::SetCellEditEnable(const QString& rowValue, int column, bool enable)
{
    int row = GetRowIndex(rowValue);
    //数据层
    QMTTableDataCache::SetCellEditEnable(rowValue, column, enable);

    //界面层
    if (row < 0 || rowValue.isEmpty())
        return;

    QWidget* widget = this->cellWidget(row, column);
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);

    if (cellWidget)
    {
        cellWidget->SetEnableEdit(enable);
    }
}

void QMTAbstractTableView::SetCurrentRow(const QString& rowValue, bool bEmit /*= false*/)
{
    //取消选中
    if (rowValue.isEmpty())
    {
        this->setCurrentItem(nullptr);
        SetViewDelegateSelectRow(-1);
        SetViewDelegateHoverRow(-1);
        return;
    }

    QStringList rowValueList = GetAllRowUniqueValueList();

    //选中的不存在
    if (rowValueList.indexOf(rowValue) < 0)
    {
        return;
    }

    int row = GetRowIndex(rowValue);

    if (false == IsRowItemWidgetCreate(rowValue))
    {
        CreateRowItemWidget(rowValue);
    }

    int curRow = GetCurrentRow();

    if (false == bEmit)//如果未false,那么只有行号变化才继续往后走，否则都必须往后执行
    {
        if (row == curRow)
        {
            //刷新界面
            SetViewDelegateSelectRow(row);
            SetViewDelegateHoverRow(-1);
            return;
        }
    }

    QScrollBar* horBar = this->horizontalScrollBar();

    if (horBar)
    {
        horBar->blockSignals(true);
    }

    this->selectRow(row);       //这边会触发横向滚动条滚动，所以先禁用QScrollBar信号

    if (horBar)
    {
        horBar->blockSignals(false);
    }

    slotTableCellClicked(row, 0);
    //刷新界面
    SetViewDelegateSelectRow(row);
    SetViewDelegateHoverRow(-1);
}

void QMTAbstractTableView::SetEditEnable(bool isEditEnable)
{
    QMTTableDataCache::SetEditEnable(isEditEnable);
    QStringList rowValueList = GetAllRowUniqueValueList();
    QList<int> columnList = GetCanEditColumnList();

    for (int row = 0; row < rowValueList.size(); ++row)
    {
        for (int column = 0; column < columnList.size(); ++column)
        {
            SetCellEditEnable(rowValueList[row], columnList[column], isEditEnable);
        }
    }
}

void QMTAbstractTableView::SetColumnEditEnable(int column, bool bEnable)
{
    QStringList rowValueList = GetAllRowUniqueValueList();
    QMTTableDataCache::SetColumnEditEnable(column, bEnable);

    for (int row = 0; row < rowValueList.size(); ++row)
    {
        SetCellEditEnable(rowValueList[row], column, bEnable);
    }
}

void QMTAbstractTableView::SetButtonChecked(const QString& rowValue, int column, bool bChecked)
{
    UpdateCellWidget(rowValue, column, QVariant::fromValue(bChecked));
}

void QMTAbstractTableView::SetHeadCheckBoxState(int column, int state, bool bEmit /*= false*/)
{
    if (nullptr == _headView)
    {
        return;
    }

    QWidget* headWidget = _headView->GetHeadCellWidget(column);
    QMTCheckBox* checkbox = qobject_cast<QMTCheckBox*>(headWidget);

    if (checkbox)
    {
        int curState = checkbox->checkState();

        if (curState == state)  //如果状态一样，那么直接返回
            return;

        if (false == bEmit)
        {
            disconnect(headWidget, SIGNAL(stateChanged(int)), this, SLOT(slotHeadCheckBoxStateChange(int)));
        }

        checkbox->setCheckState((Qt::CheckState)state);

        if (false == bEmit)
        {
            connect(headWidget, SIGNAL(stateChanged(int)), this, SLOT(slotHeadCheckBoxStateChange(int)));
        }
    }
}

void QMTAbstractTableView::SetCheckBoxState(const QString& uniqueValue, int column, int state, bool bEmit/* = true*/)
{
    //1.数据层设置
    SetCheckBoxStateInModel(uniqueValue, column, state);
    //2.设置界面
    UpdateColumnWidgetCheckBoxState(uniqueValue, column, state, bEmit);
    //3.判断全选
    if (Qt::Checked == state)
    {
        //判断是否所有的checkbox选中了
        if (true == IsAllRowChecked(column))
        {
            SetHeadCheckBoxState(column, state);
        }
    }
    else
    {
        SetHeadCheckBoxState(column, Qt::Unchecked);
    }
}

QStringList QMTAbstractTableView::GetHeadStrList()
{
    return _perRowItemParam._headParam._headStrList;
}

int QMTAbstractTableView::GetCurrentRow()
{
    return this->currentRow();
}

QString QMTAbstractTableView::GetCurUniqueValue()
{
    int row = GetCurrentRow();
    return GetRowUniqueValue(row);
}

int QMTAbstractTableView::GetRowCount()
{
    return _rowValueList.size();
    //return this->rowCount();
}

//QStringList QMTAbstractTableView::GetColumnTextList(int row)
//{
//    QStringList retStrList;
//
//    if (row < 0)
//        return retStrList;
//
//    for (int col = 0; col < _perRowItemParam._headParam._defaultColumn; ++col)
//    {
//        QString text = GetColumnText(row, col);
//        retStrList.append(text);
//    }
//
//    return retStrList;
//}


QStringList QMTAbstractTableView::GetColumnTextList(const QString& value)
{
    QStringList columnTextList;
    int columns = _perRowItemParam._headParam._defaultColumn;

    for (int column = 0; column < columns; ++column)
    {
        QString text = GetColumnText(value, column);
        columnTextList.append(text);
    }

    return columnTextList;
}

QString QMTAbstractTableView::GetRowUniqueValue(int row)
{
    QString rowValue;

    if (row < 0 || row >= _rowValueList.size())
    {
        return rowValue;
    }

    return _rowValueList[row];
    /*QString uniqueValue;

    if (row < 0 || row >= _dataModelObjList.size())
        return uniqueValue;

    QJsonObject obj = _dataModelObjList.at(row);
    uniqueValue = obj.value(_perRowItemParam._mainKey).toString();

    if (uniqueValue.isEmpty())
    {
        for (int i = 0; i < _perRowItemParam._headParam._defaultColumn; ++i)
        {
            QTableWidgetItem* item = this->item(row, i);

            if (item)
            {
                uniqueValue = item->data(QMTAbstractCellWidget::Role_MainValue).toString();

                if (uniqueValue.size() > 0)
                    break;
            }
        }
    }

    return uniqueValue;*/
}

int QMTAbstractTableView::GetRowIndex(const QString& uniqueValue)
{
    int row = _rowValueList.indexOf(uniqueValue);
    return row;
    /*int ret = -1;

    for (int i = 0; i < _dataModelObjList.size(); ++i)
    {
        QString mainKey = _perRowItemParam._mainKey;
        QJsonObject obj = _dataModelObjList.at(i);
        QString value = obj.value(mainKey).toString();

        if (value == uniqueValue)
        {
            ret = i;
            break;
        }
    }

    return ret;*/
}


int QMTAbstractTableView::GetCheckBoxState(int row, int column)
{
    QWidget* widget = this->cellWidget(row, column);
    QMTCheckBox* checkBox = qobject_cast<QMTCheckBox*>(widget);
    return checkBox->checkState();
}

void QMTAbstractTableView::TableFullCheckBoxStateChangeProc(int column, int state)
{
    if (column < 0)
        return;

    QStringList rowValueList;

    if (_curSearchText.size() == 0 && _searchResultStrList.size() == 0)
    {
        rowValueList = GetAllRowUniqueValueList();
    }
    else
    {
        rowValueList = _searchResultStrList;
    }

    for (int i = 0; i < rowValueList.size(); ++i)
    {
        QString uniqueValue = rowValueList[i];

        if (false == GetIsCheckBoxCellWidget(uniqueValue, column))
        {
            continue;
        }

        //1.数据层设置
        SetCheckBoxStateInModel(uniqueValue, column, state);
        //2.设置界面
        UpdateColumnWidgetCheckBoxState(uniqueValue, column, state, false);
    }

    //3.判断全选
    if (Qt::Checked == state)
    {
        //判断是否所有的checkbox选中了
        if (true == IsAllRowChecked(column))
        {
            SetHeadCheckBoxState(column, state);
        }
    }
    else
    {
        SetHeadCheckBoxState(column, Qt::Unchecked);
    }
}

void QMTAbstractTableView::ConnectScrollSignals()
{
    bool bOK = false;
    QScrollBar* bar = nullptr;

    if (true == _enableDynamicCreate)
    {
        bar = this->verticalScrollBar();

        if (bar)
        {
            bOK = connect((QObject*)bar, SIGNAL(valueChanged(int)), this, SLOT(slotScrollBarValueChanged(int)));   //必须得强制转换
        }
        else
        {
            qWarning() << "nullptr == this->verticalScrollBar()";
        }
    }

    bar = this->horizontalScrollBar();

    if (bar)
    {
        bOK = connect((QObject*)bar, SIGNAL(valueChanged(int)), this, SLOT(slotHorizontalScrollBarValueChanged(int)));   //必须得强制转换
    }
    else
    {
        qWarning() << "nullptr == this->horizontalScrollBar()";
    }
}
void QMTAbstractTableView::DisConnectScrollSignals()
{
    bool bOK = false;
    QScrollBar* bar = nullptr;

    if (true == _enableDynamicCreate)
    {
        bar = this->verticalScrollBar();

        if (bar)
        {
            bOK = disconnect((QObject*)bar, SIGNAL(valueChanged(int)), this, SLOT(slotScrollBarValueChanged(int)));   //必须得强制转换
        }
        else
        {
            qWarning() << "nullptr == this->verticalScrollBar()";
        }
    }

    bar = this->horizontalScrollBar();

    if (bar)
    {
        bOK = disconnect((QObject*)bar, SIGNAL(valueChanged(int)), this, SLOT(slotHorizontalScrollBarValueChanged(int)));   //必须得强制转换
    }
    else
    {
        qWarning() << "nullptr == this->horizontalScrollBar()";
    }
}

void QMTAbstractTableView::ConnectHeadWidgetSignals(int cellWidgetType, QWidget* headWidget)
{
    switch (cellWidgetType)
    {
        case DELEAGATE_QMTCheckBox:
        {
            connect(headWidget, SIGNAL(stateChanged(int)), this, SLOT(slotHeadCheckBoxStateChange(int)));
        }
        break;

        case DELEAGATE_CheckBoxLabel:
        {
            connect(headWidget, SIGNAL(sigStateChanged(int)), this, SLOT(slotHeadCheckBoxStateChange(int)));
        }
        break;

        case DELEAGATE_QMTButtonWithOrder:
        {
            connect(headWidget, SIGNAL(sigOrderStateChange(const QString&, int)), this, SLOT(slotHeadOrderStateChange(const QString&, int)));
        }
        break;

        case DELEAGATE_QMTAbsHorizontalBtns:
        {
            connect(headWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotHeadButtonClicked(int, bool)));
        }
        break;

        default:
            break;
    }
}

void QMTAbstractTableView::DisConnectHeadWidgetSignals(int cellWidgetType, QWidget* headWidget)
{
    switch (cellWidgetType)
    {
        case DELEAGATE_QMTCheckBox:
        {
            disconnect(headWidget, SIGNAL(stateChanged(int)), this, SLOT(slotHeadCheckBoxStateChange(int)));
        }
        break;

        case DELEAGATE_CheckBoxLabel:
        {
            disconnect(headWidget, SIGNAL(sigStateChanged(int)), this, SLOT(slotHeadCheckBoxStateChange(int)));
        }
        break;

        case DELEAGATE_QMTButtonWithOrder:
        {
            disconnect(headWidget, SIGNAL(sigOrderStateChange(const QString&, int)), this, SLOT(slotHeadOrderStateChange(const QString&, int)));
        }
        break;

        case DELEAGATE_QMTAbsHorizontalBtns:
        {
            disconnect(headWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotHeadButtonClicked(int, bool)));
        }
        break;

        default:
            break;
    }
}

void QMTAbstractTableView::ConnectTableViewSignlas()
{
    //bool bOk = connect(this, &QTableWidget::entered, this, &QMTAbstractTableView::slotTableWigetEnter);
    //bOk = connect(this, &QTableWidget::cellEntered, this, &QMTAbstractTableView::slotTableWigetCellEntered);
    connect(this, SIGNAL(cellClicked(int, int)), this, SLOT(slotTableCellClicked(int, int)));
    connect(this, SIGNAL(cellDoubleClicked(int, int)), this, SLOT(slotTableCellDoubleClicked(int, int)));
}

void QMTAbstractTableView::DisConnectTableViewSignlas()
{
    disconnect(this, SIGNAL(cellClicked(int, int)), this, SLOT(slotTableCellClicked(int, int)));
    disconnect(this, SIGNAL(cellDoubleClicked(int, int)), this, SLOT(slotTableCellDoubleClicked(int, int)));
}

void QMTAbstractTableView::ConnectCellWidgetSignals(int cellWidgetType, QWidget* cellWidget)
{
    if (nullptr == cellWidget)
    {
        qWarning() << "[QMTAbstractTableView::ConnectCellWidgetSignals] faild, nullptr == cellWidget";
        return;
    }

    switch (cellWidgetType)
    {
        case DELEAGATE_QMTCheckBox:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));
            connect(cellWidget, SIGNAL(stateChanged(int)), this, SLOT(slotCellWidgetStateChange(int)));
        }
        break;

        case DELEAGATE_CheckBoxLabel:
        {
            connect(cellWidget, SIGNAL(sigStateChanged(int)), this, SLOT(slotCellWidgetStateChange(int)));
        }
        break;

        case DELEAGATE_TYPE_BUTTONS:
        {
            connect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        case DELEAGATE_QMTAbsHorizontalBtns:
        {
            connect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        case DELEAGATE_QTipPushButton:
        {
            connect(cellWidget, SIGNAL(clicked(bool)), this, SLOT(slotCellWidgetButtonClicked(bool)));
        }
        break;

        case DELEAGATE_QMTAbsComboBox:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));
            connect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
            connect(cellWidget, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCellWidgetIndexChange(int)));
        }
        break;

        case DELEAGATE_QMTLineEdit:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            connect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
        }
        break;

        case DELEAGATE_TYPE_ThumbnailWidget:
        {
            connect(cellWidget, SIGNAL(sigCellWidgetClicked(int)), this, SLOT(slotCellWidgetClicked(int)));
        }
        break;

        /****新版UI皮肤新增****/
        case DELEAGATE_QCustMtLineEdit:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            connect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
        }
        break;

        case DELEAGATE_QCustMtComboBox:
        case DELEAGATE_QCustMtSearchComboBox:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            connect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
            connect(cellWidget, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCellWidgetIndexChange(int)));
        }
        break;

        case DELEAGATE_MtUnitPushButtonGroup:
        case DELEAGATE_MtUnitToolButtonGroup:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            connect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        case DELEAGATE_QCustDoubleClickLineEdit:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            connect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
        }
        break;

        case DELEAGATE_QCustLabelWithButton:
        {
            connect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            connect(cellWidget, SIGNAL(SigCustButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        default:
            break;
    }
}

void QMTAbstractTableView::DisConnectCellWidgetSignals(int cellWidgetType, QWidget* cellWidget)
{
    if (nullptr == cellWidget)
    {
        qWarning() << "[QMTAbstractTableView::DisConnectCellWidgetSignals] faild, nullptr == cellWidget";
        return;
    }

    switch (cellWidgetType)
    {
        case DELEAGATE_QMTCheckBox:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));
            disconnect(cellWidget, SIGNAL(stateChanged(int)), this, SLOT(slotCellWidgetStateChange(int)));
        }
        break;

        case DELEAGATE_CheckBoxLabel:
        {
            disconnect(cellWidget, SIGNAL(sigStateChanged(int)), this, SLOT(slotCellWidgetStateChange(int)));
        }
        break;

        case DELEAGATE_TYPE_BUTTONS:
        {
            disconnect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        case DELEAGATE_QMTAbsHorizontalBtns:
        {
            disconnect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        case DELEAGATE_QTipPushButton:
        {
            disconnect(cellWidget, SIGNAL(clicked(bool)), this, SLOT(slotCellWidgetButtonClicked(bool)));
        }
        break;

        case DELEAGATE_QMTAbsComboBox:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));
            disconnect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
            disconnect(cellWidget, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCellWidgetIndexChange(int)));
        }
        break;

        case DELEAGATE_QMTLineEdit:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            disconnect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
        }
        break;

        case DELEAGATE_TYPE_ThumbnailWidget:
        {
            disconnect(cellWidget, SIGNAL(sigCellWidgetClicked(int)), this, SLOT(slotCellWidgetClicked(int)));
        }
        break;

        /****新版UI皮肤新增****/
        case DELEAGATE_QCustMtLineEdit:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            disconnect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
        }
        break;

        case DELEAGATE_QCustMtComboBox:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            disconnect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
            disconnect(cellWidget, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCellWidgetIndexChange(int)));
        }
        break;

        case DELEAGATE_MtUnitPushButtonGroup:
        case DELEAGATE_MtUnitToolButtonGroup:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            disconnect(cellWidget, SIGNAL(sigButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }
        break;

        case DELEAGATE_QCustDoubleClickLineEdit:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            disconnect(cellWidget, SIGNAL(currentTextChanged(const QString&)), this, SLOT(slotCellWidgetTextChange(const QString&)));
        }
        break;

        case DELEAGATE_QCustLabelWithButton:
        {
            disconnect(cellWidget, SIGNAL(sigClicked(int)), this, SLOT(slotCellWidgetClicked(int)));       //必须要有这个
            disconnect(cellWidget, SIGNAL(SigCustButtonClicked(int, bool)), this, SLOT(slotCellWidgetButtonClicked(int, bool)));
        }

        default:
            break;
    }
}

void QMTAbstractTableView::DisConnectAllCellWidgetSignals()
{
    int row = this->rowCount();         //获取界面的行
    int column = this->columnCount();

    for (int i = 0; i < row; ++i)
    {
        for (int j = 0; j < column; ++j)
        {
            QWidget* cellWidget = this->cellWidget(i, j);

            if (nullptr == cellWidget)
            {
                continue;
            }

            QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(cellWidget);

            if (cellWidgetBase)
            {
                int cellDelegateType = cellWidgetBase->GetCellData(QMTAbstractCellWidget::Role_DelegateType).toInt();
                DisConnectCellWidgetSignals(cellDelegateType, cellWidget);
            }
        }
    }
}

void QMTAbstractTableView::setCellWidget(int row, int column, QWidget* widget)
{
    QTableWidget::setCellWidget(row, column, widget);
    widget->installEventFilter(this);
    QList<QWidget*> widgetList = widget->findChildren<QWidget*>();

    for (int i = 0; i < widgetList.size(); ++i)
    {
        widgetList[i]->installEventFilter(this);
    }
}

QString QMTAbstractTableView::GetColumnText(const QString& rowValue, int column)
{
    QString retText;
    int row = GetRowIndex(rowValue);

    if (row < 0 || rowValue.isEmpty())
        return retText;

    QTableWidgetItem* item = this->item(row, column);

    if (item)
    {
        retText = item->text();
    }
    else
    {
        QWidget* widget = this->cellWidget(row, column);
        QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);

        //界面已经创建了，那么从界面中获取
        if (cellWidget)
        {
            retText = cellWidget->GetCurText();
        }
        else
        {
            //界面还未创建，从缓存中获取数值
            QMap<int/*column*/, ICellWidgetParam*>cellParamMap = _rowCellWidgetParamMapMap.value(rowValue);
            ICellWidgetParam* cellParam = cellParamMap.value(column);

            if (cellParam)
            {
                retText = cellParam->_text;
            }
        }
    }

    return retText;
}

QStringList QMTAbstractTableView::GetRowTextList(int column)
{
    QStringList retStrList;
    QStringList rowValueList = GetAllRowUniqueValueList();

    for (int i = 0; i < rowValueList.size(); ++i)
    {
        QString text = GetColumnText(rowValueList[i], column);
        retStrList.append(text);
    }

    return retStrList;
}

bool QMTAbstractTableView::GetRowCellChecked(const QString& rowValue, int column, int index)
{
    bool bChecked = false;
    int row = GetRowIndex(rowValue);

    if (row < 0 || rowValue.isEmpty())
        return false;

    QTableWidgetItem* item = this->item(row, column);
    QWidget* widget = this->cellWidget(row, column);
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);

    if (nullptr == cellWidget)
        return false;

    bChecked = cellWidget->GetCellChecked(index);
    return bChecked;
}

void QMTAbstractTableView::CreateHeadView()
{
    //设置表头内容
    this->setColumnCount(_perRowItemParam._headParam._defaultColumn);           //设置列数
    this->setHorizontalHeaderLabels(_perRowItemParam._headParam._headStrList);

    if (nullptr == _headView)
    {
        if (_perRowItemParam._headParam._headCellParamMap.size() > 0)
        {
            _headView = new QMTAbsTableViewHeaderView(Qt::Horizontal, this);
            CreateHeadView(_perRowItemParam._headParam._headCellParamMap);
            this->setHorizontalHeader(_headView);
        }
    }

    if (false == _perRowItemParam._enableFlex)
    {
        for (int i = 0; i < _perRowItemParam._headParam._defaultColumn; ++i)
        {
            this->horizontalHeader()->setSectionResizeMode(i, QHeaderView::Fixed);
        }
    }

    QMap<int, int>& columnWidthMap = _perRowItemParam._headParam._columnWidthMap;

    for (int i = 0; i < _perRowItemParam._headParam._defaultColumn; ++i)
    {
        if (columnWidthMap.contains(i))
        {
            int width = columnWidthMap.value(i);

            if (width > 0)
            {
                this->setColumnWidth(i, width);
                this->horizontalHeader()->resizeSection(i, width);
            }
        }
    }
}

void QMTAbstractTableView::CreateHeadView(QMap<int, ICellWidgetParam*>& headCellParamMap)
{
    if (0 == headCellParamMap.size())
        return;

    QMap<int, QWidget*> headWidgetMap;
    QMap<int, ICellWidgetParam*>::iterator iter;

    for (iter = headCellParamMap.begin(); iter != headCellParamMap.end(); ++iter)
    {
        int column = iter.key();
        ICellWidgetParam* cellParam = iter.value();
        //更新值
        {
            if (cellParam->_paddingLeft < 0)
            {
                cellParam->_paddingLeft = _perRowItemParam._headParam._paddingLeft;
            }
        }
        QWidget* cellWidget = cellParam->CreateUIModule(_headView);
        int cellWidgetType = cellParam->_cellWidgetType;

        if (cellParam->_styleSheetStr.size() > 0)
        {
            cellWidget->setStyleSheet(cellParam->_styleSheetStr);
        }
        else
        {
            QString styleStr = GetHeadWidgetSheetCallBack(column, cellWidgetType);

            if (styleStr.size() > 0)
            {
                cellWidget->setStyleSheet(styleStr);
            }
        }

        headWidgetMap.insert(column, cellWidget);
        ConnectHeadWidgetSignals(cellWidgetType, cellWidget);
    }

    _headView->SetHeadWidgetMap(headWidgetMap);
}

bool QMTAbstractTableView::CreateRowItemWidget(const QString& uniqueValue)
{
    bool bCreate = _rowItemWidgetFlagMap.value(uniqueValue);

    if (true == bCreate)
        return bCreate;

    if (uniqueValue.isEmpty())
        return false;

    //1.创建一行中所有的单元格
    int row = GetRowIndex(uniqueValue);
    QMap<int, ICellWidgetParam*> cellWidgetParamMap = _rowCellWidgetParamMapMap.value(uniqueValue);
    int rowItemType = GetRowItemType(uniqueValue);
    QString parentValue;
    const int columns = _perRowItemParam._headParam._defaultColumn;

    for (int column = 0; column < columns; ++column)
    {
        int width = _perRowItemParam._headParam._columnWidthMap.value(column);
        ICellWidgetParam* cellWidgetParam = cellWidgetParamMap.value(column);

        if (nullptr == cellWidgetParam)
        {
            qWarning() << "[QMTAbstractTableView::CreateRowItemWidget] nullptr == cellWidgetParam";
            continue;
        }

        //更新值
        {
            cellWidgetParam->_rowValue = uniqueValue;

            if (cellWidgetParam->_paddingLeft < 0)
            {
                cellWidgetParam->_paddingLeft = _perRowItemParam._headParam._paddingLeft;
            }
        }
        int cellWidgetType = cellWidgetParam->_cellWidgetType;
        QWidget* cellWidget = cellWidgetParam->CreateUIModule(this);

        if (nullptr == cellWidget)
        {
            qWarning() << "[QMTAbstractTableView::CreateRowItemWidget] CreateUIModule faild, nullptr == cellWidget";
            qWarning() << QString("====================uniqueValue = %1, column = %2").arg(uniqueValue).arg(column);
            continue;
        }

        this->setCellWidget(row, column, cellWidget);

        if (width > 0 && column < columns - 1)
        {
            //cellWidget->setFixedWidth(width);
        }

        if (_perRowItemParam._rowWidgetHeight > 2)
        {
            //cellWidget->setFixedHeight(_perRowItemParam._rowWidgetHeight - 2);
        }

        parentValue = cellWidgetParam->_parentValue;
        QMTAbstractCellWidget* cellWidgetBase = dynamic_cast<QMTAbstractCellWidget*>(cellWidget);

        if (nullptr == cellWidgetBase)
        {
            qWarning() << "[QMTAbstractTableView::CreateRowItemWidget] dynamic_cast<QMTAbstractCellWidget*>(cellWidget) faild, nullptr == cellWidgetBase";
            qWarning() << QString("====================uniqueValue = %1, column = %2").arg(uniqueValue).arg(column);
            continue;
        }

        cellWidgetBase->SetCellData(QMTAbstractCellWidget::Role_MainValue, uniqueValue);
        cellWidgetBase->SetCellData(QMTAbstractCellWidget::Role_ParentValue, parentValue);
        cellWidgetBase->SetCellData(QMTAbstractCellWidget::Role_RowType, QString::number(rowItemType));
        cellWidgetBase->SetCellData(QMTAbstractCellWidget::Role_Column, QString::number(column));
        cellWidgetBase->SetCellData(QMTAbstractCellWidget::Role_DelegateType, QString::number(cellWidgetType));

        //更新单元格样式
        if (cellWidgetParam->_styleSheetStr.size() > 0)
        {
            cellWidget->setStyleSheet(cellWidgetParam->_styleSheetStr);
        }
        else
        {
            QString styleSheetStr = GetColumnWidgetSheetCallBack(column, cellWidgetType);

            if (styleSheetStr.size() > 0)
            {
                cellWidget->setStyleSheet(styleSheetStr);
            }
        }

        //更新单元格界面
        if (true == cellWidgetParam->_bUpdate)
        {
            cellWidgetBase->UpdateUi(cellWidgetParam->_updateData);
        }

        //绑定单元格信号
        ConnectCellWidgetSignals(cellWidgetType, cellWidget);

        //是否允许编辑
        if (false == _editEnable)
        {
            if (true == IsCanEnableEditType(column, cellWidgetType))
            {
                cellWidgetBase->SetEnableEdit(false);
            }
        }
        else
        {
            bool bEditEnable = GetCellEditEnable(uniqueValue, column);

            if (false == bEditEnable)
                cellWidgetBase->SetEnableEdit(bEditEnable);
        }

        //回调单元格创建完成
        CreateCellWidgtFinishCallBack(uniqueValue, column, cellWidgetType, cellWidget);
    }

    _rowItemWidgetFlagMap.insert(uniqueValue, true);
    //2.更新checkbox状态
    QMap<int/*column*/, int/*state*/> columnStateMap;

    if (true == GetRowItemCheckStateMap(uniqueValue, columnStateMap))
    {
        this->UpdateWidgetCheckBoxMap(uniqueValue, columnStateMap);
    }

    //3.回调创建界面完成
    CreateRowWidgetFinishCallBack(uniqueValue, rowItemType);
    return true;
}

void QMTAbstractTableView::UpdateColumnWidgetCheckBoxState(const QString& rowValue, int column, int state, bool bEmit /*= false*/)
{
    int row = GetRowIndex(rowValue);
    QWidget* cellWidget = this->cellWidget(row, column);

    if (nullptr == cellWidget)
        return;

    QMTAbstractCellWidget* cellBaseWidget = dynamic_cast<QMTAbstractCellWidget*>(cellWidget);
    ICellWidgetParam* cellParam = GetCellWidgetParam(rowValue, column);

    if (nullptr == cellBaseWidget || nullptr == cellParam)
        return;

    if (cellBaseWidget)
    {
        if (false == bEmit)
            DisConnectCellWidgetSignals(cellParam->_cellWidgetType, cellWidget);

        cellBaseWidget->UpdateUi(QVariant::fromValue(state));

        if (false == bEmit)
            ConnectCellWidgetSignals(cellParam->_cellWidgetType, cellWidget);
    }
}

void QMTAbstractTableView::UpdateWidgetCheckBoxMap(const QString& rowValue, QMap<int/*column*/, int/*state*/>& columnWidgetCheckBoxMap, bool bEmit /*= false*/)
{
    if (0 == columnWidgetCheckBoxMap.size())
        return;

    QMap<int/*column*/, int/*state*/>::iterator iter;

    for (iter = columnWidgetCheckBoxMap.begin(); iter != columnWidgetCheckBoxMap.end(); ++iter)
    {
        int column = iter.key();
        int state = iter.value();
        UpdateColumnWidgetCheckBoxState(rowValue, column, state, bEmit);
    }
}


void QMTAbstractTableView::SetModelUiColumnMap(const QMap<int/*model column*/, int/*ui column*/>& modelUiColumnMap)
{
    if (_viewDelegate)
    {
        _viewDelegate->setModelUiColumnMap(modelUiColumnMap);
    }
}

void QMTAbstractTableView::DoHorizontalScrollBarValueChangeProc()
{
    QScrollBar* horScrollBar = this->horizontalScrollBar();

    if (nullptr == horScrollBar)
    {
        return;
    }

    if (_headView)
    {
        int value = horScrollBar->value();
        _headView->SetHorScrollValue(value);
    }
}

bool QMTAbstractTableView::IsNeedCreateWidget()
{
    bool bCreateWidget = true;

    if (false == _enableDynamicCreate)
    {
        return bCreateWidget;
    }

    if (this->rowCount() > _InitPageRowCnt)
    {
        bCreateWidget = false;
    }
    else
    {
        bCreateWidget = true;
        return bCreateWidget;
    }

    if (nullptr != this->verticalScrollBar())
    {
        int maxValue = this->verticalScrollBar()->maximum();
        int curValue = this->verticalScrollBar()->value();

        if (maxValue != 0)
        {
            if (curValue < maxValue)
            {
                bCreateWidget = false;
            }
        }
        else
        {
            bCreateWidget = false;
        }
    }
    else
    {
        bCreateWidget = false;
    }

    return bCreateWidget;
}

bool QMTAbstractTableView::IsRowItemWidgetCreate(const QString& rowValue)
{
    bool bCreate = _rowItemWidgetFlagMap.value(rowValue);
    return bCreate;
}

QWidget* QMTAbstractTableView::GetCellWidget(const QString& rowValue, int column)
{
    int row = GetRowIndex(rowValue);
    QWidget* widget = this->cellWidget(row, column);
    return widget;
}

int QMTAbstractTableView::GetHoverRow()
{
    /*
    通过鼠标位置获取行通过以下方式：
    方式一：
    QModelIndex index = tableWidget->indexAt(pos);
    if (index.isValid()) {
        int row = index.row();
        int col = index.column();
    }
    方式二：
    int row = tableWidget->rowAt(pos.y());
    int col = tableWidget->columnAt(pos.x());

    */
    return m_mouseHoverRow;
}

bool QMTAbstractTableView::IsAllRowValueEquel(const QStringList& rowValueList)
{
    QStringList rowUiValueList = GetAllRowUniqueValueList();

    if (rowValueList.size() != rowUiValueList.size())
    {
        return false;
    }

    for (int i = 0; i < rowValueList.size(); ++i)
    {
        if (rowValueList[i] != rowUiValueList[i])
        {
            return false;
        }
    }

    return true;
}

bool QMTAbstractTableView::IsAllRowValueContains(const QStringList& rowValueList, bool bNeedCntEquel)
{
    QStringList rowUiValueList = GetAllRowUniqueValueList();

    if (bNeedCntEquel)
    {
        if (rowValueList.size() != rowUiValueList.size())
        {
            return false;
        }
    }

    bool bContains = true;  //假设全部都包含，只要有一个未包含，那么就是false

    for (int i = 0; i < rowValueList.size(); ++i)
    {
        if (rowUiValueList.indexOf(rowValueList[i]) < 0)
        {
            bContains = false;
            break;
        }
    }

    return bContains;
}

void QMTAbstractTableView::keyPressEvent(QKeyEvent* event)
{
    QTableWidget::keyPressEvent(event);

    if (event->key() == Qt::Key_Down || event->key() == Qt::Key_Up)
    {
        int row = this->currentRow();
        QString rowValue = GetRowUniqueValue(row);
        SetCurrentRow(rowValue, true);
        return;
    }
}


void QMTAbstractTableView::keyReleaseEvent(QKeyEvent* event)
{
    QTableWidget::keyReleaseEvent(event);

    if (event->key() == Qt::Key_Tab)
    {
        int row = this->currentRow();
        QString rowValue = GetRowUniqueValue(row);
        SetCurrentRow(rowValue, true);
        return;
    }
    else if (event->modifiers() & (Qt::ShiftModifier | Qt::Key_Tab))
    {
        int row = this->currentRow();
        QString rowValue = GetRowUniqueValue(row);
        SetCurrentRow(rowValue, true);
        return;
    }
}

void QMTAbstractTableView::mousePressEvent(QMouseEvent* event)
{
    if (Qt::RightButton == event->button())
    {
    }
    else
    {
        QTableWidget::mousePressEvent(event);
        m_mousePressRow = this->currentRow();
        //QPoint point = event->pos(); //得到窗口坐标
        //QModelIndex modelIndex = this->indexAt(point);
        //if (false == modelIndex.isValid())
        //{
        //    m_mousePressRow = -1;
        //    return;
        //}
        //m_mousePressRow = modelIndex.row();
    }
}

void QMTAbstractTableView::mouseReleaseEvent(QMouseEvent* event)
{
    QTableWidget::mouseReleaseEvent(event);

    if (Qt::LeftButton == event->button())
    {
        QPoint point = event->pos(); //得到窗口坐标
        QModelIndex modelIndex = this->indexAt(point);

        if (false == modelIndex.isValid())
        {
            return;
        }

        int releaseRow = modelIndex.row();

        if (releaseRow != m_mousePressRow/* && releaseRow >= 0 && m_mousePressRow >= 0*/)
        {
            QPoint point = event->pos(); //得到窗口坐标
            QModelIndex modelIndex = this->indexAt(point);
            QString rowValue = GetRowUniqueValue(modelIndex.row());
            QString curRowValue = GetCurUniqueValue();
            SetCurrentRow(rowValue, true);
        }

        //QPoint point = event->pos(); //得到窗口坐标
        //QModelIndex modelIndex = this->indexAt(point);
        //QString rowValue = GetRowUniqueValue(modelIndex.row());
        //QString curRowValue = GetCurUniqueValue();
        //// if (rowValue != curRowValue)
        //{
        //    SetCurrentRow(rowValue);
        //}
    }
}

void QMTAbstractTableView::mouseMoveEvent(QMouseEvent* event)
{
    if (event->buttons() & Qt::LeftButton)
    {
        //qWarning() << "mouseMoveEvent :" << event->button();
        int row = this->currentRow();
        SetViewDelegateSelectRow(row);
        SetViewDelegateHoverRow(row);
        return;
    }
    else
    {
        QPoint point = event->pos(); //得到窗口坐标
        QModelIndex modelIndex = this->indexAt(point);

        if (modelIndex.isValid())
        {
            int row = modelIndex.row();
            SetViewDelegateHoverRow(row);
        }
        else
        {
            int row = this->currentRow();
            SetViewDelegateHoverRow(row);
        }
    }

    QTableWidget::mouseMoveEvent(event);
}

bool QMTAbstractTableView::eventFilter(QObject* obj, QEvent* event)
{
    if (event->type() == QEvent::KeyPress)
    {
        //防止tab按下自己切换单元格导致实际业务中选中了第一行，按tab次数超过列数时，会自动跑到下一行并且没有信号发出
        QKeyEvent* keyEvent = static_cast<QKeyEvent*>(event);

        if (keyEvent->key() == Qt::Key_Tab)
        {
            qDebug() << "Tab in QMTAbstractTableView is invalid";
            //屏蔽Tab，不响应
            return true;
        }
    }

    bool bOK = QTableWidget::eventFilter(obj, event);

    if (QEvent::Enter == event->type() ||
        QEvent::Leave == event->type() ||
        QEvent::Move == event->type())
    {
        QWidget* cellWidget = qobject_cast<QWidget*>(obj);

        if (nullptr == cellWidget)
        {
            return bOK;
        }

        int hoverRow = -1;

        if (IsCustCellWidget(cellWidget, hoverRow))
        {
#ifdef TableView_Log
            qDebug() << "IsCustCellWidget, row = " << row;
#endif
        }

        if (_viewDelegate)
        {
            if (QEvent::Leave == event->type())
            {
                int row = this->currentRow();
                SetViewDelegateHoverRow(row);
            }
            else
            {
                SetViewDelegateHoverRow(hoverRow);
            }
        }
    }

    return bOK;
}

bool QMTAbstractTableView::viewportEvent(QEvent* event)
{
    //qWarning() << "viewportEvent : " << event->type();
    switch (event->type())
    {
        case QEvent::MouseMove:
        {
            QMouseEvent* me = static_cast<QMouseEvent*>(event);
            QModelIndex idx = indexAt(me->pos());

            if (_viewDelegate)
            {
                _viewDelegate->setDrawHoverRowEnabled(_perRowItemParam._isEnableHoverChangeBorderColor);
            }

            if (!idx.isValid())
            {
                SetViewDelegateHoverRow(-1);
            }
            else
            {
                SetViewDelegateHoverRow(idx.row());
            }
        }
        break;

        case QEvent::Enter:
        case QEvent::WindowActivate:
        {
            QEnterEvent* me = static_cast<QEnterEvent*>(event);
            QModelIndex idx = indexAt(me->pos());

            if (_viewDelegate)
            {
                _viewDelegate->setDrawHoverRowEnabled(_perRowItemParam._isEnableHoverChangeBorderColor);
            }

            if (!idx.isValid())
            {
                SetViewDelegateHoverRow(-1);
            }
            else
            {
                SetViewDelegateHoverRow(idx.row());
            }
        }
        break;

        case QEvent::HoverMove:
        case QEvent::HoverEnter:
        {
            QHoverEvent* me = static_cast<QHoverEvent*>(event);
            QModelIndex idx = indexAt(me->pos());

            if (_viewDelegate)
            {
                _viewDelegate->setDrawHoverRowEnabled(_perRowItemParam._isEnableHoverChangeBorderColor);
            }

            if (!idx.isValid())
            {
                SetViewDelegateHoverRow(-1);
            }
            else
            {
                SetViewDelegateHoverRow(idx.row());
            }
        }
        break;

        case QEvent::FocusOut:
        case QEvent::HoverLeave:
        case QEvent::Hide:
        case QEvent::HideToParent:
        case QEvent::Leave:
        {
            SetViewDelegateHoverRow(-1);

            if (QEvent::Leave == event->type())
            {
                if (_viewDelegate)
                {
                    _viewDelegate->setDrawHoverRowEnabled(false);
                }
            }
        }
        break;

        case QEvent::WindowBlocked:
            //case QEvent::WindowDeactivate:
        {
            int row = this->currentRow();
            SetViewDelegateHoverRow(row);

            if (_viewDelegate)
            {
                _viewDelegate->setDrawHoverRowEnabled(false);
            }
        }
        break;

        case QEvent::WindowUnblocked:
            //case QEvent::WindowActivate:
        {
            if (_viewDelegate)
            {
                _viewDelegate->setDrawHoverRowEnabled(_perRowItemParam._isEnableHoverChangeBorderColor);
            }
        }
        break;

        default:
            break;
    }

    return QTableWidget::viewportEvent(event);
}

void QMTAbstractTableView::leaveEvent(QEvent* event)
{
    //qWarning() << "QMTAbstractTableView::leaveEvent : " << event->type();
    QTableWidget::leaveEvent(event);

    if (nullptr != _viewDelegate)
    {
        SetViewDelegateHoverRow(-1);
    }
}


bool QMTAbstractTableView::IsCustCellWidget(QWidget* widget, int& row)
{
    QWidget* tempWidget = widget->parentWidget();

    while (tempWidget && !dynamic_cast<QMTAbstractCellWidget*>(tempWidget))
    {
        tempWidget = tempWidget->parentWidget();
    }

    if (nullptr == tempWidget)
    {
        return false;
    }

    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(tempWidget);

    if (nullptr == cellWidget)
    {
        return false;
    }

    QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
    row = GetRowIndex(uniqueValue);
    return true;
}

void QMTAbstractTableView::SetViewDelegateHoverRow(int row)
{
    if (nullptr == _viewDelegate)
    {
        return;
    }

    QString curRowValue = this->GetCurrentRow();
    int selectRow = this->currentRow();

    if (curRowValue.size() > 0 && selectRow == row)
    {
        _viewDelegate->setCurHoverRow(-1);
    }
    else
    {
        _viewDelegate->setCurHoverRow(row);
    }

    m_mouseHoverRow = row;
    this->update();
}

void QMTAbstractTableView::SetViewDelegateSelectRow(int row)
{
    if (nullptr == _viewDelegate)
    {
        return;
    }

    _viewDelegate->setCurSelectRow(row);
}

void QMTAbstractTableView::CalculateVisiablePageRow()
{
    int row_Hei = this->rowHeight(1);                                                  //每行的高度
    int scrollHeight = this->maximumViewportSize().height();               //获取滚动条的滚动范围
    float rowN = (float)scrollHeight / (float)row_Hei + 1;
    _perPageRowCnt = (int)rowN;

    if (rowN - _perPageRowCnt > 0.000001)
    {
        _perPageRowCnt++;
    }
}

QString QMTAbstractTableView::GetHeadHorizontalStyleStr()
{
    QString sheetStr;
    //表头样式
    {
        QString tmpColorStr;
        QString textColorStr;
        //
        tmpColorStr = CMtCoreWidgetUtil::getFontFamily(CMtLanguageUtil::type);
        textColorStr += QString("font-family: \"%1\";").arg(tmpColorStr);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._headParam._textColor, true);
        textColorStr += QString("color:%1;").arg(tmpColorStr);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._headParam._backGroundColor, true);
        textColorStr += QString("background:%1;").arg(tmpColorStr);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._gridColor, true);

        if (_perRowItemParam._showGrid)
        {
            textColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        }
        else if (_perRowItemParam._headParam._isHideHeadCloumnLine)
        {
            textColorStr += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        }
        else
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._gridColor, true);
            textColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        }

        textColorStr += QString("padding-left:%1px").arg(QString::number(_perRowItemParam._headParam._paddingLeft));
        sheetStr = "QHeaderView{background-color: transparent;}";
        sheetStr += QString("QHeaderView::section:horizontal{%1}").arg(textColorStr);

        //第一列的样式
        if (_perRowItemParam._showGrid)
        {
            QString leftLineColorStr = QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:1px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
            sheetStr += QString("QHeaderView::section:horizontal:first{%1}").arg(leftLineColorStr);
        }

        if (_perRowItemParam._headParam._isHideHeadCloumnLine == false && _perRowItemParam._headParam._isShowFirstCloumnLeftLine)
        {
            //第一列左侧线
            sheetStr += QString("QHeaderView::section:horizontal:first{border-style:solid;border-left-width:1px;border-color:%1;}").arg(tmpColorStr);
        }

#ifdef TableView_Log
        qDebug() << "horizontalHeader style:" << sheetStr;
#endif
    }
    //表头置灰样式
    {
        //禁用状态的颜色
        QString disableColorStr;
        QString disableTmpColorStr;
        //
        QColor textRgba = _perRowItemParam._headParam._textColor;
        textRgba.setAlphaF(0.4);
        QColor bgRgba = _perRowItemParam._headParam._backGroundColor;
        bgRgba.setAlphaF(0.15);
        QColor gridRgba = _perRowItemParam._gridColor;
        gridRgba.setAlphaF(0.5);
        disableTmpColorStr = CMtCoreWidgetUtil::formatColorStr(textRgba, true);
        disableColorStr += QString("color:%1;").arg(disableTmpColorStr);
        disableTmpColorStr = CMtCoreWidgetUtil::formatColorStr(bgRgba, true);
        disableColorStr += QString("background:%1;").arg(disableTmpColorStr);
        disableTmpColorStr = CMtCoreWidgetUtil::formatColorStr(gridRgba, true);

        if (_perRowItemParam._showGrid)
        {
            disableColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
        }
        else if (_perRowItemParam._headParam._isHideHeadCloumnLine)
        {
            disableColorStr += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
        }
        else
        {
            disableColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
        }

        //
        sheetStr += QString("QHeaderView::section:horizontal:disabled{%1}").arg(disableColorStr);

        //第一列的样式
        if (_perRowItemParam._showGrid)
        {
            //
            QString leftLineDisableColorStr = QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:1px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
            sheetStr += QString("QHeaderView::section:horizontal:first:disabled{%1}").arg(leftLineDisableColorStr);
        }

#ifdef TableView_Log
        qDebug() << "horizontalHeader style:" << sheetStr;
#endif
    }
    return sheetStr;
}

QString QMTAbstractTableView::GetHeadVerticalStyleStr()
{
    QString sheetStr;
    //表头样式
    {
        QString tmpColorStr;
        QString textColorStr;
        //
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._headParam._textColor, true);
        textColorStr += QString("color:%1;").arg(tmpColorStr);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._headParam._backGroundColor, true);
        textColorStr += QString("background:%1;").arg(tmpColorStr);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._gridColor, true);

        if (_perRowItemParam._showGrid)
        {
            textColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        }
        else if (_perRowItemParam._headParam._isHideHeadCloumnLine)
        {
            textColorStr += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        }
        else
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._gridColor, true);
            textColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(tmpColorStr);
        }

        textColorStr += QString("padding-left:%1px").arg(QString::number(_perRowItemParam._headParam._paddingLeft));
        sheetStr = "QHeaderView{background-color: transparent;}";
        sheetStr += QString("QHeaderView::section:horizontal{%1}").arg(textColorStr);
        sheetStr += QString("QHeaderView::section:vertical{%1}").arg(textColorStr);
#ifdef TableView_Log
        qDebug() << "horizontalHeader style:" << sheetStr;
#endif
    }
    //表头置灰样式
    {
        //禁用状态的颜色
        QString disableColorStr;
        QString disableTmpColorStr;
        //
        QColor textRgba = _perRowItemParam._headParam._textColor;
        textRgba.setAlphaF(0.4);
        QColor bgRgba = _perRowItemParam._headParam._backGroundColor;
        bgRgba.setAlphaF(0.15);
        QColor gridRgba = _perRowItemParam._gridColor;
        gridRgba.setAlphaF(0.5);
        disableTmpColorStr = CMtCoreWidgetUtil::formatColorStr(textRgba, true);
        disableColorStr += QString("color:%1;").arg(disableTmpColorStr);
        disableTmpColorStr = CMtCoreWidgetUtil::formatColorStr(bgRgba, true);
        disableColorStr += QString("background:%1;").arg(disableTmpColorStr);
        disableTmpColorStr = CMtCoreWidgetUtil::formatColorStr(gridRgba, true);

        if (_perRowItemParam._showGrid)
        {
            disableColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
        }
        else if (_perRowItemParam._headParam._isHideHeadCloumnLine)
        {
            disableColorStr += QString("border-style:solid;border-top-width:0px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
        }
        else
        {
            disableColorStr += QString("border-style:solid;border-top-width:1px;border-bottom-width:1px;border-left-width:0px;border-right-width:1px; border-color:%1;").arg(disableTmpColorStr);
        }

        //
        sheetStr += QString("QHeaderView::section:vertical:disabled{%1}").arg(disableColorStr);
#ifdef TableView_Log
        qDebug() << "horizontalHeader style:" << sheetStr;
#endif
    }
    return sheetStr;
}

QString QMTAbstractTableView::GetTableWidgetStyleStr(bool& bContentGridShow)
{
    QString sheetStr;
    bContentGridShow = true;
    //QTableWidget 样式
    {
        QString tmpColorStr;
        QString tmpBorderColorStr;
        QString textColorStr;
        int rgb[3];
        double alpha = 255;
        QString alphaStr;
        //字体颜色
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._textColor, true);
        textColorStr += QString("color:%1;").arg(tmpColorStr);
        //未选中的背景色
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._canvasBackColor, true);
        textColorStr += QString("background-color:%1;").arg(tmpColorStr);

        //选中的背景色
        if (_perRowItemParam._isEnableSelectChangeStyle)
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._selectBackColor, true);
        }
        else
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._unselectBackColor, true);
        }

        textColorStr += QString("selection-background-color:%1;").arg(tmpColorStr);

        //栅格线颜色.如果隐藏了竖线。那么不设置栅格
        if (true == _perRowItemParam._showGrid)
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._gridColor, true);
            textColorStr += QString("gridline-color:%1;").arg(tmpColorStr);
        }
        else
        {
        }

        textColorStr += QString("border:0px");
        sheetStr = QString("QTableWidget{%1}").arg(textColorStr);
        //item样式
        QString itemSheetStyle;
        //字体颜色重设。
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._textColor, true);
        itemSheetStyle += QString("color:%1;").arg(tmpColorStr);
        //未选中的背景色
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._unselectBackColor, true);
            itemSheetStyle += QString("background-color:%1;").arg(tmpColorStr);

            if (true == _perRowItemParam._isHideCloumnLine)
            {
                tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._gridColor, true);
                itemSheetStyle += QString("border-top:0px solid %1;").arg(tmpColorStr);
                itemSheetStyle += QString("border-bottom:1px solid %1;").arg(tmpColorStr);
                itemSheetStyle += QString("border-left:none;");
                itemSheetStyle += QString("border-right:none;");
                bContentGridShow = false;
            }
            else
            {
            }

            sheetStr += QString("QTableWidget::item{%1}").arg(itemSheetStyle);
        }
        //悬浮样式(无法生效)
        {
            /* tmpBorderColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._borderColor, true);
             QString borderStr = QString("border-top:1px solid %1;").arg(tmpBorderColorStr);
             borderStr += QString("border-bottom:1px solid %1;").arg(tmpBorderColorStr);
             borderStr += QString("order-left:1px solid %1;").arg(tmpBorderColorStr);
             borderStr += QString("order-right:1px solid %1;").arg(tmpBorderColorStr);
             sheetStr += QString("QTableView::item:hover{%1}").arg(borderStr);*/
        }
        //选中的背景色重设
        {
            if (_perRowItemParam._isEnableSelectChangeStyle)
            {
                tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._selectBackColor, true);
            }
            else
            {
                tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._unselectBackColor, true);
            }

            QString selectStr = QString("background-color:%1;").arg(tmpColorStr);

            if (_perRowItemParam._isEnableSelectChangeStyle)
            {
                tmpBorderColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._selectBorderColor, true);
            }
            else
            {
                tmpBorderColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._unselectBackColor, true);
            }

            selectStr += QString("border-top:1px solid %1;").arg(tmpBorderColorStr);
            selectStr += QString("border-bottom:1px solid %1;").arg(tmpBorderColorStr);
            selectStr += QString("border-left:0px solid %1;").arg(tmpBorderColorStr);
            selectStr += QString("border-right:0px solid %1;").arg(tmpBorderColorStr);
            sheetStr += QString("QTableView::item:selected{%1}").arg(selectStr);
        }
        //
        ///section，暂时用不到，测试用
        QString sectionTextColor;
        QString sectionSheet;
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(_perRowItemParam._unselectBackColor, true);
        sectionTextColor += QString("background-color:%1;").arg(tmpColorStr);
        //if (_perRowItemParam._panding_left > 0)
        //{
        //    //sectionTextColor += QString("padding:%1px").arg(_perRowItemParam._panding_left);
        //}
        sectionSheet = QString("QTableWidget::section{%1}").arg(sectionTextColor);
#ifdef TableView_Log
        qDebug() << "QTableWidget style:" << sheetStr;
#endif
    }
    //QTableWidget 置灰样式
    {
        QString tmpColorStr;
        QString tmpBorderColorStr;
        QString textColorStr;
        int rgb[3];
        //字体颜色
        QColor textRgba = _perRowItemParam._textColor;
        textRgba.setAlphaF(0.4);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(textRgba, true);
        textColorStr += QString("color:%1;").arg(tmpColorStr);
        //未选中的背景色
        QColor canvasBackRgba = _perRowItemParam._canvasBackColor;
        canvasBackRgba.setAlphaF(0.15);
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(canvasBackRgba, true);
        textColorStr += QString("background-color:%1;").arg(tmpColorStr);
        //
        //QColor gridRgba = _perRowItemParam._gridColor;
        //gridRgba.setAlphaF(0.5);
        QColor gridRgba = QColor(14, 14, 14);//由于gridline-color不支持透明度，这个颜色先写死

        //栅格线颜色.如果隐藏了竖线。那么不设置栅格
        if (true == _perRowItemParam._showGrid)
        {
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(gridRgba, true);
            textColorStr += QString("gridline-color:%1;").arg(tmpColorStr);
        }
        else
        {
        }

        textColorStr += QString("border:0px");
        sheetStr += QString("QTableWidget:disabled{%1}").arg(textColorStr);
        //item样式
        QString itemSheetStyle;
        //字体颜色重设。
        tmpColorStr = CMtCoreWidgetUtil::formatColorStr(textRgba, true);
        itemSheetStyle += QString("color:%1;").arg(tmpColorStr);
        //未选中的背景色
        {
            QColor unselectBackRgba = _perRowItemParam._unselectBackColor;
            unselectBackRgba.setAlphaF(0.25);
            tmpColorStr = CMtCoreWidgetUtil::formatColorStr(unselectBackRgba, true);
            itemSheetStyle += QString("background-color:%1;").arg(tmpColorStr);

            if (true == _perRowItemParam._isHideCloumnLine)
            {
                tmpColorStr = CMtCoreWidgetUtil::formatColorStr(gridRgba, true);
                itemSheetStyle += QString("border-top:0px solid %1;").arg(tmpColorStr);
                itemSheetStyle += QString("border-bottom:1px solid %1;").arg(tmpColorStr);
                itemSheetStyle += QString("border-left:none;");
                itemSheetStyle += QString("border-right:none;");
                bContentGridShow = false;
            }
            else
            {
            }

            sheetStr += QString("QTableWidget::item:disabled{%1}").arg(itemSheetStyle);
        }
    }
    return sheetStr;
}

void QMTAbstractTableView::slotTableCellClicked(int row, int column)
{
    QWidget* widget = this->cellWidget(row, column);
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);
    //qWarning() << " QMTAbstractTableView::slotTableCellClicked:" << row;

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        emit sigCellItemClicked(cellItemIndex);
        emit sigRowItemClicked(uniqueValue);
        emit sigRowItemClicked(cellItemIndex);
        QWidget* widget = dynamic_cast<QWidget*>(cellWidget);

        if (widget)
        {
            bool bEnable = widget->isEnabled();

            if (false == bEnable)
            {
                this->selectRow(row);
            }
        }
    }

    SetViewDelegateSelectRow(row);
    SetViewDelegateHoverRow(-1);
}

void QMTAbstractTableView::slotTableCellDoubleClicked(int row, int column)
{
    QWidget* widget = this->cellWidget(row, column);
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(widget);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        emit sigCellItemDpubleClicked(cellItemIndex);
        emit sigRowItemDoubleClicked(uniqueValue);
        emit sigRowItemDoubleClicked(cellItemIndex);
    }
}

void QMTAbstractTableView::slotCellWidgetClicked(int)
{
    QObject* sender = this->sender();
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(sender);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;

        if (_curRowItemValue != uniqueValue)
            SetCurrentRow(uniqueValue, true);

        //emit sigCellItemClicked(cellItemIndex);
    }
}

void QMTAbstractTableView::slotCellWidgetTextChange(const QString& newText)
{
    QObject* sender = this->sender();
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(sender);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        emit sigCellWidgetTextChange(cellItemIndex, newText);
    }
}


void QMTAbstractTableView::slotCellWidgetIndexChange(int index)
{
    QObject* sender = this->sender();
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(sender);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        emit sigCellWidgetIndexChange(cellItemIndex, index);
    }
}

void QMTAbstractTableView::slotCellWidgetButtonClicked(int btnindex, bool bchecked)
{
    QObject* sender = this->sender();
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(sender);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        emit sigCellWidgetButtonClicked(cellItemIndex, btnindex, bchecked);
    }
}

void QMTAbstractTableView::slotCellWidgetButtonClicked(bool bChecked)
{
    QObject* sender = this->sender();
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(sender);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        emit sigCellWidgetButtonClicked(cellItemIndex, 0, bChecked);
    }
}

void QMTAbstractTableView::slotCellWidgetStateChange(int state)
{
    QObject* sender = this->sender();
    QMTAbstractCellWidget* cellWidget = dynamic_cast<QMTAbstractCellWidget*>(sender);

    if (cellWidget)
    {
        QString uniqueValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_MainValue);
        QString parentValue = cellWidget->GetCellData(QMTAbstractCellWidget::Role_ParentValue);
        int column = cellWidget->GetCellData(QMTAbstractCellWidget::Role_Column).toInt();
        int rowType = cellWidget->GetCellData(QMTAbstractCellWidget::Role_RowType).toInt();
        TableWidgetItemIndex cellItemIndex;
        cellItemIndex._uniqueValue = uniqueValue;
        cellItemIndex._parentValue = parentValue;
        cellItemIndex._type = rowType;
        cellItemIndex._column = column;
        SetCheckBoxStateInModel(uniqueValue, column, state);

        if (Qt::Checked == state)
        {
            //判断是否所有的checkbox选中了
            if (true == IsAllRowChecked(column))
            {
                SetHeadCheckBoxState(column, state);
            }
        }
        else
        {
            SetHeadCheckBoxState(column, Qt::Unchecked);
        }

        emit sigCellWidgetStateChange(cellItemIndex, state);
    }
}

void QMTAbstractTableView::slotScrollBarValueChanged(int value)
{
    int maxValue = this->verticalScrollBar()->maximum();
    int minValue = this->verticalScrollBar()->minimum();
    //qDebug() << "scroll value:" << value << ", " << scrollHeight;
    int modelDataSize = _rowCellWidgetParamMapMap.size();

    if (false == _enableDynamicCreate || value > modelDataSize)
        return;

    if (value < maxValue)
    {
        //还没到滑动条底部，不动态创建
        return;
    }

    CalculateVisiablePageRow();
    int showFirsRow = value;                     //获取tableWidget显示的第一行的行号
    int rowNchanged = 0;

    if ((modelDataSize - showFirsRow) < _perPageRowCnt)
    {
        rowNchanged = _rowCellWidgetParamMapMap.size() - showFirsRow;
    }

    DisConnectScrollSignals();
    int curValue = this->verticalScrollBar()->value();
    this->verticalScrollBar()->setValue(curValue - 1);

    for (int row = 0; row < _perPageRowCnt; row++)
    {
        int showRow = row + showFirsRow;//显示的行号

        if (row == rowNchanged && (showRow == this->rowCount()))   //最后一板面时不List范围溢出
            break;

        if (showRow > modelDataSize)
            break;

        QString rowValue = GetRowUniqueValue(showRow);

        if (rowValue.isEmpty())
        {
            qWarning() << "QMTAbstractTableView::slotScrollBarValueChanged, rowValue.isEmpty()";
            continue;
        }

        bool isCreateUI = _rowItemWidgetFlagMap.value(rowValue);

        if (false == isCreateUI)
        {
            int uiCount = this->rowCount();
            //这边防止处方滚动条滚动
            this->insertRow(uiCount);     //插入新行
            CreateRowItemWidget(rowValue);
        }
    }

    //防止页面错乱
    {
        QSize size = this->size();
        this->resize(size.width() - 1, size.height() - 1);
        this->resize(size.width(), size.height());
    }
    this->verticalScrollBar()->setValue(curValue);
    ConnectScrollSignals();
}

void QMTAbstractTableView::slotHorizontalScrollBarValueChanged(int value)
{
    if (_headView)
    {
        _headView->SetHorScrollValue(value);
    }
}

void QMTAbstractTableView::slotHeadCheckBoxStateChange(int state)
{
    if (nullptr == _headView)
    {
        return;
    }

    QObject* sender = this->sender();
    QWidget* widget = qobject_cast<QWidget*>(sender);
    int column = _headView->GetHeadWidgetIndex(widget);

    if (column < 0)
        return;

    TableFullCheckBoxStateChangeProc(column, state);
    emit sigHeadCheckBoxStateChange(column, state);
}

void QMTAbstractTableView::slotHeadOrderStateChange(const QString& orderKey, int state)
{
    emit sigHeadRequestOrder(orderKey, state);
}

void QMTAbstractTableView::slotHeadButtonClicked(int btnIndex, bool bChecked)
{
    QObject* sender = this->sender();
    QWidget* widget = qobject_cast<QWidget*>(sender);
    int column = _headView->GetHeadWidgetIndex(widget);

    if (column < 0)
        return;

    UpdateAllColumnBtnChecked(column, bChecked);
    emit sigHeadButtonClicked(column, btnIndex, bChecked);
}

void QMTAbstractTableView::slotHeadWidgetTextChange(const QString& newText)
{
    QObject* sender = this->sender();
    QWidget* widget = qobject_cast<QWidget*>(sender);
    int column = _headView->GetHeadWidgetIndex(widget);
    emit sigHeadTextChange(column, newText);
}

//void QMTAbstractTableView::slotTableWigetEnter(const QModelIndex& index)
//{
//    if (_viewDelegate)
//    {
//        //_viewDelegate->SetCurHoverIndex(index);
//    }
//}
//
//void QMTAbstractTableView::slotTableWigetCellEntered(int row, int column)
//{
//    SetViewDelegateHoverRow(row);
//}

