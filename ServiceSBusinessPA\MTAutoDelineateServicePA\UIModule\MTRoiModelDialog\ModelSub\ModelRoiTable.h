﻿// *********************************************************************************
// <remarks>
// FileName    : RoiLibraryTable
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : Roi库设置列表(适用于: RoiLibraryWidget 空勾画设置)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class ModelRoiTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum
    {
        ColType_Name = 0,
        ColType_Label,
        ColType_ChName,
        ColType_Color,
        ColType_Type,
        ColType_Group,
        ColType_Desc,
        ColType_Operation
    };
    /// <summary>
    /// 构造函数
    /// </summary>
    ModelRoiTable(QWidget* parent = nullptr);
    ~ModelRoiTable();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="allRoiTypeList">[IN]全部Roi类型集合</param>
    /// <param name="allLabelList">[IN]全部标签</param>
    /// <param name="allGroupList">[IN]全部分组信息</param>
    /// <param name="stOrganList">[IN]Organ信息集合</param>
    /// <param name="modelInfoMap">[IN]模型信息集合</param>
    /// <param name="modelCollectionInfoList">[IN]模板信息集合</param>
    void init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
              const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
              const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 过滤展示行
    /// </summary>
    void refreshTable(const QString& modelID);

    /// <summary>
    /// 列表是否初始化完成.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>bool.</returns>
    bool isTableInitialized();

    /// <summary>
    /// 将标签库中同名的roi的信息同步到当前模型ROI信息中
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="stRoiLabelInfoVec">标签库中获取到的信息</param>
    /// <param name="bLabel">是否同步标签名</param>
    /// <param name="bRoiType">是否同步roi类型</param>
    /// <param name="bColor">是否同步roi颜色</param>
    /// <param name="bChName">是否同步roi中文名</param>
    /// <param name="groupList">更新的分组</param>
    /// <param name="bDesc">是否同步roi描述</param>
    /// <param name="desc">更新的roi描述</param>
    void syncLabelLibraryInfo(const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec,
                              bool bLabel, bool bRoiType, bool bColor, bool bChName, const QStringList& groupList,
                              bool bDesc, const QString& desc);

    /// <summary>
    /// 重置默认模型的器官信息为默认设置
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="stOrganDefaultList">器官默认设置信息</param>
    void resetDefaultInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 保存已编辑信息
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    void saveEdit();

    /// <summary>
    /// 还原已编辑信息
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    void cancelEdit();

    /// <summary>
    /// 获取所有Organ信息，编辑后的内存数据
    /// </summary>
    /// <returns>所有Organ信息</returns>
    QList<n_mtautodelineationdialog::ST_Organ> getAllOrganInfo();

    /// <summary>
    /// 获取所有Organ信息，编辑后的内存数据
    /// </summary>
    /// <returns>所有Organ信息</returns>
    const QMap<int, n_mtautodelineationdialog::ST_Organ>& getAllOrganInfoMap();

    /// <summary>
    /// 合并新增的器官信息到缓存
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="stOrganInfoVec">所有的器官信息.</param>
    void mergeNewOrganInfo(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec);

    /// <summary>
    /// 获取所有分组信息
    /// </summary>
    /// <returns>所有分组信息</returns>
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getAllOrganGroupInfo();

    /// <summary>
    /// 设置分组列按钮图片
    /// 图片路径
    /// </summary>
    void setGroupColumnBtnImage(const QString& imagePath);

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="stOrganInfo">[IN]Organ信息</param>
    void addRow(const n_mtautodelineationdialog::ST_Organ& stOrganInfo);

    /// <summary>
    /// 删除一行
    /// </summary>
    /// <param name="rowValue">[IN]行ID，这里是organID</param>
    void delRow(const QString& rowValue);

signals:
    /// <summary>
    /// 列表初始化完成
    /// </summary>
    void sigTableInitialized();

    /// <summary>
    /// 列表信息发生了改变
    /// </summary>
    void sigTableInfoChanged(const QString& defOrganName, int col, const QString& newText);

    /// <summary>
    /// 创建列表Item信号
    /// </summary>
    void sigCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo);

    /// <summary>
    /// 模板关联的roi取消了分组
    /// </summary>
    void sigRemoveRoiRelatedGroup(int roiID, const QString& roiName, int groupID, const QString& groupName, QMap<int/*modelID*/, QString/*modelName*/>& refModelInfoMap);

protected slots:
    /// <summary>
    /// 某个按键点击了
    /// </summary>
    void slotCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);
    void slotCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText);

    /// <summary>
    /// 分组设置按键点击了
    /// </summary>
    void slotGroupButtonClicked(int btnIndex, bool bChecked);

    /// <summary>
    /// 添加一条记录
    /// </summary>
    void slotCreateTableRowItem(const n_mtautodelineationdialog::ST_Organ& organInfo);

    /// <summary>
    /// 组发生了变化
    /// </summary>
    void slotGroupChanged(int nIndex, int state, const QString& itemText);

protected:
    /// <summary>
    /// 重载单元格创建完成后回调
    /// </summary>
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget);

    /// <summary>
    /// 重载表头按钮重载信号连接
    /// </summary>
    virtual void ConnectHeadWidgetSignals(int cellWidgetType, QWidget* cellWidget);

    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <param name="headList">[IN]表头文本集合</param>
    /// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
    void initTableView(const QStringList& headList, QVector<int> cellWidthVec);

    /// <summary>
    /// 处理行组发生改变
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="newGroupStr">改变后的分组</param>
    /// <returns>返回true表示允许发生变化，返回false表示分组改变被终止</returns>
    bool rowGroupInfoChanging(const QString& rowValue, const QString& newGroupStr);

private:
    QString m_curModelID;               //当前显示的模型ID
    QStringList m_allRoiTypeList;       //全部Roi类型集合
    QStringList m_allLabelList;         //全部标签
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> m_allOrganGroupList;                //所有的分组信息
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> m_modelCollectionInfoList;   //所有模板信息
    QMap<int, n_mtautodelineationdialog::ST_Organ> m_allOrganMap;                           //全部Organ信息集合(key-organId)
    QMap<int, n_mtautodelineationdialog::ST_Organ> m_curShowItemOrganMap;                   //记录确定修改前的Organ信息集合(key-organId)
    QString m_groupColBtnImagePath;     //分组列设置按钮图片

    bool m_bInitialized = false;        //列表是否初始化完成
    bool m_bDestroyed = false;          //列表是否被销毁
};