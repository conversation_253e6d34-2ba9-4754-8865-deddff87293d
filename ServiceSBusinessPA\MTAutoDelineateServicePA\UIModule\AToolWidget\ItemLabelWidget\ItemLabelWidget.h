﻿// *********************************************************************************
// <remarks>
// FileName    : ItemLabelWidget
// Author      : zlw
// CreateTime  : 2024-06-06
// Description : 将MtLabel嵌于QWidget中，一般用于MtListWidget
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_ItemLabelWidget.h"


class ItemLabelWidget : public QWidget
{
    Q_OBJECT

public:
    ItemLabelWidget(QWidget* parent = nullptr);
    ~ItemLabelWidget();

    /// <summary>
    /// 设置显示的文本
    /// </summary>
    /// <param name="str">[IN]显示的文本</param>
    void setShowText(const QString& str);

private:
    Ui::ItemLabelWidgetClass ui;
};
