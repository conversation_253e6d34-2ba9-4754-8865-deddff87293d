﻿#include <QHBoxLayout>
#include "AccuComponentUi\Header\UnitUIComponent\QCustColorButton.h"
#include "ui_QCustColorButton.h"
#include "MtColorButton.h"
#include "MtFrameEx.h"

QCustColorButtonParam::QCustColorButtonParam()
{
    _cellWidgetType = DELEAGATE_QCustColorButton;
}

QWidget* QCustColorButtonParam::CreateUIModule(QWidget* parent /*= nullptr*/)
{
    QCustColorButton* colorButton = new QCustColorButton(parent);
    colorButton->SetupCellWidget(*this);
    return colorButton;
}

QCustColorButton::QCustColorButton(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QCustColorButton;
    ui->setupUi(this);
}

QCustColorButton::~QCustColorButton()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QCustColorButton::SetupCellWidget(QCustColorButtonParam& cellWidgetParam)
{
    CreateByType(cellWidgetParam.showType, cellWidgetParam.topMarginNub, cellWidgetParam.lowMarginNub, cellWidgetParam.leftMarginNub, cellWidgetParam.rightMarginNub);
    m_colorButton->setText(cellWidgetParam.text);
    m_colorButton->setIndicatorWidth(cellWidgetParam.width);
    m_colorButton->setIndicatorHeight(cellWidgetParam.hight);
    m_colorButton->setIndicatorColor(cellWidgetParam.color);
    SetShowColorSelectDialog(cellWidgetParam.canSelectColor);
}

bool QCustColorButton::UpdateUi(const QVariant& updateData)
{
    if (updateData.canConvert<QCustColorButtonParam>())
    {
        QCustColorButtonParam editParam = updateData.value<QCustColorButtonParam>();
        SetupCellWidget(editParam);
    }
    else if (updateData.canConvert<QColor>())
    {
        QColor color = updateData.value<QColor>();
        m_colorButton->setIndicatorColor(color);
    }

    return true;
}


QString QCustColorButton::GetText()
{
    return m_colorButton->text();
}


void QCustColorButton::SetShowColorSelectDialog(bool isVisable)
{
    //Widget变色
    if (isVisable)
    {
        m_containFrame->setMtType(MtFrameEx::MtType::frameEx3_1_50);
    }
    else
    {
        m_containFrame->setMtType(MtFrameEx::MtType::default_type);
    }

    //Dialog可用弹窗颜色
    m_colorButton->setShowQColorDialog(isVisable);
}


std::vector<int> QCustColorButton::GetColor()
{
    QColor color = m_colorButton->indicatorColor();
    std::vector<int> result;
    result.push_back(color.red());
    result.push_back(color.green());
    result.push_back(color.blue());
    return result;
}


void QCustColorButton::CreateByType(int ToShowType, int topMarginNub, int lowMarginNub, int leftMarginNub, int rightMarginNub)
{
    if (ToShowType == QCustColorButtonParam::EM_ToShowType::Type_LeftJustifying)
    {
        ui->horizontalLayout->setContentsMargins(8, 0, 8, 0);
        //创建按钮
        m_colorButton = new MtColorButton(this);
        m_colorButton->setMtType(MtColorButton::MtType::colorbutton1);
        m_colorButton->setIndicatorColor(QColor(255, 0, 0));
        //Widget水平布局，添加按钮
        m_containFrame = new MtFrameEx(this);
        QHBoxLayout* horizontalLayout = new QHBoxLayout(this);
        horizontalLayout->setContentsMargins(QMargins(0, 5, 5, 5));
        m_containFrame->setLayout(horizontalLayout);
        m_containFrame->layout()->addWidget(m_colorButton);
        m_containFrame->setFixedHeight(26);
        //表单添加Widget
        this->layout()->addWidget(m_containFrame);
        //
        SetShowColorSelectDialog(false);
        connect(m_colorButton, &MtColorButton::indicatorColorChanged, this, &QCustColorButton::sigIndicatorColorChanged);
    }
    else
    {
        if (topMarginNub == -1)
        {
            topMarginNub = 5;
        }

        if (lowMarginNub == -1)
        {
            lowMarginNub = 5;
        }

        if (leftMarginNub == -1)
        {
            leftMarginNub = 5;
        }

        if (rightMarginNub == -1)
        {
            rightMarginNub = 5;
        }

        //创建按钮
        m_colorButton = new MtColorButton(this);
        m_colorButton->setMtType(MtColorButton::MtType::colorbutton1);
        m_colorButton->setIndicatorColor(QColor(255, 0, 0));
        //Widget水平布局，添加按钮
        m_containFrame = new MtFrameEx(this);
        QHBoxLayout* horizontalLayout = new QHBoxLayout(this);
        horizontalLayout->setContentsMargins(QMargins(leftMarginNub, topMarginNub, rightMarginNub, lowMarginNub));
        m_containFrame->setLayout(horizontalLayout);
        m_containFrame->layout()->addWidget(m_colorButton);
        m_containFrame->setFixedHeight(26);
        //表单添加Widget
        this->layout()->addWidget(m_containFrame);
        //
        SetShowColorSelectDialog(false);
        connect(m_colorButton, &MtColorButton::indicatorColorChanged, this, &QCustColorButton::sigIndicatorColorChanged);
    }
}

