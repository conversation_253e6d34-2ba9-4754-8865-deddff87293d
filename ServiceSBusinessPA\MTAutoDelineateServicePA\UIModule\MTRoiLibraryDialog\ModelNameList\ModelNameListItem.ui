<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>ModelNameListItem</class>
 <widget class="QWidget" name="ModelNameListItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>304</width>
    <height>36</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>36</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>36</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_2" stretch="0">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,0">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>28</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>16</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtLabel" name="mtLabelName">
        <property name="text">
         <string>MtTextLabel</string>
        </property>
        <property name="elideMode">
         <enum>Qt::ElideRight</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1_1</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtPushButton" name="btnModelItemOperate">
        <property name="text">
         <string/>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtPushButton::default_type</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
