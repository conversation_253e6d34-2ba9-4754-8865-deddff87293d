﻿// *********************************************************************************
// <remarks>
// FileName    : GroupItem
// Author      : zlw
// CreateTime  : 2024-01-20
// Description : 数据项Item(内嵌于: GroupItemListWidget listWidget窗体)
// </remarks>
// **********************************************************************************
#pragma once

#include <QMutex>
#include <QWidget>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QVBoxLayout>
#include "ui_GroupItem.h"
#include "MTLabelLabel.h"
#include "MtCheckBoxLabel.h"


/// <summary>
/// 格式：value1[value2,value3]
/// isCheck：是否勾选
/// isExpand：是否展开
/// </summary>
class GroupItemData
{
public:
    int groupId = -1;
    int valId = -1;
    int organType = 0; //0-主结构器官 1-找到主结构的亚结构器官 2-未找到主结构的亚结构器官
    bool isCheck = false;
    QString value1;
    QString value2;
    QString value3;
};
Q_DECLARE_METATYPE(GroupItemData);


class GroupItem : public QWidget
{
    Q_OBJECT

public:
    enum EM_PageType
    {
        Page_LabelLabel,
        Page_CheckLabel
    };
public:
    GroupItem(QWidget* parent = nullptr);
    ~GroupItem();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 设置item的宽度
    /// </summary>
    /// <param name="itemWidthNum">[IN]item的宽度</param>
    void setItemWidth(const int itemWidthNum);

    /// <summary>
    /// 初始化Item
    /// </summary>
    /// <param name="pageTypeEnum">[IN]页面类型</param>
    /// <param name="dataList">[IN]数据集合</param>
    /// <param name="maxColumn">[IN]最大列数</param>
    /// <param name="horizontalSpace">[IN]水平间距</param>
    /// <returns>项高度</returns>
    int init(const EM_PageType pageTypeEnum, const QList<GroupItemData>& dataList, int maxColumn = 3, int horizontalSpace = 24);

public slots:
    /// <summary>
    /// 勾选全部MtCheckBoxLabel项
    /// </summary>
    /// <param name="groupId">[IN]分组id</param>
    /// <param name="checked">[IN]true勾选 false不勾选</param>
    void slotCheckAllItem_MtCheckBoxLabel(const int groupId, const bool checked);

signals: //发送到父窗口
    void sigGroupItemCheckToGroupItemListWidget(const int groupId, const int id, const bool checked); //item复选框是否勾选

protected slots:

protected:

private:
    Ui::GroupItemClass ui;
    QList<MtLabelLabel*>    m_labelLabelList;
    QList<MtCheckBoxLabel*>    m_checkBoxLabelList;
    QHash<QString, QString> m_imagePathHash;
    int m_itemWidth = 300;
};
