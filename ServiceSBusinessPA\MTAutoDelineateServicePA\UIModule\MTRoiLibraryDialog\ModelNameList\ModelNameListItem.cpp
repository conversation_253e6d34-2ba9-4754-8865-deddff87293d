﻿#include "ModelNameListItem.h"
#include "AccuComponentUi\Header\Language.h"
#include "Skin/CMtSkinManager.h"
#include "CMtCoreWidgetUtil.h"


ModelNameListItem::ModelNameListItem(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    //
    m_menu = new MtMenu(this);
    m_menu->setMtType(MtMenu::menu1);
    m_menu->addAction(tr("编辑模型"), this, SLOT(slotRightMenu()));
    m_menu->addAction(tr("删除模型"), this, SLOT(slotRightMenu()));
}

ModelNameListItem::~ModelNameListItem()
{
}

void ModelNameListItem::init(int modelId, const QString& text, const QString& pixmapPath)
{
    m_modelID = modelId;
    ui.mtLabelName->setText(text);
    ui.btnModelItemOperate->setPixmapFilename(pixmapPath);
    ui.btnModelItemOperate->setVisible(false);
    updateStatus(false);
}

void ModelNameListItem::updateItemText(const QString& text)
{
    ui.mtLabelName->setText(text);
}

void ModelNameListItem::setChecked(bool bChecked)
{
    m_bChecked = bChecked;
    updateStatus(false);
}

QString ModelNameListItem::text()
{
    return ui.mtLabelName->text();
}

bool ModelNameListItem::isChecked()
{
    return m_bChecked;
}

void ModelNameListItem::updateStatus(bool bHover)
{
    QString borderStyle = "#mtFrameEx{background: rgba(@colorB1, 0.1); border: 1px solid rgba(@colorB1, 1);}";
    QString textStyle = "#mtLabelName{color: rgba(@colorB1, 1);}";

    if (!m_bChecked && !bHover)
    {
        borderStyle = "#mtFrameEx{border: 1px solid rgba(@colorA0, 0);}";
        textStyle = "#mtLabelName{color: rgba(@colorA3, 0.8);}";
    }

    CMtCoreWidgetUtil::formatStyleSheet(borderStyle);
    CMtCoreWidgetUtil::formatStyleSheet(textStyle);
    ui.mtFrameEx->setStyleSheet(borderStyle);
    ui.mtLabelName->setStyleSheet(textStyle);
}

void ModelNameListItem::showPopmenu(const QPoint& pt)
{
    m_menu->popup(this->mapToGlobal(pt));
}

void ModelNameListItem::enterEvent(QEvent* event)
{
    updateStatus(true);
    ui.btnModelItemOperate->setVisible(true);
    QWidget::enterEvent(event);
}

void ModelNameListItem::leaveEvent(QEvent* event)
{
    updateStatus(false);
    ui.btnModelItemOperate->setVisible(false);
    QWidget::leaveEvent(event);
}

void ModelNameListItem::mouseReleaseEvent(QMouseEvent* event)
{
    QWidget::mouseReleaseEvent(event);
    QRect rt = rect();

    // 在区域内弹起鼠标，认为点击鼠标
    if (event->button() == Qt::MouseButton::RightButton && event->pos().x() > rt.left() && event->pos().y() > rt.top() && event->pos().x() < rt.right() && event->pos().y() < rt.bottom())
    {
        showPopmenu(event->pos());
    }
}

void ModelNameListItem::on_btnModelItemOperate_clicked()
{
    QRect rt = ui.btnModelItemOperate->geometry();
    showPopmenu(QPoint(rt.left(), rt.bottom()));
}

void ModelNameListItem::slotRightMenu()
{
    QAction* action = (QAction*)sender();
    QString actionText = action->text();

    if (tr("编辑模型") == actionText)
    {
        emit sigEditModel(m_modelID, ui.mtLabelName->text());
    }
    else if (tr("删除模型") == actionText)
    {
        emit sigDeleteModel(m_modelID, ui.mtLabelName->text());
    }
}