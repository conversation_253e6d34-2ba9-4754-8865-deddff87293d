﻿#include "MTAutoDelineationDialog.h"
#include <QProgressBar>
#include "AccuComponentUi\Header\Language.h"
#include "ui_MTAutoDelineationDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtProgressDialog.h"
#include "MtMessageBox.h"
#include "MtToolButton.h"
#include "DataDefine/InnerStruct.h"
#include "AutoSketchSub/AutoSketchTemplateWidget.h"
#include "AutoSketchSub/OptSketchCollection.h"
#include "ToolDialog/AutoExport/AutoExportDialog.h"
#include "CommonUtil.h"

namespace n_mtautodelineationdialog
{

MTAutoDelineationDialog::MTAutoDelineationDialog(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTAutoDelineationDialogClass;
    ui->setupUi(this);
    //重新设置属性，防止subwindow把拖拽事件屏蔽了
    this->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    //基本属性
    this->setMainLayout(ui->verticalLayout);        //设置布局
    this->setDialogWidthAndContentHeight(1400, 760);//弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("自动勾画"));             //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("勾画"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("关闭"));
    //页面设置
    ui->widget_model->setEnableButtonUnattend(true); //默认无人值守按钮势能
    ui->mtPushButton_modelRoiSetting->hide();
    ui->horizontalSpacer->changeSize(0, 0, QSizePolicy::Expanding, QSizePolicy::Minimum);
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTAutoDelineationDialog, " << errMsg.toStdString();
        }
    }
    //信号槽
    connect(ui->mtPushButton_export, &QPushButton::clicked, this, &MTAutoDelineationDialog::onMtPushButtonExport);  //导出地址配置按钮点击
    connect(ui->mtCheckBox_rt, &QCheckBox::stateChanged, this, &MTAutoDelineationDialog::onMtCheckBoxRt);           //合并已有rt勾选状态
    connect(ui->mtCheckBox_export, &QCheckBox::stateChanged, this, &MTAutoDelineationDialog::onMtCheckBoxExport);   //勾画完自动导出
    connect(ui->mtPushButton_modelRoiSetting, &QPushButton::clicked, this, &MTAutoDelineationDialog::onMtPushButtonModelRoiSetting);  //模型和ROI设置
    connect(ui->widget_roi, &AutoSketchRoiWidget::SigAddOneNewTemplate, ui->widget_model, &AutoSketchTemplateWidget::SigAddOneNewTemplate);
}

MTAutoDelineationDialog::MTAutoDelineationDialog(const int width, const int height, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui = new Ui::MTAutoDelineationDialogClass;
    ui->setupUi(this);
    //重新设置属性，防止subwindow把拖拽事件屏蔽了
    this->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint);
    //基本属性
    this->setMainLayout(ui->verticalLayout);        //设置布局
    this->setDialogWidthAndContentHeight(width, height);//弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("自动勾画"));             //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("勾画"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("关闭"));
    //页面设置
    ui->widget_model->setEnableButtonUnattend(true); //默认无人值守按钮势能
    ui->mtPushButton_modelRoiSetting->hide();
    ui->horizontalSpacer->changeSize(0, 0, QSizePolicy::Expanding, QSizePolicy::Minimum);
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTAutoDelineationDialog, " << errMsg.toStdString();
        }
    }
    //信号槽
    connect(ui->mtPushButton_export, &QPushButton::clicked, this, &MTAutoDelineationDialog::onMtPushButtonExport);  //导出地址配置按钮点击
    connect(ui->mtCheckBox_rt, &QCheckBox::stateChanged, this, &MTAutoDelineationDialog::onMtCheckBoxRt);           //合并已有rt勾选状态
    connect(ui->mtCheckBox_export, &QCheckBox::stateChanged, this, &MTAutoDelineationDialog::onMtCheckBoxExport);   //勾画完自动导出
    connect(ui->mtPushButton_modelRoiSetting, &QPushButton::clicked, this, &MTAutoDelineationDialog::onMtPushButtonModelRoiSetting);  //模型和ROI设置
    connect(ui->widget_roi, &AutoSketchRoiWidget::SigAddOneNewTemplate, ui->widget_model, &AutoSketchTemplateWidget::SigAddOneNewTemplate);
}

MTAutoDelineationDialog::~MTAutoDelineationDialog()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        OptSketchCollection* temp = (OptSketchCollection*)m_ptrOptSketchCollection;
        delete temp;
        m_ptrOptSketchCollection = nullptr;
        temp = nullptr;
    }
}

/// <summary>
/// 设置是否显示合并已有rtStruct选项
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true:显示合并已有rtStruct选项</param>
/// <param name="mergeRtMap">[IN]可选择的所有可合并的rtStruct-sopInsUID集合</param>
/// <param name="selectMergeRtSopUID">[IN]可选择的所有可合并的rtStruct集合(key-rtSopInsUID value-需显示的文本)</param>
void MTAutoDelineationDialog::setIsShowMergeOption(const bool isShow, const QStringList& allMergeRtSopInsUIDList, const QMap<QString, QString>& allMergeRtValMap)
{
    m_IsShowMergeOption = isShow;

    if (isShow == false && ui->formLayout->rowCount() > 0)
    {
        ui->formLayout->removeRow(1);
        return;
    }

    ui->mtCheckBox_rt->setEnabled(!allMergeRtSopInsUIDList.isEmpty());
    ui->mtComboBox_rt->setEnabled(!allMergeRtSopInsUIDList.isEmpty());

    for (int i = 0; i < allMergeRtSopInsUIDList.size(); i++)
    {
        QString sopInsUID = allMergeRtSopInsUIDList[i];

        if (allMergeRtValMap.contains(sopInsUID) == true)
        {
            ui->mtComboBox_rt->addItem(allMergeRtValMap[sopInsUID], QVariant::fromValue(sopInsUID));
        }
    }
}

/// <summary>
/// 设置是否显示勾画完后自动导出选项
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true:显示勾画完后自动导出选项</param>
/// <param name="allExportRangeMap">[IN]所有的导出范围选项(key-范围类型(1:导出该患者所有数据 2:只导出当前勾画RtStructure 3:导出当前勾画图像及RtStructure)</param>
void MTAutoDelineationDialog::setIsShowAutoExport(const bool isShow, const QList<int>& allExportRangeList)
{
    m_IsShowAutoExport = isShow;

    if (isShow == false && ui->formLayout->rowCount() > 0)
    {
        ui->formLayout->removeRow(1);
        return;
    }

    for (int i = 0; i < allExportRangeList.size(); i++)
    {
        ui->mtComboBox_export->addItem(CommonUtil::getExportRangeText(allExportRangeList[i]), QVariant::fromValue(allExportRangeList[i]));
    }

    ui->mtCheckBox_export->setEnabled(!allExportRangeList.isEmpty());
    ui->mtComboBox_export->setEnabled(!allExportRangeList.isEmpty());
    ui->mtLineEdit_export->setEnabled(!allExportRangeList.isEmpty());
    ui->mtPushButton_export->setEnabled(!allExportRangeList.isEmpty());
}

/// <summary>
/// 设置是否显示无人值守模板页签
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true:显示</param>
void MTAutoDelineationDialog::setIsShowUnattendTab(const bool isShow)
{
    ui->widget_model->setEnableButtonUnattend(isShow);
}

/// <summary>
/// 设置需提前选中的模板id
/// \n不设置: 默认不选中
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void MTAutoDelineationDialog::setSelectTemplateId(const int templateId)
{
    ui->widget_model->setSelectTemplateId(templateId);
}

/// <summary>
/// 设置提前选中的页签类型
/// \n 不设置: 默认选中2-选择模板进行勾画
/// </summary>
/// <param name="pageType">[IN]页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</param>
void MTAutoDelineationDialog::setSelectRadioPageType(const int pageType)
{
    if (1 == pageType)
    {
        ui->mtTabWidget->setCurrentIndex(0);//设置roi tab
    }
    else
    {
        ui->mtTabWidget->setCurrentIndex(1);//设置model tab
    }
}

/// <summary>
/// 设置当模板被无人值守使用时，是否显示提示框
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">[IN]true显示</param>
void MTAutoDelineationDialog::setIsShowTipOfModUnattendUsed(const bool isShow)
{
    m_IsShowTipUnattendUsed = isShow;
}

/// <summary>
/// 设置虚拟模板
/// \n用于=选择ROI进行勾画=页签进行器官提前选中
/// </summary>
void MTAutoDelineationDialog::setVirtualSketchCollection(ST_SketchModelCollection& stSketchCollection)
{
    ui->widget_roi->setVirtualSketchCollection(stSketchCollection);
}

/// <summary>
/// 显示自动勾画界面
/// </summary>
/// <param name="allGroupInfoList">[IN]所有分组信息</param>
/// <param name="allExportAddr">[IN]所有导出地址信息</param>
/// <param name="stAutoSketch">[IN]自动勾画信息</param>
/// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
/// <returns>QDialog::DialogCode</returns>
QDialog::DialogCode MTAutoDelineationDialog::showAutoSketchDlg(
    const QList<ST_OrganGroupInfo>& allGroupInfoList,
    const QList<ST_AddrSimple>& allExportAddr,
    const ST_AutoSketch& stAutoSketch,
    bool bShowBottomFuncWidget,
    bool bShowTemplateBtnWidget,
    ST_CallBack_AutoSketch& stCallBackAutoSketch)
{
    //数据初始化
    m_addrSimple = stAutoSketch.stAddrSimple;
    m_allExportAddr = allExportAddr;
    m_stCallBackAutoSketch = stCallBackAutoSketch;

    if (m_ptrOptSketchCollection != nullptr)
    {
        delete m_ptrOptSketchCollection;
        m_ptrOptSketchCollection = nullptr;
    }

    m_ptrOptSketchCollection = new OptSketchCollection();
    ((OptSketchCollection*)m_ptrOptSketchCollection)->init(allGroupInfoList, stAutoSketch.sketchModelList, stAutoSketch.allSketchCollectionMap, n_mtautodelineationdialog::ST_SketchModelCollection());
    ((OptSketchCollection*)m_ptrOptSketchCollection)->setIsShowTipOfModUnattendUsed(m_IsShowTipUnattendUsed);

    //UI填充
    if (m_IsShowMergeOption == true)
    {
        onMtCheckBoxRt(1);
    }

    if (m_IsShowAutoExport == true)
    {
        ui->mtCheckBox_export->setChecked(stAutoSketch.checkedExport);
        onMtCheckBoxExport(1);

        if (m_addrSimple.addrType == 1) //共享文件夹
        {
            ui->mtComboBox_export->setCurrentText(CommonUtil::getExportRangeText(m_addrSimple.exportRange));
            ui->mtLineEdit_export->setText(m_addrSimple.stDirInfo.dirPath);
            ui->mtCheckBox_export->setChecked(stAutoSketch.checkedExport);
        }
        else if (m_addrSimple.addrType == 4) //SCP服务器
        {
            ui->mtComboBox_export->setCurrentText(CommonUtil::getExportRangeText(m_addrSimple.exportRange));
            ui->mtLineEdit_export->setText(m_addrSimple.stScpInfo.serverName);
            ui->mtCheckBox_export->setChecked(stAutoSketch.checkedExport);
        }
    }

    //勾画模板
    ui->widget_model->setImagePathHash(m_imagePathHash);
    ui->widget_model->setMargin(0, 0, 0, 0);
    ui->widget_model->initData((OptSketchCollection*)m_ptrOptSketchCollection, stCallBackAutoSketch);
    ui->widget_model->setMtComboBoxModality(stAutoSketch.imageModality, false);
    ui->widget_model->SetTemplateBtnWidgetVisible(bShowTemplateBtnWidget);
    //勾画roi
    ui->widget_roi->setImagePathHash(m_imagePathHash);
    ui->widget_roi->setMargin(0, 0, 0, 0);
    ui->widget_roi->initData((OptSketchCollection*)m_ptrOptSketchCollection, stCallBackAutoSketch);
    //底部操作栏
    ui->frame_func_base->setVisible(bShowBottomFuncWidget);
    return (QDialog::DialogCode)(this->exec());
}


/// <summary>
/// 获取最新的勾画信息
/// </summary>
/// <param name="outSketchCollection">[OUT]待勾画的模板信息</param>
/// <param name="outCheckedMergeRt">[OUT]是否选择了合并已有rtStruct选项</param>
/// <param name="outMergeRtSopInsUID">[OUT]合并已有rtStruct-sopInsUID</param>
/// <param name="outCheckedExport">[OUT]是否选择了勾画完导出选项</param>
/// <param name="outStAddrSimple">[OUT]导出地址简易信息</param>
void MTAutoDelineationDialog::getNewOutInfo(ST_SketchModelCollection& outSketchCollection, bool& outCheckedMergeRt, QString& outMergeRtSopInsUID, bool& outCheckedExport, ST_AddrSimple& outStAddrSimple)
{
    //模板信息
    if (ui->mtTabWidget->currentIndex() == 0)
    {
        outSketchCollection = ui->widget_roi->getSelectSketchCollection();
    }
    else
    {
        outSketchCollection = ui->widget_model->getSelectSketchCollection();
    }

    //合并信息
    if (m_IsShowMergeOption == true)
    {
        outCheckedMergeRt = ui->mtCheckBox_rt->isChecked();
        outMergeRtSopInsUID = ui->mtComboBox_rt->currentData().value<QString>();
    }

    //导出信息
    if (m_IsShowAutoExport == true)
    {
        outCheckedExport = ui->mtCheckBox_export->isChecked();
        m_addrSimple.exportRange = ui->mtComboBox_export->currentData().value<int>();

        if (m_addrSimple.addrType == 1) //共享文件夹
        {
            m_addrSimple.stDirInfo.dirPath = ui->mtLineEdit_export->text();
        }
        else if (m_addrSimple.addrType == 4) //SCP服务器
        {
            m_addrSimple.stScpInfo.serverName = ui->mtLineEdit_export->text();
        }

        outStAddrSimple = m_addrSimple;
    }
}

/// <summary>
/// 获取最新的虚拟模板
/// \n用于*选择ROI进行勾画*页签进行器官提前选中
/// </summary>
/// <returns>最新的虚拟模板</returns>
ST_SketchModelCollection MTAutoDelineationDialog::getVirtualSketchCollection()
{
    return ui->widget_roi->getVirtualSketchCollection();
}

/// <summary>
/// 获取最新的模板排序信息
/// \n不管是否点击确定都会返回最新的
/// </summary>
/// <returns>最新的模板排序信息</returns>
QMap<EM_OptDcmType, QList<int>> MTAutoDelineationDialog::getNewTemplateIdSortMap()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        return ((OptSketchCollection*)m_ptrOptSketchCollection)->getTemplateIdSortMap();
    }

    return QMap<EM_OptDcmType, QList<int>>();
}

/// <summary>
/// 获取当模板被无人值守使用时，是否显示提示框
/// </summary>
bool MTAutoDelineationDialog::getNewIsShowTipOfModUnattendUsed()
{
    if (m_ptrOptSketchCollection != nullptr)
    {
        return ((OptSketchCollection*)m_ptrOptSketchCollection)->getIsShowTipOfModUnattendUsed();
    }

    return true;
}

/// <summary>
/// 获取选中的页签类型
/// </summary>
/// <returns>页签类型(1-选择ROI进行勾画  2-选择模板进行勾画)</returns>
int MTAutoDelineationDialog::getSelectRadioPageType()
{
    if (ui->mtTabWidget->currentIndex() == 0)//ROI页
    {
        return 1;
    }
    else
    {
        return 2;//模板页
    }
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTAutoDelineationDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

/// <summary>
/// 模板是否可用
/// </summary>
/// <returns>true可用</returns>
bool MTAutoDelineationDialog::checkSketchCollectionAvail()
{
    bool bRoiSelectMode = ui->mtTabWidget->currentIndex() == 0;//是否为选择roi模式

    if (!bRoiSelectMode && ui->widget_model->isSelectOneCollection() == false)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要勾画的模板"));
        return false;
    }

    //检查是否只有空勾画
    bool onlyEmptyRoi = true;
    QSet<int> emptyOrganIdSet = ((OptSketchCollection*)m_ptrOptSketchCollection)->getEmptyOrganIdSet();
    n_mtautodelineationdialog::ST_SketchModelCollection curSketchCollection;

    if (bRoiSelectMode)
    {
        curSketchCollection = ui->widget_roi->getSelectSketchCollection();
    }
    else
    {
        curSketchCollection = ui->widget_model->getSelectSketchCollection();
    }

    if (curSketchCollection.showGroupIdMap.isEmpty() == true)
    {
        if (curSketchCollection.id == Def_TempIdOfSelectTemplate || curSketchCollection.id == Def_TempIdOfSelectRoi)
            MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择要自动勾画的ROI"));
        else
            MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许模板为空，请选择其他模板"));

        return false;
    }

    for (QMap<int, QSet<int>>::iterator it = curSketchCollection.showGroupIdMap.begin(); it != curSketchCollection.showGroupIdMap.end(); it++)
    {
        if (emptyOrganIdSet.contains(it.key()) == false)
        {
            onlyEmptyRoi = false;
            break;
        }
    }

    if (onlyEmptyRoi == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("不允许只选择空勾画，请选择其他模板"));
        return false;
    }

    return true;
}

/// <summary>
/// 导出按钮
/// </summary>
void MTAutoDelineationDialog::onMtPushButtonExport()
{
    AutoExportDialog dlg(this);
    dlg.init(m_addrSimple, m_allExportAddr, [&]()
    {
        QList<n_mtautodelineationdialog::ST_AddrSimple> newRemoteScpList;

        if (m_stCallBackAutoSketch.editRemoteScpCallBack != nullptr)
        {
            newRemoteScpList = m_stCallBackAutoSketch.editRemoteScpCallBack();
        }

        return newRemoteScpList;
    });

    if (dlg.exec() == QDialog::Accepted)
    {
        int exportRange = m_addrSimple.exportRange;
        m_addrSimple = dlg.getNewAddrInfo();
        m_addrSimple.exportRange = exportRange;
        ui->mtLineEdit_export->setText(m_addrSimple.addrType == 1 ? m_addrSimple.stDirInfo.dirPath : m_addrSimple.stScpInfo.serverName);
    }

    return;
}

/// <summary>
/// 合并已有rt
/// </summary>
void MTAutoDelineationDialog::onMtCheckBoxRt(int state)
{
    if (ui->mtComboBox_rt->count() <= 0)
    {
        ui->mtCheckBox_rt->setChecked(false);
        ui->mtCheckBox_rt->setEnabled(false);
        ui->mtComboBox_rt->setEnabled(false);
    }

    if (ui->mtCheckBox_rt->isEnabled())
    {
        ui->mtComboBox_rt->setEnabled(ui->mtCheckBox_rt->isChecked());
    }
}

/// <summary>
/// 勾画完自动导出
/// </summary>
void MTAutoDelineationDialog::onMtCheckBoxExport(int state)
{
    if (ui->mtComboBox_export->count() <= 0)
    {
        ui->mtCheckBox_export->setChecked(false);
        ui->mtCheckBox_export->setEnabled(false);
        ui->mtComboBox_export->setEnabled(false);
        ui->mtLineEdit_export->setEnabled(false);
        ui->mtPushButton_export->setEnabled(false);
    }

    if (ui->mtCheckBox_export->isEnabled())
    {
        ui->mtComboBox_export->setEnabled(ui->mtCheckBox_export->isChecked());
        ui->mtLineEdit_export->setEnabled(ui->mtCheckBox_export->isChecked());
        ui->mtPushButton_export->setEnabled(ui->mtCheckBox_export->isChecked());
    }
}

/// <summary>
/// 模型和ROI设置
/// </summary>
void MTAutoDelineationDialog::onMtPushButtonModelRoiSetting()
{
    QApplication::processEvents();

    if (m_stCallBackAutoSketch.modelAndRoiSettingCallBack != nullptr)
    {
        bool isUpdate = m_stCallBackAutoSketch.modelAndRoiSettingCallBack();

        if (isUpdate == true)
        {
            if (m_stCallBackAutoSketch.getAllSketchTemplateCallBack != nullptr)
            {
                MtProgressDialog proDlg(this);
                proDlg.progressBar()->hide();
                proDlg.setCancelButtonVisible(false);
                proDlg.setLabelText(tr("数据初始化中，请稍后..."));
                proDlg.show();
                QApplication::processEvents();
                //
                QList<ST_OrganGroupInfo> allGroupInfoList;
                QList<n_mtautodelineationdialog::ST_SketchModel> allModelList;
                QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> allSketchCollectionMap;
                m_stCallBackAutoSketch.getAllSketchTemplateCallBack(allGroupInfoList, allModelList, allSketchCollectionMap);
                ((OptSketchCollection*)m_ptrOptSketchCollection)->init(allGroupInfoList, allModelList, allSketchCollectionMap, n_mtautodelineationdialog::ST_SketchModelCollection());
                ui->widget_model->reInitData();
                ui->widget_roi->reInitData();
                //
                proDlg.hide();
            }
        }
    }
}

/// <summary>
/// 关闭按钮
/// </summary>
void MTAutoDelineationDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void MTAutoDelineationDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void MTAutoDelineationDialog::onBtnRight1Clicked()
{
    //模板是否可用
    if (checkSketchCollectionAvail() == false)
        return;

    if (ui->mtCheckBox_export->isChecked() == true && ui->mtLineEdit_export->text().isEmpty() == true)
    {
        MtMessageBox::NoIcon::information_Title(this->window(), tr("请选择导出地址"));
        return;
    }

    this->accept();
}


}
