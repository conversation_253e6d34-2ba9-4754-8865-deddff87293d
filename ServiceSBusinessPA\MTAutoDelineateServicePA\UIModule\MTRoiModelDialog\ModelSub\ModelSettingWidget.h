﻿// *********************************************************************************
// <remarks>
// FileName    : ModelSettingWidget
// Author      : zlw
// CreateTime  : 2023-12-07
// Description : 模型设置页签(内嵌于: MTModelTemplateDialog 模型模板设置弹窗)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_ModelSettingWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"
#include "MtProgressDialog.h"
#include "MTRoiModelDialog\ModelSub\ModelRoiTable.h"

class ModelSettingWidget : public QWidget
{
    Q_OBJECT

public:
    ModelSettingWidget(QWidget* parent = nullptr);
    ~ModelSettingWidget();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 显示加载状态
    /// </summary>
    void showLoadingState(bool bShow);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="roiTypeList">[IN]所有Roi类型集合</param>
    /// <param name="allLabelList">[IN]全部标签</param>
    /// <param name="allGroupList">[IN]全部分组信息</param>
    /// <param name="stOrganList">[IN]Organ信息集合</param>
    /// <param name="modelInfoMap">[IN]模型信息集合</param>
    /// <param name="modelCollectionInfoList">[IN]模板信息集合</param>
    void init(const QStringList& allRoiTypeList, const QStringList& allLabelList,
              const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
              const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
              const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
              const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList);

    /// <summary>
    /// 获取所有Organ信息
    /// 发生修改的optTypeEnum会设置为OptType_Mod
    /// </summary>
    /// <returns>所有Organ信息</returns>
    QList<n_mtautodelineationdialog::ST_Organ> getAllOrganInfo();

    QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel> getModelInfo();

    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getAllOrganGroupInfo();

    /// <summary>
    /// 获取列表控件
    /// </summary>
    /// <returns>所有分组信息</returns>
    ModelRoiTable* getTableWidget();

    /// <summary>
    /// 保存最后一次修改
    /// </summary>
    void saveLastChange();

    /// <summary>
    /// 是否需要保存到数据库
    /// </summary>
    bool isNeedSave2File();

signals:
    /// <summary>
    /// 发送信号，以获取标签库信息
    /// </summary>
    /// <param name="stRoiLabelInfoVec">[IN][OUT]返回从标签库中获取的所有记录信息</param>
    void sigGetLabelLibraryInfo(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 发送信号，以获取器官ROI默认设置
    /// </summary>
    /// <param name="stOrganDefaultList">[IN][OUT]返回从获取的器官默认设置信息</param>
    void sigGetOrganDefaultInfo(QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 发送信号，通知外部进行导入模型
    /// </summary>
    /// <param name="modelPath">[IN][模型文件路径</param>
    void sigModelImport(const QString& modelPath);

    /// <summary>
    /// 模型导入进度信号，用于更新模型导入进度条
    /// </summary>
    /// <param name="value">[IN]模型导入进度</param>
    void sigModelImportProgress(int value);

    /// <summary>
    /// 模型导入是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="bSuccess">[IN]导入是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelImportFinish(bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 模型导入入库完成信号，用于更新模型列表
    /// </summary>
    /// <param name="stOrganInfoVec">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    void sigModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 模型删除信号
    /// </summary>
    /// <param name="modelId">[IN]要删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    void sigDeleteModel(const QString& modelId, const QString& modelName);

    /// <summary>
    /// 模型删除是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="modelId">[IN]删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    /// <param name="bSuccess">[IN]删除是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void sigModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 异步刷新器官列表
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    void sigRefreshROITableAsync(const QString& modelId);

    /// <summary>
    /// 模型信息保存结果.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelId">The model identifier.</param>
    /// <param name="modelName">Name of the model.</param>
    /// <param name="result">The result，0：success.</param>
    void sigSaveModelInfoResult(const QString& modelId, const QString& modelName, int result);

protected slots:
    /// <summary>
    /// 批量设置ROI按钮
    /// </summary>
    void on_mtPushButton_settingROIInfo_clicked();

    /// <summary>
    /// 点击恢复默认按钮
    /// </summary>
    void on_mtPushButton_reset_clicked();

    /// <summary>
    /// 点击保存按钮
    /// </summary>
    void on_mtPushButton_save_clicked();

    /// <summary>
    /// 点击取消编辑按钮
    /// </summary>
    void on_mtPushButton_cancel_clicked();

    /// <summary>
    /// 点击导入模型按钮
    /// </summary>
    void on_mtToolButton_import_clicked();

    /// <summary>
    /// 点击删除模型按钮
    /// </summary>
    void on_mtToolButton_del_clicked();

    /// <summary>
    /// 列表信息发生变化
    /// </summary>
    void slotTableInfoChanged(const QString& defOrganName, int col, const QString& newText);

    /// <summary>
    /// 列表刷新完成
    /// </summary>
    void slotTableInitialized();

    /// <summary>
    /// 切换模型
    /// </summary>
    void slotModelTableChanged(const QString& modelID);

    /// <summary>
    /// 编辑了模型名和模型描述
    /// </summary>
    void slotModelInfoChanged(const QString& text);

    /// <summary>
    /// 模型导入进度信号，用于更新模型导入进度条
    /// </summary>
    /// <param name="value">[IN]模型导入进度</param>
    void slotModelImportProgress(int value);

    /// <summary>
    /// 模型导入是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="bSuccess">[IN]导入是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void slotModelImportFinish(bool bSuccess, const QString& errMsg);

    /// <summary>
    /// 模型导入入库完成信号，用于更新模型列表
    /// </summary>
    /// <param name="stOrganInfoVec">[IN]所有的器官信息</param>
    /// <param name="modelInfoMap">[IN]所有的模型信息</param>
    void slotModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap);

    /// <summary>
    /// 模型删除是否成功信号，用于更新模型列表
    /// </summary>
    /// <param name="modelId">[IN]删除的模型ID</param>
    /// <param name="modelName">[IN]要删除的模型名</param>
    /// <param name="bSuccess">[IN]删除是否成功</param>
    /// <param name="errMsg">[IN]出错提示信息</param>
    void slotModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg);

protected:
    void setTableChangedStatus();
    QString formatDateTimeStr(QString dateTimeStr);

protected:
    virtual bool eventFilter(QObject* obj, QEvent* event) override;

private:
    void showEvent(QShowEvent* e) override;

    Ui::ModelSettingWidgetClass ui;
    QWidget* m_parentDialog = nullptr;
    QHash<QString, QString> m_imagePathHash; //图片资源路径(key-name value-图片相对路径)

    QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel> m_modelInfoMap;         //所有模型信息
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> m_allGroupList;                     //所有组信息
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> m_modelCollectionInfoList;   //模板信息

    MtProgressDialog* m_progressDialog = nullptr;
    QString m_curModelID;   //当前显示的模型ID

    bool    m_bInfoChanged;     //信息是否发生了修改（使用按钮状态来记录的问题：修改了编辑框内容后触发按钮状态会出现内容没修改也会触发的问题）
    bool    m_bNeedSave2File;   //是否需要保存到数据库
};
