﻿// *********************************************************************************
// <remarks>
// FileName    : RoiCodeSettingDialog
// Author      : zlw
// CreateTime  : 2023-11-03
// Description : Roi编码创建弹窗
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_RoiCodeSettingDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class RoiCodeSettingDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="manteiaRoiLabel">[IN]manteiaRoiLabel</param>
    /// <param name="allRoiLibraryMap">[IN]已存在的manteiaRoiLabel集合(key-manteiaRoiLabel)</param>
    RoiCodeSettingDialog(const QString& manteiaRoiLabel, const QMap<n_mtautodelineationdialog::EM_Manufacturer, QMap<QString, QString>> roiCodeMap, QWidget* parent = nullptr);
    ~RoiCodeSettingDialog();

    /// <summary>
    ///  获取最新的RoiCode编码集合
    /// </summary>
    /// <param name="outManteiaRoiLabel">[OUT]Manteia-ROI标签名</param>
    /// <param name="outRoiCodeMap">[OUT]最新的RoiCode</param>
    void getNewRoiCode(QString& outManteiaRoiLabel, QMap<n_mtautodelineationdialog::EM_Manufacturer, QMap<QString, QString>>& outRoiCodeMap);

protected slots:
    void slotValueChanged(const QString& text);

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::RoiCodeSettingDialogClass ui;
    QString m_manteiaRoiLabel;
    QMap<QString, QString>  m_mapTapValue;
};
