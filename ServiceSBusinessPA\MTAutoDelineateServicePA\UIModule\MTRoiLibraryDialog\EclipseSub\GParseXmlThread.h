﻿// *********************************************************************************
// <remarks>
// FileName    : GParseXmlThread
// Author      : zlw
// CreateTime  : 2023-11-03
// Description : Eclipse-XML文件解析线程
// </remarks>
// **********************************************************************************
#pragma once

#include <QObject>
#include <QThread>
#include <QVector>
#include <QFileInfoList>
#include <QtXML/QDomDocument>
#include <QtXML/QDomNode>

#include "DataDefine/InnerStruct.h"


class GParseXmlThread : public QThread
{
    Q_OBJECT

public:
    GParseXmlThread(QObject* parent = nullptr);
    ~GParseXmlThread();

    /// <summary>
    /// 开始解析
    /// </summary>
    /// <param name="codeType">[IN]编码类型(eclipse)</param>
    /// <param name="dirPath">[IN]文件夹位置</param>
    /// <param name="filePath">[IN]文件路径</param>
    /// <param name="isSub">[IN]是否递归</param>
    void startParse(const QString& codeType, const QString& dirPath, const bool isSub);
    void startParse(const QString& codeType, const QString& filePath);

    /// <summary>
    /// 强制停止解析
    /// </summary>
    void endParse();

    /// <summary>
    /// 执行解析
    /// </summary>
    void run();

signals:
    void SigPersent(const int persent); //进度
    void SigDone(const QVector<ST_EclipseTemplate> eclipseTemplateVec); //结束

protected:
    /// <summary>
    /// 获取指定目录下的所有文件完整路径
    /// </summary>
    /// <param name="nameFilters">[IN]文件过滤(*.dcm)</param>
    /// <param name="path">[IN]搜索路径</param>
    /// <param name="isRecursion">[IN]是否递归</param>
    /// <returns>指定目录下的所有文件完整路径</returns>
    QFileInfoList getFileList(const QStringList nameFilters, const QString& dirPath, bool isRecursion = true);

    /// <summary>
    /// 获取节点属性
    /// </summary>
    /// <returns>key-nodeName value-nodeValue</returns>
    QMap<QString, QString> getAttrHash(const QDomNamedNodeMap& domNamedNodeMap);

    /// <summary>
    /// 获取Eclipse-Roi-Code模板
    /// https://manteiatech.yuque.com/manteia/mgmrmw/wp8ay5k7saf9uq2a
    /// </summary>
    /// <param name="xmlPath">[IN]XML完整路径</param>
    /// <param name="outEclipseTemplate">[OUT]eclipse模板信息</param>
    void getCodeMapOfEclipseXml(const QString& xmlPath, ST_EclipseTemplate& outStuEclipseTemplate);

    /// <summary>
    /// 解析修正读取的模板中的颜色
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="colorStr">The color string.</param>
    /// <returns>9位RGB值</returns>
    QString fixColorString(const QString& colorStr);

private:
    QString m_codeType;  //编码类型(eclipse...)
    QString m_dirPath;   //待检索文件夹完整路径
    QString m_filePath;  //要导入的文件路径（与m_dirPath互斥，解析时，选其中一个不为空的进行解析）
    bool m_isSub = false;//是否递归文件夹
    QVector<ST_EclipseTemplate> m_eclipseTemplateVec; //Eclipse模板集合
};