﻿// *********************************************************************************
// <remarks>
// FileName    : ExportRuleShowWidget
// Author      : zlw
// CreateTime  : 2024-05-23
// Description : 无人值守规则界面导出信息Widget(内嵌于: UnattendSubTableShowItem)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_ExportRuleShowWidget.h"


class ExportRuleShowWidget : public QWidget
{
    Q_OBJECT

public:
    ExportRuleShowWidget(const QString& rangeStr, const QString& formatStr, const QString& addrStr, QWidget* parent = nullptr);
    ~ExportRuleShowWidget();

private:
    Ui::ExportRuleShowWidgetClass ui;
};
