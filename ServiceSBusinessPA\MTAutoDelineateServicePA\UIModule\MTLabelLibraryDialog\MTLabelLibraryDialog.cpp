﻿#include "MTLabelLibraryDialog.h"
#include "ui_MTLabelLibraryDialog.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MTLabelLibraryDialog\LabelLibrarySub\LabelLibraryWidget.h"
#include "CommonUtil.h"
#include "CMtLanguageUtil.h"


namespace n_mtautodelineationdialog
{

MTLabelLibraryDialog::MTLabelLibraryDialog(int nWidth, int nHeight, QWidget* parent)
    : MtTemplateDialog(parent)
    , m_bImportBtnHidden(false)
    , m_bUseROIBtnHidden(false)
{
    ui = new Ui::MTLabelLibraryDialogClass;
    ui->setupUi(this);
    nWidth = -1 == nWidth ? 785 : nWidth;
    nHeight = -1 == nHeight ? 506 : nHeight;

    if (CMtLanguageUtil::type == chinese)
    {
        nWidth += 130;
    }

    //基本属性
    this->setMainLayout(ui->verticalLayout);            //设置布局
    this->setDialogWidthAndContentHeight(nWidth, nHeight); //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("标签库设置"));                   //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    //getWidgetButton()->hide();                        //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    //this->getButton(MtTemplateDialog::BtnRight1)->setEnabled(false);
    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTLabelLibraryDialog, " << errMsg.toStdString();
        }
    }
    //
    this->getButton(MtTemplateDialog::BtnRight1)->installEventFilter(this);
}

MTLabelLibraryDialog::~MTLabelLibraryDialog()
{
}

void MTLabelLibraryDialog::setHiddenColumn(const QVector<int>& hideColIndexVec)
{
    m_hiddenColVec = hideColIndexVec;
}

void MTLabelLibraryDialog::setDisableColumn(const QVector<int>& editDisableColVec)
{
    m_disableColVec = editDisableColVec;
}

void MTLabelLibraryDialog::setImportButtonHidden()
{
    m_bImportBtnHidden = true;
}

void MTLabelLibraryDialog::setUseROIFirstButtonHidden()
{
    m_bUseROIBtnHidden = true;
}

/// <summary>
/// 显示Roi库设置弹窗
/// </summary>
/// <param name="roiTypeList">[IN]Roi类型集合(如果为空将采用内置的类型集合)</param>
/// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
/// <param name="outStEmptyOrganVec">[OUT]输出manteiaRoiLabel信息集合(根据optTypeEnum判断增删改)</param>
/// <returns>QDialog::DialogCode</returns>
QDialog::DialogCode MTLabelLibraryDialog::showLabelLibrarySettingDlg(const QStringList& roiTypeList, const QList<ST_RoiLabelInfo>& stRoiLabelInfoVec, QList<ST_RoiLabelInfo>& outStRoiLabelInfoVec)
{
    LabelLibraryWidget* labelLibraryWidget = new LabelLibraryWidget(this);
    labelLibraryWidget->setImagePathHash(m_imagePathHash);
    labelLibraryWidget->init("", true, false, roiTypeList.isEmpty() == true ? CommonUtil::getRoiTypeList() : roiTypeList, stRoiLabelInfoVec);
    labelLibraryWidget->hideLabelListColumn(m_hiddenColVec);
    labelLibraryWidget->enableLabelListColumn(m_disableColVec, false);

    if (m_bImportBtnHidden) labelLibraryWidget->hideImportButton();

    if (m_bUseROIBtnHidden) labelLibraryWidget->hideUseROIFirstButton();

    ui->verticalLayout_2->addWidget(labelLibraryWidget);

    if (this->exec() == QDialog::Accepted && labelLibraryWidget->isNeedSave2File())
    {
        outStRoiLabelInfoVec = labelLibraryWidget->getAllRoiLabelInfo();
        return QDialog::Accepted;
    }

    return QDialog::Rejected;
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTLabelLibraryDialog::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

/// <summary>
/// 关闭按钮
/// </summary>
void MTLabelLibraryDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void MTLabelLibraryDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void MTLabelLibraryDialog::onBtnRight1Clicked()
{
    this->accept();
}

bool MTLabelLibraryDialog::eventFilter(QObject* obj, QEvent* event)
{
    //点击保存和取消按钮时，让焦点设置到列表上，使列表输入框失去焦点而触发编辑完成事件
    if (obj == getButton(MtTemplateDialog::BtnRight1))
    {
        if (event->type() == QEvent::MouseButtonPress)
        {
            QMouseEvent* mouse_event = static_cast<QMouseEvent*>(event);

            if (mouse_event->button() == Qt::LeftButton)
            {
                for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
                {
                    QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

                    if (item->widget())
                    {
                        LabelLibraryWidget* labelWidget = qobject_cast<LabelLibraryWidget*>(item->widget());

                        if (labelWidget)
                        {
                            labelWidget->removeFocusFromTable();
                        }
                    }
                }
            }
        }
    }

    return QWidget::eventFilter(obj, event);
}

}
