﻿#include "GParseXmlThread.h"
#include <QDir>
#include <QJsonObject>
#include <QJsonParseError>


GParseXmlThread::GParseXmlThread(QObject* parent)
    : QThread(parent)
{
}

GParseXmlThread::~GParseXmlThread()
{
}

QString GParseXmlThread::fixColorString(const QString& colorStr)
{
    QString retValue = "255000000";
    static QMap<QString, QString> colorMap ;

    if (colorMap.isEmpty())
    {
        QFile file(":/MTAutoDelineationDialog/colorMap/eclipseColorMap.json");

        if (file.open(QIODevice::ReadOnly | QIODevice::Text))
        {
            // 读取JSON数据
            QByteArray jsonData = file.readAll();
            file.close();
            // 解析JSON数据
            QJsonParseError error;
            QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData, &error);

            if (error.error == QJsonParseError::NoError)
            {
                // 检查JSON是否为对象
                if (jsonDoc.isObject())
                {
                    // 获取JSON对象
                    QJsonObject jsonObj = jsonDoc.object();

                    for (QString& keyItem : jsonObj.keys())
                    {
                        QJsonObject clrObj = jsonObj.value(keyItem).toObject();
                        colorMap[keyItem.toLower()] = clrObj.value("2d").toString();
                    }
                }
            }
        }
    }

    if (colorStr.contains("RGB"))
    {
        if (colorStr.size() < 5)
        {
            return retValue;
        }

        retValue = colorStr.mid(3);
        retValue.replace(" ", "0");
    }
    else
    {
        QString clrName = colorStr.toLower();

        if (colorMap.contains(clrName))
        {
            retValue = colorMap[clrName];
            retValue.replace(" ", "");
            QStringList tempList = retValue.split(',');
            retValue = tempList[0].rightJustified(3, '0') + tempList[1].rightJustified(3, '0') + tempList[2].rightJustified(3, '0');
        }
    }

    retValue.rightJustified(9, '0');
    retValue = QString::number(retValue.mid(0, 3).toInt(), 16).rightJustified(2, '0')
               + QString::number(retValue.mid(3, 3).toInt(), 16).rightJustified(2, '0')
               + QString::number(retValue.mid(6, 3).toInt(), 16).rightJustified(2, '0');
    return retValue;
}
/// <summary>
/// 开始解析
/// </summary>
/// <param name="codeType">[IN]编码类型(eclipse)</param>
/// <param name="dirPath">[IN]文件夹位置</param>
/// <param name="isSub">[IN]是否递归</param>
void GParseXmlThread::startParse(const QString& codeType, const QString& dirPath, const bool isSub)
{
    m_codeType = codeType;
    m_dirPath = dirPath;
    m_isSub = isSub;
    m_filePath.clear();
    m_eclipseTemplateVec.clear();
    this->start();
}

void GParseXmlThread::startParse(const QString& codeType, const QString& filePath)
{
    m_codeType = codeType;
    m_filePath = filePath;
    m_dirPath.clear();
    m_eclipseTemplateVec.clear();
    this->start();
}

/// <summary>
/// 强制停止解析
/// </summary>
void GParseXmlThread::endParse()
{
    this->terminate();
    this->wait();
}

/// <summary>
/// 执行解析
/// </summary>
void GParseXmlThread::run()
{
    if (m_filePath.isEmpty())
    {
        QFileInfoList fileInfoList = getFileList({ "*.xml" }, m_dirPath, m_isSub);
        int sum = fileInfoList.size();
        emit this->SigPersent(1);
        QThread::msleep(500);

        for (int i = 0; i < sum; i++)
        {
            ST_EclipseTemplate stuEclipseTemplate;
            getCodeMapOfEclipseXml(fileInfoList[i].absoluteFilePath(), stuEclipseTemplate);

            if (stuEclipseTemplate.roiInfoMap.isEmpty() == false)
                m_eclipseTemplateVec.push_back(stuEclipseTemplate);

            int persent = float(i) / sum * 100;
            emit this->SigPersent(persent);
        }
    }
    else
    {
        emit this->SigPersent(1);
        QThread::msleep(500);
        ST_EclipseTemplate stuEclipseTemplate;
        getCodeMapOfEclipseXml(m_filePath, stuEclipseTemplate);

        if (stuEclipseTemplate.roiInfoMap.isEmpty() == false)
            m_eclipseTemplateVec.push_back(stuEclipseTemplate);
    }

    emit this->SigPersent(100);
    emit this->SigDone(m_eclipseTemplateVec);
}

/// <summary>
/// 获取指定目录下的所有文件完整路径
/// </summary>
/// <param name="nameFilters">[IN]文件过滤(*.dcm)</param>
/// <param name="path">[IN]搜索路径</param>
/// <param name="isRecursion">[IN]是否递归</param>
/// <returns>指定目录下的所有文件完整路径</returns>
QFileInfoList GParseXmlThread::getFileList(const QStringList nameFilters, const QString& dirPath, bool isRecursion)
{
    QDir dir(dirPath);
    QFileInfoList file_list = dir.entryInfoList(QStringList() << nameFilters, QDir::Files | QDir::Hidden | QDir::NoSymLinks);

    if (isRecursion)
    {
        QFileInfoList folder_list = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);

        for (int i = 0; i != folder_list.size(); i++)
        {
            QString name = folder_list.at(i).absoluteFilePath();
            QFileInfoList child_file_list = getFileList(nameFilters, name);
            file_list.append(child_file_list);
        }
    }

    return file_list;
}

/// <summary>
/// 获取节点属性
/// </summary>
/// <returns>key-nodeName value-nodeValue</returns>
QMap<QString, QString> GParseXmlThread::getAttrHash(const QDomNamedNodeMap& domNamedNodeMap)
{
    QMap<QString, QString> nameValueMap;

    for (int i = 0; i < domNamedNodeMap.count(); i++)
    {
        QDomNode node = domNamedNodeMap.item(i);
        nameValueMap.insert(node.nodeName(), node.nodeValue());
    }

    return nameValueMap;
}

/// <summary>
/// 获取Eclipse-Roi-Code模板
/// https://manteiatech.yuque.com/manteia/mgmrmw/wp8ay5k7saf9uq2a
/// </summary>
/// <param name="xmlPath">[IN]XML完整路径</param>
/// <param name="outEclipseTemplate">[OUT]eclipse模板信息</param>
void GParseXmlThread::getCodeMapOfEclipseXml(const QString& xmlPath, ST_EclipseTemplate& outStuEclipseTemplate)
{
    //读XML文件
    QDomDocument domDocument;
    QFile file(xmlPath);

    if (!file.open(QFile::ReadOnly))
        return;

    if (!domDocument.setContent(&file))
    {
        file.close();
        return;
    }

    file.close();
    //
    //根节点 StructureTemplate
    QDomElement root = domDocument.documentElement();

    if (root.nodeName() != "StructureTemplate")
        return;

    //Preview
    QMap<QString/*nodeName*/, QString/*nodeValue*/> nodeValueMap = getAttrHash(root.firstChildElement("Preview").attributes());
    QString templateID = nodeValueMap["ID"];
    QString ApprovalStatus = nodeValueMap["ApprovalStatus"];
    QString AssignedUsers = nodeValueMap["AssignedUsers"];
    QString Description = nodeValueMap["Description"];
    //Structures
    QDomElement Structures = root.firstChildElement("Structures");
    //Structure
    QDomNodeList organNodeList = Structures.childNodes();

    for (int i = 0; i < organNodeList.size(); i++)
    {
        QDomNode Structure = organNodeList.at(i);
        nodeValueMap = getAttrHash(Structure.attributes());
        QString structureID = nodeValueMap["ID"];
        //ColorAndStyle
        QDomElement ColorElement = Structure.firstChildElement("ColorAndStyle");
        QString strColor = ColorElement.text();
        strColor = fixColorString(strColor);
        //Identification
        QDomElement Identification = Structure.firstChildElement("Identification");
        //VolumeType
        QDomElement VolumeType = Identification.firstChildElement("VolumeType");
        QString roiType = VolumeType.text();
        //StructureCode
        QDomElement StructureCode = Identification.firstChildElement("StructureCode");
        nodeValueMap = getAttrHash(StructureCode.attributes());
        QString Code = nodeValueMap["Code"];
        QString CodeScheme = nodeValueMap["CodeScheme"];
        QString CodeSchemeVersion = nodeValueMap["CodeSchemeVersion"];

        if (structureID.isEmpty() == false/* && Code.isEmpty() == false && CodeScheme.isEmpty() == false && CodeSchemeVersion.isEmpty() == false*/)
        {
            ST_EclipseRoi stuEclipseRoi;
            stuEclipseRoi.structureID = structureID;
            stuEclipseRoi.volumeType = roiType.toUpper().replace(" ", "_");
            stuEclipseRoi.ColorAndStyle = strColor;
            stuEclipseRoi.roiCodeMap["code"] = Code;
            stuEclipseRoi.roiCodeMap["label"] = structureID;        //先用structureID填充
            stuEclipseRoi.roiCodeMap["codeMean"] = structureID;     //先用structureID填充
            stuEclipseRoi.roiCodeMap["codeScheme"] = CodeScheme;
            stuEclipseRoi.roiCodeMap["codeSchemeVer"] = CodeSchemeVersion;
            //
            outStuEclipseTemplate.templateID = templateID.isEmpty() ? "Eclipse  Template" : templateID;
            outStuEclipseTemplate.approval = ApprovalStatus;
            outStuEclipseTemplate.user = AssignedUsers;
            outStuEclipseTemplate.desc = Description;
            outStuEclipseTemplate.roiInfoMap.insert(structureID + "_" + Code, stuEclipseRoi);
        }
    }
}
