<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QCustSwitchButton</class>
 <widget class="QWidget" name="QCustSwitchButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>166</width>
    <height>50</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>650</width>
    <height>50</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>16</number>
   </property>
   <property name="topMargin">
    <number>8</number>
   </property>
   <property name="rightMargin">
    <number>16</number>
   </property>
   <property name="bottomMargin">
    <number>8</number>
   </property>
   <item>
    <widget class="MtSwitchButton" name="mtSwitchButton">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtSwitchButton::switchbutton1</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>MtSwitchButton</class>
   <extends>QWidget</extends>
   <header>MtSwitchButton.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
