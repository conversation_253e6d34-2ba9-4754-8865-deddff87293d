﻿#include "AccuComponentUi\Header\QMTParamItemWidget.h"
#include "ui_QMTParamItemWidget.h"
#include "CMtCoreDefine.h"
#include <qDebug>

QMTParamItemWidget::QMTParamItemWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTParamItemWidget;
    ui->setupUi(this);
    connect(ui->cbValue, SIGNAL(currentIndexChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
}


QMTParamItemWidget::QMTParamItemWidget(ItemParam& param, bool hide,
                                       ItemParam& param_2, bool hide_2,
                                       ItemParam& param_3, bool hide_3, QWidget* parent)
{
    ui = new Ui::QMTParamItemWidget;
    ui->setupUi(this);
    connect(ui->cbValue, SIGNAL(currentIndexChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
    connect(ui->cbValue_2, SIGNAL(currentIndexChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));
    connect(ui->cbValue_3, SIGNAL(currentIndexChanged(const QString&)), this, SLOT(slotValueChanged(const QString&)));

    if (hide)
    {
    }
    else
    {
        SetItemParam(0, param);
    }

    if (hide_2)
    {
        ui->widget_5->hide();
        ((QHBoxLayout*)ui->widget_7->layout())->addStretch();
    }
    else
    {
        SetItemParam(1, param_2);
    }

    if (hide_3)
    {
        ui->widget_6->hide();
        ((QHBoxLayout*)ui->widget_7->layout())->addStretch();
    }
    else
    {
        SetItemParam(2, param_3);
    }
}

QMTParamItemWidget::~QMTParamItemWidget()
{
    MT_DELETE(ui);
}


void QMTParamItemWidget::SetItemParam(int index, ItemParam& param)
{
    QLabel* labelKey = nullptr;
    QLineEdit* leValue = nullptr;
    QComboBox* cbValue = nullptr;

    if (0 == index)
    {
        labelKey = ui->labelKey;
        leValue = ui->leValue;
        cbValue = ui->cbValue;
    }
    else if (1 == index)
    {
        labelKey = ui->labelKey_2;
        leValue = ui->leValue_2;
        cbValue = ui->cbValue_2;
    }
    else if (2 == index)
    {
        labelKey = ui->labelKey_3;
        leValue = ui->leValue_3;
        cbValue = ui->cbValue_3;
    }
    else
    {
        return;
    }

    labelKey->setText(param._key);

    if (param._isEdit)
    {
        QStringList& editValues = param._valueList;

        if (!editValues.isEmpty())
        {
            cbValue->addItems(editValues);
        }

        cbValue->setCurrentText(param._value);
        cbValue->show();
        leValue->hide();
    }
    else
    {
        leValue->setReadOnly(true);
        leValue->setText(param._value);
        leValue->show();
        cbValue->hide();
    }
}

void QMTParamItemWidget::slotValueChanged(const QString& value)
{
    QComboBox* comboBox = (QComboBox*)this->sender();
    int index = 0;

    if (ui->cbValue == comboBox)
    {
        index = 0;
    }
    else if (ui->cbValue_2 == comboBox)
    {
        index = 1;
    }
    else if (ui->cbValue_3 == comboBox)
    {
        index = 2;
    }

    index = m_row * 3 + index;
    emit sigValueChanged(index, value);
}