﻿#pragma once

#include <QWidget>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"


class QMTAbstractMenu;
namespace Ui
{
class QMTButtonMenuWidget;
}
//QMTButtonMenuWidget
class  QMTButtonMenuWidgetParam : public ICellWidgetParam
{
public:
    QStringList _textList;  //文案路径
    QStringList _iconList;  //图标路径
    int _curIndex = 0;      //当前显示

    QMTButtonMenuWidgetParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTButtonMenuWidgetParam)

/****************自定义实现的qcomboBox***********************/
class  QMTButtonMenuWidget : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QMTButtonMenuWidget(QWidget* parent = Q_NULLPTR);
    ~QMTButtonMenuWidget();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口

    void AddItems(QStringList textList);                            //新增字符串
    void AddItems(QStringList iconPathList, QStringList textList);  //新增ICON和字符串
    void SetCurIndex(int index);                                    //设置当前下标
    void SetCurText(QString);
signals:
    // void sigCurrentTextChanged(QString curText, QString preText);
    // void sigCurrentIndexChanged(int curIndex, int preIndex);
    void sigCurrentIndexChanged(int);
    void sigCurrentTextChanged(const QString&);
    void sigClicked(int);
protected:
    void mousePressEvent(QMouseEvent* event);
    int GetMaxStrWidth(const QStringList&);
private slots:
    void slotSelectMenuTriggered(int);
private:
    Ui::QMTButtonMenuWidget* ui;
    QMTAbstractMenu* _selectMenu = nullptr;
    int _preIndex = 0;
    QStringList _textList;
    QStringList _iconPathList;
};
