﻿#pragma once

/// <summary>
/// 返回const char* 类型
/// </summary>
#define QMT_JSON_VALUE_CONST_CHAR(Key,Value)\
public :\
static inline const char* Key##_##Value##(){return #Value;}

class QMTJsonValue
{
public:
    /// <summary>
    /// TODO 可优化，应该最好用 SOP Class UID 去判断dicom文件类型才是
    /// if (tmprtFileName.toUpper().contains(QMTJsonValue::Filename_RT()))
    /// </summary>
    /// <returns></returns>
    static inline const char* Filename_RT()
    {
        return "RT";
    }
    static inline const char* Filename_DOSE()
    {
        return "DOSE";
    }
    //QMT_JSON_VALUE_CONST_CHAR(Modality, RT)//RtStruct
    //QMT_JSON_VALUE_CONST_CHAR(Modality, DOSE)//RtDose
    QMT_JSON_VALUE_CONST_CHAR(Modality, MR)
    QMT_JSON_VALUE_CONST_CHAR(Modality, PT)
    QMT_JSON_VALUE_CONST_CHAR(Modality, CT)


};

