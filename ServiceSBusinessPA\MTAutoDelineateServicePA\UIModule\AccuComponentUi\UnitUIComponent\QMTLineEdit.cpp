﻿#include "AccuComponentUi\Header\QMTLineEdit.h"
#include "ui_QMTLineEdit.h"
#include "CMtCoreDefine.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"

QMTLineEditParam::QMTLineEditParam()
{
    _cellWidgetType = DELEAGATE_QMTLineEdit;
}

QWidget* QMTLineEditParam::CreateUIModule(QWidget* parent)
{
    QMTLineEdit* lineEdit = new QMTLineEdit(parent);
    lineEdit->SetupCellWidget(*this);
    return lineEdit;
}

/*****************************************************************/

QMTLineEdit::QMTLineEdit(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTLineEdit;
    ui->setupUi(this);
    setReadOnly(true);
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

QMTLineEdit::~QMTLineEdit()
{
    if (_validator)
    {
        _validator->deleteLater();
        _validator = nullptr;
    }

    MT_DELETE(ui);
}

void QMTLineEdit::SetupCellWidget(QMTLineEditParam& cellWidgetParam)
{
    this->setReadOnly(cellWidgetParam._isReadOnly);
    this->SetRegExpStr(cellWidgetParam._regExpStr);
    this->setText(cellWidgetParam._text);

    if (cellWidgetParam._maxLength > 0)
    {
        this->GetLineEdit()->setMaxLength(cellWidgetParam._maxLength);
    }

    if (cellWidgetParam._placeholderText.size() > 0)
    {
        this->GetLineEdit()->setPlaceholderText(cellWidgetParam._placeholderText);
    }
}

bool QMTLineEdit::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString str = updateData.toString();
        disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        this->setText(str);
        connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        SetEnableEdit(bEditEnable);
    }

    return false;
}

QString QMTLineEdit::GetCurText()
{
    return this->getText();
}

QLabel_Dot* QMTLineEdit::GetLable()
{
    return ui->label;
}

QLineEdit* QMTLineEdit::GetLineEdit()
{
    return ui->lineEdit;
}

void QMTLineEdit::SetRegExpStr(const QString& regExpStr)
{
    if (0 == regExpStr.size())
        return;

    if (nullptr != _validator)
    {
        delete _validator;
        _validator = nullptr;
    }

    QRegExp regExp(regExpStr);
    _validator = new QRegExpValidator(regExp, this);
    ui->lineEdit->setValidator(_validator);
}

void QMTLineEdit::SetItemValidator(QValidator* regExp)
{
    //这边不能对_validator赋值,外面delete后，析构会引起奔溃
    ui->lineEdit->setValidator(_validator);
}

void QMTLineEdit::setText(const QString& text)
{
    ui->label->setTextElided(text);
    ui->label->update();
    UpdateLineEditText(text);
}

void QMTLineEdit::setReadOnly(bool readOnly)
{
    if (readOnly)
    {
        ui->label->show();
        ui->lineEdit->hide();
        QString text = ui->lineEdit->text();
        ui->label->setTextElided(text);
        m_bIsInEditing = false;
    }
    else
    {
        ui->label->hide();
        ui->lineEdit->show();
        ui->lineEdit->setFocus();
        QString text = ui->label->GetFullString();
        UpdateLineEditText(text);
        m_bIsInEditing = true;
    }
}

void QMTLineEdit::SetMyStyleSheet(QString& sheetStr)
{
    ui->label->setStyleSheet(sheetStr);
    ui->lineEdit->setStyleSheet(sheetStr);
}

void QMTLineEdit::SetEnableEdit(bool enable)
{
    setReadOnly(true);
    _isDClickEdit = enable;
    this->setEnabled(_isDClickEdit);
}

QString QMTLineEdit::getText()
{
    return ui->lineEdit->text();
}

void QMTLineEdit::resizeEvent(QResizeEvent* event)
{
    int width = this->width();
    int height = this->height();
    ui->label->setFixedWidth(width);
    ui->lineEdit->setFixedWidth(width - 6);

    if (height > 30)
    {
        height = 30;
    }

    ui->lineEdit->setFixedHeight(height - 6);
    QWidget::resizeEvent(event);
}

void QMTLineEdit::mousePressEvent(QMouseEvent* event)
{
    emit sigClicked(0);
    QWidget::mousePressEvent(event);
}

void QMTLineEdit::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (false == _isDClickEdit)
    {
        QWidget::mouseDoubleClickEvent(event);
    }
    else
    {
        _oldText = ui->lineEdit->text();
        this->setReadOnly(false);
    }
}

void QMTLineEdit::enterEvent(QEvent* e)
{
    if (!m_bIsInEditing && !m_bIsEnterInThisWgt && this->isEnabled())
    {
        m_bIsEnterInThisWgt = true;
    }

    QWidget::enterEvent(e);
}

void QMTLineEdit::leaveEvent(QEvent* e)
{
    if (!m_bIsInEditing && m_bIsEnterInThisWgt && this->isEnabled())
    {
        m_bIsEnterInThisWgt = false;
    }

    QWidget::leaveEvent(e);
}

void QMTLineEdit::UpdateLineEditText(const QString& text)
{
    disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
    ui->lineEdit->setText(text);
    ui->label->setFocus();
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

void QMTLineEdit::slotLineEditingFinished()
{
    if (false == m_bIsInEditing || true == ui->lineEdit->isHidden())
    {
        return;
    }

    this->setReadOnly(true);
    QString newText = ui->lineEdit->text();

    if (_oldText != newText)
    {
        emit currentTextChanged(newText);
        _oldText = newText;
    }
}