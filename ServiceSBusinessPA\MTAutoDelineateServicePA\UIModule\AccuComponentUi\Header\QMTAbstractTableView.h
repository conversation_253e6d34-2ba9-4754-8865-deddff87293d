﻿// ************************************************************
// <remarks>
// Author      : Qiuchh
// CreateTime  : 2023-08-14
// Description : 一级表单组件，支持自定义单元格
// </remarks>
// ************************************************************
#pragma once

#include <QHeaderView>
#include <QTableWidget>
#include <QJsonObject>
#include <QJsonArray>
#include <QItemDelegate>
#include <QStyledItemDelegate>
#include <QMutex>
#include <QMouseEvent>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "QMTUIDataStruct.h"
#include "QMTTableDataCache.h"
#include "QMTUIDefine.h"
#include "QTipPushButton.h"
#include "UnitUIComponent\QMTButtonWithOrder.h"
#include "QMTLineEdit.h"
#include "UnitUIComponent\QMTAbsComboBox.h"
#include "UnitUIComponent\QMTAbsHorizontalBtns.h"
#include "UnitUIComponent\QMTCheckBox.h"
#include "QMTButtonMenuWidget.h"
#include "UnitUIComponent\QMTVDoubleLabelDot.h"
//新版组件，优先使用
#include "UnitUIComponent\QCustMtLabel.h"
#include "UnitUIComponent\QCustMtComboBox.h"
#include "UnitUIComponent\QCustMtSearchComboBox.h"
#include "UnitUIComponent\QCustMtLineEdit.h"
#include "UnitUIComponent\MtUnitLabelWithColor.h"
#include "UnitUIComponent\QCustDoubleClickLineEdit.h"

class QMTAbsTableViewDelegate;

/// <summary>
/// 一级列表自定义表头
/// </summary>
class  QMTAbsTableViewHeaderView : public QHeaderView
{
    Q_OBJECT
public:
    QMTAbsTableViewHeaderView(Qt::Orientation orientation, QWidget* parent = 0);
    /// <summary>
    /// Finalizes an instance of the <see cref="QMTAbsTableViewHeaderView"/> class.
    /// </summary>
    virtual ~QMTAbsTableViewHeaderView();

    /*----------------------add----------------------*/
    /// <summary>
    /// Sets the head widget map.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="headWidgetMap">The head widget map.</param>
    void SetHeadWidgetMap(QMap<int, QWidget*>& headWidgetMap);
    /// <summary>
    /// Gets the head widget map.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QMap&lt;Key, T&gt;.</returns>
    QMap<int, QWidget*> GetHeadWidgetMap();
    /*----------------------update----------------------*/
    /// <summary>
    /// 设置当前水平滑动条的值
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="value">The value.</param>
    void SetHorScrollValue(int value);
    /// <summary>
    /// 设置隐藏的列
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="bHide">The b hide.</param>
    void HideColumn(int column, bool bHide);

    /*-----------------get-----------------*/
    /// <summary>
    /// 获取表头widget
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <returns>QWidget *.</returns>
    QWidget* GetHeadCellWidget(int column);
    /// <summary>
    /// 获取表头所在列
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="headWidget">The head widget.</param>
    /// <returns>int.</returns>
    int GetHeadWidgetIndex(QWidget* headWidget);

protected:
    /// <summary>
    /// Paints the section.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="painter">The painter.</param>
    /// <param name="rect">The rect.</param>
    /// <param name="logicalIndex">Index of the logical.</param>
    virtual void paintSection(QPainter* painter, const QRect& rect, int logicalIndex) const;

private:
    /// <summary>
    /// 表头自定义widget
    /// </summary>
    QMap<int, QWidget*> _headWidgetMap;
    /// <summary>
    /// 水平滚动条的值
    /// </summary>
    int _horScrollValue = -1;
    /// <summary>
    /// 是否刷新自定义表头
    /// </summary>
    bool _bPaintSection = true;
    /// <summary>
    /// 是否隐藏
    /// </summary>
    QMap<int/*column*/, bool/*bHide*/> _columnHideMap;
};

/// <summary>
/*---------------------------------------------------------
一级列表基础类(继承QTableWidget):
1.支持自定义表头、自定义单元格
如果自定义单元格需要能够插入到表格中，那么自定义单元格界面需要继承QMTAbstractCellWidget,并且对应定义参数类(需继承ICellWidgetParam)。可参照QLabel_Dot
2.支持大数据动态展示，不卡顿，已测试50万数量情况
3.允许拖动列宽。
4.行号从0开始。

注意事项：
1.插入自定义单元格，自定义单元格必须是透明的.
2.覆盖GetColumnWidgetSheetCallBack，可以在子类实现自定义样式.
3.如果有搜索功能，那么建议外部直接调用setRowHidden接口。
4.如果有排序功能，那么建议外部ClearDataModel后重新SetDataModel即可。
-----------------------------------------------------------*/
/// </summary>
class  QMTAbstractTableView : public QTableWidget, public QMTTableDataCache
{
    Q_OBJECT

public:
    QMTAbstractTableView(QWidget* parent);
    /// <summary>
    /// Finalizes an instance of the <see cref="QMTAbstractTableView"/> class.
    /// </summary>
    ~QMTAbstractTableView();
    /*--------------------------子类回调处理函数-----------------------------*/
    /// <summary>
    /// 获取表头样式回调
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="delegateType">Type of the delegate.</param>
    /// <returns>QString.</returns>
    virtual QString GetHeadWidgetSheetCallBack(int column, int delegateType);
    /// <summary>
    /// 获取单元格样式回调
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="delegateType">Type of the delegate.</param>
    /// <returns>QString.</returns>
    virtual QString GetColumnWidgetSheetCallBack(int column, int delegateType);

    /// <summary>
    /// 获取单元格QssPropertyKey属性
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="delegateType">Type of the delegate.</param>
    /// <returns>QString.</returns>
    virtual QString GetColumnWidgetStylePropertyCallBack(int column, int delegateType);
    /// <summary>
    /// 单元格创建完成回调(用于外部刷新定制化单元格界面)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="cellType">Type of the cell.</param>
    /// <param name="cellWidget">The cell widget.</param>
    virtual void CreateCellWidgtFinishCallBack(const QString& rowValue, const int column, const int cellType, QWidget* cellWidget);
    /// <summary>
    /// 创建行界面完成回调,用于外部设置定制化UI刷新
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="rowItemType">Type of the row item.</param>
    virtual void CreateRowWidgetFinishCallBack(const QString& rowValue, int rowItemType);

    /*--------------------------------Init-------------------------------*/
    /// <summary>
    /// 初始化表格
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    void InitTableView(QMTAbsRowWidgetItemParam& tableParam);
    /// <summary>
    /// 获取界面参数
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QMTAbsRowWidgetItemParam &.</returns>
    QMTAbsRowWidgetItemParam& GetPerRowItemParam();
    /// <summary>
    /// 获取自定义表头指针
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QMTAbsTableViewHeaderView *.</returns>
    QMTAbsTableViewHeaderView* GetTableViewHeaderView();
    /// <summary>
    ///是否动态创建widget
    ///1.不能设置滚动为像素级别，否则动态创建会有异常。不能调用setVerticalScrollMode和setHorizontalScrollMode接口
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="enable">The enable.</param>
    void SetEnableDynamicCreateUi(bool enable);
    /// <summary>
    /// 设置初始化创建的widget个数
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="count">The count.</param>
    void SetInitPageRowCount(int count);
    /// <summary>
    /// 设置每次滚动到最大值创建的widget个数
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="count">The count.</param>
    void SetPerPageRowCount(int count);
    /// <summary>
    /// 获取每次滚动到最大值创建的widget个数
    /// </summary>
    /// <returns>每页条数.</returns>
    /// <remarks>[Version]:3.1.5.0 Change: </remarks> 
    int GetPerPageRowCount();
    /*--------------------------------Init END-------------------------------*/

    /*--------------------------------Add-------------------------------*/
    /// <summary>
    /// 初始化数据(ICellWidgetParam*,外部必须用new，列表中管理释放，外部无需释放)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValueList">The row value list.</param>
    /// <param name="cellWidgetParamMapMap">The cell widget parameter map map.</param>
    /// <returns>bool.</returns>
    bool SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QMap<int/*column*/, ICellWidgetParam*>>& cellWidgetParamMapMap);
    /// <summary>
    /// Sets the data model.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValueList">The row value list.</param>
    /// <param name="rowColumnStrListMap">The row column string list map.</param>
    /// <returns>bool.</returns>
    bool SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QStringList/*column string list*/>& rowColumnStrListMap);

    /// <summary>
    /// 新增一行（如果超过的行数大于_InitPageRowCnt变量的值，那么界面不会创建，滚动滚动条的时候创建界面，回调CreateRowWidgetFinishCallBack）
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="cellWidgetParamMap">The cell widget parameter map.</param>
    void AddRowItem(const QString& rowValue, QMap<int/*column*/, ICellWidgetParam*>& cellWidgetParamMap);
    /// <summary>
    /// Adds the per row item.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="columnStrList">The column string list.</param>
    void AddPerRowItem(const QString& rowValue, const QStringList& columnStrList);

    /// <summary>
    /// 某个位置插入一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="rowValue">The row value.</param>
    /// <param name="cellWidgetParamMap">The cell widget parameter map.</param>
    void InsertRowItem(int row, const QString& rowValue, QMap<int/*column*/, ICellWidgetParam*>& cellWidgetParamMap);
    /// <summary>
    /// Inserts the per row item.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="rowValue">The row value.</param>
    /// <param name="columnStrList">The column string list.</param>
    void InsertPerRowItem(int row, const QString& rowValue, const QStringList& columnStrList);
    /*--------------------------------Add END-------------------------------*/

    /*--------------------------------Delete-------------------------------*/
    /// <summary>
    /// 删除当前选中行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    void DeleteCurRow();
    /// <summary>
    /// 删除某一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    virtual void DeleteRowItem(const QString& rowValue)override;
    /// <summary>
    /// 清空所有数据
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    virtual void ClearDataModel()override;
    /// <summary>
    /// delete 某行某列界面
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="column">The column.</param>
    void DeleteCellWidgt(int row, int column);
    /*--------------------------------Delete END-------------------------------*/

    /*--------------------------------Update-------------------------------*/
    /// <summary>
    /// 更新表头的字符串
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="updateStr">The update string.</param>
    void UpdateHeadText(int column, const QString& updateStr);
    /// <summary>
    /// 更新表头单元格数据
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="updateData">The update data.</param>
    /// <returns>bool.</returns>
    bool UpdateHeadCellWidget(int column, QVariant& updateData);
    /// <summary>
    /// 更新某个单元格。会调用到每个单元格的UpdateUi接口
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="updateData">The update data.</param>
    /// <returns>bool.</returns>
    bool UpdateCellWidget(const QString& rowValue, int column, const QVariant& updateData);
    /// <summary>
    /// Updates the text map.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="updateTextMap">The update text map.</param>
    void UpdateTextMap(const QString& rowValue, QMap<int, QString>& updateTextMap);

    /// <summary>
    /// 更新某一行文案
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="columnStrList">The column string list.</param>
    void UpdateRowData(const QString& rowValue, const QStringList& columnStrList);

    /// <summary>
    /// 更新某列所有行数据
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="value">The value.</param>
    void UpdateAllColumnText(int column, const QString& value);

    /// <summary>
    /// 更新某一行的唯一标识key。但是新的唯一标识必须是界面中不存在的，否则会报错
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="oldMainValue">The old main value.</param>
    /// <param name="newMainValue">必须是界面中不存在的，否则会报错.</param>
    void UpdateRowMainKey(const QString& oldMainValue, const QString& newMainValue);

    /// <summary>
    /// 重新排序行唯一标识的值。
    /// </summary>
    /// <param name="rowValueList">个数必须和界面的相等，不然会报错</param>
    /// <remarks>[Version]:3.3.4 Change: </remarks>
    void SortRowValueList(const QStringList& newRowValueList);

    /// <summary>
    ///隐藏某一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="bHide">The b hide.</param>
    void HideRowItem(const QString& rowValue, bool bHide);

    /// <summary>
    /// 隐藏某一列
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="bHide">The b hide.</param>
    virtual void HideColumn(int column, bool bHide = true)override;

    /// <summary>
    /// 显示/隐藏某个单元格
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="column">The column.</param>
    /// <param name="hide">The hide.</param>
    void HideColumnCellWidget(int row, int column, bool hide);

    /// <summary>
    ///合并单元格
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="rowSpanCount">The row span count.</param>
    /// <param name="columnSpanCount">The column span count.</param>
    /// <param name="text">The text.</param>
    /// <param name="styleStr">The style string.</param>
    void SetSpanText(const QString& rowValue, int column, int rowSpanCount, int columnSpanCount, const QString& text, const QString& styleStr = "");
    /// <summary>
    /// Sets the span row.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="text">The text.</param>
    /// <param name="styleStr">The style string.</param>
    void SetSpanRow(const QString& rowValue, const QString& text, const QString& styleStr = "");

    /// <summary>
    /// 设置当前行,如果rowValue为空那么就会取消选中
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="bEmit">The b emit.</param>
    virtual void SetCurrentRow(const QString& rowValue, bool bEmit = false);
    /*--------------------------------Update END-------------------------------*/

    /*--------------------------------是否允许编辑接口-------------------------------*/
    /// <summary>
    /// 是否允许编辑，锁定后不允许编辑
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="isEditEnable">The is edit enable.</param>
    virtual void SetEditEnable(bool isEditEnable)override;
    /// <summary>
    /// 设置某一列是否都不允许编辑
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="bEnable">The b enable.</param>
    virtual void SetColumnEditEnable(int column, bool bEnable) override;

    /// <summary>
    /// 设置某个单元格不允许双击修改，如果row是-1则为所有行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="enable">The enable.</param>
    virtual void SetCellEditEnable(const QString& rowValue, int column, bool enable)override;
    /*--------------------------------是否允许编辑接口 END-------------------------------*/

    /*--------------------------------按键相关接口-------------------------------*/
    /// <summary>
    /// 更新某列所有行按键check
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="bChecked">The b checked.</param>
    void UpdateAllColumnBtnChecked(int column, bool bChecked);
    /// <summary>
    /// 设置按键的checked状态
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="bChecked">The b checked.</param>
    void SetButtonChecked(const QString& rowValue, int column, bool bChecked);
    /*--------------------------------按键相关接口 END-------------------------------*/

    /*--------------------------------checkbox相关接口-------------------------------*/
    /// <summary>
    /// 设置表头的checkbox状态
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="state">The state.</param>
    /// <param name="bEmit">The b emit.</param>
    void SetHeadCheckBoxState(int column, int state, bool bEmit = false);
    /// <summary>
    ///设置checkbox状态
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="state">The state.</param>
    /// <param name="bEmit">The b emit.</param>
    void SetCheckBoxState(const QString& rowValue, int column, int state, bool bEmit = true);
    /// <summary>
    /// 获取checkbox状态
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="column">The column.</param>
    /// <returns>int.</returns>
    int GetCheckBoxState(int row, int column);

    /// <summary>
    /// 设置表格的所有的checkbox状态变化(如果表头默认是checked状态，需要调用此接口)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="state">The state.</param>
    void TableFullCheckBoxStateChangeProc(int column, int state);
    /*--------------------------------checkbox相关接口 END-------------------------------*/

    /*--------------------------------get相关接口-------------------------------*/
    /// <summary>
    /// 获取表头字符串
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QStringList.</returns>
    QStringList GetHeadStrList();

    /// <summary>
    /// 获取当前行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>int.</returns>
    int GetCurrentRow();
    /// <summary>
    /// 获取当前行唯一值
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QString.</returns>
    QString GetCurUniqueValue();
    /// <summary>
    /// 获取model中的数据个数
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>int.</returns>
    int GetRowCount();
    /// <summary>
    /// 根据行号获取某一行的唯一值
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <returns>QString.</returns>
    QString GetRowUniqueValue(int row);
    /// <summary>
    /// 根据唯一值获取对应的行号
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="uniqueValue">The unique value.</param>
    /// <returns>int.</returns>
    int GetRowIndex(const QString& uniqueValue);

    /// <summary>
    /// 获取某一行所有列显示字符串
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <returns>QStringList.</returns>
    QStringList GetColumnTextList(const QString& rowValue);
    /// <summary>
    /// 获取某一行某一列显示字符串
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <returns>QString.</returns>
    QString GetColumnText(const QString& rowValue, int column);
    /// <summary>
    /// 获取某一列所有行字符串
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <returns>QStringList.</returns>
    QStringList GetRowTextList(int column);
    /// <summary>
    /// 获取checked状态
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="index">The index.</param>
    /// <returns>bool.</returns>
    bool GetRowCellChecked(const QString& rowValue, int column, int index);

    /// <summary>
    /// 是否需要创建界面
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>bool.</returns>
    bool IsNeedCreateWidget();
    /// <summary>
    /// 界面是否已经创建（动态创建的时候可能还未创建）
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <returns>bool.</returns>
    bool IsRowItemWidgetCreate(const QString& rowValue);

    /// <summary>
    /// 获取单元格界面
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <returns>QWidget *.</returns>
    QWidget* GetCellWidget(const QString& rowValue, int column);
    /// <summary>
    /// 获取当前鼠标悬浮的行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>int.</returns>
    int GetHoverRow();
    /// <summary>
    /// 是否所有的行唯一标识和界面上的行唯一标识是一一对应的，个数都一致
    /// </summary>
    /// <param name="rowValueList">The row value list.</param>
    /// <returns>bool.</returns>
    /// <remarks>[Version]:3.3.4 Change: </remarks>
    bool IsAllRowValueEquel(const QStringList& rowValueList);

    /// <summary>
    /// 是否界面上的所有行唯一标识都包含传入的行唯一标识
    /// </summary>
    /// <param name="rowValueList">The row value list.</param>
    /// <param name="bNeedCntEquel">是否需要判断个数也是一致的.</param>
    /// <returns>bool.</returns>
    /// <remarks>[Version]:3.3.4 Change: </remarks>
    bool IsAllRowValueContains(const QStringList& rowValueList, bool bNeedCntEquel);

    /*--------------------------------get相关接口 END-------------------------------*/

    /*--------------------------------signal-------------------------------*/
    /// <summary>
    /// 滑动条信号
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    virtual void ConnectScrollSignals();
    /// <summary>
    /// Dises the connect scroll signals.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    virtual void DisConnectScrollSignals();

    /// <summary>
    /// 表头信号
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellWidgetType">Type of the cell widget.</param>
    /// <param name="cellWidget">The cell widget.</param>
    virtual void ConnectHeadWidgetSignals(int cellWidgetType, QWidget* cellWidget);
    /// <summary>
    /// Dises the connect head widget signals.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellWidgetType">Type of the cell widget.</param>
    /// <param name="cellWidget">The cell widget.</param>
    virtual void DisConnectHeadWidgetSignals(int cellWidgetType, QWidget* cellWidget);

    /// <summary>
    /// 基类QTableWidget信号
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    virtual void ConnectTableViewSignlas();
    /// <summary>
    /// Dises the connect table view signlas.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    virtual void DisConnectTableViewSignlas();

    /// <summary>
    /// 单元格信号
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellWidgetType">Type of the cell widget.</param>
    /// <param name="cellWidget">The cell widget.</param>
    virtual void ConnectCellWidgetSignals(int cellWidgetType, QWidget* cellWidget);
    /// <summary>
    /// Dises the connect cell widget signals.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellWidgetType">Type of the cell widget.</param>
    /// <param name="cellWidget">The cell widget.</param>
    virtual void DisConnectCellWidgetSignals(int cellWidgetType, QWidget* cellWidget);
    /// <summary>
    /// 解绑所有的单元格的信号，主要是为了防止界面清空了，然后还会发信号
    /// </summary>
    void DisConnectAllCellWidgetSignals();
    /*--------------------------------signal END-------------------------------*/

    /// <summary>
    /// 重写函数
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="column">The column.</param>
    /// <param name="widget">The widget.</param>
    void setCellWidget(int row, int column, QWidget* widget);

signals:
    /*
    外部继承此类的接口，可以绑定以下信号，实时获取表头或者表体的单元格的变化
    */
    /// <summary>
    /// 某一列所有的checkBox改变了
    /// </summary>
    void sigHeadCheckBoxStateChange(int column, int state);
    /// <summary>
    /// 点击了排序,外部决定排序的顺序，调用接口RefreshOrderRowItemList刷新界面
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="orderKey">The order key.</param>
    /// <param name="state">The state.</param>
    void sigHeadRequestOrder(const QString& orderKey, int state);
    /// <summary>
    /// 点击了表头按键
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="btnIndex">Index of the BTN.</param>
    /// <param name="bChecked">The b checked.</param>
    void sigHeadButtonClicked(int column, int btnIndex, bool bChecked);
    /// <summary>
    /// 表头某一文案改变了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="column">The column.</param>
    /// <param name="newText">The new text.</param>
    void sigHeadTextChange(int column, const QString& newText);

    /*---------------------row item widget signals-------------------*/
    /// <summary>
    /// 点击了某一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    void sigRowItemClicked(const QString& rowValue);
    /// <summary>
    /// 双击了某一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    void sigRowItemDoubleClicked(const QString& rowValue);
    /// <summary>
    /// 单击了某一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    void sigRowItemClicked(const mtuiData::TableWidgetItemIndex&);
    /// <summary>
    /// 双击了某一行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    void sigRowItemDoubleClicked(const mtuiData::TableWidgetItemIndex&);
    /*---------------------row item widget signals END-------------------*/

    /*---------------------cell widget signals---------------------*/
    /// <summary>
    /// 点击了某个单元格
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    void sigCellItemClicked(const mtuiData::TableWidgetItemIndex&);
    /// <summary>
    /// 点击了某个单元格
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    void sigCellItemDpubleClicked(const mtuiData::TableWidgetItemIndex&);
    /// <summary>
    /// 某一单元格文案被修改了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellItemIndex">Index of the cell item.</param>
    /// <param name="newText">The new text.</param>
    void sigCellWidgetTextChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, const QString& newText);
    /// <summary>
    /// 下拉框改变了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellItemIndex">Index of the cell item.</param>
    /// <param name="index">The index.</param>
    void sigCellWidgetIndexChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, int index);
    /// <summary>
    /// 某个按键点击了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellItemIndex">Index of the cell item.</param>
    /// <param name="btnindex">The btnindex.</param>
    /// <param name="ischecked">The ischecked.</param>
    void sigCellWidgetButtonClicked(const mtuiData::TableWidgetItemIndex& cellItemIndex, int btnindex, bool ischecked);
    /// <summary>
    /// 某个单元格状态改变了（目前只有checkbox，可支持更多）
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="cellItemIndex">Index of the cell item.</param>
    /// <param name="state">The state.</param>
    void sigCellWidgetStateChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, int state);
    /*---------------------cell widget signals END---------------------*/

    /*---------------------search signals---------------------*/
    /// <summary>
    /// Sigs the request search.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="searchText">The search text.</param>
    void sigRequestSearch(const QString& searchText);
    /*---------------------search signals END---------------------*/
protected:
    /// <summary>
    /// Keys the press event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    virtual void keyPressEvent(QKeyEvent* event)override;                   //鼠标上下键生效
    /// <summary>
    /// Keys the press event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    virtual void keyReleaseEvent(QKeyEvent* event)override;                   //鼠标上下键生效
    /// <summary>
    /// Mouses the press event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    virtual void mousePressEvent(QMouseEvent* event)override;
    /// <summary>
    /// Mouses the release event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    virtual void mouseReleaseEvent(QMouseEvent* event)override;
    /// <summary>
    /// Mouses the move event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    virtual void mouseMoveEvent(QMouseEvent* event)override;
    /// <summary>
    /// Events the filter.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="obj">The object.</param>
    /// <param name="event">The event.</param>
    /// <returns>bool.</returns>
    virtual bool eventFilter(QObject* obj, QEvent* event) override;
    /// <summary>
    /// Viewports the event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    /// <returns>bool.</returns>
    virtual bool viewportEvent(QEvent* event)override;
    /// <summary>
    /// Leaves the event.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="event">The event.</param>
    virtual void leaveEvent(QEvent* event) override;

    /// <summary>
    /// Determines whether [is customer cell widget] [the specified widget].
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="widget">The widget.</param>
    /// <param name="row">The row.</param>
    /// <returns>bool.</returns>
    bool IsCustCellWidget(QWidget* widget, int& row);
    /// <summary>
    /// Sets the view delegate hover row.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    void SetViewDelegateHoverRow(int row);
    /// <summary>
    /// Sets the view delegate select row.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    void SetViewDelegateSelectRow(int row);

    /*---------------------Init---------------------*/
    /// <summary>
    /// 计算可见行
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    void CalculateVisiablePageRow();

    /// <summary>
    /// 获取表头Horizontal样式
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QString.</returns>
    QString GetHeadHorizontalStyleStr();
    /// <summary>
    /// 获取表头Vertical样式
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>QString.</returns>
    QString GetHeadVerticalStyleStr();
    /// <summary>
    /// 获取表体样式
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="bContentGridShow">The b content grid show.</param>
    /// <returns>QString.</returns>
    QString GetTableWidgetStyleStr(bool& bContentGridShow);
    /*---------------------Init END---------------------*/

    /*---------------------Add---------------------*/

    /// <summary>
    /// 创建表头
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    void CreateHeadView();

    /// <summary>
    /// 创建自定义表头widget
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="headCellParamMap">The head cell parameter map.</param>
    void CreateHeadView(QMap<int, ICellWidgetParam*>& headCellParamMap);
    /// <summary>
    /// 根据model中的数据创建界面
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <returns>bool.</returns>
    virtual bool CreateRowItemWidget(const QString& rowValue);
    /*---------------------Add END---------------------*/

    /*---------------------Delete---------------------*/
    /// <summary>
    /// Deletes the widget list.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="widgetList">The widget list.</param>
    void DeleteWidgetList(QList<QWidget*>& widgetList);                        //delete widget
    /// <summary>
    /// Deletes the table widget item list.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="widgetItemList">The widget item list.</param>
    void DeleteTableWidgetItemList(QList<QTableWidgetItem*>& widgetItemList);   //delete widget
    /*---------------------Delete END---------------------*/

    /*---------------------Update---------------------*/
    /// <summary>
    /// 更新checkbox状态
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="column">The column.</param>
    /// <param name="state">The state.</param>
    /// <param name="bEmit">The b emit.</param>
    void UpdateColumnWidgetCheckBoxState(const QString& rowValue, int column, int state, bool bEmit = false);
    /// <summary>
    /// Updates the widget CheckBox map.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="rowValue">The row value.</param>
    /// <param name="columnWidgetCheckBoxMap">The column widget CheckBox map.</param>
    /// <param name="bEmit">The b emit.</param>
    void UpdateWidgetCheckBoxMap(const QString& rowValue, QMap<int/*column*/, int/*state*/>& columnWidgetCheckBoxMap, bool bEmit = false);

    /// <summary>
    /// 如果界面的列允许变化，调用了接口swapSections。那么model的列和实际看到的列不一致，所以需要做映射。
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="modelUiColumnMap">key:model的列，列未变化前的；value:界面实际看到的列</param>
    void SetModelUiColumnMap(const QMap<int/*model column*/, int/*ui column*/>& modelUiColumnMap);
    /// <summary>
    /// 执行横向滚动条变化操作(有些场景会解绑滚动条信号，导致表头没有及时刷新滚动条值，就需要手动调用此接口)
    /// </summary>
    /// <remarks>[Version]:4.0.1.0 Change: </remarks>
    void DoHorizontalScrollBarValueChangeProc();
    /*---------------------Update END---------------------*/

protected slots:
    /*---------------------scroll slots---------------------*/
    /// <summary>
    ///竖滑动条
    /// </summary>
    virtual void slotScrollBarValueChanged(int);
    /// <summary>
    /// 横向滚动条
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    virtual void slotHorizontalScrollBarValueChanged(int);
    /*---------------------scroll slots END---------------------*/

    /*---------------------head---------------------*/
    /// <summary>
    /// 某一列所有的checkBox改变了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="state">The state.</param>
    virtual void slotHeadCheckBoxStateChange(int state);
    /// <summary>
    /// 点击了排序,外部决定排序的顺序，调用接口RefreshOrderRowItemList刷新界面
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="orderKey">The order key.</param>
    /// <param name="state">The state.</param>
    virtual void slotHeadOrderStateChange(const QString& orderKey, int state);
    /// <summary>
    ///点击了表头按键
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="btnIndex">Index of the BTN.</param>
    /// <param name="bChecked">The b checked.</param>
    virtual void slotHeadButtonClicked(int btnIndex, bool bChecked);
    /// <summary>
    /// 表头文案改变了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="newText">The new text.</param>
    void slotHeadWidgetTextChange(const QString& newText);
    /*---------------------head END---------------------*/

    /*---------------------table widget slots---------------------*/
    /// <summary>
    /// 单击了某个单元格(row从0开始)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="column">The column.</param>
    virtual void slotTableCellClicked(int row, int column);
    /// <summary>
    /// 双击了某个单元格
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="row">The row.</param>
    /// <param name="column">The column.</param>
    virtual void slotTableCellDoubleClicked(int row, int column);
    /*---------------------table widget slots END---------------------*/

    /*---------------------cell widget slots---------------------*/
    /*
    这边用来接收单元格的信号的，内部会发送信号出去，对于外部来说，只需要通过ConnectCellWidgetSignals绑定信号到此处，然后监听信号sigCellXXX即可
    */
    /// <summary>
    /// 某个单元格被点击了(弥补未响应slotTableCellClicked的情况)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="">The .</param>
    virtual void slotCellWidgetClicked(int);
    /// <summary>
    /// 某一列文案改变了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="newText">The new text.</param>
    virtual void slotCellWidgetTextChange(const QString& newText);
    /// <summary>
    /// comboBox下拉框改变了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="index">The index.</param>
    virtual void slotCellWidgetIndexChange(int index);
    /// <summary>
    /// 某个按键点击了
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="btnindex">The btnindex.</param>
    /// <param name="bchecked">The bchecked.</param>
    virtual void slotCellWidgetButtonClicked(int btnindex, bool bchecked);
    /// <summary>
    /// Slots the cell widget button clicked.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="bChecked">The b checked.</param>
    virtual void slotCellWidgetButtonClicked(bool bChecked);
    /// <summary>
    /// 某一行的状态改变了(可能是checkBox或者其它)
    /// </summary>
    /// <remarks>
    /// <para> [Version]:3.3.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="state">The state.</param>
    virtual void slotCellWidgetStateChange(int state);
    /*---------------------cell widget slots END---------------------*/
protected:

    /// <summary>
    /// 表头是否已经创建
    /// </summary>
    bool _bCreateHead = false;
    /// <summary>
    /// 样式代理类
    /// </summary>
    QMTAbsTableViewDelegate* _viewDelegate = nullptr;
    /// <summary>
    /// 自定义表头
    /// </summary>
    QMTAbsTableViewHeaderView* _headView = nullptr;

    /*---------------骚操作缓存变量---------------------*/
    /// <summary>
    /// The m mouse press row
    /// </summary>
    int m_mousePressRow = -1;
    /// <summary>
    /// The m mouse hover row
    /// </summary>
    int m_mouseHoverRow = -1;
};
