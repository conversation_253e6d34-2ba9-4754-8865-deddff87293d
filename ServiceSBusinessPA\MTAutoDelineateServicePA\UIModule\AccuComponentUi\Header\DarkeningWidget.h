﻿//*****************************************************************************
// Medical Imaging Solutions
// Contact number 17317554131
// xinbo.fu.
//
// Filename: DarkeningWidget.h
//
//*****************************************************************************

#ifndef __DarkeningWidget_h
#define __DarkeningWidget_h

#include <QWidget>

/**
 * @class DarkeningWidget
 * <AUTHOR>
 *
 * @brief This top-level widget can be used by modal dialogs to dim the window behind the dialog
 *
 * To use, instantiate and show this widget before calling exec().  This widget is implemented as a top level window
 * so that vtk views can continue to render behind it.  The size is set to be the same as the application's currently
 * active top level window and it positions itself directly over that window.
 */
class  DarkeningWidget: public QWidget
{
    Q_OBJECT

public:
    // Constructor and destructor
    DarkeningWidget(QWidget* parent = NULL, double opacityPercentage = 50, int toBottom = 40);
    virtual ~DarkeningWidget();

protected:
    virtual void showEvent(QShowEvent* event);
    virtual void hideEvent(QHideEvent* event);
    virtual void mousePressEvent(QMouseEvent* event);

signals:
    /**
     * emitted when the mouse clicked
     */
    void mouseClickedOnDarkening(QMouseEvent*);
};

#endif
