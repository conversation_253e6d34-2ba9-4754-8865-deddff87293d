﻿#pragma once

#if 0
#include <QLabel>
#include "TextManage.h"
#endif
#include "MtLabel.h"
#include "AccuComponentUi/Header/QMTUIModuleParam.h"


//QLabel参数
class  QLabelParam : public ICellWidgetParam
{
public:
    //QString _text;        //文案
    bool _showPix = false;  //是否显示图片
    QString _pixPath;       //图片路径
    bool _enableDot = true; //是否需要...
    bool _bWordWrap = false;    //是否自动换行，如果_enableDot为false的时候生效
    QLabelParam();
    ~QLabelParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QLabelParam)


/// <summary>
/// 字符串过长以至于无法显示完全时，末尾显示…
/// 1. 如果超长导致末尾截断为…，那么设置toolTip，当鼠标悬停时会显示完全的信息
///
/// 已知bug：
/// 1. 英文字符过长会无法显示完全。(QLabel 也会如此)
/// 2. 多个段落，最后一段太短无法正常显示 …
/// 3. 富文本直接赋值，不能转为 …
/// </summary>
//class QLabel_Dot : public QLabel, public TextManage, public QMTAbstractCellWidget
class  QLabel_Dot : public MtLabel, public QMTAbstractCellWidget// 完全移植到MtLabel
{
    Q_OBJECT

public:
    QLabel_Dot(QWidget* parent = Q_NULLPTR, Qt::WindowFlags f = Qt::WindowFlags());
    QLabel_Dot(const QString& text, QWidget* parent = Q_NULLPTR, Qt::WindowFlags f = Qt::WindowFlags());
#if 1
    virtual ~QLabel_Dot();
#endif

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual QString GetCurText();                      //获取当前界面展示文案

    void setTextElided(const QString& text);        //设置字符串
    QString GetFullString();                        //获取完整字符串
    void SetEnableDot(bool bEnable);

#if 0
protected:
    bool event(QEvent* event);
    virtual void paintEvent(QPaintEvent* event) override;
    virtual void resizeEvent(QResizeEvent* event) override;
private:
    QString _fullStr;           //完整字符串
    bool _bEnableDot = true;    //是否需要显示...
#endif
};
