﻿// *********************************************************************************
// <remarks>
// FileName    : LabelLibraryWidget
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : 标签库页签(内嵌LabelLibraryTable)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_LabelLibraryWidget.h"

class LabelLibraryWidget : public QWidget
{
    Q_OBJECT

public:
    LabelLibraryWidget(QWidget* parent = nullptr);
    ~LabelLibraryWidget();

    // <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="dirPath">[IN]待检索目录完整路径</param>
    /// <param name="isSub">[IN]是否检索子目录</param>
    /// <param name="isUsedDef">[IN]是否默认使用Roi库颜色等</param>
    /// <param name="roiTypeList">[IN]Roi类型集合</param>
    /// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
    void init(const QString& dirPath, const bool isSub, const bool isUsedDef, const QStringList& roiTypeList, const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec);

    /// <summary>
    /// 隐藏标签列表中的几列.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="columnIndexVec">要隐藏的列索引，从0开始</param>
    /// <param name="bHide">是否隐藏</param>
    void hideLabelListColumn(const QVector<int>& columnIndexVec, bool bHide = true);

    /// <summary>
    /// 设置列是否可以编辑
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="columnIndexVec">要设置的列索引，从0开始</param>
    /// <param name="bEnable">是否可编辑</param>
    void enableLabelListColumn(const QVector<int> columnIndexVec, bool bEnable = true);

    /// <summary>
    /// 隐藏导入按钮
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    void hideImportButton();

    /// <summary>
    /// 隐藏优先使用复选按钮
    /// </summary>
    /// <remarks>
    /// <para> [Version]:1.0.0.1</para>
    /// ChangeInfo:
    /// </remarks>
    void hideUseROIFirstButton();

    /// <summary>
    /// 获取所有manteiaRoiLabel信息
    /// </summary>
    /// <returns>所有manteiaRoiLabel信息</returns>
    QList<n_mtautodelineationdialog::ST_RoiLabelInfo> getAllRoiLabelInfo();

    void removeFocusFromTable();

    bool isNeedSave2File();

    void resetSaveFileStatus();

public slots:
    /// <summary>
    /// 添加按钮
    /// </summary>
    void onMtToolButton_add();

    /// <summary>
    /// 删除按钮
    /// </summary>
    void onMtToolButton_del();

    /// <summary>
    /// 刷新按钮
    /// </summary>
    void onMtToolButton_flush();

    /// <summary>
    /// 导入
    /// </summary>
    void onMtPushButton_import();

    /// <summary>
    /// 搜索框文本变化
    /// </summary>
    void onMtLineEditTextChanged(const QString& text);

    /// <summary>
    /// 列表初始化完成
    /// </summary>
    void slotTableInitialized();

protected:
    void setTableChangedStatus();
    void showLoadingState(bool bShow);

private:
    Ui::LabelLibraryWidgetClass ui;
    bool m_bSub = true;
    QString m_searchDir;
    QStringList m_roiTypeList;
    QAction* m_searchIconAction = nullptr;
    QWidget* m_parentDialog = nullptr;

    bool m_bNeedSave2File; //是否需要保存到数据库
};
