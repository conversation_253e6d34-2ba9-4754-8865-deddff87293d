﻿#pragma once

#include <QObject>
#include <functional>
#include <QMessageBox>
#include "QMTEnumDef.h"
#include "MtMenu.h"


#define MAKE_MENU_CALLBACK(memberFunc) std::bind( &memberFunc, this)
typedef std::function<void()> QMTMenuCallback;

//菜单组件化
class  QMTAbstractMenu : public QObject
{
    Q_OBJECT

public:
    QMTAbstractMenu(QObject* parent);
    ~QMTAbstractMenu();
    virtual void InitMenu();
    void PopMenu();
    void PopMenu(const QPoint& point);
    //add
    QAction* AddAction(const QString& text);
    QAction* AddAction(const QString& text, QMTMenuCallback callback);
    QAction* AddAction(MtMenu* menu, const QString& text, QMTMenuCallback callback);//指定action添加到哪个menu下
    QAction* AddAction(const QIcon& icon, const QString& text, QMTMenuCallback callback = NULL);
    QAction* AddAction(const QString& text, const QObject* receiver, const char* member, const QKeySequence& shortcut = 0);
    QAction* AddAction(const QIcon& icon, const QString& text, const QObject* receiver, const char* member, const QKeySequence& shortcut = 0);

    QAction* AddMenu(MtMenu* menu);
    MtMenu* AddMenu(const QString& title);
    MtMenu* AddMenu(MtMenu* menu, const QString& title);//指定menu添加到哪个menu下
    MtMenu* AddMenu(const QIcon& icon, const QString& title);


    void AddSeparator(MtMenu* menu = NULL);
    ///update
    void SetActionEnable(int index, bool enable, QString iconPath = QString());//设置某一项enable
    void SetAllActionEnable(bool);  //设置所有的item enable
    void SetMenuEnable(int, bool);  //设置mune enable
    void SetMenuFixedWidth(int);    //设置menu的宽度
    void SetIconPathList(bool enable, QStringList&);    //设置使能的图标

    //get
    QList<QAction*> GetAllActionList();
    MtMenu* GetMenu();
    QStringList GetIconPathList(bool enable);       //获取使能的图标
    QString GetIconPath(int index, bool enable);    //获取某个位置的图标路径
    //clear
    void Clear();
signals:
    void sigTriggered(QAction*);
    void sigTriggered(int);    //第几个action点击了
private slots:
    void slotActionTriggered(QAction*);
private:

private:
    MtMenu* _popMenu = nullptr;
    QList<QAction*> _actionList;
    QList<QMTMenuCallback> _actionCallbackList;
    QList<MtMenu*> _menuList;
    QStringList _enableIconPathList;            //enable状态下的图标
    QStringList _disableIconPathList;         //disable状态下的图标
};
