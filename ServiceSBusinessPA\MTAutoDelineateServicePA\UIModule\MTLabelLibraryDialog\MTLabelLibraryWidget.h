﻿// *********************************************************************************
// <remarks>
// FileName    : MTLabelLibraryWidget
// Author      : zlw
// CreateTime  : 2023-11-01
// Description : 标签库设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTLabelLibraryWidgetClass;
}

namespace n_mtautodelineationdialog
{

class MTLabelLibraryWidget : public QWidget, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="nWidth">[IN]窗口宽度，-1时，使用默认宽度</param>
    /// <param name="nHeight">[IN]窗口高度，-1时，使用默认高度</param>
    /// <param name="parent">[IN]父窗口</param>
    MTLabelLibraryWidget(int nWidth = -1, int nHeight = -1, QWidget* parent = nullptr);
    ~MTLabelLibraryWidget();

    /// <summary>
    /// 设置隐藏的列，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="hideColIndexVec">要隐藏的列索引，从0开始</param>
    void setHiddenColumn(const QVector<int>& hideColIndexVec);

    /// <summary>
    /// 设置禁用编辑的列，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="editDisableColVec">不可编辑的列索引，从0开始</param>
    void setDisableColumn(const QVector<int>& editDisableColVec);

    /// <summary>
    /// 隐藏导入按钮，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void setImportButtonHidden();

    /// <summary>
    /// 隐藏优先使用复选按钮，在显示窗口前调用
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void setUseROIFirstButtonHidden();

    /// <summary>
    /// 检查数据是否发生了修改.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>bool.</returns>
    bool isInfoChanged();

    /// <summary>
    /// 重置数据修改状态，将修改状态设置为false.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    void resetInfoChangeStatus();

    /// <summary>
    /// 获取修改后的数据.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <param name="outStRoiLabelInfoList">[OUT]输出修改后的标签信息，注意：保存标签信息的时候需要将roiName添加到标签的别名中</param>
    /// <param name="outAllGroupList">[OUT]输出修改后分组信息</param>
    void getWidgetData(QList<ST_RoiLabelInfo>& outStRoiLabelInfoList
    /*, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& outAllGroupList*/);

    /// <summary>
    /// 创建标签库设置弹窗
    /// </summary>
    /// <param name="roiTypeList">[IN]Roi类型集合(如果为空将采用内置的类型集合)</param>
    /// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
    void initLabelLibrarySettingWidget(const QStringList& roiTypeList, const QList<ST_RoiLabelInfo>& stRoiLabelInfoVec);

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

    QWidget* getContentWidget();

protected:
    virtual bool eventFilter(QObject* obj, QEvent* event) override;

private:
    Ui::MTLabelLibraryWidgetClass* ui;
    QHash<QString, QString> m_imagePathHash;     //图标map(key-name value-图片相对路径)
    QVector<int> m_hiddenColVec;                    //设置的隐藏列索引
    QVector<int> m_disableColVec;                   //设置的禁用编辑的列索引
    bool m_bImportBtnHidden;                        //是否隐藏导入按钮
    bool m_bUseROIBtnHidden;                        //是否隐藏默认使用ROI库复选按钮
};

}
