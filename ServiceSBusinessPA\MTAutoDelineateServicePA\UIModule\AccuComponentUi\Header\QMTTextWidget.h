﻿#pragma once

#include <QWidget>


namespace Ui
{
class QMTTextWidget;
}

class  QMTTextWidget : public QWidget
{
    Q_OBJECT

public:
    QMTTextWidget(QWidget* parent = Q_NULLPTR);
    ~QMTTextWidget();
    void SetText(QString&);
    void ClearColor();
    void SetColor(QColor& backColor, QColor& borderColor);
    void SetLabelProperty(QString);
private:
    Ui::QMTTextWidget* ui;
    QString _backStyle;
};
