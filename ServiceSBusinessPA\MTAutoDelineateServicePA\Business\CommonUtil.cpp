﻿#include "CommonUtil.h"
#include <QStorageInfo>
#include <QJsonObject>
#include <QMutex>
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\Language.h"
#include "DataDefine/InnerStruct.h"

#include "MTServiceAccessMgr.h"
#include "MTRPCTcpClientService/IMTRPCTcpClientService.h"


#pragma comment(lib, "iphlpapi.lib")

QString CommonUtil::m_clientCfgPath;
QString CommonUtil::m_organDefaultConfigInfoPath;

CommonUtil::CommonUtil(QObject* parent)
    : QObject(parent)
{
}

CommonUtil::~CommonUtil()
{
}

void CommonUtil::SetClientConfigPath(const QString& cfgPath)
{
    m_clientCfgPath = cfgPath;
}

QString CommonUtil::GetClientConfigPath()
{
    return m_clientCfgPath;
}

void CommonUtil::SetOrganDefaultConfigPath(const QString& cfgPath)
{
    m_organDefaultConfigInfoPath = cfgPath;
}

QString CommonUtil::GetOrganDefaultConfigPath()
{
    return m_organDefaultConfigInfoPath;
}

bool CommonUtil::GetServerIpPortClientId(QString& svrIP, int& svrPort, QString& clientId)
{
    //获取服务端IP和端口号
    QString errMsg;
    IMTRPCTcpClientService* rpcService = MTServiceAccessMgr::GetMTServicePluginObj<IMTRPCTcpClientService>();

    if (0 != rpcService->GetServerIpAndPort(svrIP, svrPort, errMsg))
    {
        errMsg = QString(tr("获取服务端IP和端口号失败：")) + errMsg;
        return false;
    }

    clientId = rpcService->GetClientID();
    return true;
}

/*
* 主机名
*/
QString CommonUtil::getLocalHostName()
{
    return QHostInfo::localHostName();
}
/*
* 获取Tcp端口状态
*/
BOOL CommonUtil::GetTcpPortState(ULONG nPort, ULONG* nStateID)
{
    PMIB_TCPTABLE pTcpTable = NULL;
    DWORD dwSize = 0;

    //获得pTcpTable所需要的真实长度,dwSize
    if ((GetTcpTable(pTcpTable, &dwSize, TRUE) != ERROR_INSUFFICIENT_BUFFER) || (dwSize <= 0))
    {
        dwSize = 5120;
    }

    //申请空间
    pTcpTable = (MIB_TCPTABLE*)malloc((UINT)dwSize);

    if (pTcpTable == NULL)
        return FALSE;

    //获取端口占用情况
    if (NO_ERROR == GetTcpTable(pTcpTable, &dwSize, TRUE))
    {
        DWORD nCount = pTcpTable->dwNumEntries;

        if (nCount > 0)
        {
            for (DWORD i = 0; i < nCount; i++)
            {
                MIB_TCPROW TcpRow = pTcpTable->table[i];
                DWORD temp1 = TcpRow.dwLocalPort;
                int temp2 = temp1 / 256 + (temp1 % 256) * 256;

                if (temp2 == nPort)
                {
                    *nStateID = TcpRow.dwState;
                    free(pTcpTable);
                    return TRUE;
                }
            }
        }

        *nStateID = 0;
        free(pTcpTable);
        return TRUE;
    }

    free(pTcpTable);
    return FALSE;
}

/*
* 获取五位随机数
*/
QString CommonUtil::getRandValue()
{
    QThread::msleep(2);
    qsrand(QTime(0, 0, 0, 0).msecsTo(QTime::currentTime()));
    QString newValue = QString("%1").arg(QString::number((unsigned int)qrand()), 5, QLatin1Char('0'));
    return newValue;
}

/*
* 获取指定范围内的随机数
*/
int CommonUtil::getRandValue(int max, int min)
{
    QThread::msleep(200);
    qsrand(QTime(0, 0, 0, 0).msecsTo(QTime::currentTime()));
    int num = qrand() % (max - min);
    return num + min;
}

/*
 * 16进制字符串转10进制
 */
int CommonUtil::hexToDecimal(char s[])
{
    int i, m, temp = 0, n;
    m = strlen(s);

    for (i = 0; i < m; i++)
    {
        if (s[i] >= 'A' && s[i] <= 'F') //十六进制还要判断他是不是在A-F或者a-f之间a=10。。
            n = s[i] - 'A' + 10;
        else if (s[i] >= 'a' && s[i] <= 'f')
            n = s[i] - 'a' + 10;
        else n = s[i] - '0';

        temp = temp * 16 + n;
    }

    return temp;
}

/// <summary>
/// 格式化CSS样式
/// </summary>
QString CommonUtil::formatStyleByCMtCoreWidgetUtil(const QString& str)
{
    QString newStr = str;
    CMtCoreWidgetUtil::formatStyleSheet(newStr);
    return newStr;
}

/// <summary>
/// 获取当前时间(间隔10ms)
/// </summary>
QString CommonUtil::getCurrentDateTime()
{
    static QMutex mutex_t;
    QString dataTime;
    mutex_t.lock();
    QThread::msleep(20);
    dataTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmsszzz");
    mutex_t.unlock();
    return dataTime;
}

QStringList CommonUtil::getRoiTypeList()
{
    static QStringList stringList;

    if (stringList.isEmpty())
    {
        QList<QString> roiList =
        {
            "NONE", "ORGAN", "GTV", "CTV", "PTV", "BODY", "EXTERNAL", "SUPPORT", "CONTROL", "AVOIDANCE",
            "CAVITY", "CONTRAST_AGENT", "DOSE_REGION", "FIXATION", "IRRAD_VOLUME", "TREATED_VOLUME", "BOLUS"
        };
        stringList = roiList;
    }

    return stringList;
}

QString CommonUtil::stringListToStr(const QStringList& stringList)
{
    QString str;

    for (int i = 0; i < stringList.size(); i++)
    {
        str.append(stringList[i]).append("/");
    }

    return str.left(str.length() - 1);
}

QStringList CommonUtil::strToStringList(const QString& str)
{
    QStringList outList;
    outList.reserve(20);
    QStringList tempList = str.split("/", QString::SkipEmptyParts);

    for (int i = 0; i < tempList.size(); i++)
    {
        QString str = tempList[i].trimmed();

        if (str.isEmpty() == false)
        {
            outList.push_back(str);
        }
    }

    return outList;
}

QString CommonUtil::getExportRangeText(const int exportRange)
{
    static QMap<int, QString> strMap = { {1, QObject::tr("导出该患者所有数据")}, {2, QObject::tr("只导出当前勾画RtStructure")}, {3, QObject::tr("导出当前勾画图像及RtStructure")} };

    if (strMap.contains(exportRange))
        return strMap[exportRange];

    return QString();
}

QString CommonUtil::getTextFromLocalAddrType(const int addrType)
{
    static QMap<int/*sketchRuleType*/, QString/*text*/> strMap;

    if (strMap.isEmpty())
        strMap = { {1, tr("共享文件夹")}, {2, tr("FTP网络站点")}, {4, tr("本地节点")} };

    return  strMap[addrType];
}

QString CommonUtil::getTextFromExportRange(const int exportRange)
{
    static QMap<int/*sketchRuleType*/, QString/*text*/> strMap;

    if (strMap.isEmpty())
        strMap = { {2, tr("仅勾画")}, {3, tr("全部")} }; //zlw 20230427 因为无人值守的图像和勾画包括plan，dose，所以只把文本改为全部

    return strMap[exportRange];
}

QString CommonUtil::getTextFromExportFormat(const QString exportFormat)
{
    static QMap<QString/*sketchRuleType*/, QString/*text*/> strMap;

    if (strMap.isEmpty())
        strMap = { {"0", tr("默认格式")}, {"3239DF85-89FB-419D-99BE-56E9C1D5DE50", QString("Eclipse 15+") + tr("格式")} };

    return  strMap[exportFormat];
}

/*
* 检查盘符是否合法
*/
bool CommonUtil::checkDrives(const QString& dirPath)
{
    QFileInfoList infoList = QDir::drives();

    for (int i = 0; i < infoList.size(); i++)
    {
        if (infoList[i].absoluteFilePath().toUpper().contains(dirPath))
        {
            QStorageInfo info(dirPath);

            if (info.isReadOnly() == false && info.fileSystemType().isEmpty() == false && info.bytesFree() > 0)
            {
                return true;
            }
        }
    }

    return false;
}

/*
* 格式化输出文本
*/
QString CommonUtil::getElidedText(QFont font, QString str, int MaxWidth)
{
    if (str.isEmpty())
    {
        return "";
    }

    QFontMetrics fontWidth(font);
    //计算字符串宽度
    int width = fontWidth.width(str);

    //当字符串宽度大于最大宽度时进行转换
    if (width >= MaxWidth)
    {
        //右部显示省略号
        str = fontWidth.elidedText(str, Qt::ElideRight, MaxWidth);
    }

    //返回处理后的字符串
    return str;
}

/// <summary>
/// 获取Roi分组名对应的英文名
/// </summary>
/// <remarks>
/// <returns>const QMap&lt;QString,QString&gt;&.</returns>
const QMap<QString, QString>& CommonUtil::getRoiGroupNameMap()
{
    static QMap<QString/*数据库内置分组名*/, QString/*对应翻译名*/> nameMap
        = { {"头颈部", tr("头颈部")}, {"男胸部", tr("男胸部")}
            , {"女胸部", tr("女胸部")}, {"腹部", tr("腹部")}
            , {"男下腹部", tr("男下腹部")}, {"女下腹部", tr("女下腹部")}
            , {"空勾画", tr("空勾画")}, {"全身", tr("全身及其他")}
            , {"临时分组", tr("临时分组")}
    };
    return nameMap;
}

QString CommonUtil::getTextFromBodyPartAI(const QString& bodypart)
{
    static QMap<QString, QString> strMap =
    {
        {"100000", QString(tr("头颈部"))}, {"010000", QString(tr("男胸部"))}, {"001000", QString(tr("女胸部"))},
        {"000100", QString(tr("腹部"))}, {"000010", QString(tr("男下腹"))}, {"000001", QString(tr("女下腹"))},
        {"110000", QString(tr("头颈部+胸部_男"))}, {"101000", QString(tr("头颈部+胸部_女"))}, {"010100", QString(tr("胸部+腹部_男"))},
        {"001100", QString(tr("胸部+腹部_女"))}, {"000110", QString(tr("腹部+下腹部_男"))}, {"000101", QString(tr("腹部+下腹部_女"))},
        {"110100", QString(tr("头颈部+胸部+腹部_男"))}, {"101100", QString(tr("头颈部+胸部+腹部_女"))}, {"010110", QString(tr("胸部+腹部+下腹部_男"))},
        {"001101", QString(tr("胸部+腹部+下腹部_女"))}, {"110110", QString(tr("头颈部+胸部+腹部+下腹部_男"))}, {"101101", QString(tr("头颈部+胸部+腹部+下腹部_女"))}
    };
    return strMap[bodypart];
}

QStringList CommonUtil::getCodeFromBodyPartAI()
{
    static QStringList stringList =
    {
        "100000", "010000", "001000", "000100", "000010", "000001", "110000", "101000", "010100",
        "001100", "000110", "000101", "110100", "101100", "010110", "001101", "110110", "101101"
    };
    return stringList;
}

/// <summary>
/// 获取DICOM字段匹配对应的文本
/// </summary>
/// <param name="dcmTag">DICOM-Tag:00180015</param>
QString CommonUtil::getTextFromDicomTag(const QString& dcmTag)
{
    static QMap<QString, QString> strMap =
    {
        {"00180015", "BodyPartExmined (0018,0015)"}, {"00081030", "Study Description (0008,1030)"},
        {"0008103E", "Series Description (0008,103E)"}, {"00321060", "RequestedProduceDes (0032,1060)"}
    };
    return strMap[dcmTag];
}

// <summary>
/// 获取DICOM性别匹配对应的文本
/// </summary>
/// <param name="dcmTag">[IN]DICOM性别:ALL F M</param>
QString CommonUtil::getTextFromDicomSex(const QString& sex)
{
    if (sex.toUpper() == "M")
        return tr("男");
    else if (sex.toUpper() == "F")
        return tr("女");

    return  tr("通用");
}

QString CommonUtil::getUnattendModelNameOfInerDefault(const QString& language, const QString& bodypart)
{
    //"head","chest","abdomen","prostate"
    if (language == "ch")
    {
        QMap<QString, QString> strMap =
        {
            {"100000", Def_Chinese_HeadNeck}, {"010000", Def_Chinese_Thorax_M}, {"001000", Def_Chinese_Thorax_F},
            {"000100", Def_Chinese_Abdomen}, {"000010", Def_Chinese_Pelvis_M}, {"000001", Def_Chinese_Pelvis_F},
            {"110000", Def_Chinese_HeadNeck_Thorax_M}, {"101000", Def_Chinese_HeadNeck_Thorax_F}, {"010100", Def_Chinese_Thorax_Abdomen_M},
            {"001100", Def_Chinese_Thorax_Abdomen_F}, {"000110", Def_Chinese_Abdomen_Pelvis_M}, {"000101", Def_Chinese_Abdomen_Pelvis_F},
            {"110100", Def_Chinese_HeadNeck_Thorax_Abdomen_M}, {"101100", Def_Chinese_HeadNeck_Thorax_Abdomen_F}, {"010110", Def_Chinese_Thorax_Abdomen_Pelvis_M},
            {"001101", Def_Chinese_Thorax_Abdomen_Pelvis_F}, {"110110", Def_Chinese_All_Parts_M}, {"101101", Def_Chinese_All_Parts_F}
        };

        if (strMap.contains(bodypart) == true)
        {
            return strMap[bodypart];
        }

        return QString();
    }

    //英文
    QMap<QString, QString> strMap =
    {
        {"100000", Def_English_HeadNeck}, {"010000", Def_English_Thorax_M}, {"001000", Def_English_Thorax_F},
        {"000100", Def_English_Abdomen}, {"000010", Def_English_Pelvis_M}, {"000001", Def_English_Pelvis_F},
        {"110000", Def_English_HeadNeck_Thorax_M}, {"101000", Def_English_HeadNeck_Thorax_F}, {"010100", Def_English_Thorax_Abdomen_M},
        {"001100", Def_English_Thorax_Abdomen_F}, {"000110", Def_English_Abdomen_Pelvis_M}, {"000101", Def_English_Abdomen_Pelvis_F},
        {"110100", Def_English_HeadNeck_Thorax_Abdomen_M}, {"101100", Def_English_HeadNeck_Thorax_Abdomen_F}, {"010110", Def_English_Thorax_Abdomen_Pelvis_M},
        {"001101", Def_English_Thorax_Abdomen_Pelvis_F}, {"110110", Def_English_All_Parts_M}, {"101101", Def_English_All_Parts_F}
    };

    if (strMap.contains(bodypart) == true)
    {
        return strMap[bodypart];
    }

    return QString();
}

/// <summary>
/// QString转QJsonObject
/// </summary>
QJsonObject CommonUtil::qStringToqJsonObject(const QString jsonString)
{
    QJsonObject jsonObject;
    QJsonDocument jsonDocument = QJsonDocument::fromJson(jsonString.toUtf8());

    if (jsonDocument.isNull())
    {
        return jsonObject;
    }

    jsonObject = jsonDocument.object();
    return jsonObject;
}

/// <summary>
/// QString转QJsonObject
/// </summary>
QJsonArray CommonUtil::qStringToqJsonArray(const QString jsonString)
{
    QJsonDocument jsonDocument = QJsonDocument::fromJson(jsonString.toUtf8());

    if (jsonDocument.isNull())
    {
        return QJsonArray();
    }

    return jsonDocument.array();
}

/// <summary>
/// QJsonObject转QString
/// </summary>
QString CommonUtil::qJsonObjectToqString(const QJsonObject& jsonObject)
{
    return QString(QJsonDocument(jsonObject).toJson(QJsonDocument::Compact));
}

/// <summary>
/// QVariantList转QStringList
/// </summary>
QStringList CommonUtil::qVariantListToQStringList(const QVariantList& variantList)
{
    QStringList strList;

    for (int i = 0; i < variantList.size(); i++)
    {
        strList.push_back(variantList[i].toString());
    }

    return strList;
}

/// <summary>
/// 获取指定目录下的所有文件完整路径
/// </summary>
/// <param name="path">[IN]搜索路径</param>
/// <param name="ignoreDirVec">[IN]忽略的目录名</param>
/// <param name="isRecursion">[IN]是否递归</param>
/// <returns>指定目录下的所有文件完整路径</returns>
QFileInfoList CommonUtil::getFileList(const QString& path, QVector<QString> ignoreDirVec, bool isRecursion)
{
    QDir dir(path);
    QFileInfoList file_list = dir.entryInfoList(QDir::Files | QDir::Hidden | QDir::NoSymLinks);

    if (isRecursion)
    {
        QFileInfoList folder_list = dir.entryInfoList(QDir::Dirs | QDir::NoDotAndDotDot);

        for (int i = 0; i != folder_list.size(); i++)
        {
            bool bSign = false;

            for (int j = 0; j < ignoreDirVec.size(); j++)
            {
                if (ignoreDirVec[j] == folder_list.at(i).completeBaseName())
                {
                    bSign = true;
                    break;
                }
            }

            if (bSign == true)
                continue;

            QString name = folder_list.at(i).absoluteFilePath();
            QFileInfoList child_file_list = getFileList(name);
            file_list.append(child_file_list);
        }
    }

    return file_list;
}

/// <summary>
/// 创建文件夹，返回实际文件夹路径
/// </summary>
QString CommonUtil::makPathDir(const QString& dirPath)
{
    QDir dir(dirPath);

    if (!dir.exists())
    {
        if (dir.mkpath(dirPath) == false)
        {
            QThread::sleep(2);

            if (dir.mkpath(dirPath) == false)
                return QString();
        }
    }

    return (dirPath.right(1) != "/" ? dirPath + "/" : dirPath);
}

/// <summary>
/// 删除文件
/// </summary>
/// <param name="filePath">[IN]文件完整路径</param>
/// <param name="errMsg">[OUT]错误信息</param>
/// <returns>true:成功</returns>
bool CommonUtil::removeFile(const QString& filePath)
{
    QFile file(filePath);

    if (!file.exists())
        return true;

    file.setPermissions(QFile::WriteOwner);

    if (!file.remove())
    {
        QThread::msleep(20);

        if (!file.remove())
        {
            return false;
        }
    }

    return true;
}

/// <summary>
/// 删除文件夹(包括子文件夹)
/// </summary>
/// <param name="dirPath">[IN]文件夹完整路径</param>
/// <returns>true:成功</returns>
bool CommonUtil::removeDir(const QString& dirPath)
{
    if (dirPath.isEmpty() == false && QFileInfo::exists(dirPath) == true)
    {
        QDir dir(dirPath);
        return dir.removeRecursively();
    }

    return true;
}

/// <summary>
/// 拷贝文件到指定路径下(同名文件替换)
/// </summary>
/// <param name="srcFilePath">[IN]源文件完整路径</param>
/// <param name="dstFilePath">[IN]目的文件完整路径</param>
/// <returns>成功true</returns>
bool CommonUtil::copyFile(const QString& srcFilePath, const QString& dstFilePath, const bool repalceSameFile)
{
    //目的地址有同名文件先删除
    if (repalceSameFile == true)
    {
        if (removeFile(dstFilePath) == false)
        {
            return false;
        }
    }

    //拷贝
    if (!QFile::copy(srcFilePath, dstFilePath))
    {
        QThread::msleep(200);

        if (!QFile::copy(srcFilePath, dstFilePath))
        {
            return false;
        }
    }

    return true;
}