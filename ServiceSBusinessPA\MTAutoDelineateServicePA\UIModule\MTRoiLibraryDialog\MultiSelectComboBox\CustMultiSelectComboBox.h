﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QColor>
#include <QVariant>
#include <QList>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MultiSelectComboBox.h"

//CustMultiSelectComboBox参数
class CustMultiSelectComboBoxParam : public ICellWidgetParam
{
public:

    QStringList _textList;                  //下拉框集合
    QList<QVariant> _userDataList;          //下拉框的业务数据
    QList<int> _comboBoxIndexList;          //下拉框的下标列表
    QList<int> _comboBoxDisableIndexList;   //禁用的下拉框选项下标列表
    bool _bEnabaleDrawSquare = false;       //true:文案后面绘制正方形，使用对象MtComboBoxDrawSquareColor；false：只有下拉框，使用对象MtComboBox
    CustMultiSelectComboBoxParam();
    ~CustMultiSelectComboBoxParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(CustMultiSelectComboBoxParam)

namespace Ui
{
class CustMultiSelectComboBox;
}

class CustMultiSelectComboBox :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    CustMultiSelectComboBox(QWidget* parent = Q_NULLPTR, bool bEnabaleDrawSquare = false);
    ~CustMultiSelectComboBox();
    void SetupCellWidget(CustMultiSelectComboBoxParam& cellWidgetParam);

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual void SetEnableEdit(bool bEdit);                  //设置是否允许编辑

    virtual QStringList currentText();                     //获取当前界面展示文案
    //设置当前索引--单
    virtual void setCurrentIndex(int index);
    //设置当前索引--多
    virtual void setCurrentIndex(const QList<int> indexList);

    //设置选中文本--单
    virtual void setCurrentText(const QString& text);
    //设置选中文本--多
    virtual void setCurrentText(const QStringList& text_list);


    /*新增业务接口*/
    QStringList GetAllItemStrList();            //获取下拉框所有值的接口
    void AddItem(const QString& itemStr, const QVariant& auserData = QVariant());       //添加下拉框的值
    void AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList = QList<QVariant>());       //添加下拉框的值
    void RemoveItem(const QString& itemStr);    //清空下拉框的值
    void ClearItems();                          //清空下拉框所有值
    void SetDisableIndex(const QList<int>& indexList);

    /*绘制正方形框*/
    void RegisterSquareColor(int index, const QColor& color);
    void UnRegisterSquareColor(int index);

    /*获取界面*/
    n_mtautodelineationdialog::MultiSelectComboBox* GetMtComboBox();

signals:
    void sigClicked(int);
    void currentTextChanged(const QString& newText);    //文案改变了
    void currentIndexChanged(int index);

protected slots:
    void slotCurrentTextChanged(const QString& text);
    void slotCurrentIndexChanged(int index);
protected:
    bool eventFilter(QObject* obj, QEvent* evt);
    void resizeEvent(QResizeEvent* event);          //保证...
    void mousePressEvent(QMouseEvent* event);

private:
    Ui::CustMultiSelectComboBox* ui = nullptr;
    n_mtautodelineationdialog::MultiSelectComboBox* m_comboBox = nullptr;
};

/*
下拉框带有正方形绘制的组件
*/
class MultiSelectComboBoxDrawSquareColor :
    public n_mtautodelineationdialog::MultiSelectComboBox
{
    Q_OBJECT
public:
    MultiSelectComboBoxDrawSquareColor(QWidget* parent = Q_NULLPTR);
    ~MultiSelectComboBoxDrawSquareColor();

    /*绘制正方形框接口*/
    void RegisterSquareColor(int index, const QColor& color);
    void UnRegisterSquareColor(int index);

protected:
    void paintEvent(QPaintEvent* event);
private:
    void DrawSquareAppendText(QPaintEvent* event);     //绘制提示小正方形

private:
    QMap<int, QColor> m_indexSquareColorMap;
};