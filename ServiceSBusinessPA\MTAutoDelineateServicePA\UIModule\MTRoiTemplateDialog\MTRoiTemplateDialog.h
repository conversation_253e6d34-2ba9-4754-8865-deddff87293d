﻿// *********************************************************************************
// <remarks>
// FileName    : MTRoiTemplateDialog
// Author      : zlw
// CreateTime  : 2024-01-10
// Description : 模板设置界面
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "Skin/IMtCustSkinWidget.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


namespace Ui
{
class MTRoiTemplateDialogClass;
}

namespace n_mtautodelineationdialog
{

class MTRoiTemplateDialog : public MtTemplateDialog, public IMtCustSkinWidget
{
    Q_OBJECT

public:
    MTRoiTemplateDialog(QWidget* parent = nullptr);
    ~MTRoiTemplateDialog();

    /// <summary>
    /// 设置是否显示无人值守模板页签
    /// 不设置: 默认显示
    /// </summary>
    /// <param name="isShow">[IN]true:显示</param>
    void setIsShowUnattendTab(const bool isShow);

    /// <summary>
    /// 设置当模板被无人值守使用时，是否显示提示框
    /// \n不设置: 默认显示
    /// </summary>
    /// <param name="isShow">true显示</param>
    void setIsShowTipOfModUnattendUsed(const bool isShow);

    /// <summary>
    /// 显示自动勾画模板界面
    /// </summary>
    /// <param name="allGroupInfoList">[IN]所有分组信息</param>
    /// <param name="allSketchModelList">[IN]所有勾画模型</param>
    /// <param name="allSketchCollectionList">[IN]所有排序后的勾画模板</param>
    /// <param name="stCallBackAutoSketch">[OUT]数据回调</param>
    /// <returns>QDialog::DialogCode</returns>
    QDialog::DialogCode showSketchTemplateDlg(
        const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
        const QList<ST_SketchModel>& allSketchModelList,
        const QMap<EM_OptDcmType, QList<ST_SketchModelCollection>>& allSketchCollectionMap,
        ST_CallBack_AutoSketch& stCallBackAutoSketch
    );

    /// <summary>
    /// 获取最新的模板排序信息
    /// \n不管是否点击确定都会返回最新的
    /// </summary>
    /// <returns>最新的模板排序信息</returns>
    QMap<EM_OptDcmType, QList<int>> getNewTemplateIdSortMap();

    /// <summary>
    /// 获取当模板被无人值守使用时，是否显示提示框
    /// </summary>
    bool getNewIsShowTipOfModUnattendUsed();

protected:
    /// <summary>
    /// 加载界面的皮肤配置信息
    /// </summary>
    /// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
    virtual void LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile) override;

private:
    Ui::MTRoiTemplateDialogClass* ui;
    bool m_IsShowTipUnattendUsed = true;
    QHash<QString, QString> m_imagePathHash;        //图标map(key-name value-图片相对路径)
    ST_CallBack_AutoSketch m_stCallBackAutoSketch;  //数据回调
    void* m_ptrOptSketchCollection = nullptr;       //自动勾画模板信息
};

}
