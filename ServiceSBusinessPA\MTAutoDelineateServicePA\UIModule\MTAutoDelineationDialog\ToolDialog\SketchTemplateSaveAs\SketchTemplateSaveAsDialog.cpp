﻿#include "SketchTemplateSaveAsDialog.h"
#include "MtToolButton.h"


SketchTemplateSaveAsDialog::SketchTemplateSaveAsDialog(const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& sketchCollectionList, QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.verticalLayout);         //设置布局
    this->setDialogWidthAndContentHeight(466, 50);  //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("另存"));                           //设置标题
    this->setAllowDrag(true);                       //设置是否可拖动
    //getWidgetButton()->hide();                    //如果不想右下角有按钮
    this->SetKeyEnterEnable(false);                 //不响应Enter按键
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));

    //数据初始化
    for (int i = 0; i < sketchCollectionList.size(); i++)
    {
        m_sketchCollectionMap.insert(sketchCollectionList[i].id, sketchCollectionList[i]);
    }
}

SketchTemplateSaveAsDialog::~SketchTemplateSaveAsDialog()
{
}

/// <summary>
/// 获取新的模板名称
/// </summary>
/// <returns>模板名称</returns>
QString SketchTemplateSaveAsDialog::getNewTemplateName()
{
    return ui.lineEdit->text();
}

/// <summary>
/// 关闭按钮
/// </summary>
void SketchTemplateSaveAsDialog::onBtnCloseClicked()
{
    this->reject();
}

/// <summary>
/// 取消按钮
/// </summary>
void SketchTemplateSaveAsDialog::onBtnRight2Clicked()
{
    this->reject();
}

/// <summary>
/// 确定按钮
/// </summary>
void SketchTemplateSaveAsDialog::onBtnRight1Clicked()
{
    if (ui.lineEdit->text().isEmpty())
    {
        ui.lineEdit->setWarningBorderStatus(tr("未填写模板名称"));
        return;
    }

    for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it = m_sketchCollectionMap.begin(); it != m_sketchCollectionMap.end(); it++)
    {
        if (ui.lineEdit->text().trimmed().toUpper() == it.value().templateName.trimmed().toUpper())
        {
            ui.lineEdit->setWarningBorderStatus(tr("模板名称已存在"));
            return;
        }
    }

    this->accept();
}