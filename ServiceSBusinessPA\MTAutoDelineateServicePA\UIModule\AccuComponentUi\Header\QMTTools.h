﻿#pragma once

#include <QObject>
#include <QListWidget>
#include <QMutex>

/*系统相关工具类*/
class QMTTools : public QObject
{
    Q_OBJECT

public:
    static QMTTools* getInstance();

    QSize GetSystemScreenSize();//获取可用屏幕大小
    void WaitEventLoop(int msec);

    //ui
    void ClearListWidgetWindow(QListWidget*);//释放qlistwidget的所有空间
    void WaitForTryLock(QMutex& mutex);

    //UI样式
    QString GetScrollStyleSheetStr();               //获取滚动条条样式
    QString GetComboBoxStyleSheetStr();             //获取comboBox样式
    QString GetSliderStyleSheetStr(bool bHorizontal);       //获取滑动条样式

    QString GetCurDateTimeStr();                //获取当前日期时间字符串
private:
    static QMTTools* m_instance;
};
