<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>LabelInfo2ROI</class>
 <widget class="QWidget" name="LabelInfo2ROI">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>357</width>
    <height>88</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>LabelInfo2ROI</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>12</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="MtLabel" name="mtLabel">
        <property name="text">
         <string>是否要将标签库的配置信息同步至模型和ROI设置？</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel2</enum>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtFrameEx" name="mtFrameEx_3">
        <layout class="QVBoxLayout" name="verticalLayout_3">
         <property name="spacing">
          <number>0</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <widget class="MtLabel" name="mtLabel_2">
           <property name="text">
            <string>请选择要同步的内容</string>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtLabel::myLabel1</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MtFrameEx" name="mtFrameEx_2">
           <layout class="QHBoxLayout" name="horizontalLayout">
            <property name="spacing">
             <number>24</number>
            </property>
            <property name="leftMargin">
             <number>0</number>
            </property>
            <property name="rightMargin">
             <number>0</number>
            </property>
            <item>
             <widget class="MtCheckBox" name="mtCheckBox_chName">
              <property name="text">
               <string>器官名称</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtCheckBox" name="mtCheckBox_name">
              <property name="text">
               <string>ROI名称</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtCheckBox" name="mtCheckBox_type">
              <property name="text">
               <string>ROI类型</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item>
             <widget class="MtCheckBox" name="mtCheckBox_color">
              <property name="text">
               <string>颜色</string>
              </property>
              <property name="_mtType" stdset="0">
               <enum>MtCheckBox::checkbox1</enum>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../Resources/Manteia1.2.qrc"/>
 </resources>
 <connections/>
</ui>
