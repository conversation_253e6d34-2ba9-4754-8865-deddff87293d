﻿#pragma once

#include <QWidget>
#include <QMetaType>
#include <QColor>
#include <QVariant>
#include <QStyledItemDelegate>
#include <QPainter>
#include <QProxyStyle>
#include <QStyle>
#include <QList>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"

#include "MtComboBox.h"

/*******
由QMtComboBox组成
*********/

class QCustMtComboBoxDelegate;
class NoIconComboBox;

//QCustMtComboBox参数
class  QCustMtComboBoxParam : public ICellWidgetParam
{
public:

    QStringList _textList;              //下拉框集合
    QList<QVariant> _userDataList;      //下拉框的业务数据
    int _comboBoxIndex = -1;            //下拉框的下标
    bool _bEnabaleDrawSquare = false;   //true:文案后面绘制正方形，使用对象MtComboBoxDrawSquareColor；false：只有下拉框，使用对象MtComboBox
    /// <summary>
    /// 记录最大最小值的顺序
    /// </summary>
    QList<int> listMaxMin;
    /// <summary>
    /// 是否使用新样式
    /// </summary>
    bool m_newStyle = false;
    QCustMtComboBoxParam();
    ~QCustMtComboBoxParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QCustMtComboBoxParam)

namespace Ui
{
class QCustMtComboBox;
}
class MtComboBox;

class  QCustMtComboBox :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    QCustMtComboBox(QWidget* parent = Q_NULLPTR, bool bEnabaleDrawSquare = false, bool bNewStyle = false);
    virtual ~QCustMtComboBox();
    void SetupCellWidget(QCustMtComboBoxParam& cellWidgetParam);

    /****************单元格公共接口*********************/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口
    virtual QString GetCurText();                            //获取当前界面展示文案
    virtual void SetEnableEdit(bool bEdit);                  //设置是否允许编辑

    virtual QString currentText();                     //获取当前界面展示文案
    virtual void setCurrentIndex(int index);
    virtual void setCurrentText(const QString& text);


    /*新增业务接口*/
    QStringList GetAllItemStrList();            //获取下拉框所有值的接口
    void AddItem(const QString& itemStr, const QVariant& auserData = QVariant());       //添加下拉框的值
    void AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList = QList<QVariant>());       //添加下拉框的值
    void RemoveItem(const QString& itemStr);    //清空下拉框的值
    void ClearItems();                          //清空下拉框所有值

    /*绘制正方形框*/
    void RegisterSquareColor(int index, const QColor& color);
    void UnRegisterSquareColor(int index);

    /*获取界面*/
    MtComboBox* GetMtComboBox();

signals:
    void sigClicked(int);
    void currentTextChanged(const QString& newText);    //文案改变了
    void currentIndexChanged(int index);
    void sigUIUpdated(int index);

protected slots:
    void slotCurrentTextChanged(const QString& text);
    void slotCurrentIndexChanged(int index);
protected:
    bool eventFilter(QObject* obj, QEvent* evt);
    void resizeEvent(QResizeEvent* event);          //保证...
    void mousePressEvent(QMouseEvent* event);

private:
    Ui::QCustMtComboBox* ui = nullptr;
    MtComboBox* m_comboBox = nullptr;
    /// <summary>
    /// 特殊样式代理
    /// </summary>
    QCustMtComboBoxDelegate* m_delegate = nullptr;
};

/*
下拉框带有正方形绘制的组件
*/
class MtComboBoxDrawSquareColor :
    public MtComboBox
{
    Q_OBJECT
public:
    MtComboBoxDrawSquareColor(QWidget* parent = Q_NULLPTR);
    ~MtComboBoxDrawSquareColor();

    /*绘制正方形框接口*/
    void RegisterSquareColor(int index, const QColor& color);
    void UnRegisterSquareColor(int index);

protected:
    void paintEvent(QPaintEvent* event);
private:
    void DrawSquareAppendText(QPaintEvent* event);     //绘制提示小正方形

private:
    QMap<int, QColor> m_indexSquareColorMap;
};


class QCustMtComboBoxDelegate : public QStyledItemDelegate
{
    Q_OBJECT
public:
    explicit QCustMtComboBoxDelegate(QObject* parent = nullptr) : QStyledItemDelegate(parent)
    {
    }
    void setLastSelectedIndex(int index)
    {
        m_lastSelectedIndex = index;
    }
    void paint(QPainter* painter, const QStyleOptionViewItem& option, const QModelIndex& index) const override
    {
        // 不再调用基类的绘制函数，而是手动绘制图标和文本
        // 获取文本
        QString text = index.data(Qt::DisplayRole).toString();
        // 获取文本的宽度
        QFontMetrics fontMetrics(option.font);
        int textWidth = fontMetrics.width(text);
        // 计算左侧图标的位置
        int iconWidth = 0;

        if (option.state & QStyle::State_Selected)
        {
            painter->fillRect(option.rect, CMtCoreWidgetUtil::formatColor("rgba(@colorA2,1)"));
        }

        if (option.widget)
        {
            // 检查是否是下拉列表的绘制过程，如果是，则绘制图标
            if (index.data(Qt::DecorationRole).isValid())
            {
                QIcon icon = qvariant_cast<QIcon>(index.data(Qt::DecorationRole));
                iconWidth = icon.actualSize(option.rect.size()).width();
                QRect iconRect = option.rect.adjusted(9, (option.rect.height() - 11) / 2, 2 + iconWidth, 0); // 调整图标位置使其紧贴在左侧，并在垂直方向上居中
                painter->setRenderHint(QPainter::SmoothPixmapTransform, true); // 开启平滑缩放
                icon.paint(painter, iconRect, Qt::AlignLeft);
            }
        }

        // 根据状态设置文本颜色
        if (index.row() == m_lastSelectedIndex)
        {
            painter->setPen(CMtCoreWidgetUtil::formatColor("rgba(@colorB1, 1)")); // 选中状态下文本颜色为蓝色
        }
        else
        {
            painter->setPen(CMtCoreWidgetUtil::formatColor("rgba(@colorA4, 1)")); // 非选中状态下文本颜色为白色
        }

        // 绘制文本，调整位置使其左对齐
        QRect textRect = option.rect.adjusted(22 + 2, 0, 0, 0); // 调整文本位置，使其紧跟在图标后面
        painter->setRenderHint(QPainter::TextAntialiasing, true); // 开启文本抗锯齿
        painter->drawText(textRect, Qt::AlignLeft | Qt::AlignVCenter, text);
        //QStyledItemDelegate::paint(painter, option, index);

        // 在右侧绘制打勾图标（如果项被选中）
        if (index.row() == m_lastSelectedIndex)
        {
            QPixmap checkIcon(":/AccuUIComponentImage/images/icon_chosen.png");
            int targetWidth = 17; // 设置目标宽度
            int targetHeight = 16; // 计算相应的高度，保持宽高比
            QSize scaledSize(targetWidth, targetHeight);
            QRect checkIconRect = option.rect.adjusted(option.rect.width() - scaledSize.width() - 2, 0, 0, 0);
            //painter->setRenderHint(QPainter::SmoothPixmapTransform, true); // 开启平滑缩放
            painter->drawPixmap(checkIconRect, checkIcon.scaled(scaledSize));
        }
    }
protected:
    QSize sizeHint(const QStyleOptionViewItem& option, const QModelIndex& index) const override
    {
        return QStyledItemDelegate::sizeHint(option, index);
    }

    // 重写初始化样式选项方法以设置背景颜色
    void initStyleOption(QStyleOptionViewItem* option, const QModelIndex& index) const override
    {
        QStyledItemDelegate::initStyleOption(option, index);

        if (option->state & QStyle::State_Selected)
        {
            // 如果鼠标悬停在项上，则设置背景颜色为灰色
            option->backgroundBrush = QBrush(CMtCoreWidgetUtil::formatColor("rgba(@colorA2,1)")); // 灰色背景色
        }
    }
public slots:
    void SlotCheckIndexChanged(int index)
    {
        m_lastSelectedIndex = index;
    }
private:
    int m_lastSelectedIndex;
};

class NoIconComboBox : public MtComboBox
{
    Q_OBJECT
public:
    explicit NoIconComboBox(QWidget* parent = nullptr) : MtComboBox(parent)
    {
        connect(this, SIGNAL(currentIndexChanged(int)), this, SLOT(updateLineEditText(int)));
    }

private slots:
    void setLineEditTextWithoutIcon(const QString& text)
    {
        QLineEdit* lineEdit = qobject_cast<QLineEdit*>(QComboBox::lineEdit());

        if (lineEdit)
        {
            lineEdit->setText(text);
        }
    }

    void updateLineEditText(int index)
    {
        if (index >= 0)
        {
            // 设置编辑框的文本为当前选中项目的文本部分
            const QString text = itemText(index);
            setLineEditTextWithoutIcon(text);
        }
    }
};