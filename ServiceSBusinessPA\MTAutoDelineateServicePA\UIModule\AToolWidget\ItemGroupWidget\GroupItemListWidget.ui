<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GroupItemListWidgetClass</class>
 <widget class="QWidget" name="GroupItemListWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>400</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>GroupItemListWidget</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtListWidget" name="mtListWidget">
     <property name="scrollType">
      <enum>MtScrollBar::scrollbar1</enum>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtListWidget::listwidget2</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtListWidget</class>
   <extends>QListWidget</extends>
   <header>MtListWidget.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
