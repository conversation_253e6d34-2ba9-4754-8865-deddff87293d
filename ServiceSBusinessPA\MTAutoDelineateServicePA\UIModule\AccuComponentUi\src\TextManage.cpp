﻿#include "AccuComponentUi\Header\TextManage.h"
#include "QFontMetrics"
#include <QTextDocument>


TextManage::TextManage()
{
}


TextManage::~TextManage()
{
}

QString TextManage::GetElidedText(QFont font, QString str, int MaxWidth)
{
    QFontMetrics fontWidth(font);
    int width = fontWidth.width(str);

    if (width > MaxWidth)
    {
        str = fontWidth.elidedText(str, Qt::ElideRight, MaxWidth);
    }

    return str;
}

int TextManage::GetTextWidth(QFont font, QString str)
{
    QFontMetrics fontWidth(font);
    return fontWidth.width(str);
}

QString TextManage::EnsurePlainText(const QString& text)
{
    QString out;

    if (Qt::mightBeRichText(text))
    {
        // is html -> convert to plain text
        QTextDocument textDoc;
        textDoc.setHtml(text);
        out = textDoc.toPlainText();
    }
    else
    {
        out = text;
    }

    return out;
}
