<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTInformButton</class>
 <widget class="QWidget" name="QMTInformButton">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>94</width>
    <height>32</height>
   </rect>
  </property>
  <property name="minimumSize">
   <size>
    <width>17</width>
    <height>21</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QMTInformButton</string>
  </property>
  <property name="styleSheet">
   <string notr="true">#pushButton{
	background-color: rgb(37, 41, 48);
	background-image:url(:/AccuUIComponentImage/images/bell.png);
	border:none;
}

#pushButton:hover{
	background-image:url(:/AccuUIComponentImage/images/bell.png);
	border:none;
}</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QPushButton" name="pushButton">
        <property name="minimumSize">
         <size>
          <width>17</width>
          <height>21</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>17</width>
          <height>21</height>
         </size>
        </property>
        <property name="text">
         <string/>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_2" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QMTBubbleWidget" name="widget_bubble" native="true">
        <property name="minimumSize">
         <size>
          <width>6</width>
          <height>6</height>
         </size>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>QMTBubbleWidget</class>
   <extends>QWidget</extends>
   <header>AccuComponentUi\Header\qmtbubblewidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
