﻿#pragma once

#include "MtTemplateDialog.h"
#include "ui_RoiSettingDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"

class RoiSettingDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    RoiSettingDialog(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList, QWidget* parent = 0);

    /// <summary>
    /// 是否启用标签信息
    /// </summary>
    bool isEnableLabel();

    /// <summary>
    /// 是否启用ROI类型
    /// </summary>
    bool isEnableROIType();

    /// <summary>
    /// 是否启用标签颜色
    /// </summary>
    bool isEnableColor();

    /// <summary>
    /// 是否启用中文名
    /// </summary>
    bool isEnableChineseName();

    /// <summary>
    /// 是否启用描述
    /// </summary>
    bool isEnableDescription();

    /// <summary>
    /// 设置的分组信息
    /// </summary>
    QStringList getGroupList();

    /// <summary>
    /// 设置的描述
    /// </summary>
    QString getDescription();

protected slots:
    /// <summary>
    /// 是否启用标签库信息选项状态改变
    /// </summary>
    void slotEnableROINameStateChanged(int state);

    /// <summary>
    /// 是否应用所有组选项状态改变
    /// </summary>
    void slotEnableGroupStateChanged(int state);

    /// <summary>
    /// 是否应该所有描述选项状态改变
    /// </summary>
    void slotEnableDescriptionStateChanged(int state);

    /// <summary>
    /// 设置选项发生了改变，用于修改确定按钮启用状态
    /// </summary>
    void slotSettingChanged(int state);

    /// <summary>
    /// 分组信息发生改变
    /// </summary>
    void slotGroupChanged(int nIndex, int state, const QString& itemText);

protected:
    virtual void onBtnCloseClicked();
    virtual void onBtnRight1Clicked();
    virtual void onBtnRight2Clicked();

private:
    Ui::RoiSettingDialog ui;
};
