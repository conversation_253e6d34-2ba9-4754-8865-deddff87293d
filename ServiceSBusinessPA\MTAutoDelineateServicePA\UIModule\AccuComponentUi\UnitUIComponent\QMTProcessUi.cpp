﻿#include <QDate>
#include <QTime>
#include "AccuComponentUi\Header\UnitUIComponent\QMTProcessUi.h"
#include "ui_QMTProcessUi.h"
#include "CMtCoreDefine.h"
#include "AccuComponentUi\Header\QMTEnumDef.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"

QMTProcessUiParam::QMTProcessUiParam()
{
    _cellWidgetType = DELEAGATE_TYPE_PROCESSBAR;
}

QWidget* QMTProcessUiParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTProcessUi* processUi = new QMTProcessUi(parent);
    processUi->SetupCellWidget(*this);
    return processUi;
}

QMTProcessUi::QMTProcessUi(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTProcessUi;
    ui->setupUi(this);
    HideProcessBar(true);
    QString msg = tr("未勾画");
    ui->label_exportstatus->hide();
    SetStatusString(msg);
    InitTemplateStyle();
    // Language::setFontSize<QMTProcessUi>(this);
}

QMTProcessUi::~QMTProcessUi()
{
    MT_DELETE(ui);
}

bool QMTProcessUi::UpdateUi(const QVariant& updateData)
{
    bool bOK = false;
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        SetStatusString(text);
        return true;
    }
    else if (QMetaType::Int == userType)
    {
        int value = updateData.toInt();
        SetValue(value);
    }
    else if (updateData.canConvert<QMTProcessUi::ProcessStatusUpdateInfo>())
    {
        ProcessStatusUpdateInfo updateInfo = updateData.value<QMTProcessUi::ProcessStatusUpdateInfo>();

        if (updateInfo.delineateValue >= 0)
        {
            SetAutoDelineateValue(updateInfo.delineateValue, updateInfo.statusMsg);
        }
        else if (updateInfo.exportValue >= 0)
        {
            SetExportValue(updateInfo.exportValue, updateInfo.statusMsg);
        }
        else
        {
            HideProcessBar(true);
            SetStatusString(updateInfo.statusMsg);
        }

        bOK = true;
    }

    return bOK;
}

void QMTProcessUi::SetupCellWidget(QMTProcessUiParam& cellWidgetParam)
{
    QString styleSheetStr;
    QString textColor;
    int rgb[3];
    rgb[0] = cellWidgetParam._textColor.red();
    rgb[1] = cellWidgetParam._textColor.green();
    rgb[2] = cellWidgetParam._textColor.blue();
    double alpha = cellWidgetParam._textColor.alpha();
    alpha = alpha / 255;
    textColor = GetFontStyleSheetStr(cellWidgetParam._fontSize, QString::number(rgb[0]), QString::number(rgb[1]), QString::number(rgb[2]), QString::number(alpha, 'f', 1));
    styleSheetStr = "#status_label,#status2_label,#label_exportstatus{" + textColor + ";}";
    SetStatusStyle(styleSheetStr);
    SetSetStatusExportStyle(styleSheetStr);

    if (cellWidgetParam.delineateValue >= 0)
    {
        SetAutoDelineateValue(cellWidgetParam.delineateValue, cellWidgetParam.statusMsg);
    }
    else if (cellWidgetParam.exportValue >= 0)
    {
        SetExportValue(cellWidgetParam.exportValue, cellWidgetParam.statusMsg);
    }
    else
    {
        SetStatusString(cellWidgetParam.statusMsg);
    }
}

void QMTProcessUi::SetCustSheet(const QString& styleSheetStr)
{
    this->setStyleSheet(styleSheetStr);
}

void QMTProcessUi::HideProcessBar(bool isHide)
{
    if (isHide)
    {
        //ui->widget_status->hide();//不隐藏
        ui->progressBar->setValue(0);
        ui->progressBar->hide();
        ui->status2_label->hide();

        if (Language::type == English)
        {
            ui->status_label->setMinimumHeight(50);
        }
    }
    else
    {
        ui->widget_status->show();
        ui->progressBar->show();
        ui->status2_label->show();

        if (Language::type == English)
        {
            ui->status_label->setMinimumHeight(0);
        }
    }
}

void QMTProcessUi::SetValue(int percent)
{
    if (percent <= 0)
    {
        HideProcessBar(true);
    }
    else
    {
        //if (percent >= 100)
        //  percent = 99;   //防止显示不全
        HideProcessBar(false);
        QString percentStr = QString("%0%").arg(percent);
        ui->status2_label->setText(percentStr);
        ui->progressBar->setValue(percent);
    }
}

void QMTProcessUi::SetAutoDelineateValue(int value, QString msg)
{
    SetValue(value);
    SetStatusString(msg);
}

void QMTProcessUi::SetExportValue(int value, QString msg)
{
    SetValue(value);
    SetStatusString(msg);
}

void QMTProcessUi::SetPercentStyle(const QString& styleSheetStr)
{
    if (styleSheetStr.size() > 0)
    {
        ui->progressBar->setStyleSheet(styleSheetStr);
    }
}

void QMTProcessUi::SetStatusString(QString msg, QString style)
{
    //if (msg.size() > 0)   //这边不做判断
    {
        ui->status_label->setText(msg);
    }

    if (style.size() > 0)
    {
        QString tempStyle = ui->status_label->styleSheet();

        if (tempStyle != style)
            ui->status_label->setStyleSheet(style);
    }
}

void QMTProcessUi::SetStatusExportString(QString msg, QString style)
{
    ui->label_exportstatus->show();

    if (msg.size() > 0)
    {
        ui->label_exportstatus->setText(msg);
    }
    else
    {
        ui->label_exportstatus->hide();
    }

    if (style.size() > 0)
        ui->label_exportstatus->setStyleSheet(style);
}

void QMTProcessUi::SetStatusStyle(const QString& styleSheetStr)
{
    ui->status_label->setStyleSheet(styleSheetStr);
    ui->status2_label->setStyleSheet(styleSheetStr);
}

void QMTProcessUi::SetSetStatusExportStyle(const QString& styleSheetStr)
{
    ui->label_exportstatus->setStyleSheet(styleSheetStr);
}

/*
{
    "QMTProcessUi": {
        "percent": "value",
        "percentStyle":"",
        "statusStr": "口腔正在勾画...",
        "statusStyle": ""
    }
}
*/
void QMTProcessUi::SetMode(QJsonObject& obj)
{
    // if (Language::type != English)
    //ui->status_label->setStyleSheet("font-size:12px");
    if (obj.contains(TOString(QMTProcessUi)))
    {
        QJsonObject subObj = obj.value(TOString(QMTProcessUi)).toObject();

        if (subObj.contains(TOString(percent)))
        {
            QString value = subObj.value(TOString(percent)).toString();
            SetValue(value.toInt());
        }

        if (subObj.contains(TOString(percentStyle)))
        {
            QString style = subObj.value(TOString(percentStyle)).toString();
            SetPercentStyle(style);
        }

        QString statusStr = subObj.value(TOString(statusStr)).toString();
        QString statusStyle = subObj.value(TOString(statusStyle)).toString();

        if (subObj.contains(TOString(exportStr)))
        {
            QString exportStr = subObj.value(TOString(exportStr)).toString();
            QString exportStyle = subObj.value(TOString(exportStyle)).toString();
            SetStatusExportString(exportStr, exportStyle);
        }

        SetStatusString(statusStr, statusStyle);
    }
}

void QMTProcessUi::SetEnglishSize()
{
    ui->status_label->setMinimumHeight(0);
}

void QMTProcessUi::InitTemplateStyle()
{
    QString sheetStr;
    sheetStr += "QLabel{border-width:0px;color:rgba(@colorA3, 0.6);font-size : 12px;}";
    CMtCoreWidgetUtil::formatStyleSheet(sheetStr);
    SetCustSheet(sheetStr);
}


