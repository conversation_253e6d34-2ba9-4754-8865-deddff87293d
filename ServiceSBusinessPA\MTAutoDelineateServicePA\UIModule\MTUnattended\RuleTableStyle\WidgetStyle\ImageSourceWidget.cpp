﻿#include "ImageSourceWidget.h"

ImageSourceWidget::ImageSourceWidget(const QString imageSourceUniqueKey, QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    m_imageSourceUniqueKey = imageSourceUniqueKey;
    ui.mtLabel_serverName->clear();
    ui.mtLabel_serverType->clear();
}

ImageSourceWidget::~ImageSourceWidget()
{
}

/// <summary>
/// 更新UI
/// </summary>
/// <param name="enableImgPath">[IN]true开关图片路径</param>
/// <param name="serverName">[IN]本地服务器名</param>
/// <param name="serverType">[IN]本地服务器类型</param>
void ImageSourceWidget::updateUI(const QString enableImgPath, const QString& serverNameStr, const QString& serverTypeStr)
{
    if (enableImgPath.isEmpty() == false)
        ui.mtLabel_enable->setPixmap(QPixmap(enableImgPath));

    if (serverNameStr.isEmpty() == false)
        ui.mtLabel_serverName->setText(serverNameStr);

    if (serverTypeStr.isEmpty() == false)
        ui.mtLabel_serverType->setText(serverTypeStr);
}

/// <summary>
/// 获取影像来源唯一标识
/// </summary>
QString ImageSourceWidget::getImageSourceUniqueKey()
{
    return m_imageSourceUniqueKey;
}

/// <summary>
/// 获取服务器名
/// </summary>
QString ImageSourceWidget::getServerName()
{
    return ui.mtLabel_serverName->text();
}
