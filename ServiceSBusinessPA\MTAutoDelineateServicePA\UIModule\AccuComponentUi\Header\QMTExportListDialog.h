﻿#pragma once

#include <QWidget>
#include <QDialog>
#include <QJsonArray>
#include <QJsonObject>
#include "QMouseEvent"
#include "QMTAbstractTableView.h"


enum DialogType
{
    Dialog_None,
    Dialog_DiceValue,
};
namespace Ui
{
class QMTExportListDialog;
};
class  QMTExportListDialog : public QDialog
{
    Q_OBJECT

    ///******** 属性 ******/
    //public:
    //    QString ExportExcelName; // 导出时Excel的名称
    //    QString ExportSheetName; // 导出时Excel中sheet的名称

public:
    QMTExportListDialog(QWidget* parent = Q_NULLPTR, DialogType type = Dialog_None);
    ~QMTExportListDialog();

    void InitTableView(QMTAbsRowWidgetItemParam& perItemParam);  //初始化表格
    QMTAbstractTableView* GetTableView();           //获取中间一级列表

    //初始化数据(ICellWidgetParam*,外部必须用new，列表中管理释放，外部无需释放)
    bool SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QMap<int/*column*/, ICellWidgetParam*>>& cellWidgetParamMapMap);
    bool SetDataModel(const QStringList& rowValueList, QMap<QString/*rowValue*/, QStringList/*column string list*/>& rowColumnStrListMap);

    //设置UI参数部分
    void SetColumnCount(int columns);
    void SetColumnWidth(int column, int width);
    bool SetHorizontalHeaderList(QStringList value);
    void SetHorizontalHeaderVisible(bool);
    void SetBtnExportVisible(bool);
    void SetBtnShowAndHideVisible(bool);

    /////
    void SetDialogTitle(QString);                       //设置弹窗标题
    void SetDialogThumbnail(QString);                   //设置收束后的缩略图
    void SetDefaultExpName(QString);
    void SetCloseMessage(QString message1, QString message2);// 设置 slotCloseClicked 弹窗信息

signals:
    void sigRequestExportSheetFile();                   //请求导出
    void sigIsOpenOut(bool);                            //是否展开状态变化了
    void sigClickedClose();                             //关闭了弹窗
    void sigCellClicked(QString);                       //点击了某一行

protected slots:
    virtual void slotExportClicked();                       //点击了导出按键
    virtual void slotShowAndHideClicked();                  //点击了收束和展开按键
    virtual void slotCloseClicked();                        //点击了关闭按键
    virtual void slotCellClicked(QString uniqueValue);      //点击了一级列表某一行


protected:
    bool nativeEvent(const QByteArray& eventType, void* message, long* result);
    void mousePressEvent(QMouseEvent* e);
    void mouseMoveEvent(QMouseEvent* e);
    void resizeEvent(QResizeEvent* size);
    void InitDialogType(DialogType type);
    //键盘按键消息
    virtual void keyPressEvent(QKeyEvent* ev);
protected:
    Ui::QMTExportListDialog* ui;
    DialogType _dialogType;                             //弹窗类型
    QMTAbstractTableView* _listTableView;               //中间一级列表部分
    bool _isShow = true;
    QString _dialogTitle;
    QString _dialogThumbnail;
    int boundaryWidth;
    QPoint clickPos;
    bool _isPress = false;
    QString _defaultExportName;
    bool _isHorizontalHeaderVisible = true;

    // 关闭弹窗的提示信息
    QString _closeMessage1;
    QString _closeMessage2;
};
