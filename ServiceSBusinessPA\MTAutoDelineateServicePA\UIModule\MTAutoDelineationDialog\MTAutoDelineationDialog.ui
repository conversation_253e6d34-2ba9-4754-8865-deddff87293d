<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MTAutoDelineationDialogClass</class>
 <widget class="QDialog" name="MTAutoDelineationDialogClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>911</width>
    <height>700</height>
   </rect>
  </property>
  <property name="acceptDrops">
   <bool>false</bool>
  </property>
  <property name="windowTitle">
   <string>MTAutoDelineationDialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrame" name="mtFrame">
     <layout class="QVBoxLayout" name="verticalLayout_2" stretch="1,0">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>6</number>
      </property>
      <item>
       <widget class="MtTabWidget" name="mtTabWidget">
        <property name="currentIndex">
         <number>0</number>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtTabWidget::tabwidget1</enum>
        </property>
        <widget class="QWidget" name="tabROI">
         <attribute name="title">
          <string>选择ROI</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <item>
           <widget class="AutoSketchRoiWidget" name="widget_roi" native="true">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <layout class="QVBoxLayout" name="verticalLayout_3">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QWidget" name="tabModel">
         <attribute name="title">
          <string>选择模板</string>
         </attribute>
         <layout class="QVBoxLayout" name="verticalLayout_5">
          <item>
           <widget class="AutoSketchTemplateWidget" name="widget_model" native="true">
            <layout class="QHBoxLayout" name="horizontalLayout_2"/>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
      <item>
       <widget class="MtFrameEx" name="frame_func_base">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>30</height>
         </size>
        </property>
        <layout class="QVBoxLayout" name="verticalLayout_6">
         <property name="spacing">
          <number>16</number>
         </property>
         <property name="leftMargin">
          <number>0</number>
         </property>
         <property name="topMargin">
          <number>0</number>
         </property>
         <property name="rightMargin">
          <number>0</number>
         </property>
         <property name="bottomMargin">
          <number>0</number>
         </property>
         <item>
          <layout class="QFormLayout" name="formLayout">
           <property name="horizontalSpacing">
            <number>16</number>
           </property>
           <property name="verticalSpacing">
            <number>8</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item row="2" column="0">
            <widget class="MtCheckBox" name="mtCheckBox_export">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>勾画完成后自动导出</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
           <item row="2" column="1">
            <widget class="QWidget" name="widget" native="true">
             <layout class="QHBoxLayout" name="horizontalLayout_3">
              <property name="spacing">
               <number>8</number>
              </property>
              <property name="leftMargin">
               <number>0</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="rightMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="MtComboBox" name="mtComboBox_export">
                <property name="minimumSize">
                 <size>
                  <width>310</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>310</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="elideMode">
                 <enum>Qt::ElideRight</enum>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtComboBox::combobox1</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="MtLineEdit" name="mtLineEdit_export">
                <property name="minimumSize">
                 <size>
                  <width>0</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>16777215</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="readOnly">
                 <bool>true</bool>
                </property>
                <property name="elideMode">
                 <enum>Qt::ElideRight</enum>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtLineEdit::lineedit1</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="MtPushButton" name="mtPushButton_export">
                <property name="minimumSize">
                 <size>
                  <width>40</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="maximumSize">
                 <size>
                  <width>40</width>
                  <height>30</height>
                 </size>
                </property>
                <property name="text">
                 <string>....</string>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtPushButton::pushbutton13</enum>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item row="1" column="1">
            <widget class="MtComboBox" name="mtComboBox_rt">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>16777215</width>
               <height>30</height>
              </size>
             </property>
             <property name="elideMode">
              <enum>Qt::ElideRight</enum>
             </property>
             <property name="viewTextElideMode">
              <enum>Qt::ElideRight</enum>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtComboBox::combobox1</enum>
             </property>
            </widget>
           </item>
           <item row="1" column="0">
            <widget class="MtCheckBox" name="mtCheckBox_rt">
             <property name="minimumSize">
              <size>
               <width>0</width>
               <height>30</height>
              </size>
             </property>
             <property name="text">
              <string>合并已有Struct</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtCheckBox::checkbox1</enum>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtPushButton" name="mtPushButton_modelRoiSetting">
             <property name="text">
              <string>模型和ROI设置</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtPushButton::pushbutton5</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
          </layout>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtTabWidget</class>
   <extends>QTabWidget</extends>
   <header>MtTabWidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrame</class>
   <extends>QFrame</extends>
   <header>MtFrame.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>AutoSketchTemplateWidget</class>
   <extends>QWidget</extends>
   <header>MTAutoDelineationDialog/AutoSketchSub/autosketchtemplatewidget.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>AutoSketchRoiWidget</class>
   <extends>QWidget</extends>
   <header>MTAutoDelineationDialog/AutoSketchSub/autosketchroiwidget.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
