﻿#pragma once

#include <QListWidget>
#include <QEvent>
#include <QTimer>
#include "MtComboBox.h"
#include "MtLineEdit.h"

#define ShowComboBoxSearchBar 1

namespace n_mtautodelineationdialog
{

class MultiSelectComboBox : public MtComboBox
{
    Q_OBJECT

public:
    MultiSelectComboBox(QWidget* parent = Q_NULLPTR);
    ~MultiSelectComboBox();

    //添加一条选项
    void addItem(const QString& _text, const QVariant& _variant = QVariant());

    //添加多条选项
    void addItems(const QStringList& _text_list);

    //返回当前选中选项--所有选择
    QStringList currentText();

    //返回当前选择项的索引
    QList<int> currentIndex();

    //返回当前选项条数
    int count()const;

    //设置搜索框默认文字
    void SetSearchBarPlaceHolderText(const QString _text);

    //设置文本框默认文字
    void SetPlaceHolderText(const QString& _text);

    //下拉框状态恢复默认
    void ResetSelection();

    //清空所有内容
    void clear();

    //文本框内容清空
    void TextClear();

    //设置选中文本--单
    void setCurrentText(const QString& _text);

    //设置选中文本--多
    void setCurrentText(const QStringList& _text_list);

    //设置当前索引--单
    void setCurrentIndex(int index);

    //设置当前索引--多
    void setCurrentIndex(const QList<int>& indexList);

    //设置禁用的选项列表
    void setDisabledIndex(const QList<int>& indexList);

    //设置搜索框是否禁用
    void SetSearchBarHidden(bool _flag);

    //设置索引选择状态--单
    void setItemState(int index, Qt::CheckState state);

    //设置多个索引选择状态--多
    void setItemsState(const QList<int>& indexList, Qt::CheckState state);

    //设置索引选择状态--单
    void setItemState(const QString& text, Qt::CheckState state);

    //设置多个索引选择状态--多
    void setItemsState(const QList<QString>& textList, Qt::CheckState state);

protected:

    //事件过滤器
    virtual bool eventFilter(QObject* watched, QEvent* event);

    //滚轮事件
    virtual void wheelEvent(QWheelEvent* event);

    //按键事件
    virtual void keyPressEvent(QKeyEvent* event);

protected:
    void SetLineEditText(const QString& text);

private slots:

    //槽函数：文本框文本变化
    void stateChange(int _row);

    //槽函数：搜索框文本变化
    void onSearch(const QString& _text);

    //槽函数：点击下拉框选项
    void itemClicked(int _index);

signals:

    //信号：发送当前选中选项
    void sigSelectionChange(int nIndex, int state, const QString& itemText);
    void sigSelectedTextChange(const QString& editText);

private:
    //下拉框
    QListWidget* m_list_widget;
    //文本框
    MtLineEdit* m_lineEdit;
#if ShowComboBoxSearchBar
    //搜索框
    MtLineEdit* m_search_bar;
#endif
    //搜索框显示标志
    bool m_hidden_flag;
};

}
