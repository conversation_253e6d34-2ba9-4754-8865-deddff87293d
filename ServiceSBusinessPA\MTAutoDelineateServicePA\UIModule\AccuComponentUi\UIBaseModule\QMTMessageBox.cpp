﻿#include "AccuComponentUi\Header\QMTMessageBox.h"
#include <QHelpEvent>
#include <QToolTip>
#include <QDebug>
#include <QGraphicsDropShadowEffect>
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "ui_QMTMessageBox.h"


/// <summary>
/// 构造
/// 1. 无边框，工具类窗体
/// </summary>
/// <param name="parent">父窗体</param>
/// <returns></returns>
QMTMessageBox::QMTMessageBox(QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint | Qt::Tool), ui(nullptr)
{
    ui = new Ui::QMTMessageBox;
    ui->setupUi(this);    // Create the UI
    this->setWindowFlags(Qt::Dialog | Qt::FramelessWindowHint | Qt::WindowStaysOnTopHint | Qt::SubWindow);
    ui->messageLabel->setProperty(QssPropertyKey, QssPropertyLabelThirdTitle);
    ui->messageLabel2->setProperty(QssPropertyKey, QssLabelThirdTitleComment);
    QApplication::restoreOverrideCursor();      //恢复鼠标状态
    this->setAttribute(Qt::WA_TranslucentBackground);
    this->setFixedHeight(198);
    darkenWidget = new DarkeningWidget();// 不能继承this，否则会显示在最前面
    // 1. 模态对话框
    setModal(true);
    setAttribute(Qt::WA_TranslucentBackground);
    // 2. 默认设置，yes按键获取焦点，设置message pixelSize（由于需要在显示之前对字符串长度进行判断）
    ui->pushButton_yes->setFocus();
    resultButton = QMessageBox::NoButton;
    InitTemplateStyle();
    ui->messageLabel2->setWordWrap(true);
    // 3. 信号槽，设置返回值，关闭窗体
    connect(ui->pushButton_cancel, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Cancel; this->close();
    });//this->setResult(QMessageBox::Cancel);
    connect(ui->pushButton_no, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::No; ; this->close();
    });
    connect(ui->pushButton_yes, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Yes;; this->close();
    });
    connect(ui->pushButton_save, &QPushButton::clicked, this, [&]
    {
        this->resultButton = QMessageBox::Save; ; this->close();
    });
    //////悬浮效果
    shadow_effect = new QGraphicsDropShadowEffect(this);
    shadow_effect->setOffset(4, 4);
    shadow_effect->setBlurRadius(20);//目前测试20效果最佳
    shadow_effect->setColor(QColor(0, 0, 0, 40));
    ui->horizontalLayout->setMargin(12);
    ui->frame->setGraphicsEffect(shadow_effect);
}

QMTMessageBox::~QMTMessageBox()
{
    if (ui)
    {
        delete ui;
        ui = nullptr;
    }

    if (darkenWidget != NULL)
    {
        darkenWidget->hide();
        delete darkenWidget;
    }

    if (shadow_effect != NULL)
    {
        delete shadow_effect;
        shadow_effect = nullptr;
    }
}

/// <summary>
/// 测试用，不建议直接使用
/// </summary>
/// <returns></returns>
QMessageBox::StandardButton QMTMessageBox::getResultButton()
{
    return this->resultButton;
}

void QMTMessageBox::About(QString message, QString buttonText /*= tr("确认")*/, bool cancel/*= true*/,
                          bool hideLogo /*= true*/, bool hideDarken /*= true*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        messagebox.SetShowButtons(QMessageBox::Yes | QMessageBox::Cancel);
    }
    else
    {
        messagebox.SetShowButtons(QMessageBox::Yes);
    }

    //messagebox.SetButtonText(QList<QString>() << buttonText);
    messagebox.SetYesBtnText(buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.HideSaveButton();
    messagebox.HideNoButton();
    messagebox.exec();
}


void QMTMessageBox::About(QList<QString> messageList, QString buttonText /*= tr("确认")*/, bool cancel /*= true*/, bool hideLogo /*= false*/, QString logoPath /*= ""*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        messagebox.SetShowButtons(QMessageBox::Yes | QMessageBox::Cancel);
    }
    else
    {
        messagebox.SetShowButtons(QMessageBox::Yes);
    }

    messagebox.SetYesBtnText(buttonText);

    if (messageList.size() >= 2)
    {
        messagebox.SetMessage1(messageList[0], false);
        messagebox.SetMessage2(messageList[1]);
    }
    else if (1 == messageList.size())
    {
        messagebox.SetMessage1(messageList[0]);
    }

    messagebox.HideLogo(hideLogo);
    messagebox.HideSaveButton();
    messagebox.HideNoButton();

    if (messageList.size() >= 2 && hideLogo)
    {
        messagebox.ui->horizontalSpacer_8->changeSize(50, 20);
        messagebox.ui->messageLabel->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
        messagebox.ui->messageLabel2->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    }

    messagebox.exec();
}

QMessageBox::StandardButton QMTMessageBox::Information(bool cancel, QList<QString> messageList, bool hideLogo /*= true*/, QString logoPath /*= ""*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    messagebox.SetShowButtons(buttons);
    QStringList buttonText;
    buttonText << "" << tr("取消") << tr("确定");
    messagebox.SetButtonText(buttonText);

    if (messageList.size() > 1)
    {
        messagebox.SetMessage1(messageList[0], false);
        messagebox.SetMessage2(messageList[1]);
    }
    else
    {
        messagebox.SetMessage1(messageList[0]);
    }

    messagebox.HideLogo(hideLogo);

    if (logoPath.size() > 0)
    {
        messagebox.SetIcon(logoPath);
    }

    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton QMTMessageBox::Information(bool cancel, QList<QString> messageList, QString noStyle /*= ""*/, QString OkStyle /*= ""*/, bool hideLogo /*= true*/, QString logoPath /*= ""*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    messagebox.SetShowButtons(buttons);
    QStringList buttonText;
    buttonText << "" << tr("取消") << tr("确定");
    messagebox.SetButtonText(buttonText);

    if (noStyle.size())
    {
        messagebox.SetNoBtnStyle(noStyle);
    }

    if (OkStyle.size())
    {
        messagebox.SetYesBtnStyle(OkStyle);
    }

    if (messageList.size() > 1)
    {
        messagebox.SetMessage1(messageList[0], false);
        messagebox.SetMessage2(messageList[1]);
    }
    else
    {
        messagebox.SetMessage1(messageList[0]);
    }

    messagebox.HideLogo(hideLogo);

    if (logoPath.size() > 0)
    {
        messagebox.SetIcon(logoPath);
    }

    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton QMTMessageBox::Information(QString message, bool cancel /*= true*/, bool hideLogo /*= true*/, QString logoPath /*= ""*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    messagebox.SetShowButtons(buttons);
    QStringList buttonText;
    buttonText << "" << tr("取消") << tr("确定");
    messagebox.SetButtonText(buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);

    if (logoPath.size() > 0)
    {
        messagebox.SetIcon(logoPath);
    }

    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton QMTMessageBox::ShowWarning(bool cancel, QString message, QString message2,
                                                       QString noStyle, QString yesStyle, bool hideDarken, QWidget* parent)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    //QStringList buttonText;
    //buttonText << tr("确定") << tr("取消");
    messagebox.HideDarken(hideDarken);
    messagebox.SetShowButtons(buttons);
    messagebox.SetNoBtnText(tr("取消"));
    messagebox.SetYesBtnText(tr("确定"));

    if (0 == noStyle.size())
    {
        noStyle = "#pushButton_no{background-color: rgb(78,156,213);color: rgb(@colorA4);}";
        noStyle += "#pushButton_no:hover{background-color: rgb(60,143,203);}";
    }

    CMtCoreWidgetUtil::formatStyleSheet(noStyle);
    messagebox.SetNoBtnStyle(noStyle);

    if (0 == yesStyle.size())
    {
        yesStyle = "#pushButton_yes{background-color: rgb(@colorB4);color: rgb(@colorA4);}";
        yesStyle += "#pushButton_yes:hover{background-color: rgba(@colorB4, 0.6);}";
    }

    CMtCoreWidgetUtil::formatStyleSheet(yesStyle);
    messagebox.SetYesBtnStyle(yesStyle);

    // messagebox.SetButtonText(buttonText);

    if (message2.size() > 0)
    {
        messagebox.SetMessage1(message, false);
        messagebox.SetMessage2(message2);
    }
    else
    {
        messagebox.SetMessage1(message);
    }

    //messagebox.HideLogo(hideLogo);
    messagebox.HideLogo(false);
    messagebox.SetIcon("image: url(:/AccuUIComponentImage/images/warning.png);");
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton QMTMessageBox::ShowDeleteWarning(bool cancel, QString message, QString message2 /*= ""*/, bool hideLogo /*= true*/, bool hideDarken /*= true*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;
    QString noStyle = "#pushButton_no{background-color: rgb(78,156,213);color:rgb(@colorA4);border-width:0px;}"
        "#pushButton_no:hover{background-color: rgb(60,143,203);}";
    QString yesStyle = "#pushButton_yes{background-color:rgb(@colorB4);color:rgb(@colorA4);border-width:0px;}"
        "#pushButton_yes:hover{background-color: rgb(194,86,86);}";
    CMtCoreWidgetUtil::formatStyleSheet(noStyle);
    CMtCoreWidgetUtil::formatStyleSheet(yesStyle);

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    QStringList buttonText;
    buttonText << tr("确定") << tr("取消");
    messagebox.HideDarken(hideDarken);
    messagebox.SetShowButtons(buttons);
    messagebox.SetNoBtnText(tr("取消"));
    messagebox.SetYesBtnText(tr("确定"));

    if (noStyle.size())
    {
        messagebox.SetNoBtnStyle(noStyle);
    }

    if (yesStyle.size())
    {
        messagebox.SetYesBtnStyle(yesStyle);
    }

    ///messagebox.SetButtonText(buttonText);

    if (message2.size() > 0)
    {
        messagebox.SetMessage1(message, false);
        messagebox.SetMessage2(message2);
    }
    else
    {
        messagebox.SetMessage1(message);
    }

    messagebox.HideLogo(hideLogo);
    messagebox.SetIcon("image: url(:/AccuUIComponentImage/images/warning.png);");
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton QMTMessageBox::ShowSaveAndNo(bool cancel, QString message, bool hideLogo /*= true*/, bool hideDarken /*= true*/, QWidget* parent /*= Q_NULLPTR*/)
{
    QMTMessageBox messagebox(parent);
    QMessageBox::StandardButtons buttons;

    if (cancel)
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else
    {
        buttons = QMessageBox::Yes | QMessageBox::No;
    }

    QStringList buttonText;
    buttonText << "" << tr("不保存") << tr("保存");
    messagebox.HideDarken(hideDarken);
    messagebox.SetShowButtons(buttons);
    messagebox.SetButtonText(buttonText);
    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

QMessageBox::StandardButton QMTMessageBox::ShowSelectBox(QString message, QStringList buttonTextList, bool hideLogo /*= true*/)
{
    QMTMessageBox messagebox(NULL);
    QMessageBox::StandardButtons buttons;

    if (2 == buttonTextList.size())
    {
        buttons = QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }
    else if (3 == buttonTextList.size())
    {
        buttons = QMessageBox::Save | QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel;
    }

    QString yesStyle = "#pushButton_yes{font-size:12px;min-width:80px;min-height:30px;max-width:80px;max-height:30px;background-color: rgb(56,67,85);color: rgba(188,186,186,1);border-style: outset; border-width: 1px;  border-color: rgba(188,186,186,0.34);}"
        "#pushButton_yes:hover{background-color:rgba(216, 216, 216, 0.1);}";
    messagebox.SetYesBtnStyle(yesStyle);
    messagebox.HideDarken(false);
    messagebox.SetShowButtons(buttons);

    if (2 == buttonTextList.size())
    {
        messagebox.SetNoBtnText(buttonTextList[0]);
        messagebox.SetYesBtnText(buttonTextList[1]);
    }
    else
    {
        messagebox.SetButtonText(buttonTextList);
    }

    messagebox.SetMessage1(message);
    messagebox.HideLogo(hideLogo);
    messagebox.exec();
    return (QMessageBox::StandardButton)messagebox.resultButton;
}

/// <summary>
/// 设置显示的button
/// </summary>
/// <param name="buttons">
/// Yes: 显示yes按键
/// No: 显示no按键
/// Save: 显示save按键
/// Cancel: 显示cancel按键
/// </param>
void QMTMessageBox::SetShowButtons(QMessageBox::StandardButtons buttons)
{
    if ((buttons & QMessageBox::Yes) == QMessageBox::NoButton)
        this->HideYesButton(true);
    else
        this->HideYesButton(false);

    if ((buttons & QMessageBox::No) == QMessageBox::NoButton)
        this->HideNoButton(true);
    else
        this->HideNoButton(false);

    if ((buttons & QMessageBox::Save) == QMessageBox::NoButton)
        this->HideSaveButton(true);
    else
        this->HideSaveButton(false);

    if ((buttons & QMessageBox::Cancel) == QMessageBox::NoButton)
        this->HideCancelButton(true);
    else
        this->HideCancelButton(false);
}

/// <summary>
/// 隐藏Save按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void QMTMessageBox::HideSaveButton(bool value)
{
    if (value)
        ui->pushButton_save->hide();
    else
        ui->pushButton_save->show();
}

/// <summary>
/// 隐藏No按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void QMTMessageBox::HideNoButton(bool value)
{
    if (value)
        ui->pushButton_no->hide();
    else
        ui->pushButton_no->show();
}
/// <summary>
/// 隐藏Yes按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void QMTMessageBox::HideYesButton(bool value)
{
    if (value)
        ui->pushButton_yes->hide();
    else
        ui->pushButton_yes->show();
}

/// <summary>
/// 隐藏Cancel按键
/// </summary>
/// <param name="value">true(默认):隐藏。 false: 显示</param>
void QMTMessageBox::HideCancelButton(bool value)
{
    if (value)
        ui->pushButton_cancel->hide();
    else
        ui->pushButton_cancel->show();
}

/// <summary>
/// 隐藏logo图片
/// 1. 隐藏的情况下Message居中，显示的情况下Message居左
/// 2. 隐藏情况下label宽度延申置350（默认305）
/// </summary>
/// <param name="value">true(默认):隐藏logo。 false: 显示</param>
void QMTMessageBox::HideLogo(bool value)
{
    if (value)
    {
        ui->label_image->hide();
        ui->verticalSpacer_manteia1->changeSize(0, 0);
        ui->verticalSpacer_manteia2->changeSize(0, 0);
        ui->messageLabel->setMinimumWidth(350);
        ui->messageLabel2->setMinimumWidth(350);
        ui->messageLabel->setAlignment(Qt::AlignHCenter | Qt::AlignBottom);
        ui->messageLabel2->setAlignment(Qt::AlignHCenter | Qt::AlignTop);
        ui->verticalSpacer_manteia3->changeSize(0, 0, QSizePolicy::Fixed, QSizePolicy::Fixed);
    }
    else
    {
        ui->label_image->show();
        ui->messageLabel->setAlignment(Qt::AlignLeft | Qt::AlignBottom);
        ui->messageLabel2->setAlignment(Qt::AlignLeft | Qt::AlignTop);
        ui->verticalSpacer_manteia3->changeSize(16, 20, QSizePolicy::Fixed, QSizePolicy::Fixed);
    }
}

/// <summary>
/// 是否显示全屏黑色背景
/// </summary>
/// <param name="value"></param>
void QMTMessageBox::HideDarken(bool value)
{
    if (value)
        this->darkenWidget->hide();
    else
        this->darkenWidget->show();
}

void QMTMessageBox::SetYesBtnStyle(QString style)
{
    ui->pushButton_yes->setStyleSheet(style);
    //this->pushButton_yes->repaint();
    ui->pushButton_yes->update();
}

void QMTMessageBox::SetMessage1Style(QString styleStr)
{
    ui->messageLabel->setStyleSheet(styleStr);
}

void QMTMessageBox::SetMessage2Style(QString styleStr)
{
    ui->messageLabel2->setStyleSheet(styleStr);
}

/// <summary>
/// 设置label和label2的text
/// 1. label2 样式为QLabel_Dot，字符串超出自动截断为…
/// </summary>
/// <param name="messages">字符串数量为label数量（2个），顺序为label,label2</param>
void QMTMessageBox::SetMessage(QList<QString> messages)
{
    if (messages.count() > 0)
    {
        this->SetMessage1(messages.at(0), false);
    }

    if (messages.count() > 1 && !messages.at(1).isEmpty())
    {
        this->SetMessage2(messages.at(1));
    }
    else
        ui->messageLabel2->hide();
}

/// <summary>
/// 设置label的text
/// 1. 文本截断，过长的字符串末尾变成 …
/// 2. 隐藏label2
/// </summary>
/// <param name="message">label中需要显示的文字</param>
/// <param name="newLine">true(默认):超长启动新行，总共最多两行。false:不启动新行</param>
/// <param name="hideLabel2">true(默认）：隐藏label2。false:显示</param>
void QMTMessageBox::SetMessage1(QString message, bool hideLabel2)
{
    SetMessageWidth(message, ui->messageLabel);

    // 2. 隐藏label2
    if (hideLabel2)
    {
        ui->verticalSpacer_9->changeSize(0, 0, QSizePolicy::Fixed, QSizePolicy::Fixed);
        ui->messageLabel2->hide();
    }
}
/// <summary>
/// 设置label2的text
/// 1. 文本截断，过长的字符串末尾变成 …
/// </summary>
/// <param name="message"></param>
/// <param name="newLine">true(默认):超长启动新行，总共最多两行。false:不启动新行</param>
void QMTMessageBox::SetMessage2(QString message)
{
    SetMessageWidth(message, ui->messageLabel2);
    ui->verticalSpacer_9->changeSize(10, 10, QSizePolicy::Fixed, QSizePolicy::Fixed);
}

/// <summary>
/// 设置icon 的图片
/// </summary>
/// <param name="urls"></param>
void QMTMessageBox::SetIcon(QString urls)
{
    //QString styleStr = QString("#label_image{image: url(%1);}").arg(urls);
    ui->label_image->setStyleSheet(urls);
}

/// <summary>
/// 设置所有button的text
/// </summary>
void QMTMessageBox::SetButtonText(QList<QString> texts)
{
    if (texts.count() > 0)
        ui->pushButton_save->setText(texts.at(0));

    if (texts.count() > 1)
        ui->pushButton_no->setText(texts.at(1));

    if (texts.count() > 2)
        ui->pushButton_yes->setText(texts.at(2));
}

void QMTMessageBox::SetSaveBtnText(QString text)
{
    ui->pushButton_save->setText(text);
}

void QMTMessageBox::SetNoBtnText(QString text)
{
    ui->pushButton_no->setText(text);
}

void QMTMessageBox::SetYesBtnText(QString text)
{
    ui->pushButton_yes->setText(text);
}

void QMTMessageBox::SetNoBtnStyle(QString style)
{
    ui->pushButton_no->setStyleSheet(style);
    //this->pushButton_no->repaint();
    ui->pushButton_no->update();
}

void QMTMessageBox::SetSaveBtnStyle(QString style)
{
    ui->pushButton_save->setStyleSheet(style);
    ui->pushButton_save->update();
}

/// <summary>
/// 设置messsage 的显示宽度
/// 1. 通过字符串对应的字体宽度设置label最小宽度
/// </summary>
/// <param name="message"></param>
/// <param name="label"></param>
void QMTMessageBox::SetMessageWidth(QString message, QLabel_Dot* label)
{
    QString temp = message;
    QStringList tmpmessage = message.split("\n");
    int tmpwidth = 0;

    foreach(QString msg, tmpmessage)
    {
        QFontMetrics fontWidth(label->font());
        int tmpwidth1 = fontWidth.width(msg);
        tmpwidth = tmpwidth1 > tmpwidth ? tmpwidth1 : tmpwidth;
    }

    if (tmpwidth > label->maximumWidth())
    {
        label->setMinimumWidth(label->maximumWidth());
    }
    else
    {
        label->setMinimumWidth(tmpwidth + 50);// ??? 单纯tmpwidth的情况下一直会有一个字符被强制换行
    }

    label->setTextElided(temp);
}

void QMTMessageBox::SetMessageWidth(QString message, QLabel* label)
{
    QString temp = message;
    QStringList tmpmessage = message.split("\n");
    int tmpwidth = 0;

    foreach(QString msg, tmpmessage)
    {
        QFontMetrics fontWidth(label->font());
        int tmpwidth1 = fontWidth.width(msg);
        tmpwidth = tmpwidth1 > tmpwidth ? tmpwidth1 : tmpwidth;
    }

    if (tmpwidth > label->maximumWidth())
    {
        label->setMinimumWidth(label->maximumWidth());
    }
    else
    {
        label->setMinimumWidth(tmpwidth + 50);// ??? 单纯tmpwidth的情况下一直会有一个字符被强制换行
    }

    label->setWordWrap(true);
    label->setText(temp);
}

void QMTMessageBox::InitTemplateStyle()
{
    QString styleStr;
    styleStr += "#frame{background-color: rgb(56, 67, 85); }";
    styleStr += "#widget_line{min-height:1px;max-height:1px;background-color: rgba(219,226,241,0.1);}";
    styleStr += "QPushButton{border-radius: 4px;}";
    styleStr += "#QMTMessageBox{border-radius:4px;}";
    styleStr += "#messageLabel{color:rgba(219,226,241,1);}";
    styleStr += "#messageLabel2{color:rgba(146,155,170,1);}";
    styleStr += "QFrame{border-radius:4px;}QPushButton{border-radius: 4px;}";
    styleStr += "#QMTMessageBox{border-radius:4px;}";
    this->setStyleSheet(styleStr);
    //设置message样式
    styleStr = "#messageLabel{color:rgba(219,226,241,1);}";
    SetMessage1Style(styleStr);
    styleStr = "#messageLabel2{color:rgba(146,155,170,1);}";
    SetMessage2Style(styleStr);
    styleStr = "image: url(:/AccuUIComponentImage/images/manteia.png);";
    SetIcon(styleStr);
    //设置按键样式
    styleStr = "#pushButton_save{background-color: rgb(56,67,85);color: rgba(188,186,186,1);border-style: outset; border-width: 1px;  border-color: rgba(188,186,186,0.34);}";
    styleStr += "#pushButton_save:hover{background-color: rgba(216,216,216,0.1);}";
    SetSaveBtnStyle(styleStr);
    styleStr = "#pushButton_no{background-color: rgb(56,67,85);color: rgba(188,186,186,1);border-style: outset; border-width: 1px;  border-color: rgba(188,186,186,0.34);}";
    styleStr += "#pushButton_no:hover{background-color: rgba(216,216,216,0.1);}";
    SetNoBtnStyle(styleStr);
    styleStr = "#pushButton_yes{background-color: rgb(78,156,213);color: rgb(@colorA4);}";
    styleStr += "#pushButton_yes:hover{background-color: rgb(60,143,203);}";
    styleStr = CMtCoreWidgetUtil::formatColorStr(styleStr);
    SetYesBtnStyle(styleStr);
    styleStr = "#pushButton_cancel{border-image:url(:/AccuUIComponentImage/images/btn_close.png)}";
    styleStr += "#pushButton_cancel:hover{ background-color: rgba(216,216,216,0.1);}";
    ui->pushButton_cancel->setStyleSheet(styleStr);
}
