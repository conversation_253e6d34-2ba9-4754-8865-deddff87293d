#include "ModelImportHelper.h"
#include <QNetworkRequest>
#include <QDebug>
#include <QFileInfo>
#include <QDir>
#include <QEventLoop>
#include <QCoreApplication>
#include <QApplication>
#include <QHttpMultiPart>
#include <QHttpPart>
#include <QTextStream>
#include <QDateTime>
#include "CommonUtil.h"

ModelImportHelper::ModelImportHelper()
    : m_networkManager(nullptr)
    , m_currentReply(nullptr)
    , m_uploadSuccess(false)
{
    m_networkManager = new QNetworkAccessManager(this);
    m_progressDlg.setHidden(true);
}

ModelImportHelper::~ModelImportHelper()
{
    if (m_currentReply)
    {
        m_currentReply->deleteLater();
    }
}

bool ModelImportHelper::UploadFile(const QString& filePath, QString& errMsg)
{
    m_uploadSuccess = false;
    m_errorMessage.clear();
    QString modelType = "algorithm";
    QString description = "manteia ai model";
    // 检查文件是否存在
    QFileInfo fileInfo(filePath);

    if (!fileInfo.exists())
    {
        errMsg = "File does not exist: " + filePath;
        return false;
    }

    // 构建上传URL
    QString ip;
    int port = -1;
    QString clientId;

    if (!CommonUtil::GetServerIpPortClientId(ip, port, clientId))
    {
        return false;
    }

    //上传请求接口
    QString uploadUrl = QString("http://%1:%2/api/model/upload").arg(ip).arg(port);
    // 创建multipart请求
    QHttpMultiPart* multiPart = new QHttpMultiPart(QHttpMultiPart::FormDataType);
    // 添加文件部分
    QHttpPart filePart;
    filePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant(QString("form-data; name=\"file\"; filename=\"%1\"").arg(fileInfo.fileName())));
    filePart.setHeader(QNetworkRequest::ContentTypeHeader, QVariant("application/octet-stream"));
    QFile* file = new QFile(filePath);

    if (!file->open(QIODevice::ReadOnly))
    {
        errMsg = "Failed to open file for reading";
        delete file;
        delete multiPart;
        return false;
    }

    filePart.setBodyDevice(file);
    file->setParent(multiPart); // 设置父对象，自动清理
    multiPart->append(filePart);
    // 添加模型类型
    QHttpPart modelTypePart;
    modelTypePart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant("form-data; name=\"modelType\""));
    modelTypePart.setBody(modelType.toUtf8());
    multiPart->append(modelTypePart);
    // 添加描述
    QHttpPart descriptionPart;
    descriptionPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant("form-data; name=\"description\""));
    descriptionPart.setBody(description.toUtf8());
    multiPart->append(descriptionPart);
    // 添加客户端ID
    QHttpPart clientIdPart;
    clientIdPart.setHeader(QNetworkRequest::ContentDispositionHeader, QVariant("form-data; name=\"clientId\""));
    clientIdPart.setBody(clientId.toUtf8());
    multiPart->append(clientIdPart);
    // 创建网络请求
    QNetworkRequest request;
    request.setUrl(QUrl(uploadUrl));
    request.setRawHeader("User-Agent", "ModelUpload/1.0");
    // 发送POST请求
    m_currentReply = m_networkManager->post(request, multiPart);
    multiPart->setParent(m_currentReply); // 设置父对象，自动清理
    // 连接信号
    connect(m_currentReply, &QNetworkReply::finished, this, &ModelImportHelper::OnUploadFinished);
    connect(m_currentReply, &QNetworkReply::uploadProgress, this, &ModelImportHelper::OnUploadProgress);
    connect(m_currentReply, QOverload<QNetworkReply::NetworkError>::of(&QNetworkReply::error), this, &ModelImportHelper::OnNetworkError);
    // 等待上传完成（最多120秒）
    QEventLoop loop;
    QTimer timeoutTimer;
    timeoutTimer.setSingleShot(true);
    timeoutTimer.setInterval(120000); // 120秒超时
    connect(this, &ModelImportHelper::SigUploadFinished, &loop, &QEventLoop::quit);
    connect(&timeoutTimer, &QTimer::timeout, &loop, &QEventLoop::quit);
    //打开进度窗口
    m_progressDlg.setWindowTitle(tr("模型导入中..."));
    m_progressDlg.setLabelText(tr("模型导入中..."));
    m_progressDlg.setValue(0);
    m_progressDlg.setCancelButtonVisible(false);
    m_progressDlg.setHidden(false);
    //
    timeoutTimer.start();
    loop.exec();

    if (timeoutTimer.isActive())
    {
        timeoutTimer.stop();

        if (m_uploadSuccess)
        {
            return true;
        }
        else
        {
            errMsg = m_errorMessage.isEmpty() ? "Upload failed" : m_errorMessage;
            return false;
        }
    }
    else
    {
        errMsg = "Upload timeout after 120 seconds";
        return false;
    }

    m_progressDlg.setHidden(true);
}

void ModelImportHelper::OnUploadFinished()
{
    if (!m_currentReply)
    {
        m_errorMessage = "No active upload reply";
        emit SigUploadFinished();
        return;
    }

    if (m_currentReply->error() != QNetworkReply::NoError)
    {
        m_errorMessage = QString("Network error: %1").arg(m_currentReply->errorString());
        emit SigUploadFinished();
        return;
    }

    // 获取响应状态码
    int statusCode = m_currentReply->attribute(QNetworkRequest::HttpStatusCodeAttribute).toInt();

    if (statusCode != 200)
    {
        m_errorMessage = QString("HTTP error: %1").arg(statusCode);
        emit SigUploadFinished();
        return;
    }

    // 获取响应内容
    QByteArray responseData = m_currentReply->readAll();
    QString responseString = QString::fromUtf8(responseData);
    m_uploadSuccess = true;
    emit SigUploadFinished();
    // 清理
    m_currentReply->deleteLater();
    m_currentReply = nullptr;
}

void ModelImportHelper::OnUploadProgress(qint64 bytesSent, qint64 bytesTotal)
{
    if (bytesTotal > 0)
    {
        double progress = (double)bytesSent / bytesTotal * 100.0;
        m_progressDlg.setLabelText(tr("模型导入中..."));
        m_progressDlg.setValue(progress);
    }
}

void ModelImportHelper::OnNetworkError(QNetworkReply::NetworkError error)
{
    QString errorString = m_currentReply ? m_currentReply->errorString() : "Unknown network error";
    m_errorMessage = QString("Network error: %1").arg(errorString);
    emit SigUploadFinished();
}
