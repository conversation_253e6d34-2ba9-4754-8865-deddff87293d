﻿#include "AccuComponentUi\Header\QMTTreeGroupItem.h"
#include "ui_QMTTreeGroupItem.h"
#include "CMtCoreDefine.h"
#include <QColorDialog>
#include <qDebug>
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "CMtCoreWidgetUtil.h"

QMTTreeGroupItem::QMTTreeGroupItem(QString uniqueValue, QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTTreeGroupItem;
    ui->setupUi(this);
    //ui->label_ItemNum->setProperty(QssPropertyKey, QssPropertyLabelSecondTitle);
    _isShowAll = true;
    _isExpand = true;
    _isSelected = false;
    _isFillAll = false;
    //_RTNo = RTNo;
    _uniqueValue = uniqueValue;
    connect(ui->checkBox_Expand, SIGNAL(clicked()), this, SLOT(on_actionExpandChanged()));
    connect(ui->checkBox_Show, SIGNAL(clicked()), this, SLOT(on_actionShowAllChanged()));
    ui->checkBox_Show->setChecked(_isShowAll);
    ui->checkBox_Expand->setChecked(_isExpand);
    ui->label_ItemNum->hide();
}

QMTTreeGroupItem::~QMTTreeGroupItem()
{
    //qDebug() << "QMTTreeGroupItem::~QMTTreeGroupItem()";
    MT_DELETE(ui);
}

void QMTTreeGroupItem::InitTreeGroupItem()
{
    ui->checkBox_Show->setChecked(_isShowAll);
    ui->checkBox_Expand->setChecked(_isExpand);
}

void QMTTreeGroupItem::setName(QString name)
{
    ui->sliderTextWidget->setName(name);
    _name = name;
}

QString QMTTreeGroupItem::getName()
{
    return _name;
}

QString QMTTreeGroupItem::getUniqueValue()
{
    return _uniqueValue;
}

bool QMTTreeGroupItem::GetShowed()
{
    //return _isShowAll;
    return ui->checkBox_Show->isChecked();
}
void QMTTreeGroupItem::ExpandBtnClicked()
{
    _isExpand = !_isExpand;
    ui->checkBox_Expand->setChecked(_isExpand);
    emit ui->checkBox_Expand->clicked();
}

void QMTTreeGroupItem::ShowBtnClicked()
{
    ui->checkBox_Show->setChecked(!_isShowAll);
    emit ui->checkBox_Show->clicked();
}
void QMTTreeGroupItem::SetFillAll(bool isFillAll, bool bEmitSig)
{
    _isFillAll = isFillAll;

    if (bEmitSig)
    {
        emit sigIsFillAllGroup(_uniqueValue, _isFillAll);
    }
}

bool QMTTreeGroupItem::GetFillAll()
{
    return _isFillAll;
}
void QMTTreeGroupItem::SetShowItemNum(bool isShow)
{
    if (isShow)
    {
        ui->label_ItemNum->show();
    }
    else
    {
        ui->label_ItemNum->hide();
    }
}

void QMTTreeGroupItem::SetItemNum(int ItemNum)
{
    QString strItemNum = QString("[%1]").arg(ItemNum);
    ui->label_ItemNum->setText(strItemNum);
    _ItemNum = ItemNum;
}

int QMTTreeGroupItem::GetItemNum()
{
    return _ItemNum;
}
void QMTTreeGroupItem::SetHideWidget(bool bHide)
{
    if (bHide)
    {
        ui->frame->hide();
    }
    else
    {
        ui->frame->show();
    }
}

bool QMTTreeGroupItem::GetGroupExpand()
{
    return _isExpand;
}

void QMTTreeGroupItem::FillAllBtnClicked()
{
    SetFillAll(!_isFillAll, true);
}

void QMTTreeGroupItem::SetShowed(bool isShowAll, bool bEmitSig)
{
    _isShowAll = isShowAll;
    ui->checkBox_Show->setChecked(_isShowAll);

    if (bEmitSig)
    {
        emit ui->checkBox_Show->clicked();
    }
}

void QMTTreeGroupItem::SetSelectBtnTipString(const QString& tips, const QString& tipsEx)
{
    if (tipsEx.isEmpty())
    {
        ui->checkBox_Expand->setToolTipText(tips);
    }
    else
    {
        ui->checkBox_Expand->setToolTipText(tips, tipsEx);
    }
}
void QMTTreeGroupItem::SetShowHideBtnTipString(const QString& tips, const QString& tipsEx)
{
    if (tipsEx.isEmpty())
    {
        ui->checkBox_Show->setToolTipText(tips);
    }
    else
    {
        ui->checkBox_Show->setToolTipText(tips, tipsEx);
    }
}

void QMTTreeGroupItem::SetSelected(bool isSelected)
{
    if (isSelected == _isSelected)
    {
        return;
    }

    //QString styleStr;树item中设置了样式，这边不再设置，以免样式冲突

    if (isSelected)
    {
        //styleStr = "#widget_3{background:rgba(@colorB1, 0.26); border: 1px solid rgba(@colorB1, 1);border-radius: 1px;}";
        //CMtCoreWidgetUtil::formatStyleSheet(styleStr);
        //ui->widget_3->setStyleSheet(styleStr);
    }
    else
    {
        //styleStr = "#widget_3{background: transparent; border: 0px solid; border-radius: 1px;}";
        //CMtCoreWidgetUtil::formatStyleSheet(styleStr);
        //ui->widget_3->setStyleSheet(styleStr);
    }

    _isSelected = isSelected;
}

void QMTTreeGroupItem::on_actionExpandChanged()
{
    _isExpand = ui->checkBox_Expand->isChecked();
    emit sigIsExpandGroup(_uniqueValue, _isExpand);
}

void QMTTreeGroupItem::on_actionShowAllChanged()
{
    _isShowAll = ui->checkBox_Show->isChecked();
    emit sigShowAllGroupItem(_uniqueValue, _isShowAll);
}

void QMTTreeGroupItem::enterEvent(QEvent* e)
{
    QWidget::enterEvent(e);
}
void QMTTreeGroupItem::leaveEvent(QEvent* e)
{
    QWidget::leaveEvent(e);
}
