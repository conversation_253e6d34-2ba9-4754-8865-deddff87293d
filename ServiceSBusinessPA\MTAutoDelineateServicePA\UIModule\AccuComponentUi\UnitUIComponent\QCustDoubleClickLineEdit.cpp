﻿#include "AccuComponentUi/Header/UnitUIComponent\QCustDoubleClickLineEdit.h"
#include "ui_QCustDoubleClickLineEdit.h"
#include "AccuComponentUi/Header/QMTUIDefine.h"
#include <qDebug>

QCustDoubleClickLineEditParam::QCustDoubleClickLineEditParam()
{
    _cellWidgetType = DELEAGATE_QCustDoubleClickLineEdit;
}

QCustDoubleClickLineEditParam::~QCustDoubleClickLineEditParam()
{
}

//QJsonObject QCustDoubleClickLineEditParam::ToJson()
//{
//    QJsonObject retObj;
//    retObj.insert("isReadOnly", _isReadOnly);
//    retObj.insert("text", _text);
//    retObj.insert("regExpStr", _regExpStr);
//    retObj.insert("maxLength", _maxLength);
//    return retObj;
//}
//
//void QCustDoubleClickLineEditParam::ParseFrom<PERSON>son(QJsonObject& obj)
//{
//    this->_isReadOnly = obj.value("isReadOnly").toBool();
//    this->_text = obj.value("text").toString();
//    this->_regExpStr = obj.value("regExpStr").toString();
//    this->_maxLength = obj.value("maxLength").toInt();
//}

QWidget* QCustDoubleClickLineEditParam::CreateUIModule(QWidget* parent)
{
    QCustDoubleClickLineEdit* lineEdit = new QCustDoubleClickLineEdit(parent);
    lineEdit->SetupCellWidget(*this);
    return lineEdit;
}

/*****************************************************************/

QCustDoubleClickLineEdit::QCustDoubleClickLineEdit(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QCustDoubleClickLineEdit;
    ui->setupUi(this);
    //ui->lineEdit->setStyleSheet("QLineEdit{color:rgba(0,0,0,1);}");
    //ui->label->setProperty(QssPropertyKey, QssPropertyLabelThirdTitle);
    //ui->lineEdit->setProperty(QssPropertyKey, QssPropertyLineEidtdoubleEdit);
    ui->label->setStyleSheet("QLabel{color:rgb(219,226,241);font-size:12px;padding-left: 3px;}");
    ui->lineEdit->setContextMenuPolicy(Qt::NoContextMenu);
    ui->lineEdit->setMtType(MtLineEdit::MtType::lineedit1);
    setReadOnly(true);
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

QCustDoubleClickLineEdit::~QCustDoubleClickLineEdit()
{
    if (_validator)
    {
        _validator->deleteLater();
        _validator = nullptr;
    }

    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QCustDoubleClickLineEdit::SetupCellWidget(QCustDoubleClickLineEditParam& cellWidgetParam)
{
    this->setReadOnly(cellWidgetParam._isReadOnly);
    this->SetRegExpStr(cellWidgetParam._regExpStr);
    this->setText(cellWidgetParam._text);

    if (cellWidgetParam._maxLength > 0)
    {
        this->GetLineEdit()->setMaxLength(cellWidgetParam._maxLength);
    }

    if (cellWidgetParam._placeholderText.size() > 0)
    {
        this->GetLineEdit()->setPlaceholderText(cellWidgetParam._placeholderText);
    }
}

bool QCustDoubleClickLineEdit::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString str = updateData.toString();
        disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        this->setText(str);
        connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        SetEnableEdit(bEditEnable);
    }
    else if (updateData.canConvert<MtRangeLineEdit::RangeParamInfo>())
    {
        MtRangeLineEdit::RangeParamInfo inputRange = updateData.value<MtRangeLineEdit::RangeParamInfo>();
        this->GetLineEdit()->SetEditRange(inputRange);
    }

    return false;
}

QString QCustDoubleClickLineEdit::GetCurText()
{
    return this->getText();
}

QLabel_Dot* QCustDoubleClickLineEdit::GetLable()
{
    return ui->label;
}

MtRangeLineEdit* QCustDoubleClickLineEdit::GetLineEdit()
{
    return ui->lineEdit;
}

void QCustDoubleClickLineEdit::SetRegExpStr(QString& regExpStr)
{
    if (0 == regExpStr.size())
        return;

    if (nullptr != _validator)
    {
        delete _validator;
        _validator = nullptr;
    }

    QRegExp regExp(regExpStr);
    _validator = new QRegExpValidator(regExp, this);
    ui->lineEdit->setValidator(_validator);
}

void QCustDoubleClickLineEdit::SetItemValidator(QValidator* regExp)
{
    //这边不能对_validator赋值,外面delete后，析构会引起奔溃
    ui->lineEdit->setValidator(_validator);
}

void QCustDoubleClickLineEdit::setText(const QString& text)
{
    ui->label->setTextElided(text);
    ui->label->update();
    UpdateLineEditText(text);
}

void QCustDoubleClickLineEdit::setReadOnly(bool readOnly)
{
    if (readOnly)
    {
        ui->label->show();
        ui->lineEdit->hide();
        QString text = ui->lineEdit->text();
        ui->label->setTextElided(text);
        m_bIsInEditing = false;
    }
    else
    {
        ui->label->hide();
        ui->lineEdit->show();
        ui->lineEdit->setFocus();
        QString text = ui->label->GetFullString();
        UpdateLineEditText(text);
        m_bIsInEditing = true;
    }
}

void QCustDoubleClickLineEdit::SetMyStyleSheet(QString& sheetStr)
{
    ui->label->setStyleSheet(sheetStr);
    ui->lineEdit->setStyleSheet(sheetStr);
}

void QCustDoubleClickLineEdit::SetEnableEdit(bool enable)
{
    setReadOnly(true);
    _isDClickEdit = enable;
    this->setEnabled(_isDClickEdit);
}

QString QCustDoubleClickLineEdit::getText()
{
    return ui->lineEdit->text();
}

void QCustDoubleClickLineEdit::resizeEvent(QResizeEvent* event)
{
    int width = this->width();
    int height = this->height();
    ui->label->setFixedWidth(width);
    ui->lineEdit->setFixedWidth(width - 6);

    if (height > 30)
    {
        height = 30;
    }

    ui->lineEdit->setFixedHeight(height - 6);
    QWidget::resizeEvent(event);
}

void QCustDoubleClickLineEdit::mousePressEvent(QMouseEvent* event)
{
    emit sigClicked(0);
    QWidget::mousePressEvent(event);
}

void QCustDoubleClickLineEdit::mouseDoubleClickEvent(QMouseEvent* event)
{
    if (false == _isDClickEdit)
    {
        QWidget::mouseDoubleClickEvent(event);
    }
    else
    {
        _oldText = ui->lineEdit->text();
        this->setReadOnly(false);
    }
}

void QCustDoubleClickLineEdit::enterEvent(QEvent* e)
{
    if (!m_bIsInEditing && !m_bIsEnterInThisWgt && this->isEnabled())
    {
        m_bIsEnterInThisWgt = true;
        // ui->label->setStyleSheet("background-color:rgba(30,32,40,0.47);font-family:\"MicrosoftYaHei\";color:rgba(219,226,241,1);");
    }

    QWidget::enterEvent(e);
}

void QCustDoubleClickLineEdit::leaveEvent(QEvent* e)
{
    if (!m_bIsInEditing && m_bIsEnterInThisWgt && this->isEnabled())
    {
        m_bIsEnterInThisWgt = false;
        //  ui->label->setStyleSheet("font-family:\"MicrosoftYaHei\";color:rgba(219,226,241,1);");
    }

    QWidget::leaveEvent(e);
}

void QCustDoubleClickLineEdit::UpdateLineEditText(const QString& text)
{
    disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
    ui->lineEdit->setText(text);
    ui->label->setFocus();
    connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
}

void QCustDoubleClickLineEdit::slotLineEditingFinished()
{
    if (false == m_bIsInEditing || true == ui->lineEdit->isHidden())
    {
        return;
    }

    this->setReadOnly(true);
    QString newText = ui->lineEdit->text();

    if (_oldText != newText)
    {
        _oldText = newText;
        emit currentTextChanged(newText);       //抛出信号必须放最后，因为信号外面清空了所有界面
    }
}