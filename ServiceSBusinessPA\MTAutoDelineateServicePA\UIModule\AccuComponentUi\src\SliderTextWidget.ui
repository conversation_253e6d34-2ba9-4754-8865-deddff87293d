<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SliderTextWidget</class>
 <widget class="QWidget" name="SliderTextWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>120</width>
    <height>30</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>SliderTextWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtLabel" name="label_name">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="text">
      <string>TextLabel</string>
     </property>
     <property name="elideMode">
      <enum>Qt::ElideRight</enum>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtLabel::myLabel1</enum>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
