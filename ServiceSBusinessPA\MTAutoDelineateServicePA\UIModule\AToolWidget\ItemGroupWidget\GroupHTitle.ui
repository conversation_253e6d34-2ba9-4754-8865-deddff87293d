<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>GroupHTitleClass</class>
 <widget class="QWidget" name="GroupHTitleClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>600</width>
    <height>45</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>GroupHTitle</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>12</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>6</number>
   </property>
   <item>
    <widget class="QStackedWidget" name="stackedWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="page_title">
      <layout class="QHBoxLayout" name="horizontalLayout_4">
       <property name="spacing">
        <number>18</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>14</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout">
         <property name="spacing">
          <number>5</number>
         </property>
         <item>
          <widget class="MtToolButton" name="mtToolButton_pageTitle">
           <property name="minimumSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
           <property name="text">
            <string>...</string>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtToolButton::toolbutton2</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MtLabel" name="mtLabel_titleName">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Preferred">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>MtTextLabel</string>
           </property>
           <property name="elideMode">
            <enum>Qt::ElideRight</enum>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtLabel::myLabel2_1</enum>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>1</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>1</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::frameEx5</enum>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="page_titleCheck">
      <layout class="QHBoxLayout" name="horizontalLayout_9">
       <property name="spacing">
        <number>18</number>
       </property>
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>16</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2">
         <property name="spacing">
          <number>5</number>
         </property>
         <item>
          <widget class="MtToolButton" name="mtToolButton_pageCheck">
           <property name="minimumSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
           <property name="maximumSize">
            <size>
             <width>16</width>
             <height>16</height>
            </size>
           </property>
           <property name="text">
            <string>...</string>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtToolButton::toolbutton2</enum>
           </property>
          </widget>
         </item>
         <item>
          <widget class="MtCheckBox" name="mtCheckBox_titleName">
           <property name="sizePolicy">
            <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
             <horstretch>0</horstretch>
             <verstretch>0</verstretch>
            </sizepolicy>
           </property>
           <property name="text">
            <string>MtCheckBox</string>
           </property>
           <property name="elideMode">
            <enum>Qt::ElideNone</enum>
           </property>
           <property name="_mtType" stdset="0">
            <enum>MtCheckBox::checkbox1</enum>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="MtFrameEx" name="mtFrameEx_2">
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>1</height>
          </size>
         </property>
         <property name="maximumSize">
          <size>
           <width>16777215</width>
           <height>1</height>
          </size>
         </property>
         <property name="_mtType" stdset="0">
          <enum>MtFrameEx::frameEx5</enum>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtCheckBox</class>
   <extends>QCheckBox</extends>
   <header>MtCheckBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
