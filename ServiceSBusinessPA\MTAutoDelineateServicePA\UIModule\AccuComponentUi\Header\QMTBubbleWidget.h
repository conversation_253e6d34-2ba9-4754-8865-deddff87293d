﻿#ifndef QMTBubbleWidget_H
#define QMTBubbleWidget_H

#include <QWidget>
#include <QTimer>


class  QMTBubbleWidget : public QWidget
{
    Q_OBJECT
public:
    explicit QMTBubbleWidget(QWidget* parent = 0);
    //void setRotateDelta(int delta);
    void setPersent(int persent);
    void setValue(int persent);
    int GetValue()
    {
        return m_value;
    }
    void SetArcColor(QColor& color)
    {
        m_arcColor = color;
        this->update();
    }
signals:

protected:
    void paintEvent(QPaintEvent*);

private:
    int m_rotateAngle = 360;//旋转角度
    int m_persent = 100; //百分比
    int m_value = 0;
    QSize m_arcSize = QSize(30, 30);
    /////
    QTimer* m_timer = nullptr;
    QColor m_arcColor = QColor(217, 0, 27);//QColor(255, 107, 107);

};

#endif // QMTBubbleWidget_H
