﻿#include "AccuComponentUi\Header\UnitUIComponent\QMTButtonsUi.h"
#include "ui_QMTButtonsUi.h"
#include "CMtCoreDefine.h"
#include <QJsonArray>
#include <QList>
#include <QJsonObject>
#include "AccuComponentUi\Header\QMTEnumDef.h"
#include "CMtCoreWidgetUtil.h"
//#include "QMTJsonValue.h"

using namespace mtuiData;

QMTButtonsUiParam::QMTButtonsUiParam()
{
    _cellWidgetType = DELEAGATE_TYPE_BUTTONS;
}

QWidget* QMTButtonsUiParam::CreateUIModule(QWidget* parent /*= NULL*/)
{
    QMTButtonsUi* createWidget = new QMTButtonsUi(parent);
    createWidget->SetupCellWidget(*this);
    return createWidget;
}

QMTButtonsUi::QMTButtonsUi(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTButtonsUi;
    ui->setupUi(this);
    this->setAttribute(Qt::WA_DeleteOnClose);
    _btnGroup = new QButtonGroup(this);
    _btnGroup->addButton(ui->pushButton_delinete, ButtonType::Button_Delinete);
    _btnGroup->addButton(ui->pushButton_cancel, ButtonType::Button_Cancel);
    _btnGroup->addButton(ui->pushButton_redo, ButtonType::Button_Redo);
    _btnGroup->addButton(ui->pushButton_open, ButtonType::Button_Open);
    InitTemplateStyle();
    ui->pushButton_redo->hide();
    ui->pushButton_cancel->hide();
    connect(_btnGroup, SIGNAL(buttonClicked(int)), this, SLOT(BtnClicked(int)));
}

QMTButtonsUi::~QMTButtonsUi()
{
    MT_DELETE(ui);
    /*if (_btnGroup)        //会自动释放
    {
        delete _btnGroup;
        _btnGroup = NULL;
    }*/
}


bool QMTButtonsUi::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)     //更改文案
    {
        QString text = updateData.toString();
        SetPushButtonText(Btn_Delinete, text);
        return true;
    }
    else if (QMetaType::Bool == userType)  //使能
    {
        bool bEnable = updateData.toBool();
        SetButtonEnable(Btn_Delinete, bEnable);
        return true;
    }
    else if (updateData.canConvert<QMTButtonsUi::OneButtonsUiUpdateInfo>())
    {
        OneButtonsUiUpdateInfo updateInfo = updateData.value<QMTButtonsUi::OneButtonsUiUpdateInfo>();

        if (updateInfo.text.size() > 0)
        {
            SetPushButtonText((ButtonIndex)updateInfo.btnIndex, updateInfo.text);
        }

        HideButton((ButtonIndex)updateInfo.btnIndex, updateInfo.bHide);
        SetButtonEnable((ButtonIndex)updateInfo.btnIndex, updateInfo.bEnable);
    }

    return false;
}

void QMTButtonsUi::SetButtonEnable(int btnIndex/**/, bool bEnable)
{
    QAbstractButton* btn = _btnGroup->button(btnIndex);

    if (btn != NULL)
    {
        btn->setEnabled(bEnable);
    }
}

void QMTButtonsUi::SetPushButtonText(ButtonIndex index, QString text)
{
    QAbstractButton* btn = _btnGroup->button(index);

    if (btn != NULL)
    {
        btn->setText(text);;
    }
}

void QMTButtonsUi::SetPushButtonName(ButtonIndex index, QString name)
{
    QAbstractButton* btn = _btnGroup->button(index);

    if (btn != NULL)
    {
        btn->setObjectName(name);
    }
}

void QMTButtonsUi::HideAllButtons(bool bHide)
{
    for (int i = 0; i < Btn_Max; ++i)
    {
        HideButton((ButtonIndex)i, bHide);
    }
}

void QMTButtonsUi::HideButton(ButtonIndex index, bool bHide)
{
    QAbstractButton* btn = _btnGroup->button(index);

    if (btn != NULL)
    {
        if (bHide)
        {
            btn->hide();
        }
        else
        {
            btn->show();
        }
    }
}

void QMTButtonsUi::BtnClicked(int index)
{
    emit sigButtonClicked(this, index);
    TableWidgetItemIndex cellWidgetItemIndex;
    cellWidgetItemIndex._uniqueValue = this->GetCellData(Role_MainValue);
    cellWidgetItemIndex._parentValue = this->GetCellData(Role_ParentValue);
    cellWidgetItemIndex._type = this->GetCellData(Role_RowType).toInt();
    emit sigButtonClicked(cellWidgetItemIndex, index);
    emit sigButtonClicked(index, false);
}

/*
{
    "QMTButtonsUi": [
        {
            "ButtonType": 0,
            "isVisial": true,
            "enable": true,
            "style": "",
            "ButtonStyle":0,
            "Text":"打开"
        },
        {
            "ButtonType": 1,
            "isVisial": true,
            "enable": false,
            "style": " "
            "ButtonStyle":1,
            "Text":"关闭"
        }
    ],
    "Modality":"CT"
}
*/

/// <summary>
/// 从Json中设置将要怎样显示Button
/// 1. 如果style 为空才会考虑ButtonStyle
/// 2. style 在AccuContour中没用，只能通过setProperty("rtenable", "false"); 这种方式去改变button状态。所以样式和业务绑定很强。
///     a. 单独改变pushButton的stylesheet没用，但如果直接设置ui->widget->setStyleSheet("")，却可以清空样式，但还是无法设置pushButton样式？？？
/// </summary>
/// <param name="obj"></param>
void QMTButtonsUi::SetMode(QJsonObject& obj)
{
    if (!obj.contains(TOString(QMTButtonsUi)))
        return;

    QJsonArray array = obj.value(TOString(QMTButtonsUi)).toArray();

    for (int i = 0; i < array.size(); ++i)
    {
        QJsonObject subobj = array.at(i).toObject();

        if (!subobj.contains(TOString(ButtonType)))
            continue;

        if (!subobj.contains(TOString(isVisial)))
            continue;

        ButtonType buttonType = (ButtonType)subobj.value(TOString(ButtonType)).toInt();
        bool isVisial = subobj.value(TOString(isVisial)).toBool();
        bool enable;

        if (subobj.contains(TOString(enable)))
        {
            enable = subobj.value(TOString(enable)).toBool();
        }
        else
        {
            enable = true;
        }

        QString style = subobj.value(TOString(style)).toString();

        if (style.isEmpty())
        {
            if (obj.contains(TOString(ButtonStyle)))
            {
                ButtonStyle buttonStyle = (ButtonStyle)obj.value(TOString(ButtonStyle)).toInt();
                style = GetButtonStyle(buttonStyle);
            }

            if ((Button_Delinete == buttonType || Button_Redo == buttonType) && obj.contains(TOString(Modality)))
            {
                QString modity = obj.value(TOString(Modality)).toString();
            }
        }

        QString Text;

        if (subobj.contains(TOString(Text)))
        {
            Text = subobj.value(TOString(Text)).toString();
        }

        //QVariantHash tmp = obj.toVariantHash();
        //qDebug() << tmp;
        SetButtonStyle(buttonType, isVisial, enable, style, "", Text);
    }

    //ui->pushButton_open->setProperty("opentype", "rtfile");
    //ui->pushButton_open->setStyleSheet("/**/");
}

// 使用原子组件样式，此函数置空
void QMTButtonsUi::InitTemplateStyle()
{
}

QString QMTButtonsUi::GetButtonStyle(ButtonStyle style)
{
    QString styleGrayButton =
        "QPushButton{"
        "background-color: transparent;"
        "color:rgba(219,226,241,0.6);"
        "border-style:solid;"
        "border-width:1px;"
        "border-color:rgba(219,226,241,0.34);"
        "border-radius: 4px;"
        "opacity:168;"
        "font-size : 12px;}"
        "QPushButton:hover{"
        "border-style:solid;"
        "border-width:1px;"
        "border-color:rgba(219,226,241, 0.44);"
        "color:rgba(219,226,241,0.7);}";
    QString styleDisenableGrayButton =
        "QPushButton{"
        "background-color: rgb(70, 84, 106);"
        "color:rgba(188,186,186,1);"
        "border-style:outset;"
        "border-width:1px;"
        "border-color:rgba(188, 186, 186, 0.34);"
        "font-size : 12px;}"
        "QPushButton:hover{"
        "background-color:rgba(216,216,216,0.1);}";
    QString styleBlueButton =
        "QPushButton{"
        "border:none;"
        "background-color: rgb(77,155,213);"
        "color:rgb(255,255,255);"
        "border-radius:4px;"
        "font-size:12px;}"
        "QPushButton:hover{"
        "background-color:rgba(77,155,213,0.9);}";
    QString ret;

    switch (style)
    {
        case Style_Gray:
            ret = styleGrayButton;
            break;

        case Style_Blue:
            ret = styleBlueButton;
            break;

        case Sytle_DisableGray:
            ret = styleDisenableGrayButton;
            break;

        default:
            break;
    }

    return ret;
}
/// <summary>
/// 获取Rt 情况下的style
/// </summary>
/// <param name="type"></param>
/// <param name="enable"></param>
QString QMTButtonsUi::GetRtButtonStyle(ButtonType type, bool enable)
{
    QString style = "";
    //ui->pushButton_open->setProperty("enable", "unknown");
    //ui->pushButton_open->setStyleSheet("/**/");

    if (Button_Open == type)
    {
        if (enable)
        {
            style = "QPushButton{"
                "border:none;"
                "background-color: rgb(138,162,214);"
                "color:rgb(255,255,255);"
                "border-radius:4px;"
                "font-size:12px;}"
                "QPushButton:hover{"
                "background-color:rgba(138,162,214, 0.5);}";
            //ui->pushButton_open->setEnabled(true);
            ui->pushButton_open->setProperty("rtenable", "true");
        }
        else//不存在ct的时候
        {
            style = "QPushButton{"
                "border:none;"
                "background-color: rgba(61,69,84,1);"
                "color:rgba(246,247,250,0.35);"
                "border-radius:4px;"
                "font-size:12px;}"
                "QPushButton:hover{"
                "background-color:rgba(61,69,84,1);}";
            // ui->pushButton_open->setProperty("opentype", "111");
            ui->pushButton_open->setProperty("rtenable", "false");
            // ui->pushButton_open->setStyleSheet("/**/");
            //ui->pushButton_open->setEnabled(false);
        }
    }

    return style;
}

//设置rt按键
void QMTButtonsUi::SetButtonEnable(ButtonType type, bool enable)
{
    QString style = GetRtButtonStyle(type, enable);
    SetButtonStyle(type, true, enable, "");
}


void QMTButtonsUi::SetupCellWidget(QMTButtonsUiParam& buttonUiParam)
{
    HideAllButtons(true);

    for (int i = 0; i < buttonUiParam._showBtnIndexList.size(); ++i)
    {
        int index = buttonUiParam._showBtnIndexList.at(i);
        this->HideButton((QMTButtonsUi::ButtonIndex)index, false);
    }

    for (int i = 0; i < buttonUiParam._disableBtnList.size(); ++i)
    {
        int index = buttonUiParam._disableBtnList.at(i);
        this->SetButtonEnable((QMTButtonsUi::ButtonIndex)index, false);
    }

    QMap<int, QString>::iterator iter;

    for (iter = buttonUiParam._styleStrMap.begin(); iter != buttonUiParam._styleStrMap.end(); ++iter)
    {
        int btnIndex = iter.key();
        QString styleStr = iter.value();
        this->SetButtonStyle((QMTButtonsUi::ButtonIndex)btnIndex, styleStr);
    }

    for (iter = buttonUiParam._btnTextMap.begin(); iter != buttonUiParam._btnTextMap.end(); ++iter)
    {
        int btnIndex = iter.key();
        QString text = iter.value();
        this->SetPushButtonText((QMTButtonsUi::ButtonIndex)btnIndex, text);
    }
}

void QMTButtonsUi::SetButtonStyle(ButtonIndex index, QString style)
{
    QAbstractButton* btn = _btnGroup->button(index);

    if (btn != NULL)
    {
        btn->setStyleSheet(style);
    }
}

void QMTButtonsUi::SetButtonEnable(ButtonIndex index, bool enable)
{
    QAbstractButton* btn = _btnGroup->button(index);

    if (btn != NULL)
    {
        btn->setEnabled(enable);
    }
}

/// <summary>
/// QString类型只有不为空才会进行设置
/// 1. style - setStyleSheet
/// 2. text - setText
/// 3. ButtonType对应的是不同的按钮，不是说只设置一次ButtonType就可以控制QMTButtonsUi的所有按钮显示。
/// </summary>
/// <param name="type"></param>
/// <param name="isVisial"></param>
/// <param name="enable"></param>
/// <param name="style"></param>
/// <param name="property"></param>
/// <param name="text"></param>
void QMTButtonsUi::SetButtonStyle(ButtonType type,
                                  bool isVisial, bool enable, QString style, QString property, QString text)
{
    QPushButton* pushButton = nullptr;

    switch (type)
    {
        case Button_Redo:
            pushButton = ui->pushButton_redo;
            break;

        case Button_Delinete:
            pushButton = ui->pushButton_delinete;
            break;

        case Button_Cancel:
            pushButton = ui->pushButton_cancel;
            break;

        case Button_Open:
            pushButton = ui->pushButton_open;
            break;
    }

    if (pushButton == nullptr)
        return;

    pushButton->setVisible(isVisial);
    pushButton->setEnabled(enable);

    if (!style.isEmpty())
    {
        pushButton->setStyleSheet(style);
    }

    if (!text.isEmpty())
    {
        pushButton->setText(text);
    }

    // TODO 优化
    switch (type)
    {
        case Button_Redo:
        {
            if (style.isEmpty())
            {
                _reDelineteStyle = style;
                //update();
            }

            if (enable)
            {
                //ui->pushButton_redo->setProperty("enable", "true");
                //ui->pushButton_redo->setStyleSheet("/**/");
            }
            else
            {
                ui->pushButton_redo->setProperty("enable", "false");
                //ui->pushButton_redo->setStyleSheet("/**/");
                ui->pushButton_redo->update();
            }
        }
        break;

        case Button_Delinete:
        {
            if (style.isEmpty())
            {
                _delineteStyle = style;
                update();
            }

            if (enable)
            {
                QString styleBlueButton = GetButtonStyle(Style_Blue);
                ui->pushButton_delinete->setStyleSheet(styleBlueButton);
                //            bool bSucc =  ui->pushButton_delinete->setProperty("enable", "true");
                //            ui->pushButton_delinete->update();
            }
            else
            {
                ui->pushButton_delinete->setProperty("enable", "false");
                //ui->pushButton_delinete->setStyleSheet("/**/");
                ui->pushButton_delinete->update();
            }
        }
        break;

        case Button_Cancel:
        {
            _cancelEnable = enable;

            if (enable)
            {
                //正常鼠标样式，背景色变回
                ui->pushButton_cancel->setCursor(QCursor(Qt::ArrowCursor));
                ui->pushButton_cancel->setStyleSheet(GetButtonStyle(Style_Gray));
            }
            else
            {
                //打转，背景色变淡
                ui->pushButton_cancel->setCursor(QCursor(Qt::BusyCursor));
                ui->pushButton_cancel->setStyleSheet(GetButtonStyle(Sytle_DisableGray));
            }

            this->repaint();
        }
        break;

        case Button_Open:
        {
            if (style.isEmpty())
            {
                _openStyle = style;
                this->update();
            }

#ifdef hzg_candelete
            else
            {
                _openStyle.clear();
            }

#endif

            if (property.size() > 0)
            {
                ui->pushButton_open->setProperty("opentype", property);
                ui->pushButton_open->setStyleSheet("/**/");
            }
        }
        break;

        default:
            break;
    }

    if (style.isEmpty())
    {
        _needUpdata = 0;
    }
}


