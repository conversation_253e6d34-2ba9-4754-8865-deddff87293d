﻿#include "AccuComponentUi\Header\QMTTools.h"
#include <QApplication>
#include <QDesktopWidget>
#include <QTimer>
#include <QDateTime>

QMTTools* QMTTools::m_instance = NULL;
QMTTools* QMTTools::getInstance()
{
    if (m_instance == NULL)
        m_instance = new QMTTools();

    return m_instance;
}

QSize QMTTools::GetSystemScreenSize()
{
    QDesktopWidget* pDesktopWidget = QApplication::desktop();
    //获取可用桌面大小
    QRect deskRect = QApplication::desktop()->availableGeometry();
    //获取主屏幕分辨率
    QRect screenRect = QApplication::desktop()->screenGeometry();
    //获取屏幕数量
    int nScreenCount = QApplication::desktop()->screenCount();
    int width = deskRect.width();
    int height = deskRect.height();
    QSize ret(width, height);
    return ret;
}

void QMTTools::WaitEventLoop(int msec)
{
    QEventLoop loop;
    QTimer::singleShot(msec, &loop, SLOT(quit()));
    loop.exec();
}

void QMTTools::ClearListWidgetWindow(QListWidget* listwidget)
{
    for (int i = listwidget->count() - 1; i >= 0; i--)
    {
        QListWidgetItem* listWidgetItem = listwidget->item(i);
        listwidget->takeItem(i);//删除
        QWidget* itemWidget = listwidget->itemWidget(listWidgetItem);
        delete listWidgetItem;
        delete itemWidget;
    }
}

void QMTTools::WaitForTryLock(QMutex& mutex)
{
    while (!mutex.tryLock())
    {
        QEventLoop loop;
        QTimer::singleShot(200, &loop, SLOT(quit()));
        loop.exec();
    }
}

QString QMTTools::GetScrollStyleSheetStr()
{
    return "";
}

QString QMTTools::GetComboBoxStyleSheetStr()
{
    QString styleSheetStr;
    styleSheetStr += "QComboBox {background-color: rgba(56,64,80,0.59);color: rgba(219,226,241,1);border-radius: 2px;font-size:12px;border:1px solid rgba(28,31,36,1); padding-left: 14px;}";
    styleSheetStr += "QComboBox::disabled {color: rgba(219,226,241,0.5);background-color: rgba(37,41,48,0.4);}";
    styleSheetStr += "QComboBox::drop-down {width: 20px;height:20px;border: 0;subcontrol-position: center right;padding-right: 10px;padding-left: 14px;}";
    //styleSheetStr += "QComboBox::QlineEdit {background-color: rgba(56,64,80,1);color: rgba(219,226,241,1);font-size:12px;}";
    styleSheetStr += "QComboBox::down-arrow {image: url(:/AccuUIComponentImage/images/pull-down2.png)}";
    styleSheetStr += "QComboBox QAbstractItemView {background-color: rgba(45,52,64,1);color: rgb(219,226,241);selection-background-color: #4E9CD5;border:1px solid rgba(28,31,36,1);font-size:12px;outline:none;}";
    styleSheetStr += "QComboBox QAbstractItemView::Item { border:none; padding-left: 11px; font-size:12px; }";
    styleSheetStr += "QComboBox QAbstractItemView::item:hover { background:rgba(78,156,213,1);border-radius:4px;} ";
    return styleSheetStr;
}

QString QMTTools::GetSliderStyleSheetStr(bool bHorizontal)
{
    QString styleSheetStr;

    if (bHorizontal)
    {
        styleSheetStr += "QSlider::groove:horizontal {height:5px;width: 149px;border-radius: 1px;padding-left:-1px;padding-right:-1px;padding-top:-1px;padding-bottom:-1px;}";
        styleSheetStr += "QSlider::sub-page:horizontal {background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1,stop: 0 #5DCCFF, stop: 1 #1874CD);}";
        styleSheetStr += "QSlider::add-page:horizontal {background: #384355;}";
        styleSheetStr += "QSlider::handle:horizontal {background: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.5, fx:0.5, fy:0.5, stop:0.6 #45ADED, stop:0.778409 rgba(78,156,213,1));width: 13px;margin-top: -6px;margin-bottom: -6px;border-radius: 8px;border: 2px solid #252930;}";
        styleSheetStr += "QSlider::groove:horizontal:disabled{background:rgba(56,60,66,1);}";
        styleSheetStr += "QSlider::sub-page:horizontal:disabled {background:rgba(56,60,66,1);}";
        styleSheetStr += "QSlider::add-page:horizontal:disabled {background:rgba(56,60,66,1);}";
        styleSheetStr += "QSlider::handle:horizontal:disabled {background:rgba(56,60,66,1);}";
    }
    else
    {
        styleSheetStr += "QSlider::groove:vertical {height:142px;width: 5px;border-radius: 1px;padding-left:-1px;padding-right:-1px;padding-top:-1px;padding-bottom:-1px;}";
        styleSheetStr += "QSlider::sub-page:vertical {background: #384355;}";
        styleSheetStr += "QSlider::add-page:vertical {background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 #c4c4c4, stop:1 #B1B1B1);background: qlineargradient(x1: 0, y1: 0.2, x2: 1, y2: 1,stop: 0 #5DCCFF, stop: 1 #1874CD);}";
        styleSheetStr += "QSlider::handle:vertical {background: qradialgradient(spread:pad, cx:0.5, cy:0.5, radius:0.1, fx:0.5, fy:0.5, stop:0.6 #45ADED, stop:0.778409 rgba(78,156,213,1));height: 13px;margin-left: -6px;margin-right: -6px;border-radius: 8px;border: 2px solid #252930;}";
        styleSheetStr += "QSlider::groove:vertical:disabled{background:rgba(56,60,66,1);}";
        styleSheetStr += "QSlider::sub-page:vertical:disabled {background:rgba(56,60,66,1);}";
        styleSheetStr += "QSlider::add-page:vertical:disabled {background:rgba(56,60,66,1);}";
        styleSheetStr += "QSlider::handle:vertical:disabled {background:rgba(56,60,66,1);}";
    }

    return styleSheetStr;
}

QString QMTTools::GetCurDateTimeStr()
{
    QString createDateTime = QDateTime::currentDateTime().toString("yyyyMMddhhmmss");
    return createDateTime;
}

