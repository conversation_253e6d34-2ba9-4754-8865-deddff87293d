﻿// *********************************************************************************
// <remarks>
// FileName    : EclipseImportResultDialog
// Author      : zlw
// CreateTime  : 2024-04-07
// Description : Eclipse模板导入结果弹窗
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_EclipseImportResultDialog.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


class EclipseImportResultDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    EclipseImportResultDialog(QWidget* parent = nullptr);
    ~EclipseImportResultDialog();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="sketchCollectionList">[IN]勾画模板列表</param>
    void init(const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& sketchCollectionList);

protected:
    virtual void onBtnCloseClicked() override;          //关闭按钮
    virtual void onBtnRight2Clicked() override;         //取消按钮
    virtual void onBtnRight1Clicked() override;         //确认按钮

private:
    Ui::EclipseImportResultDialogClass ui;
};
