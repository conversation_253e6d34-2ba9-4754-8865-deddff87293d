﻿#pragma once

#include <QWidget>
#include <QLabel>
#include "QMTEnumDef.h"
#include "QMTAbstractRowItemWithBorder.h"


namespace Ui
{
class QMTAbstractListView;
}
class  QMTAbstractListView : public QWidget
{
    Q_OBJECT

public:
    QMTAbstractListView(QWidget* parent = Q_NULLPTR);
    ~QMTAbstractListView();
    //ui
    void InitListViewParam(QMTPerRowItemWidgetParam&);
    bool SetHorizontalHeaderList(QStringList value);
    void SetColumnCount(int columns);
    void SetColumnWidth(int column, int width);
    void SetHorizontalScrollBarPolicy(Qt::ScrollBarPolicy policy);
    //add
    void AddRowItem(QJsonObject);
    virtual QMTAbstractRowItemWithBorder* AddRowItemWidget(QJsonObject jsonObj);
    //set
    void ResetColumnItemString(int index, int column, QString defalutStr = "");
    //get
    QWidget* GetRowItemWidget(int index);
    QWidget* GetColumnItemWidget(int index, int column);
    QString GetColumnItemString(int index, int column);
    int GetRowCount();
    int GetColumnCount();
    QStringList GetHeadList();
    //signals
    void SetPerRowWidgetSignals(QWidget*);

private slots:
    void slotCurrentIndexChanged(int, QString);
private:
    Ui::QMTAbstractListView* ui;
    QList<QLabel*> _headerWidgetList;
    QWidget* _contentParent = nullptr;
public:
    QMTPerRowItemWidgetParam _listViewParam;
    QList<QVariantHash> _widgetUiHashList;
};
