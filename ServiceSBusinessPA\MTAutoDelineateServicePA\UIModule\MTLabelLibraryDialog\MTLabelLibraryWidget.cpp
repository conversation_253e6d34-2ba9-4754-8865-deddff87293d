﻿#include "MTLabelLibraryWidget.h"
#include "ui_MTLabelLibraryWidget.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "MtToolButton.h"
#include "MTLabelLibraryDialog\LabelLibrarySub\LabelLibraryWidget.h"
#include "CommonUtil.h"
#include "CMtLanguageUtil.h"


namespace n_mtautodelineationdialog
{

MTLabelLibraryWidget::MTLabelLibraryWidget(int nWidth, int nHeight, QWidget* parent)
    : QWidget(parent)
    , m_bImportBtnHidden(false)
    , m_bUseROIBtnHidden(false)
{
    ui = new Ui::MTLabelLibraryWidgetClass;
    ui->setupUi(this);
    nWidth = -1 == nWidth ? 785 : nWidth;
    nHeight = -1 == nHeight ? 506 : nHeight;

    if (CMtLanguageUtil::type == chinese)
    {
        nWidth += 200;
    }

    //
    //加载自定义qss
    //此时需要把qss/xml文件加入到qrc中，由外部使用者通过CMtWidgetManager初始化
    {
        QString errMsg;
        CMtSkinManager* skinManager = CMtWidgetManager::getInstance()->GetMtSkinManager();

        if (skinManager->LoadDomainWidgetSkinStyle(this, errMsg) == false)
        {
            std::cout << "LoadDomainWidgetSkinStyle failed,MTLabelLibraryWidget, " << errMsg.toStdString();
        }
    }
    //
    this->installEventFilter(this);
}

MTLabelLibraryWidget::~MTLabelLibraryWidget()
{
}

void MTLabelLibraryWidget::setHiddenColumn(const QVector<int>& hideColIndexVec)
{
    m_hiddenColVec = hideColIndexVec;
}

void MTLabelLibraryWidget::setDisableColumn(const QVector<int>& editDisableColVec)
{
    m_disableColVec = editDisableColVec;
}

void MTLabelLibraryWidget::setImportButtonHidden()
{
    m_bImportBtnHidden = true;
}

void MTLabelLibraryWidget::setUseROIFirstButtonHidden()
{
    m_bUseROIBtnHidden = true;
}

bool MTLabelLibraryWidget::isInfoChanged()
{
    QWidget* wdg = getContentWidget();
    LabelLibraryWidget* labelLibraryWidget = qobject_cast<LabelLibraryWidget*>(wdg);

    if (nullptr != labelLibraryWidget)
    {
        return labelLibraryWidget->isNeedSave2File();
    }

    return false;
}

void MTLabelLibraryWidget::resetInfoChangeStatus()
{
    QWidget* wdg = getContentWidget();
    LabelLibraryWidget* labelLibraryWidget = qobject_cast<LabelLibraryWidget*>(wdg);

    if (nullptr != labelLibraryWidget)
    {
        labelLibraryWidget->resetSaveFileStatus();
    }
}

void MTLabelLibraryWidget::getWidgetData(QList<ST_RoiLabelInfo>& outStRoiLabelInfoList
/*, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& outAllGroupList*/)
{
    QWidget* wdg = getContentWidget();
    LabelLibraryWidget* labelLibraryWidget = qobject_cast<LabelLibraryWidget*>(wdg);
    outStRoiLabelInfoList.clear();

    if (nullptr != labelLibraryWidget)
    {
        //获取缓存数据
        outStRoiLabelInfoList = labelLibraryWidget->getAllRoiLabelInfo();
        //outAllGroupList = roiLibraryWidget->getAllOrganGroupInfo();
    }
}

/// <summary>
/// 显示Roi库设置弹窗
/// </summary>
/// <param name="roiTypeList">[IN]Roi类型集合(如果为空将采用内置的类型集合)</param>
/// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
void MTLabelLibraryWidget::initLabelLibrarySettingWidget(const QStringList& roiTypeList, const QList<ST_RoiLabelInfo>& stRoiLabelInfoVec)
{
    QWidget* wdg = getContentWidget();
    LabelLibraryWidget* labelLibraryWidget = qobject_cast<LabelLibraryWidget*>(wdg);

    if (nullptr != labelLibraryWidget)
    {
        delete labelLibraryWidget;
        labelLibraryWidget = nullptr;
    }

    labelLibraryWidget = new LabelLibraryWidget(this);
    labelLibraryWidget->setImagePathHash(m_imagePathHash);
    labelLibraryWidget->init("", true, false, roiTypeList.isEmpty() == true ? CommonUtil::getRoiTypeList() : roiTypeList, stRoiLabelInfoVec);
    labelLibraryWidget->hideLabelListColumn(m_hiddenColVec);
    labelLibraryWidget->enableLabelListColumn(m_disableColVec, false);

    if (m_bImportBtnHidden) labelLibraryWidget->hideImportButton();

    if (m_bUseROIBtnHidden) labelLibraryWidget->hideUseROIFirstButton();

    ui->verticalLayout_2->addWidget(labelLibraryWidget);
}

/// <summary>
/// 加载界面的皮肤配置信息
/// </summary>
/// <param name="xmlSettingFile">[IN]xml资源文件路径</param>
void MTLabelLibraryWidget::LoadWidgetSkinStyleWithSettingFile(const QString& xmlSettingFile)
{
    m_imagePathHash = CMtCoreWidgetUtil::readComponentXmlFile(xmlSettingFile);
}

bool MTLabelLibraryWidget::eventFilter(QObject* obj, QEvent* event)
{
    //点击保存和取消按钮时，让焦点设置到列表上，使列表输入框失去焦点而触发编辑完成事件
    if (event->type() == QEvent::MouseButtonPress)
    {
        QMouseEvent* mouse_event = static_cast<QMouseEvent*>(event);

        if (mouse_event->button() == Qt::LeftButton)
        {
            for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
            {
                QLayoutItem* item = ui->verticalLayout_2->itemAt(i);

                if (item->widget())
                {
                    LabelLibraryWidget* labelWidget = qobject_cast<LabelLibraryWidget*>(item->widget());

                    if (labelWidget)
                    {
                        labelWidget->removeFocusFromTable();
                    }
                }
            }
        }
    }

    return QWidget::eventFilter(obj, event);
}
QWidget* MTLabelLibraryWidget::getContentWidget()
{
    for (int i = 0; i < ui->verticalLayout_2->count(); ++i)
    {
        QLayoutItem* item = ui->verticalLayout_2->itemAt(i);
        LabelLibraryWidget* tabWidget = qobject_cast<LabelLibraryWidget*>(item->widget());

        if (tabWidget)
        {
            return item->widget();
        }
    }

    return nullptr;
}

}
