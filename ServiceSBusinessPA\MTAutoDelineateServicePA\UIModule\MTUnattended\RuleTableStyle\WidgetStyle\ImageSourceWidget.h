﻿// *********************************************************************************
// <remarks>
// FileName    : ImageSourceWidget
// Author      : zlw
// CreateTime  : 2024-05-24
// Description : 无人值守规则界面数据来源Widget(内嵌于: UnattendSubWidget下的mtListWidget_left)
// </remarks>
// **********************************************************************************
#pragma once

#include <QWidget>
#include "ui_ImageSourceWidget.h"


class ImageSourceWidget : public QWidget
{
    Q_OBJECT

public:
    ImageSourceWidget(const QString imageSourceUniqueKey, QWidget* parent = nullptr);
    ~ImageSourceWidget();

    /// <summary>
    /// 更新UI
    /// </summary>
    /// <param name="serverName">[IN]本地服务器名</param>
    /// <param name="serverType">[IN]本地服务器类型</param>
    void updateUI(const QString enableImgPath, const QString& serverNameStr, const QString& serverTypeStr);

    /// <summary>
    /// 获取影像来源唯一标识
    /// </summary>
    QString getImageSourceUniqueKey();

    /// <summary>
    /// 获取服务器名
    /// </summary>
    QString getServerName();

private:
    Ui::ImageSourceWidgetClass ui;
    QString m_imageSourceUniqueKey;
};
