﻿//*****************************************************************************
// Medical Imaging Solutions
// Contact number 17317554131
// xinbo.fu.
//
// Filename: ProgressDialog.cpp
//
//*****************************************************************************

#include "AccuComponentUi\Header\DarkeningWidget.h"
#include "AccuComponentUi\Header\ProgressDialog.h"

#include <QKeyEvent>
#include <QDebug>
#include <QThread>
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include "ui_ProgressDialog.h"

/**
 * Default constructor
 */
ProgressDialog::ProgressDialog(QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint | Qt::SubWindow), ui(nullptr) //不能使用Qt::TOOL,否则会导致上层最小化问题
{
    ui = new Ui::ProgressDialog;
    // Create the UI
    ui->setupUi(this);
    ui->progressLabel->setProperty(QssPropertyKey, QssPropertyLabelSecondTitle);
    Initialize(true);
}

ProgressDialog::ProgressDialog(bool connectSlotPushButtonClose, QWidget* parent)
    : QDialog(parent, Qt::FramelessWindowHint | Qt::SubWindow) //不能使用Qt::TOOL,否则会导致上层最小化问题
{
    // Create the UI
    ui->setupUi(this);
    Initialize(connectSlotPushButtonClose);
}

void ProgressDialog::Initialize(bool connectSlotPushButtonClose)
{
    _darkeningWidget = new DarkeningWidget();
    // Make the background transparent
    setAttribute(Qt::WA_TranslucentBackground);
    setValue(0);
    // Hide the cancel button by default
    ui->cancelPushButton->setVisible(false);
    connect(ui->cancelPushButton, SIGNAL(clicked()), this, SIGNAL(progressCancelled()));

    if (connectSlotPushButtonClose)
    {
        connect(ui->cancelPushButton, SIGNAL(clicked()), this, SLOT(slotPushButtonClose()));
    }

    // Make this a modal dialog (needs to be modal for the screen dimming to work as implemented)
    setModal(true);
}

/**
 * Virtual destructor
 */
ProgressDialog::~ProgressDialog()
{
    //doing this here in case hide() isn't called.
    if (ui)
    {
        delete ui;
    }

    _darkeningWidget->hide();
    delete _darkeningWidget;
    _darkeningWidget = NULL;
}

/**
 * Sets the message area text and updates the size of the dialog
 * @param text The text for the message area (translated)
 */
void ProgressDialog::setMessage(const QString& text)
{
    if (_darkeningWidget)
    {
        _darkeningWidget->show();
        ui->progressLabel->setText(text);
    }

    //dialogContainer->adjustSize();
}

void ProgressDialog::slotPushButtonClose()
{
    this->close();
}

void ProgressDialog::hideProgress(bool value)
{
    if (value)
    {
        ui->progressBar->hide();
        ui->label_bar->hide();
    }
    else
    {
        ui->progressBar->show();
    }
}

/**
 * Sets the message area text for Import progress and updates the size of the dialog
 */
void ProgressDialog::setImportMessage()
{
    ui->progressLabel->setText(tr("Importing patient images..."));
    //dialogContainer->adjustSize();
}

/**
 * Sets the message area text for Segmentation progress and updates the size of the dialog
 */
void ProgressDialog::setSegmentMessage()
{
    ui->progressLabel->setText(tr("Segmenting Lungs..."));
    //dialogContainer->adjustSize();
}

/**
 * Shows/hides the cancel button
 * @param show true to show the button, false to hide
 */
void ProgressDialog::showCancelButton(bool show)
{
    ui->cancelPushButton->setVisible(show);
    ui->widget_line->setVisible(show);
    //dialogContainer->adjustSize();
}

void ProgressDialog::enableCancelButton(bool enable)
{
    ui->cancelPushButton->setEnabled(enable);
}

/**
 * Sets the range of values that the progress bar represents. 0 to 100 by
 * default.
 * @param min The minimum value (integer)
 * @param max The maximum value (integer)
 */
void ProgressDialog::setRange(int min, int max)
{
    ui->progressBar->setRange(min, max);
}

/**
 * Sets the value of the progress bar
 * @param value Integer value from min to max
 */
void ProgressDialog::setValue(int value)
{
    if (value > 100)
        return;

    ui->progressBar->setValue(value);
    QString valueStr = QString::number(value) + QString("%");
    ui->label_bar->setText(valueStr);
    ui->label_bar->repaint();
}

/**
 * Gets the value of the progress bar
 * @return An integer value from min to max
 */
int ProgressDialog::value()
{
    return ui->progressBar->value();
}

void ProgressDialog::changeValue(int value)
{
    ui->progressBar->setValue(value);
    ui->label_bar->setText(QString::number(value) + QString("%"));

    if (value >= 100)
    {
        this->hide();
    }
}

/**
 * Re-implemented to dim the screen behind the dialog
 */
void ProgressDialog::showEvent(QShowEvent* event)
{
    _darkeningWidget->show();
    ui->progressBar->setFocus();
    QDialog::showEvent(event);
}

/**
 * Re-implemented to dim the screen behind the dialog
 */
void ProgressDialog::hideEvent(QHideEvent* event)
{
    _darkeningWidget->hide();
    QDialog::hideEvent(event);
}

void ProgressDialog::hideDialog(bool hide)
{
    if (hide)
    {
        _darkeningWidget->hide();
        this->hide();
    }
    else
    {
        _darkeningWidget->show();
        this->show();
    }
}

void ProgressDialog::setButtonTest(const QString& text)
{
    setCancelButtonText(text);
}

void ProgressDialog::setCancelButtonText(const QString& text)
{
    ui->cancelPushButton->setText(text);
}

/// <summary>
/// 禁用esc按钮
/// </summary>
/// <param name="event"></param>
void ProgressDialog::keyPressEvent(QKeyEvent* event)
{
    switch (event->key())
    {
        case Qt::Key_Escape:
            break;

        default:
            QDialog::keyPressEvent(event);
    }
}
