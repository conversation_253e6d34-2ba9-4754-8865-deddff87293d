﻿#include "EclipseImportResultTable.h"
#include "Skin/CMtSkinManager.h"
#include "CMtWidgetManager.h"
#include "CMtCoreWidgetUtil.h"
#include "AccuComponentUi\Header\UnitUIComponent/QCustMtLabel.h"
#include "AccuComponentUi\Header\UnitUIComponent/QMTAbsHorizontalBtns.h"
#include "MtMessageBox.h"
#include "CommonUtil.h"
#include "AccuComponentUi\Header\Language.h"


/// <summary>
/// 构造函数
/// </summary>
EclipseImportResultTable::EclipseImportResultTable(QWidget* parent)
    : QMTAbstractTableView(parent)
{
}

EclipseImportResultTable::~EclipseImportResultTable()
{
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="sketchCollectionList">[IN]勾画模板列表</param>
void EclipseImportResultTable::init(const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& sketchCollectionList)
{
    QVector<int> cellWidthVec = { 46, 344, 116 };

    if (Language::type == English)
        cellWidthVec = { 66, 344, 96 };

    //初始化标题
    initTableView(
        {
            tr("序号"),  tr("模板名称"),  tr("状态")
        },
        cellWidthVec);

    for (int i = 0; i < sketchCollectionList.size(); i++)
    {
        n_mtautodelineationdialog::ST_SketchModelCollection info = sketchCollectionList[i];
        addRow(i + 1, info.templateName, tr("创建完成"));
    }
}

/// <summary>
/// 添加一行
/// </summary>
/// <param name="noNum">[IN]编号</param>
/// <param name="templateName">[IN]模板名称</param>
/// <param name="status">[IN]创建状态</param>
void EclipseImportResultTable::addRow(const int noNum, const QString& templateName, const QString& status)
{
    QMap<int/*column*/, ICellWidgetParam*> cellWidgetParamMap;
    //序号
    QCustMtLabelParam* numLabelParam = new QCustMtLabelParam();
    numLabelParam->_text = QString::number(noNum);
    cellWidgetParamMap.insert(COL_Num, numLabelParam);
    //模板名称
    QCustMtLabelParam* templateNameLabelParam = new QCustMtLabelParam();
    templateNameLabelParam->_text = templateName;
    cellWidgetParamMap.insert(COL_TemplateName, templateNameLabelParam);
    //状态
    QCustMtLabelParam* statusLabelParam = new QCustMtLabelParam();
    statusLabelParam->_text = status;
    cellWidgetParamMap.insert(COL_TemplateStatus, statusLabelParam);
    //
    this->AddRowItem(QString::number(noNum), cellWidgetParamMap);
}

/// <summary>
/// 初始化表格
/// </summary>
/// <param name="headList">[IN]表头文本集合</param>
/// <param name="cellWidthVec">[IN]单元格宽度集合(最后一列自适应不用传)</param>
void EclipseImportResultTable::initTableView(const QStringList& headList, QVector<int> cellWidthVec)
{
    QMTAbsRowWidgetItemParam firstViewParam;
    firstViewParam._templateWidgetType = QMTAbsRowWidgetItemParam::GetCurrentType();
    //表头
    firstViewParam._headParam._headStrList = headList;
    firstViewParam._headParam._defaultColumn = headList.size();
    //firstViewParam._headParam._paddingLeft = 16;
    firstViewParam._headParam._fontSize = 12;
    firstViewParam._headParam._headHeight = 30;
    firstViewParam._headParam._isHideHeadCloumnLine = true;

    for (int i = 0; i < cellWidthVec.size(); i++)
    {
        firstViewParam._headParam._columnWidthMap.insert(i, cellWidthVec[i]);
    }

    //行对应
    firstViewParam._fontsize = 12;
    firstViewParam._rowWidgetHeight = 36;
    firstViewParam._isHideCloumnLine = false;
    firstViewParam._showGrid = true;
    firstViewParam._enableFlex = false;
    InitTableView(firstViewParam);
}
