﻿#pragma once

#include <QWidget>
#include <QPushButton>
#include <QToolButton>
#include <QMap>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"
#include "MtToolButton.h"

namespace Ui
{
class MtUnitToolButtonGroup;
}
class MtToolButton;

//MtUnitToolButtonGroup 参数
class  MtUnitToolButtonGroupParam : public ICellWidgetParam
{
public:
    QStringList _btnIconPathList;      //按键对应的图片
    int _btnWidth = -1;
    int _btnHeight = -1;
    QMap<int/*btnIndex*/, int/*mtType*/> _btnIndexMtTypeMap;    //按键下标对应的mtType
    QMap<int/*btnIndex*/, bool/*enabled*/> _btnIndexEnabledMap; //按键可用状态
    QMap<int/*btnIndex*/, QString/*tip*/> _btnIndexTipMap;      //按键下标对应的tip

    MtUnitToolButtonGroupParam();
    ~MtUnitToolButtonGroupParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(MtUnitToolButtonGroupParam)

/*
MtToolButton集合
*/

class  MtUnitToolButtonGroup :
    public QWidget,
    public QMTAbstractCellWidget
{
    Q_OBJECT
public:

    MtUnitToolButtonGroup(QWidget* parent = Q_NULLPTR);
    ~MtUnitToolButtonGroup();

    /*单元格公共接口*/
    //更新界面接口
    virtual bool UpdateUi(const QVariant& updateData);
    //设置是否允许编辑(有是否允许编辑状态的子控件必须实现)
    virtual void SetEnableEdit(bool bEdit);
    //按键使能设置
    virtual void SetButtonEnable(int btnIndex/**/, bool bEnable);
    //获取checked
    virtual bool GetCellChecked(int index);
    //设置按键无焦点
    virtual void SetButtonNoFocus(int btnIndex);

    /******************ui************************/
    void SetupCellWidget(MtUnitToolButtonGroupParam& param);
    void HideButton(int btnIndex, bool bHide);          //是否隐藏按键
    void SetButtonIconPathFile(int btnIndex, QString& iconPathFile);    //设置某个按键的图标

signals:
    void sigClicked(int);
    void sigButtonClicked(int/*btnIndex*/, bool/*ischecked*/);  //某个按键点击了

protected:
    /*******************delete*************************/
    void DeleteButtons();

private slots:
    void slotButtonClicked(bool);

private:
    Ui::MtUnitToolButtonGroup* ui;
    MtUnitToolButtonGroupParam _pushBtnGroupParam;
    QList<MtToolButton*> _buttonList;

};
