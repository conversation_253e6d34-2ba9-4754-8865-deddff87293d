<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MtUnitPushButtonGroup</class>
 <widget class="QWidget" name="MtUnitPushButtonGroup">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>218</width>
    <height>41</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>MtUnitPushButtonGroup</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>8</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="QWidget" name="widget_btnsBK" native="true">
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_2">
      <property name="spacing">
       <number>16</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget_expand" native="true">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources>
  <include location="../../Resources/CustomizeUiModule.qrc"/>
 </resources>
 <connections/>
</ui>
