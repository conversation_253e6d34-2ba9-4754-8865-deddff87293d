﻿#include "ModelInfoSetting.h"
#include <QAbstractButton>
#include <QDateTime>

ModelInfoSetting::ModelInfoSetting(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //基本属性
    this->setMainLayout(ui.horizontalLayout_2);            //设置布局
    this->setDialogWidthAndContentHeight(466, 146);    //弹窗的宽度 里面内容的实际高度(实际高减去102)
    this->setTitle(tr("编辑模型"));                  //设置标题
    this->setAllowDrag(true);                           //设置是否可拖动
    this->SetKeyEnterEnable(false);                     //不响应Enter按键
    //getWidgetButton()->hide();                        //如果不想右下角有按钮
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("确定"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
}

ModelInfoSetting::~ModelInfoSetting()
{
}

void ModelInfoSetting::setInfo(const QString& name, const QString& time, const QString& description)
{
    ui.mtLineEdit_name->setText(name);
    ui.mtLineEdit_time->setText(formatDateTimeStr(time));
    ui.mtTextEdit_desc->setText(description);
}

void ModelInfoSetting::info(QString& name, QString& description)
{
    name = ui.mtLineEdit_name->text();
    description = ui.mtTextEdit_desc->toPlainText();
}

QString ModelInfoSetting::formatDateTimeStr(QString dateTimeStr)
{
    bool isOK = false;
    QString dataAndTimeStr;
    dateTimeStr = dateTimeStr.remove(QRegExp("[\\s:/]")); //去除所有空格

    if (dateTimeStr.size() == 14)
    {
        dataAndTimeStr = QDateTime::fromString(dateTimeStr, "yyyyMMddhhmmss").toString("yyyy-MM-dd hh:mm:ss");
    }
    else if (dataAndTimeStr.size() == 8)
    {
        dataAndTimeStr = QDateTime::fromString(dateTimeStr, "yyyyMMdd").toString("yyyy-MM-dd");
    }
    else
    {
        dataAndTimeStr = "-";
    }

    return dataAndTimeStr;
}

void ModelInfoSetting::onBtnCloseClicked()
{
    this->reject();
}

void ModelInfoSetting::onBtnRight2Clicked()
{
    this->reject();
}

void ModelInfoSetting::onBtnRight1Clicked()
{
    this->accept();
}