﻿// *********************************************************************************
// <remarks>
// FileName    : OptSketchCollection
// Author      : zlw
// CreateTime  : 2024-01-25
// Description : 自动勾画模板信息
// </remarks>
// **********************************************************************************
#pragma once

#include <QObject>
#include <iostream>
#include "DataDefine/MTAutoDelineationDialogData.h"


struct ST_OrganGroupSort
{
    QMap<int/*groupId*/, QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>> organByGroupIdMap;
};

class OptSketchCollection : public QObject
{
    Q_OBJECT

public:
    OptSketchCollection();
    ~OptSketchCollection();

    /// <summary>
    /// 设置当模板被无人值守使用时，是否显示提示框
    /// \n不设置: 默认显示
    /// </summary>
    /// <param name="isShow">true显示</param>
    void setIsShowTipOfModUnattendUsed(const bool isShow);

    /// <summary>
    /// 获取当模板被无人值守使用时，是否显示提示框
    /// </summary>
    bool getIsShowTipOfModUnattendUsed();

    /// <summary>
    /// 初始化
    /// </summary>
    /// <param name="allGroupInfoList">[IN]所有分组信息(包括主分组亚分组)</param>
    /// <param name="allModelList">[IN]所有模型集合</param>
    /// <param name="allSketchCollectionMap">[IN]所有模板集合</param>
    /// <param name="virtualSketchCollection">[IN]虚拟模板，用于选择ROI进行勾画页签</param>
    void init(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
              const QList<n_mtautodelineationdialog::ST_SketchModel>& allModelList,
              const QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>& allSketchCollectionMap,
              const n_mtautodelineationdialog::ST_SketchModelCollection virtualSketchCollection);

    /// <summary>
    /// 重新初始化Organ自定义器官名
    /// </summary>
    /// <param name="allOrganList">全部Organ信息</param>
    void reInitOrganCustomName(const QList<n_mtautodelineationdialog::ST_Organ>& newAllOrganList);

    /// <summary>
    /// 获取模板id排序顺序
    /// </summary>
    /// <returns>value-templateId</returns>
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> getTemplateIdSortMap();

    /// <summary>
    /// 获取所有主分组信息集合
    /// </summary>
    /// <returns>所有主分组信息集合</returns>
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getAllMainGroupInfoList();

    /// <summary>
    /// 更新所有主分组信息集合
    /// </summary>
    /// <returns>所有主分组信息集合</returns>
    void setAllMainGroupInfoList(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& groupInfoList);

    /// <summary>
    /// 获取亚结构groupId和主分组gorupId对应关系
    /// </summary>
    /// <returns>亚结构groupId和主分组gorupId对应关系</returns>
    QMap<int/*亚结构groupId*/, QSet<int/*主结构groupId*/>> getMainGroupIdBySubGroupIdMap();

    /// <summary>
    /// 将分组集合中的亚分组转换为主分组
    /// </summary>
    /// <param name="allGroupIdSet">分组集合(包括主亚)</param>
    /// <returns>亚分组转换为主分组后的集合</returns>
    QSet<int> changeSubGroupIdToMainGroupId(const QSet<int>& allGroupIdSet);

    /// <summary>
    /// 将分组集合中的亚分组转换为主分组
    /// </summary>
    /// <param name="allGroupIdSet">[IN]原始分组集合(包括主亚)</param>
    /// <param name="outOldMainGroupIdSet">[OUT]原始主分组集合</param>
    /// <returns>亚分组转换为主分组后的集合</returns>
    QSet<int> changeSubGroupIdToMainGroupId(const QSet<int>& allGroupIdSet, QSet<int>& outOldMainGroupIdSet);

    /// <summary>
    /// 将分组集合中的亚分组转换为主分组
    /// </summary>
    /// <param name="allGroupIdSet">[IN]原始分组集合(包括主亚)</param>
    /// <param name="outOldMainGroupIdList">[OUT]原始主分组集合(排序后的)</param>
    /// <returns>亚分组转换为主分组后的集合</returns>
    QSet<int> changeSubGroupIdToMainGroupId(const QSet<int>& allGroupIdSet, QList<int>& outOldMainGroupIdList);

    /// <summary>
    /// 获取主分组集合中的第一个分组id
    /// </summary>
    /// <param name="mainGroupIdSet">[IN]主分组集合</param>
    /// <returns>主分组集合中的第一个分组id</returns>
    int getFirstMainGroupIdOfSet(const QSet<int>& mainGroupIdSet);

    /// <summary>
    /// 按模态获取器官按照groupId分组,编辑时显示全部Roi信息用
    /// </summary>
    /// <param name="dcmTypeEnum">[IN]模板模态</param>
    /// <returns>排序后的模板信息集合</returns>
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> getOrganByGroupIdMap(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum);

    /// <summary>
    /// 整理器官将亚结构位置插入到主结构后面(纯展示用)
    /// </summary>
    /// <param name="organByGroupIdMap">[IN]整理前数据(key-groupId)</param>
    /// <param name="subOrganTypeMap">[OUT]亚结构的信息(key-亚结构organId value-1:找到主结构 2:未找到主结构)</param>
    /// <returns>整理后数据(key-groupId)<</returns>
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> sortOrganByGroupIdMapToShow(const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap, QMap<int, int>& subOrganTypeMap);

    /// <summary>
    /// 整理器官将亚结构位置插入到主结构后面,不区分亚结构(纯展示用)
    /// </summary>
    /// <param name="organByGroupIdMap">[IN]整理前数据(key-groupId)</param>
    /// <returns>整理后数据(key-groupId)<</returns>
    QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> sortOrganByGroupIdMapToShow_NoSortSub(const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap);

    /// <summary>
    /// 获取空勾画的id集合
    /// </summary>
    /// <returns>空勾画的id集合</returns>
    QSet<int> getEmptyOrganIdSet();

    /// <summary>
    /// 添加空勾画
    /// </summary>
    /// <param name="emptyRoiMap">[IN]空勾画信息(key-organId)</param>
    void addEmptyOrgan(const QMap<int, n_mtautodelineationdialog::ST_Organ>& emptyRoiMap);

    /// <summary>
    /// 创建一个新的模板名称(从老的名称中+(2))
    /// </summary>
    /// <param name="oldTemplateName">[IN]老模板名称</param>
    /// <returns>新的模板名称</returns>
    QString createNewTemplateName(const QString& oldTemplateName);

    /// <summary>
    /// 获取所有模板信息,不用排序
    /// </summary>
    /// <returns>所有模板信息,不用排序</returns>
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> getSketchCollectionListNotSort();

    /// <summary>
    /// 获取排序后的模板信息集合
    /// </summary>
    /// <param name="dcmTypeEnum">[IN]模板模态</param>
    /// <returns>排序后的模板信息集合</returns>
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> getSortSketchCollectionList(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum);

    /// <summary>
    /// 是否存在模板名称
    /// </summary>
    /// <param name="templateName">[IN]模板名称</param>
    /// <param name="ignoreTemplateId">[IN]忽略的模板id</param>
    /// <returns>true存在</returns>
    bool isExistTemplateName(const QString& templateName, const int ignoreTemplateId);

    /// <summary>
    /// 是否存在该模板id
    /// </summary>
    /// <param name="dcmTypeEnum">[IN]模板模态</param>
    /// <param name="templateId">[IN]模板id</param>
    /// <returns>true存在</returns>
    bool isExistTemplateId(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, const int templateId);

    /// <summary>
    /// 获取模板
    /// </summary>
    /// <param name="dcmTypeEnum">[IN]模板模态</param>
    /// <param name="templateId">[IN]模板id</param>
    /// <returns>模板</returns>
    n_mtautodelineationdialog::ST_SketchModelCollection getSketchColleciton(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, const int templateId);

    /// <summary>
    /// 添加模板
    /// </summary>
    /// <param name="stSketchCollection">[IN]模板信息</param>
    void addCollection(const n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection);

    /// <summary>
    /// 更新模板
    /// </summary>
    /// <param name="stSketchCollection">[IN]模板信息</param>
    void updateCollection(const n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection);

    /// <summary>
    /// 删除模板
    /// </summary>
    /// <param name="stSketchCollection">[IN]模板id</param>
    void delCollection(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, const int templateId);

    /// <summary>
    /// 更新模板排序
    /// </summary>
    /// <param name="dcmTypeEnum">[IN]模板模态</param>
    /// <param name="newTemplateIdSortList">[IN]排序后的模板id集合</param>
    void updateAllTemplateIsSort(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, QList<int>& newTemplateIdSortList);

    /// <summary>
    /// 设置模板拖拽使能
    /// </summary>
    /// <param name="enable">true使能</param>
    void setTableNameDropEnable(const bool enable);

signals:
    void sigTableNameDropEnable(const bool enable); //模板拖拽使能

protected:
    /// <summary>
    /// 按照器官自定义名称排序
    /// </summary>
    void sortOrganInfoByCustomName(const QList<n_mtautodelineationdialog::ST_SketchModel>& allModelList);

private:
    QSet<int> m_emptyOrganIdSet;            //空勾画id集合
    bool m_IsShowTipUnattendUsed = true;    //当模板被无人值守使用时，是否显示提示框
    n_mtautodelineationdialog::ST_SketchModelCollection m_virtualSketchCollection;  //虚拟模板，用于选择ROI进行勾画页签
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> m_allMainGroupInfoList;     //排序后的所有主分组信息(不包括亚组)
    QList<n_mtautodelineationdialog::ST_OrganGroupInfo> m_allSubGroupList;          //所有亚分组信息
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> m_templateIdSortMap; //模板id排序(value-templateId)
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection>> m_sketchCollectionMap; //模板集合
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>> m_organByGroupIdMap; //器官按照groupId分组,编辑时显示全部Roi信息用（亚结构分组会被归到主分组）
    QMap<int/*亚结构groupId*/, QSet<int/*主结构groupId*/>> m_mainGroupIdBySubGroupIdMap; //亚结构分组和主结构分组对应关系
};