﻿#pragma once

#include <QWidget>
#include <QJsonObject>
#include "AccuComponentUi/Header/QMTUIModuleParam.h"


//QMTAbsComboBox 参数
class  QMTVDoubleLabelDotParam : public ICellWidgetParam
{
public:
    QString _firstText;         //第一行文案
    QString _secondText;        //第二行文案
    QString _preFirstText;      //第一行前言文案
    QString _preSecondText;     //第二行前言文案

    //前言ui设置
    bool _bShowPreLabel = false;    //是否要显示前言文案
    int _preLabelWidth = 56;        //前言label的宽度，后部分文案宽度自适应
    QColor _preBackgroundColor = QColor(219, 226, 241, 0.09 * 255);    //前言背景色
    QColor _preLableTextColor = QColor(219, 226, 241, 0.6 * 255);     //前言字体颜色
    int _preLabelFontSize = 12;     //前言字体大小

    //正文ui设置
    int _labelHeight = 0;           //显示label的高度，统一设置
    QColor _lableTextColor = QColor(219, 226, 241);       //正文字体颜色
    int _labelTopFontSize = 12;     //正文上部分字体大小
    int _labelBottomFontSize = 12;  //正文下部分字体大小

    int _verSpacing = 3;            //上下文案间隔
    QMTVDoubleLabelDotParam();
    virtual QWidget* CreateUIModule(QWidget* parent = NULL);
};
Q_DECLARE_METATYPE(QMTVDoubleLabelDotParam)


namespace Ui
{
class QMTVDoubleLabelDot;
}

/*两行单元格*/
class  QMTVDoubleLabelDot : public QWidget, public QMTAbstractCellWidget
{
    Q_OBJECT

public:
    typedef struct ST_UpdateVDoubleLabelDotInfo
    {
        QString _firstText;         //第一行文案
        QString _secondText;        //第二行文案
        QString _preFirstText;      //第一行前言文案
        QString _preSecondText;     //第二行前言文案
    } UpdateVDoubleLabelDotInfo;

    QMTVDoubleLabelDot(QWidget* parent = Q_NULLPTR);
    ~QMTVDoubleLabelDot();

    /*单元格公共接口*/
    virtual bool UpdateUi(const QVariant& updateData);       //更新界面接口


    void SetupCellWidget(QMTVDoubleLabelDotParam& cellWidgetParam); //初始化单元格界面

    void SetPreFirstValue(const QString& text);       //设置第一行前言字符串
    void SetPreSecondValue(const QString& text);       //设置第二行前言字符串

    void SetFirstValue(const QString& text);           //设置第一行字符串
    void SetSecondValue(const QString& text);          //设置第二行字符串

    void SetPreLableStyleSheet(const QString& styleSheetStr);           //设置前言QLabel文案样式
    void SetContentLableStyleSheet(const QString& styleSheetStr);       //设置正文QLabel文案样式
    void SetCustStyleSheet(const QString& text);        //设置自定义样式
    void SetFirstQssProperty(const QString& value);        //设置第一行qss属性
    void SetSecondQssProperty(const QString& value);       //设置第二行qss属性


protected:
    void InitTemplateStyle();
private:
    Ui::QMTVDoubleLabelDot* ui;
};

Q_DECLARE_METATYPE(QMTVDoubleLabelDot::UpdateVDoubleLabelDotInfo)
