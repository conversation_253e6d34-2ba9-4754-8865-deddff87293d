﻿#include "OptSketchCollection.h"
#include "../DataDefine/InnerStruct.h"

#define Def_MaxColumn   3   //一行3列


OptSketchCollection::OptSketchCollection()
{
}

OptSketchCollection::~OptSketchCollection()
{
}

/// <summary>
/// 设置当模板被无人值守使用时，是否显示提示框
/// \n不设置: 默认显示
/// </summary>
/// <param name="isShow">true显示</param>
void OptSketchCollection::setIsShowTipOfModUnattendUsed(const bool isShow)
{
    m_IsShowTipUnattendUsed = isShow;
}

/// <summary>
/// 获取当模板被无人值守使用时，是否显示提示框
/// </summary>
bool OptSketchCollection::getIsShowTipOfModUnattendUsed()
{
    return m_IsShowTipUnattendUsed;
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="allGroupInfoList">[IN]所有分组信息(包括主分组亚分组)</param>
/// <param name="allModelList">[IN]所有模型集合</param>
/// <param name="allSketchCollectionMap">[IN]所有模板集合</param>
/// <param name="virtualSketchCollection">[IN]虚拟模板，用于选择ROI进行勾画页签</param>
void OptSketchCollection::init(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupInfoList,
                               const QList<n_mtautodelineationdialog::ST_SketchModel>& allModelList,
                               const QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>>& allSketchCollectionMap,
                               const n_mtautodelineationdialog::ST_SketchModelCollection virtualSketchCollection)
{
    //清空
    m_allMainGroupInfoList.clear();
    m_allSubGroupList.clear();
    m_emptyOrganIdSet.clear();
    m_templateIdSortMap.clear();
    m_sketchCollectionMap.clear();
    m_organByGroupIdMap.clear();
    m_mainGroupIdBySubGroupIdMap.clear();
    //虚拟模板
    m_virtualSketchCollection = virtualSketchCollection;
    //整理分组，去除亚组
    m_allMainGroupInfoList.reserve(allGroupInfoList.size() + 1);

    for (int i = 0; i < allGroupInfoList.size(); i++)
    {
        if (allGroupInfoList[i].type != 3)
        {
            m_allMainGroupInfoList.push_back(allGroupInfoList[i]);
        }
        else
        {
            m_allSubGroupList.push_back(allGroupInfoList[i]);
        }
    }

    //整理排序信息
    m_templateIdSortMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    m_templateIdSortMap[n_mtautodelineationdialog::OptDcmType_MR].clear();
    //
    //整理模板信息
    m_sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    m_sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].clear();

    if (allSketchCollectionMap.contains(n_mtautodelineationdialog::OptDcmType_CT))
    {
        QList<n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionList = allSketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT];

        for (int i = 0; i < sketchCollectionList.size(); i++)
        {
            m_templateIdSortMap[n_mtautodelineationdialog::OptDcmType_CT].push_back(sketchCollectionList[i].id);
            m_sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_CT].insert(sketchCollectionList[i].id, sketchCollectionList[i]);
        }
    }

    if (allSketchCollectionMap.contains(n_mtautodelineationdialog::OptDcmType_MR))
    {
        QList<n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionList = allSketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR];

        for (int i = 0; i < sketchCollectionList.size(); i++)
        {
            m_templateIdSortMap[n_mtautodelineationdialog::OptDcmType_MR].push_back(sketchCollectionList[i].id);
            m_sketchCollectionMap[n_mtautodelineationdialog::OptDcmType_MR].insert(sketchCollectionList[i].id, sketchCollectionList[i]);
        }
    }

    //按照器官自定义名称排序
    m_organByGroupIdMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    m_organByGroupIdMap[n_mtautodelineationdialog::OptDcmType_MR].clear();
    sortOrganInfoByCustomName(allModelList);
}

/// <summary>
/// 重新初始化Organ自定义器官名
/// </summary>
/// <param name="allOrganList">全部Organ信息</param>
void OptSketchCollection::reInitOrganCustomName(const QList<n_mtautodelineationdialog::ST_Organ>& newAllOrganList)
{
    QMap<int/*organId*/, n_mtautodelineationdialog::ST_Organ> newAllOrganMap;

    for (int i = 0; i < newAllOrganList.size(); i++)
    {
        newAllOrganMap.insert(newAllOrganList[i].id, newAllOrganList[i]);
    }

    //重新排序(其实只是将已有器官进行排序)
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>> tempMap = m_organByGroupIdMap;
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>>::iterator it = tempMap.begin();
    m_organByGroupIdMap[n_mtautodelineationdialog::OptDcmType_CT].clear();
    m_organByGroupIdMap[n_mtautodelineationdialog::OptDcmType_MR].clear();

    for (it; it != tempMap.end(); it++)
    {
        n_mtautodelineationdialog::EM_OptDcmType optDcmTypeEnum = it.key();
        QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> groupIdMap = it.value();

        for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator iter_2 = groupIdMap.begin(); iter_2 != groupIdMap.end(); iter_2++)
        {
            int groupId = iter_2.key();
            QList<n_mtautodelineationdialog::ST_Organ> organList = iter_2.value();
            QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ> sortOrganMap; //按照customName排序后的器官集合

            for (int i = 0; i < organList.size(); i++)
            {
                n_mtautodelineationdialog::ST_Organ organInfo = organList[i];

                if (newAllOrganMap.contains(organInfo.id) == true)
                {
                    organInfo.customOrganName = newAllOrganMap[organInfo.id].customOrganName;
                }

                //重新排序
                QString newTag = organInfo.customOrganName + Def_Separator + QString::number(organInfo.id);
                sortOrganMap.insert(newTag, organInfo);
            }

            m_organByGroupIdMap[optDcmTypeEnum][groupId] = sortOrganMap.values();
        }
    }
}

/// <summary>
/// 获取模板id排序顺序
/// </summary>
/// <returns>value-templateId</returns>
QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>> OptSketchCollection::getTemplateIdSortMap()
{
    return m_templateIdSortMap;
}

/// <summary>
/// 获取所有主分组信息
/// </summary>
/// <returns>所有主分组信息</returns>
QList<n_mtautodelineationdialog::ST_OrganGroupInfo> OptSketchCollection::getAllMainGroupInfoList()
{
    return m_allMainGroupInfoList;
}

void OptSketchCollection::setAllMainGroupInfoList(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& groupInfoList)
{
    m_allMainGroupInfoList = groupInfoList;
}

/// <summary>
/// 获取亚结构groupId和主分组gorupId对应关系
/// </summary>
/// <returns>亚结构groupId和主分组gorupId对应关系</returns>
QMap<int, QSet<int>> OptSketchCollection::getMainGroupIdBySubGroupIdMap()
{
    return m_mainGroupIdBySubGroupIdMap;
}

/// <summary>
/// 将分组集合中的亚分组转换为主分组
/// </summary>
/// <param name="allGrpupIdSet">分组集合(包括主亚)</param>
/// <returns>亚分组转换为主分组后的集合</returns>
QSet<int> OptSketchCollection::changeSubGroupIdToMainGroupId(const QSet<int>& allGroupIdSet)
{
    QSet<int> newGroupIdSet = allGroupIdSet;

    //和已知的所有亚组比较
    for (int i = 0; i < m_allSubGroupList.size(); i++)
    {
        int subGroupId = m_allSubGroupList[i].id;

        if (allGroupIdSet.contains(subGroupId) == true)
        {
            newGroupIdSet.remove(subGroupId);

            if (m_mainGroupIdBySubGroupIdMap.contains(subGroupId) == true)
            {
                newGroupIdSet = newGroupIdSet + m_mainGroupIdBySubGroupIdMap[subGroupId];
            }
        }
    }

    return newGroupIdSet;
}

/// <summary>
/// 将分组集合中的亚分组转换为主分组
/// </summary>
/// <param name="allGroupIdSet">[IN]原始分组集合(包括主亚)</param>
/// <param name="outOldMainGroupIdSet">[OUT]原始主分组集合</param>
/// <returns>亚分组转换为主分组后的集合</returns>
QSet<int> OptSketchCollection::changeSubGroupIdToMainGroupId(const QSet<int>& allGroupIdSet, QSet<int>& outOldMainGroupIdSet)
{
    QSet<int> newGroupIdSet = allGroupIdSet;
    QSet<int> outOldSubGroupIdSet;

    //和已知的所有亚组比较
    for (int i = 0; i < m_allSubGroupList.size(); i++)
    {
        int subGroupId = m_allSubGroupList[i].id;

        if (allGroupIdSet.contains(subGroupId) == true)
        {
            outOldSubGroupIdSet.insert(subGroupId);
            newGroupIdSet.remove(subGroupId);

            if (m_mainGroupIdBySubGroupIdMap.contains(subGroupId) == true)
            {
                newGroupIdSet = newGroupIdSet + m_mainGroupIdBySubGroupIdMap[subGroupId];
            }
        }
    }

    outOldMainGroupIdSet = allGroupIdSet - outOldSubGroupIdSet;
    return newGroupIdSet;
}

/// <summary>
/// 将分组集合中的亚分组转换为主分组
/// </summary>
/// <param name="allGroupIdSet">[IN]原始分组集合(包括主亚)</param>
/// <param name="outOldMainGroupIdList">[OUT]原始主分组集合(排序后的)</param>
/// <returns>亚分组转换为主分组后的集合</returns>
QSet<int> OptSketchCollection::changeSubGroupIdToMainGroupId(const QSet<int>& allGroupIdSet, QList<int>& outOldMainGroupIdList)
{
    QSet<int> oldMainGroupIdSet;
    QSet<int> newGroupIdSet = changeSubGroupIdToMainGroupId(allGroupIdSet, oldMainGroupIdSet);

    for (int i = 0; i < m_allMainGroupInfoList.size(); i++)
    {
        if (oldMainGroupIdSet.contains(m_allMainGroupInfoList[i].id) == true)
        {
            outOldMainGroupIdList.push_back(m_allMainGroupInfoList[i].id);
        }
    }

    return newGroupIdSet;
}

/// <summary>
/// 获取主分组集合中的第一个分组id
/// </summary>
/// <param name="mainGroupIdSet">[IN]主分组集合</param>
/// <returns>主分组集合中的第一个分组id</returns>
int OptSketchCollection::getFirstMainGroupIdOfSet(const QSet<int>& mainGroupIdSet)
{
    for (int i = 0; i < m_allMainGroupInfoList.size(); i++)
    {
        if (mainGroupIdSet.contains(m_allMainGroupInfoList[i].id) == true)
        {
            return m_allMainGroupInfoList[i].id;
        }
    }

    return -1;
}

/// <summary>
/// 获取器官按照groupId分组,编辑时显示全部Roi信息用
/// </summary>
QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> OptSketchCollection::getOrganByGroupIdMap(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum)
{
    return m_organByGroupIdMap[dcmTypeEnum];
}

/// <summary>
/// 整理器官将亚结构位置插入到主结构后面(纯展示用)
/// </summary>
/// <param name="organByGroupIdMap">[IN]整理前数据(key-groupId)</param>
/// <param name="subOrganTypeMap">[OUT]亚结构的信息(key-亚结构organId value-1:找到主结构 2:未找到主结构)</param>
/// <returns>整理后数据(key-groupId)<</returns>
QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> OptSketchCollection::sortOrganByGroupIdMapToShow(const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap, QMap<int, int>& subOrganTypeMap)
{
    struct timespec tmv1, tmv2;
    timespec_get(&tmv1, 1);
    //
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap = organByGroupIdMap;

    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = newOrganByGroupIdMap.begin(); it != newOrganByGroupIdMap.end(); it++)
    {
        QSet<int> mainOrganIdSet;
        QList<n_mtautodelineationdialog::ST_Organ> oldOrganList = it.value();

        //先统计一下主结构
        for (int i = 0; i < oldOrganList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = oldOrganList[i];

            if (stOrgan.organGroupInfoMap.isEmpty() == false && stOrgan.organGroupInfoMap.begin().value().type != 3)
            {
                mainOrganIdSet.insert(stOrgan.id);
            }
        }

        //统计一下有主结构的亚结构并去除
        QMap<QString/*customName.id*/, n_mtautodelineationdialog::ST_Organ> subOrganMap;
        QMap<int/*主结构organId*/, QMap<QString/*亚结构customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>> subOrganByMainOrganIdMap;
        QList<n_mtautodelineationdialog::ST_Organ> newOrganList;
        newOrganList.reserve(oldOrganList.size() + 1);

        for (int i = 0; i < oldOrganList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ stOrgan = oldOrganList[i];

            if (stOrgan.organGroupInfoMap.size() == 1 && stOrgan.organGroupInfoMap.begin().value().type == 3)
            {
                int mainOrganId = stOrgan.organGroupInfoMap.begin().value().refOrganId;

                if (mainOrganIdSet.contains(mainOrganId) == true)
                {
                    QString customNameAndId = stOrgan.customOrganName + Def_Separator + QString::number(stOrgan.id); //用于按照自定义名称排序
                    subOrganByMainOrganIdMap[mainOrganId].insert(customNameAndId, stOrgan);
                    subOrganTypeMap.insert(stOrgan.id, 1);
                    continue;
                }
                else //没找到挂载的主结构(搜索时会出现)
                {
                    subOrganTypeMap.insert(stOrgan.id, 2);
                }
            }

            newOrganList.push_back(stOrgan);
        }

        //将亚结构排在主结构后面
        for (int i = 0; i < newOrganList.size(); i++)
        {
            n_mtautodelineationdialog::ST_Organ mainOrgan = newOrganList[i];

            if (subOrganByMainOrganIdMap.contains(mainOrgan.id) == false)
                continue;

            int subInsertNum = i; //插入的位置
            QMap<QString/*亚结构器官customOrganName.id*/, n_mtautodelineationdialog::ST_Organ> subOrganMap = subOrganByMainOrganIdMap[mainOrgan.id];

            for (QMap<QString/*亚结构器官customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>::iterator iter_sub = subOrganMap.begin(); iter_sub != subOrganMap.end(); iter_sub++)
            {
                n_mtautodelineationdialog::ST_Organ subOrgan = iter_sub.value();
                subInsertNum = subInsertNum + 1;
                newOrganList.insert(subInsertNum, subOrgan);
            }

            //去掉这个亚结构
            subOrganByMainOrganIdMap.remove(mainOrgan.id);
        }

        //按纵向排序
        QList<n_mtautodelineationdialog::ST_Organ> colOneList, colTwoList, colThreeList;
        colOneList.reserve(100);
        colTwoList.reserve(100);
        colThreeList.reserve(100);
        // 计算每列应包含的最大元素数量
        int colSize = newOrganList.size() / 3;      //每列几个元素
        int extraElements = newOrganList.size() % 3;//最后一列多出几个元素

        // 分配元素到三列
        for (int i = 0; i < colSize; i++)
        {
            colOneList.push_back(newOrganList[i]);
        }

        for (int i = colSize; i < colSize * 2; i++)
        {
            colTwoList.push_back(newOrganList[i]);
        }

        for (int i = colSize * 2; i < newOrganList.size(); i++)
        {
            colThreeList.push_back(newOrganList[i]);
        }

        //如果第三列数量超过了前两列
        if (colThreeList.size() == colSize + 1)
        {
            if (colTwoList.size() > 0)
            {
                colOneList.push_back(colTwoList[0]);
                colTwoList.pop_front();
                colTwoList.push_back(colThreeList[0]);
                colThreeList.pop_front();
            }
        }
        else if (colThreeList.size() == colSize + 2)
        {
            if (colTwoList.size() > 0)
            {
                colOneList.push_back(colTwoList[0]);
                colTwoList.pop_front();
            }

            if (colThreeList.size() > 0)
            {
                colTwoList.push_back(colThreeList[0]);
                colThreeList.pop_front();
            }

            if (colThreeList.size() > 0)
            {
                colTwoList.push_back(colThreeList[0]);
                colThreeList.pop_front();
            }
        }

        //重新合成newOrganList
        newOrganList.clear();
        int size = std::max(colOneList.size(), std::max(colTwoList.size(), colThreeList.size()));

        for (int i = 0; i < size; i++)
        {
            if (i < colOneList.size())
            {
                newOrganList.push_back(colOneList[i]);
            }

            if (i < colTwoList.size())
            {
                newOrganList.push_back(colTwoList[i]);
            }

            if (i < colThreeList.size())
            {
                newOrganList.push_back(colThreeList[i]);
            }
        }

        it.value() = newOrganList;
    }

    timespec_get(&tmv2, 1);
    printf("sortOrganByGroupIdMapToShow time: %lld\n", tmv2.tv_sec - tmv1.tv_sec);
    return newOrganByGroupIdMap;
}

/// <summary>
/// 整理器官将亚结构位置插入到主结构后面,不区分亚结构(纯展示用)
/// </summary>
/// <param name="organByGroupIdMap">[IN]整理前数据(key-groupId)</param>
/// <returns>整理后数据(key-groupId)<</returns>
QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> OptSketchCollection::sortOrganByGroupIdMapToShow_NoSortSub(const QMap<int, QList<n_mtautodelineationdialog::ST_Organ>>& organByGroupIdMap)
{
    struct timespec tmv1, tmv2;
    timespec_get(&tmv1, 1);
    //
    QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> newOrganByGroupIdMap = organByGroupIdMap;

    for (QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>::iterator it = newOrganByGroupIdMap.begin(); it != newOrganByGroupIdMap.end(); it++)
    {
        QList<n_mtautodelineationdialog::ST_Organ> newOrganList = it.value();
        //按纵向排序
        QList<n_mtautodelineationdialog::ST_Organ> colOneList, colTwoList, colThreeList;
        colOneList.reserve(100);
        colTwoList.reserve(100);
        colThreeList.reserve(100);
        // 计算每列应包含的最大元素数量
        int colSize = newOrganList.size() / 3;      //每列几个元素
        int extraElements = newOrganList.size() % 3;//最后一列多出几个元素

        //如果少于3个
        if (colSize == 0)
        {
            continue;;
        }

        // 分配元素到三列
        for (int i = 0; i < colSize; i++)
        {
            colOneList.push_back(newOrganList[i]);
        }

        for (int i = colSize; i < colSize * 2; i++)
        {
            colTwoList.push_back(newOrganList[i]);
        }

        for (int i = colSize * 2; i < newOrganList.size(); i++)
        {
            colThreeList.push_back(newOrganList[i]);
        }

        //如果第三列数量超过了前两列
        if (colThreeList.size() == colSize + 1)
        {
            if (colTwoList.size() > 0)
            {
                colOneList.push_back(colTwoList[0]);
                colTwoList.pop_front();
                colTwoList.push_back(colThreeList[0]);
                colThreeList.pop_front();
            }
        }
        else if (colThreeList.size() == colSize + 2)
        {
            if (colTwoList.size() > 0)
            {
                colOneList.push_back(colTwoList[0]);
                colTwoList.pop_front();
            }

            if (colThreeList.size() > 0)
            {
                colTwoList.push_back(colThreeList[0]);
                colThreeList.pop_front();
            }

            if (colThreeList.size() > 0)
            {
                colTwoList.push_back(colThreeList[0]);
                colThreeList.pop_front();
            }
        }

        //重新合成newOrganList
        newOrganList.clear();
        int size = std::max(colOneList.size(), std::max(colTwoList.size(), colThreeList.size()));

        for (int i = 0; i < size; i++)
        {
            if (i < colOneList.size())
            {
                newOrganList.push_back(colOneList[i]);
            }

            if (i < colTwoList.size())
            {
                newOrganList.push_back(colTwoList[i]);
            }

            if (i < colThreeList.size())
            {
                newOrganList.push_back(colThreeList[i]);
            }
        }

        it.value() = newOrganList;
    }

    timespec_get(&tmv2, 1);
    printf("sortOrganByGroupIdMapToShow_NoSortSub time: %lld\n", tmv2.tv_sec - tmv1.tv_sec);
    return newOrganByGroupIdMap;
}

/// <summary>
/// 获取空勾画的id集合
/// </summary>
/// <returns>空勾画的id集合</returns>
QSet<int> OptSketchCollection::getEmptyOrganIdSet()
{
    return m_emptyOrganIdSet;
}

/// <summary>
/// 添加空勾画
/// </summary>
/// <param name="emptyRoiMap">[IN]空勾画信息(key-organId)</param>
void OptSketchCollection::addEmptyOrgan(const QMap<int, n_mtautodelineationdialog::ST_Organ>& emptyRoiMap)
{
    auto sortRoi = [](QList<n_mtautodelineationdialog::ST_Organ> srcOrganInfoList, QList<n_mtautodelineationdialog::ST_Organ> newOrganInfoList)->QList<n_mtautodelineationdialog::ST_Organ>
    {
        QMap<QString, n_mtautodelineationdialog::ST_Organ> newOrganMap;
        srcOrganInfoList.append(newOrganInfoList);

        for (int i = 0; i < srcOrganInfoList.size(); i++)
        {
            newOrganMap.insert(srcOrganInfoList[i].customOrganName, srcOrganInfoList[i]);
        }

        return newOrganMap.values();
    };

    if (emptyRoiMap.isEmpty() == true)
        return;

    //空勾画分组id是7
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>>>::iterator it = m_organByGroupIdMap.begin();

    for (it; it != m_organByGroupIdMap.end(); it++)
    {
        QMap<int/*groupId*/, QList<n_mtautodelineationdialog::ST_Organ>> groupOrganMap = it.value();

        if (groupOrganMap.contains(7) == true) //已经有空勾画队列
        {
            m_organByGroupIdMap[it.key()].insert(7, sortRoi(groupOrganMap[7], emptyRoiMap.values()));
        }
        else //不存在空勾画队列
        {
            m_organByGroupIdMap[it.key()].insert(7, sortRoi(QList<n_mtautodelineationdialog::ST_Organ>(), emptyRoiMap.values()));
        }
    }
}

/// <summary>
/// 创建一个新的模板名称(从老的名称中+(2))
/// </summary>
/// <param name="oldTemplateName">[IN]老模板名称</param>
/// <returns>新的模板名称</returns>
QString OptSketchCollection::createNewTemplateName(const QString& oldTemplateName)
{
    QSet<QString> templateNameSet;
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection>>::iterator it = m_sketchCollectionMap.begin();

    for (it; it != m_sketchCollectionMap.end(); it++)
    {
        QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionMap = it.value();

        for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it2 = sketchCollectionMap.begin(); it2 != sketchCollectionMap.end(); it2++)
        {
            templateNameSet.insert(it2.value().templateName);
        }
    }

    for (int i = 1; i < 100; i++)
    {
        QString newName = oldTemplateName + QString("(") + QString::number(i) + QString(")");

        if (templateNameSet.contains(newName) == false)
        {
            return newName;
        }
    }

    return QString();
}

/// <summary>
/// 获取所有模板信息,不用排序
/// </summary>
/// <returns>所有模板信息,不用排序</returns>
QList<n_mtautodelineationdialog::ST_SketchModelCollection> OptSketchCollection::getSketchCollectionListNotSort()
{
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> dataList;
    dataList.reserve(100);
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection>>::iterator it = m_sketchCollectionMap.begin();

    for (it; it != m_sketchCollectionMap.end(); it++)
    {
        QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionMap = it.value();

        for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it2 = sketchCollectionMap.begin(); it2 != sketchCollectionMap.end(); it2++)
        {
            dataList.push_back(it2.value());
        }
    }

    return dataList;
}

/// <summary>
/// 获取排序后的模板信息集合
/// </summary>
/// <returns>排序后的模板信息集合</returns>
QList<n_mtautodelineationdialog::ST_SketchModelCollection> OptSketchCollection::getSortSketchCollectionList(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum)
{
    auto getSortCollection = [this](const QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection> sketchColletctionMap, QList<int>& templateIdSortList)->QList<n_mtautodelineationdialog::ST_SketchModelCollection>
    {
        QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection> tempColletctionMap = sketchColletctionMap;
        //
        QList<n_mtautodelineationdialog::ST_SketchModelCollection> listData;
        listData.reserve(tempColletctionMap.size() + 1);

        for (int i = 0; i != templateIdSortList.size(); i++)
        {
            int templateId = templateIdSortList[i];

            if (tempColletctionMap.contains(templateId) == true)
            {
                listData.push_back(tempColletctionMap[templateId]);
                tempColletctionMap.remove(templateId);
            }
        }

        //将没在排序中的整理一下，并更新
        for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it = tempColletctionMap.begin(); it != tempColletctionMap.end(); it++)
        {
            listData.push_back(it.value());
            templateIdSortList.push_back(it.key());
        }



        return listData;
    };
    //
    QList<n_mtautodelineationdialog::ST_SketchModelCollection> sortCollectionList;
    sortCollectionList = getSortCollection(m_sketchCollectionMap[dcmTypeEnum], m_templateIdSortMap[dcmTypeEnum]);
    return sortCollectionList;
}

/// <summary>
/// 是否存在模板名称
/// </summary>
/// <param name="templateName">[IN]模板名称</param>
/// <param name="ignoreTemplateId">[IN]忽略的模板id</param>
/// <returns>true存在</returns>
bool OptSketchCollection::isExistTemplateName(const QString& templateName, const int ignoreTemplateId)
{
    QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection>>::iterator it = m_sketchCollectionMap.begin();

    for (it; it != m_sketchCollectionMap.end(); it++)
    {
        QMap<int/*templateId*/, n_mtautodelineationdialog::ST_SketchModelCollection> sketchCollectionMap = it.value();

        for (QMap<int, n_mtautodelineationdialog::ST_SketchModelCollection>::iterator it2 = sketchCollectionMap.begin(); it2 != sketchCollectionMap.end(); it2++)
        {
            if (it2.value().id != ignoreTemplateId && templateName.trimmed().toUpper() == it2.value().templateName.trimmed().toUpper())
            {
                return true;
            }
        }
    }

    return false;
}

/// <summary>
/// 是否存在该模板id
/// </summary>
/// <param name="templateId">[IN]模板id</param>
/// <returns>true存在</returns>
bool OptSketchCollection::isExistTemplateId(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, const int templateId)
{
    return m_sketchCollectionMap[dcmTypeEnum].contains(templateId);
}

/// <summary>
/// 获取模板
/// </summary>
/// <param name="dcmTypeEnum">[IN]模板模态</param>
/// <param name="templateId">[IN]模板id</param>
/// <returns>模板</returns>
n_mtautodelineationdialog::ST_SketchModelCollection OptSketchCollection::getSketchColleciton(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, const int templateId)
{
    if (m_sketchCollectionMap[dcmTypeEnum].contains(templateId))
        return m_sketchCollectionMap[dcmTypeEnum][templateId];

    return n_mtautodelineationdialog::ST_SketchModelCollection();
}

/// <summary>
/// 添加模板
/// </summary>
/// <param name="stSketchCollection">[IN]模板信息</param>
void OptSketchCollection::addCollection(const n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection)
{
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum;

    if (stSketchCollection.modality.toUpper() == "CT")
    {
        dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
    }
    else if (stSketchCollection.modality.toUpper() == "MR")
    {
        dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_MR;
    }

    m_sketchCollectionMap[dcmTypeEnum][stSketchCollection.id] = stSketchCollection;
    m_templateIdSortMap[dcmTypeEnum].push_back(stSketchCollection.id);
}

/// <summary>
/// 更新模板
/// </summary>
/// <param name="stSketchCollection">[IN]模板信息</param>
void OptSketchCollection::updateCollection(const n_mtautodelineationdialog::ST_SketchModelCollection& stSketchCollection)
{
    n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum;

    if (stSketchCollection.modality.toUpper() == "CT")
    {
        dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_CT;
    }
    else if (stSketchCollection.modality.toUpper() == "MR")
    {
        dcmTypeEnum = n_mtautodelineationdialog::OptDcmType_MR;
    }

    m_sketchCollectionMap[dcmTypeEnum][stSketchCollection.id] = stSketchCollection;
}

/// <summary>
/// 删除模板
/// </summary>
/// <param name="templateId">[IN]模板id</param>
void OptSketchCollection::delCollection(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, const int templateId)
{
    m_sketchCollectionMap[dcmTypeEnum].remove(templateId);
    m_templateIdSortMap[dcmTypeEnum].removeAll(templateId);
}

/// <summary>
/// 更新模板排序
/// </summary>
/// <param name="dcmTypeEnum">[IN]模板模态</param>
/// <param name="newTemplateIdSortList">[IN]排序后的模板id集合</param>
void OptSketchCollection::updateAllTemplateIsSort(const n_mtautodelineationdialog::EM_OptDcmType dcmTypeEnum, QList<int>& newTemplateIdSortList)
{
    //数量一致的时候直接赋值，例如全部模板
    if (newTemplateIdSortList.size() == m_templateIdSortMap[dcmTypeEnum].size())
    {
        m_templateIdSortMap[dcmTypeEnum] = newTemplateIdSortList;
    }
    else//不一致时重新排序，例如无人值守
    {
        QSet<int> newIdSet;

        for (int i = 0; i < newTemplateIdSortList.size(); i++)
        {
            newIdSet.insert(newTemplateIdSortList[i]);
        }

        //获取原始位置
        QList<int> oldSubIdList;
        QList<int> oldSortList = m_templateIdSortMap[dcmTypeEnum];

        for (int i = 0; i < oldSortList.size(); i++)
        {
            if (newIdSet.contains(oldSortList[i]) == true)
            {
                oldSubIdList.push_back(oldSortList[i]);
            }
        }

        //判断发生改变的编号
        int modOldId = -1;
        int modNewId = -1;

        for (int i = 0; i < oldSubIdList.size(); i++)
        {
            int oldId = oldSubIdList[i];
            int newId = newTemplateIdSortList[i];

            if (oldId != newId)
            {
                modOldId = oldId;
                modNewId = newId;
                break;
            }
        }

        //将原始排序中newId排在oldId之前
        if (modNewId != -1)
        {
            QList<int> newSortList;

            for (int i = 0; i < oldSortList.size(); i++)
            {
                if (modOldId == oldSortList[i])
                {
                    newSortList.push_back(modNewId);
                }
                else if (modNewId == oldSortList[i])
                {
                    continue;
                }

                newSortList.push_back(oldSortList[i]);
            }

            m_templateIdSortMap[dcmTypeEnum] = newSortList;
        }
    }
}

/// <summary>
/// 设置模板拖拽使能
/// </summary>
/// <param name="enable">true使能</param>
void OptSketchCollection::setTableNameDropEnable(const bool enable)
{
    emit this->sigTableNameDropEnable(enable);
}

/// <summary>
/// 按照器官自定义名称排序
/// </summary>
void OptSketchCollection::sortOrganInfoByCustomName(const QList<n_mtautodelineationdialog::ST_SketchModel>& allModelList)
{
    struct timespec tmv1, tmv2;
    timespec_get(&tmv1, 1);
    //
    struct ST_GroupIdByMainOrganId //主器官中，涉及到的主分组集合 以及 挂载的亚结构器官集合
    {
        QSet<int/*groupId*/> mainGroupIdSet;
        QMap<QString/*亚结构器官customOrganName.id*/, n_mtautodelineationdialog::ST_Organ> subOrganMap;
    };
    //亚结构分组和主分组无从属关系，故需加入主分组
    QMap<int/*groupId*/, QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>> ctMainOrganByGroupIdMap;
    QMap<int/*groupId*/, QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>> mrMainOrganByGroupIdMap;
    QMap<int/*主结构器官organId*/, ST_GroupIdByMainOrganId> subOrganByMainOrganIdMap;

    for (int i = 0; i < allModelList.size(); i++)
    {
        n_mtautodelineationdialog::ST_SketchModel stSketchModel = allModelList[i];
        QList<n_mtautodelineationdialog::ST_Organ> organList = stSketchModel.organList;

        //空勾画id=-1特殊处理
        if (allModelList[i].id == -1)
        {
            for (int m = 0; m < organList.size(); m++)
            {
                n_mtautodelineationdialog::ST_Organ stOrgan = organList[m];
                stOrgan.isVisiable = false;
                QString customNameAndId = stOrgan.customOrganName + Def_Separator + QString::number(stOrgan.id); //用于按照自定义名称排序
                QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoMap = stOrgan.organGroupInfoMap;

                for (QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo>::Iterator it = groupInfoMap.begin(); it != groupInfoMap.end(); it++)
                {
                    int groupId = it.key();
                    m_emptyOrganIdSet.insert(stOrgan.id);

                    //亚结构的分组独占一个map，不会再有其他主分组和亚分组
                    if (it.value().type == 3) //1系统内置 2自定义 3亚结构
                    {
                        subOrganByMainOrganIdMap[it.value().refOrganId].subOrganMap[customNameAndId] = stOrgan;
                        break;
                    }

                    //普通空勾画CT/MR都应该加
                    ctMainOrganByGroupIdMap[groupId].insert(customNameAndId, stOrgan);
                    mrMainOrganByGroupIdMap[groupId].insert(customNameAndId, stOrgan);
                    subOrganByMainOrganIdMap[stOrgan.id].mainGroupIdSet.insert(groupId);
                }
            }
        }
        else
        {
            for (int m = 0; m < organList.size(); m++)
            {
                n_mtautodelineationdialog::ST_Organ stOrgan = organList[m];
                stOrgan.isVisiable = false;
                QString customNameAndId = stOrgan.customOrganName + Def_Separator + QString::number(stOrgan.id); //用于按照自定义名称排序
                QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo> groupInfoMap = stOrgan.organGroupInfoMap;

                for (QMap<int/*groupId*/, n_mtautodelineationdialog::ST_OrganGroupInfo>::Iterator it = groupInfoMap.begin(); it != groupInfoMap.end(); it++)
                {
                    int groupId = it.key();

                    //亚结构的分组独占一个map，不会再有其他主分组和亚分组
                    if (it.value().type == 3) //1系统内置 2自定义 3亚结构
                    {
                        subOrganByMainOrganIdMap[it.value().refOrganId].subOrganMap[customNameAndId] = stOrgan;
                        break;
                    }

                    //主结构器官
                    if (stSketchModel.modality.toUpper() == "CT")
                        ctMainOrganByGroupIdMap[groupId].insert(customNameAndId, stOrgan);
                    else
                        mrMainOrganByGroupIdMap[groupId].insert(customNameAndId, stOrgan);

                    subOrganByMainOrganIdMap[stOrgan.id].mainGroupIdSet.insert(groupId);
                }
            }
        }
    }

    //将亚结构加入到主结构中,MR没有亚结构
    auto addSubOrganToMainOrganList = [this](const bool isCtMain, const QMap<int/*主结构器官organId*/, ST_GroupIdByMainOrganId>& subOrganByMainOrganIdMap,
                                             QMap<int/*groupId*/, QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>>& mainOrganByGroupIdMap)-> void
    {

        for (QMap<int/*主结构器官organId*/, ST_GroupIdByMainOrganId>::const_iterator it = subOrganByMainOrganIdMap.begin(); it != subOrganByMainOrganIdMap.end(); it++)
        {
            int mainOrganId = it.key();
            ST_GroupIdByMainOrganId stGroupIdByMainOrganId = it.value();

            if (isCtMain == false || stGroupIdByMainOrganId.subOrganMap.isEmpty() == true || stGroupIdByMainOrganId.mainGroupIdSet.isEmpty() == true)
                continue;

            for (QSet<int/*groupId*/>::iterator iter2 = stGroupIdByMainOrganId.mainGroupIdSet.begin(); iter2 != stGroupIdByMainOrganId.mainGroupIdSet.end(); iter2++)
            {
                int mainGroupId = *iter2;

                if (mainOrganByGroupIdMap.contains(mainGroupId) == false)
                    continue;

                for (QMap<QString/*亚结构器官customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>::iterator iter3 = stGroupIdByMainOrganId.subOrganMap.begin();
                     iter3 != stGroupIdByMainOrganId.subOrganMap.end(); iter3++)
                {
                    mainOrganByGroupIdMap[mainGroupId].insert(iter3.key(), iter3.value());
                }
            }

            for (QMap<QString/*亚结构器官customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>::iterator iter3 = stGroupIdByMainOrganId.subOrganMap.begin();
                 iter3 != stGroupIdByMainOrganId.subOrganMap.end(); iter3++)
            {
                QSet<int> tempSet = stGroupIdByMainOrganId.mainGroupIdSet;
                m_mainGroupIdBySubGroupIdMap.insert(iter3.value().organGroupInfoMap.begin().key(), stGroupIdByMainOrganId.mainGroupIdSet);//整理亚结构分组和主结构分组对应关系
            }
        }
    };
    //
    //CT-整理主结构和亚结构
    addSubOrganToMainOrganList(true, subOrganByMainOrganIdMap, ctMainOrganByGroupIdMap);

    for (QMap<int/*groupId*/, QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>>::iterator it = ctMainOrganByGroupIdMap.begin(); it != ctMainOrganByGroupIdMap.end(); it++)
    {
        int groupId = it.key();
        QList<n_mtautodelineationdialog::ST_Organ> tempList = it.value().values();
        m_organByGroupIdMap[n_mtautodelineationdialog::OptDcmType_CT][groupId] = tempList;
    }

    //MR-整理主结构和亚结构
    addSubOrganToMainOrganList(false, subOrganByMainOrganIdMap, mrMainOrganByGroupIdMap);

    for (QMap<int/*groupId*/, QMap<QString/*customOrganName.id*/, n_mtautodelineationdialog::ST_Organ>>::iterator it = mrMainOrganByGroupIdMap.begin(); it != mrMainOrganByGroupIdMap.end(); it++)
    {
        int groupId = it.key();
        QList<n_mtautodelineationdialog::ST_Organ> tempList = it.value().values();
        m_organByGroupIdMap[n_mtautodelineationdialog::OptDcmType_MR][groupId] = tempList;
    }

    timespec_get(&tmv2, 1);
    printf("sortOrganInfoByCustomName time: %lld\n", tmv2.tv_sec - tmv1.tv_sec);
}

