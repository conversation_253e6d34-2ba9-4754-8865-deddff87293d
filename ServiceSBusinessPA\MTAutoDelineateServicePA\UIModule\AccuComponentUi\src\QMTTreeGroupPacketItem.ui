<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QMTTreeGroupPacketItem</class>
 <widget class="QWidget" name="QMTTreeGroupPacketItem">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>182</width>
    <height>21</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>0</width>
    <height>21</height>
   </size>
  </property>
  <property name="maximumSize">
   <size>
    <width>16777215</width>
    <height>21</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>QMTTreeGroupPacketItem</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="frame">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="maximumSize">
      <size>
       <width>16777215</width>
       <height>30</height>
      </size>
     </property>
     <property name="autoFillBackground">
      <bool>false</bool>
     </property>
     <property name="styleSheet">
      <string notr="true"/>
     </property>
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::default_type</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <spacer name="horizontalSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>16</width>
          <height>0</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QCheckBox" name="checkBox_Expand">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>26</width>
          <height>34</height>
         </size>
        </property>
        <property name="layoutDirection">
         <enum>Qt::LeftToRight</enum>
        </property>
        <property name="autoFillBackground">
         <bool>false</bool>
        </property>
        <property name="styleSheet">
         <string notr="true">QCheckBox::indicator:unchecked {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_down.png);  
 }  
  
 QCheckBox::indicator:unchecked:hover {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_down_hover.png);  
 }  
  
 QCheckBox::indicator:unchecked:pressed {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_down.png);  
 }  
  
 QCheckBox::indicator:checked {  
     image: url(:/AccuUIComponentImage/images/treeGroup_up.png);  
 }  
  
 QCheckBox::indicator:checked:hover {  
     image: url(:/AccuUIComponentImage/images/treeGroup_up_hover.png);  
 }  
  
 QCheckBox::indicator:checked:pressed {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_up.png);  
 }  

 QCheckBox::indicator:indeterminate:hover {  
     image: url(:/AccuUIComponentImage/images/treeGroup_up_hover.png);  
 }  
  
 QCheckBox::indicator:indeterminate:pressed {  
     image:  url(:/AccuUIComponentImage/images/treeGroup_up.png);   
 }</string>
        </property>
        <property name="text">
         <string/>
        </property>
        <property name="iconSize">
         <size>
          <width>26</width>
          <height>26</height>
         </size>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>5</width>
          <height>0</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="MtLabel" name="sliderTextWidget">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>1234567</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_4">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>0</width>
          <height>0</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="MtLabel" name="label_Num">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Preferred" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>(0)</string>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtLabel::myLabel1</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
