﻿#include "GroupHTitle.h"

#define Def_ItemHeight      16  //项高度
#define Def_TreeClose       "treeClose"
#define Def_TreeExpand      "treeExpand"


GroupHTitle::GroupHTitle(QWidget* parent)
    : QWidget(parent)
{
    ui.setupUi(this);
    //信号槽
    connect(ui.mtCheckBox_titleName, &QCheckBox::stateChanged, this, &GroupHTitle::onCheckBoxStateChanged); //checkbox勾选
    connect(ui.mtToolButton_pageTitle, &QToolButton::clicked, this, &GroupHTitle::onMtToolButton_pageTitle); //非编辑页MtLabelLabel展开/收起按钮
    connect(ui.mtToolButton_pageCheck, &QToolButton::clicked, this, &GroupHTitle::onMtToolButton_pageCheck); //编辑页MtLabelCheck展开/收起按钮
}

GroupHTitle::~GroupHTitle()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void GroupHTitle::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    m_imagePathHash = imagePathHash;
}

/// <summary>
/// 初始化Item
/// </summary>
/// <param name="pageTypeEnum">[IN]页面类型</param>
/// <param name="data">[IN]数据</param>
/// <param name="topMargin">[IN]上边距</param>
/// <param name="bottomMargin">[IN]下边距</param>
/// <returns>项高度</returns>
int GroupHTitle::init(const EM_PageType pageTypeEnum, const GroupHTitleData& data, int topMargin, int bottomMargin)
{
    ui.verticalLayout->setContentsMargins(0, topMargin, 0, bottomMargin);
    m_pageTypeEnum = pageTypeEnum;
    m_groupId = data.groupId;

    if (pageTypeEnum == Page_Label)
    {
        ui.stackedWidget->setCurrentWidget(ui.page_title);
        ui.mtLabel_titleName->setStyleSheet("font-weight:bold;color:#269ED8;");
        ui.mtLabel_titleName->setText(data.value);
        ui.mtToolButton_pageTitle->setPixmapFilename(m_imagePathHash["icon_treeExpand"]);
        ui.mtToolButton_pageTitle->setWhatsThis(Def_TreeExpand);
    }
    else if (pageTypeEnum == Page_Check)
    {
        ui.stackedWidget->setCurrentWidget(ui.page_titleCheck);
        ui.mtCheckBox_titleName->setCheckState(data.state);
        ui.mtCheckBox_titleName->setStyleSheet("font-size:14px;font-weight:bold;color:#269ED8;");
        QString name = data.value;
        ui.mtCheckBox_titleName->setText(name.replace("&", "&&"));//避免button 将& 当做ampersand characterr
        ui.mtToolButton_pageCheck->setPixmapFilename(m_imagePathHash["icon_treeExpand"]);
        ui.mtToolButton_pageCheck->setWhatsThis(Def_TreeExpand);
    }

    //返回实际高度
    return Def_ItemHeight + topMargin + bottomMargin;
}

/// <summary>
/// 设置复选框状态
/// </summary>
void GroupHTitle::setCheckState(const Qt::CheckState state)
{
    disconnect(ui.mtCheckBox_titleName, &QCheckBox::stateChanged, this, &GroupHTitle::onCheckBoxStateChanged);
    ui.mtCheckBox_titleName->setCheckState(state);
    connect(ui.mtCheckBox_titleName, &QCheckBox::stateChanged, this, &GroupHTitle::onCheckBoxStateChanged);
}
/// <summary>
/// 设置复选框状态
/// </summary>
void GroupHTitle::setCheckState(const bool isCheck)
{
    if (m_pageTypeEnum == Page_Check)
    {
        ui.mtCheckBox_titleName->setChecked(isCheck);
    }
}

/// <summary>
/// 获取展开状态
/// </summary>
bool GroupHTitle::getExpandButtonState()
{
    if (m_pageTypeEnum == Page_Label)
    {
        if (ui.mtToolButton_pageTitle->whatsThis() == Def_TreeClose)
            return false;

        return true;
    }
    else if (m_pageTypeEnum == Page_Check)
    {
        if (ui.mtToolButton_pageCheck->whatsThis() == Def_TreeClose)
            return false;

        return true;
    }

    return false;
}

/// <summary>
/// 设置展开按钮状态
/// </summary>
/// <param name="expand">[IN]true展开</param>
void GroupHTitle::setExpandButtonState(const bool expand)
{
    if (m_pageTypeEnum == Page_Label)
    {
        onMtToolButton_pageTitle();
    }
    else if (m_pageTypeEnum == Page_Check)
    {
        onMtToolButton_pageCheck();
    }
}

/// <summary>
/// checkbox勾选
/// </summary>
void GroupHTitle::onCheckBoxStateChanged(int state)
{
    disconnect(ui.mtCheckBox_titleName, &QCheckBox::stateChanged, this, &GroupHTitle::onCheckBoxStateChanged);

    if (state == Qt::Unchecked)
    {
        ui.mtCheckBox_titleName->setChecked(false);
        emit this->sigGroupHTitleCheckToGroupItemListWidget(m_groupId, false);
    }
    else
    {
        ui.mtCheckBox_titleName->setChecked(true);
        emit this->sigGroupHTitleCheckToGroupItemListWidget(m_groupId, true);
    }

    connect(ui.mtCheckBox_titleName, &QCheckBox::stateChanged, this, &GroupHTitle::onCheckBoxStateChanged);
}

/// <summary>
/// 展开/收起按钮MtLabelLabel
/// </summary>
void GroupHTitle::onMtToolButton_pageTitle()
{
    //判断前一个状态
    if (ui.mtToolButton_pageTitle->whatsThis() == Def_TreeClose)
    {
        ui.mtToolButton_pageTitle->setPixmapFilename(m_imagePathHash["icon_treeExpand"]);
        ui.mtToolButton_pageTitle->setWhatsThis(Def_TreeExpand);
        emit this->sigGroupHTitleExpandToGroupItemListWidget(Page_Label, m_groupId, true);
    }
    else if (ui.mtToolButton_pageTitle->whatsThis() == Def_TreeExpand)
    {
        ui.mtToolButton_pageTitle->setPixmapFilename(m_imagePathHash["icon_treeClose"]);
        ui.mtToolButton_pageTitle->setWhatsThis(Def_TreeClose);
        emit this->sigGroupHTitleExpandToGroupItemListWidget(Page_Label, m_groupId, false);
    }
}

/// <summary>
/// 展开/收起按钮MtLabelCheck
/// </summary>
void GroupHTitle::onMtToolButton_pageCheck()
{
    //判断前一个状态
    if (ui.mtToolButton_pageCheck->whatsThis() == Def_TreeClose)
    {
        ui.mtToolButton_pageCheck->setPixmapFilename(m_imagePathHash["icon_treeExpand"]);
        ui.mtToolButton_pageCheck->setWhatsThis(Def_TreeExpand);
        emit this->sigGroupHTitleExpandToGroupItemListWidget(Page_Check, m_groupId, true);
    }
    else if (ui.mtToolButton_pageCheck->whatsThis() == Def_TreeExpand)
    {
        ui.mtToolButton_pageCheck->setPixmapFilename(m_imagePathHash["icon_treeClose"]);
        ui.mtToolButton_pageCheck->setWhatsThis(Def_TreeClose);
        emit this->sigGroupHTitleExpandToGroupItemListWidget(Page_Check, m_groupId, false);
    }
}
