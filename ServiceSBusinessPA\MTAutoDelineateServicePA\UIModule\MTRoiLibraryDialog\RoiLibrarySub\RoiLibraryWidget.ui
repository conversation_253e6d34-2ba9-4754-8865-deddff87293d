<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>RoiLibraryWidgetClass</class>
 <widget class="QWidget" name="RoiLibraryWidgetClass">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1531</width>
    <height>448</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RoiLibraryWidget</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout_8">
   <property name="spacing">
    <number>0</number>
   </property>
   <property name="leftMargin">
    <number>0</number>
   </property>
   <property name="topMargin">
    <number>0</number>
   </property>
   <property name="rightMargin">
    <number>0</number>
   </property>
   <property name="bottomMargin">
    <number>0</number>
   </property>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_9">
     <property name="minimumSize">
      <size>
       <width>260</width>
       <height>0</height>
      </size>
     </property>
     <property name="maximumSize">
      <size>
       <width>260</width>
       <height>16777215</height>
      </size>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <property name="spacing">
       <number>1</number>
      </property>
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="CWidgetButton" name="wdgAllROI" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>36</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="CWidgetButton" name="wdgDefaultROI" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>36</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="CWidgetButton" name="wdgEmptyROI" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>36</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="CWidgetButton" name="wdgImportedModel" native="true">
        <property name="minimumSize">
         <size>
          <width>0</width>
          <height>36</height>
         </size>
        </property>
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>36</height>
         </size>
        </property>
       </widget>
      </item>
      <item>
       <widget class="MtListWidget" name="tableModel">
        <property name="scrollType">
         <enum>MtScrollBar::scrollbar1</enum>
        </property>
        <property name="_mtType" stdset="0">
         <enum>MtListWidget::listwidget2</enum>
        </property>
        <property name="_mtItemType" stdset="0">
         <enum>MtListWidget::default_item_type</enum>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="MtFrameEx" name="mtFrameEx_8">
     <property name="_mtType" stdset="0">
      <enum>MtFrameEx::default_type</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <property name="spacing">
       <number>0</number>
      </property>
      <property name="leftMargin">
       <number>16</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>16</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <property name="spacing">
         <number>0</number>
        </property>
        <property name="topMargin">
         <number>0</number>
        </property>
        <property name="bottomMargin">
         <number>0</number>
        </property>
        <item>
         <layout class="QVBoxLayout" name="verticalLayout_4">
          <property name="spacing">
           <number>16</number>
          </property>
          <property name="bottomMargin">
           <number>16</number>
          </property>
          <item>
           <widget class="MtLabel" name="mtLabel_ModelName">
            <property name="text">
             <string>MtTextLabel</string>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtLabel::myLabel3</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="MtFrameEx" name="mtFrameEx_10">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>1</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>1</height>
             </size>
            </property>
            <property name="_mtType" stdset="0">
             <enum>MtFrameEx::default_type</enum>
            </property>
           </widget>
          </item>
         </layout>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_SearchBar">
          <layout class="QVBoxLayout" name="verticalLayout_5">
           <property name="spacing">
            <number>8</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>16</number>
           </property>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <property name="spacing">
              <number>24</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <widget class="MtFrameEx" name="mtFrameEx_chName">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_9">
                <property name="spacing">
                 <number>16</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtLabel" name="mtLabel_4">
                  <property name="text">
                   <string>器官名称</string>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLabel::myLabel1</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtLineEdit" name="mtLineEdit_roiChName">
                  <property name="minimumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maxLength">
                   <number>64</number>
                  </property>
                  <property name="placeholderText">
                   <string>器官名称</string>
                  </property>
                  <property name="elideMode">
                   <enum>Qt::ElideRight</enum>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLineEdit::lineedit1</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="MtFrameEx" name="mtFrameEx_5">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_3">
                <property name="spacing">
                 <number>16</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtLabel" name="mtLabel">
                  <property name="text">
                   <string>ROI名称</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLabel::myLabel1</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtLineEdit" name="mtLineEdit_roiName">
                  <property name="minimumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maxLength">
                   <number>64</number>
                  </property>
                  <property name="placeholderText">
                   <string>ROI名称</string>
                  </property>
                  <property name="elideMode">
                   <enum>Qt::ElideRight</enum>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLineEdit::lineedit1</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="MtFrameEx" name="mtFrameEx_4">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_4">
                <property name="spacing">
                 <number>16</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtLabel" name="mtLabel_2">
                  <property name="text">
                   <string>标签</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLabel::myLabel1</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtLineEdit" name="mtLineEdit_label">
                  <property name="minimumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maxLength">
                   <number>64</number>
                  </property>
                  <property name="placeholderText">
                   <string>标签名称</string>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLineEdit::lineedit1</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="MtFrameEx" name="mtFrameEx_6">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_5">
                <property name="spacing">
                 <number>16</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtLabel" name="mtLabel_3">
                  <property name="text">
                   <string>分组</string>
                  </property>
                  <property name="alignment">
                   <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtLabel::myLabel1</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtComboBox" name="mtComboBox_group">
                  <property name="minimumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>153</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtComboBox::combobox1</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <widget class="MtFrameEx" name="mtFrameEx_2">
               <property name="minimumSize">
                <size>
                 <width>1</width>
                 <height>26</height>
                </size>
               </property>
               <property name="maximumSize">
                <size>
                 <width>1</width>
                 <height>26</height>
                </size>
               </property>
               <property name="_mtType" stdset="0">
                <enum>MtFrameEx::default_type</enum>
               </property>
              </widget>
             </item>
             <item>
              <widget class="MtFrameEx" name="mtFrameEx_opBtn">
               <property name="minimumSize">
                <size>
                 <width>0</width>
                 <height>0</height>
                </size>
               </property>
               <layout class="QHBoxLayout" name="horizontalLayout_6">
                <property name="spacing">
                 <number>16</number>
                </property>
                <property name="leftMargin">
                 <number>0</number>
                </property>
                <property name="topMargin">
                 <number>0</number>
                </property>
                <property name="rightMargin">
                 <number>0</number>
                </property>
                <property name="bottomMargin">
                 <number>0</number>
                </property>
                <item>
                 <widget class="MtPushButton" name="mtPushButton_search">
                  <property name="minimumSize">
                   <size>
                    <width>80</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>80</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>检索</string>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtPushButton::pushbutton1</enum>
                  </property>
                 </widget>
                </item>
                <item>
                 <widget class="MtPushButton" name="mtPushButton_resetSearch">
                  <property name="minimumSize">
                   <size>
                    <width>80</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="maximumSize">
                   <size>
                    <width>80</width>
                    <height>26</height>
                   </size>
                  </property>
                  <property name="text">
                   <string>重置</string>
                  </property>
                  <property name="_mtType" stdset="0">
                   <enum>MtPushButton::pushbutton2</enum>
                  </property>
                 </widget>
                </item>
               </layout>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer_2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
           <item>
            <layout class="QHBoxLayout" name="horizontalLayout_10">
             <item>
              <spacer name="horizontalSpacer_4">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </item>
          </layout>
         </widget>
        </item>
        <item>
         <widget class="RoiLibraryTable" name="widget_table" native="true"/>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx_line">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>1</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>1</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::frameEx3</enum>
          </property>
          <property name="mtType" stdset="0">
           <string notr="true">frameEx3</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="MtFrameEx" name="mtFrameEx">
          <property name="minimumSize">
           <size>
            <width>0</width>
            <height>37</height>
           </size>
          </property>
          <property name="maximumSize">
           <size>
            <width>16777215</width>
            <height>37</height>
           </size>
          </property>
          <property name="_mtType" stdset="0">
           <enum>MtFrameEx::default_type</enum>
          </property>
          <layout class="QHBoxLayout" name="horizontalLayout">
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="MtToolButton" name="btnBatchROISetting">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>批量设置ROI信息</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="btnCreateEmptyROI">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>创建空勾画</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="btnDeleteEmptyROI">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>删除空勾画</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="btnRecoverROI">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>恢复默认设置</string>
             </property>
             <property name="pixmapFilename">
              <string notr="true"/>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <widget class="MtToolButton" name="btnROI2Template">
             <property name="minimumSize">
              <size>
               <width>26</width>
               <height>26</height>
              </size>
             </property>
             <property name="text">
              <string>...</string>
             </property>
             <property name="toolTipText">
              <string>添加进模板</string>
             </property>
             <property name="pixmapFilename">
              <string notr="true"/>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtToolButton::toolbutton2</enum>
             </property>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer_3">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="MtFrameEx" name="mtFrameEx_3">
             <layout class="QHBoxLayout" name="horizontalLayout_7">
              <property name="spacing">
               <number>4</number>
              </property>
              <property name="topMargin">
               <number>0</number>
              </property>
              <property name="bottomMargin">
               <number>0</number>
              </property>
              <item>
               <widget class="MtLabel" name="label_data_loading">
                <property name="text">
                 <string>数据加载中</string>
                </property>
                <property name="_mtType" stdset="0">
                 <enum>MtLabel::myLabel1_1</enum>
                </property>
               </widget>
              </item>
              <item>
               <widget class="MRotate" name="label_rotate">
                <property name="text">
                 <string/>
                </property>
               </widget>
              </item>
             </layout>
            </widget>
           </item>
           <item>
            <spacer name="horizontalSpacer">
             <property name="orientation">
              <enum>Qt::Horizontal</enum>
             </property>
             <property name="sizeHint" stdset="0">
              <size>
               <width>40</width>
               <height>20</height>
              </size>
             </property>
            </spacer>
           </item>
           <item>
            <widget class="MtPushButton" name="mtPushButton_help">
             <property name="text">
              <string>帮助</string>
             </property>
             <property name="pixmapFilename">
              <string notr="true">:/images/images/icon_help.png</string>
             </property>
             <property name="_mtType" stdset="0">
              <enum>MtPushButton::pushbutton8</enum>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <customwidgets>
  <customwidget>
   <class>MtPushButton</class>
   <extends>QPushButton</extends>
   <header>MtPushButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtToolButton</class>
   <extends>QToolButton</extends>
   <header>MtToolButton.h</header>
  </customwidget>
  <customwidget>
   <class>MtListWidget</class>
   <extends>QListWidget</extends>
   <header>MtListWidget.h</header>
  </customwidget>
  <customwidget>
   <class>MtFrameEx</class>
   <extends>QFrame</extends>
   <header>MtFrameEx.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MtComboBox</class>
   <extends>QComboBox</extends>
   <header>MtComboBox.h</header>
  </customwidget>
  <customwidget>
   <class>MtLineEdit</class>
   <extends>QLineEdit</extends>
   <header>MtLineEdit.h</header>
  </customwidget>
  <customwidget>
   <class>MtLabel</class>
   <extends>QLabel</extends>
   <header>MtLabel.h</header>
  </customwidget>
  <customwidget>
   <class>RoiLibraryTable</class>
   <extends>QWidget</extends>
   <header>MTRoiLibraryDialog\RoiLibrarySub\roilibrarytable.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>MRotate</class>
   <extends>QLabel</extends>
   <header>AccuComponentUi\Header\mrotate.h</header>
  </customwidget>
  <customwidget>
   <class>CWidgetButton</class>
   <extends>QWidget</extends>
   <header>MTRoiLibraryDialog\CWidgetButton\cwidgetbutton.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../../../Resources/MTAutoDelineateServicePA.qrc"/>
 </resources>
 <connections/>
</ui>
