﻿#pragma once

#include "MtTemplateDialog.h"
#include "ui_LabelInfo2ROI.h"

class LabelInfo2ROI : public MtTemplateDialog
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="parent">[IN]父窗口</param>
    LabelInfo2ROI(QWidget* parent = Q_NULLPTR);
    /// <summary>
    /// 析构函数
    /// </summary>
    ~LabelInfo2ROI();

    /// <summary>
    /// 同步方式
    /// </summary>
    enum EM_LabelInfo2ROI_Type
    {
        Type_None = 0,
        /// <summary>
        /// 名称
        /// </summary>
        Type_Name = 1,
        /// <summary>
        /// 类型
        /// </summary>
        Type_Type = 2,
        /// <summary>
        /// 颜色
        /// </summary>
        Type_Color = 4,
        /// <summary>
        /// 中文名
        /// </summary>
        Type_ChName = 8
    };

    /// <summary>
    /// 获取同步方式
    /// </summary>
    /// <returns>同步方式</returns>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    unsigned GetSyncType();

protected slots:
    /// <summary>
    /// 操作状态变化
    /// </summary>
    /// <param name="state">操作状态</param>
    /// <remarks>[Version]:4.0.3.0 Change: </remarks>
    void SlotOperationStateChanged(int state);

protected:

    /// <summary>
    /// 点击关闭按钮
    /// </summary>
    virtual void onBtnCloseClicked() override;
    /// <summary>
    /// 点击取消按钮
    /// </summary>
    virtual void onBtnRight2Clicked() override;
    /// <summary>
    /// 点击确定按钮
    /// </summary>
    virtual void onBtnRight1Clicked() override;

private:
    /// <summary>
    /// UI界面指针
    /// </summary>
    Ui::LabelInfo2ROI ui;
};
