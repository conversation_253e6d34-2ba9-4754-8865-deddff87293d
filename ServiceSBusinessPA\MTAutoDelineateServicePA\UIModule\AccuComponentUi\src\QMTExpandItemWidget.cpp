﻿#include "AccuComponentUi\Header\QMTExpandItemWidget.h"
#include "ui_QMTExpandItemWidget.h"
#include "CMtCoreDefine.h"
#include "MtMessageBox.h"

QMTExpandItemWidget::QMTExpandItemWidget(QWidget* parent)
    : QWidget(parent)
{
    ui = new Ui::QMTExpandItemWidget;
    ui->setupUi(this);
}

QMTExpandItemWidget::~QMTExpandItemWidget()
{
    MT_DELETE(ui);
}

void QMTExpandItemWidget::mousePressEvent(QMouseEvent* event)
{
    slotBtnExpandClicked();
}

void QMTExpandItemWidget::InitExpandItem(ExpandItemType type)
{
    if (Expand_BLWithClose == type)
    {
        _btnExpand = new QPushButton(ui->widget_content);
        _btnExpand->setObjectName(QStringLiteral("pushButton_expand"));
        _btnExpand->setFixedSize(QSize(16, 16));
        ui->widget_content->layout()->addWidget(_btnExpand);
        _label = new QLabel(ui->widget_content);
        _label->setObjectName(QStringLiteral("label"));
        ui->widget_content->layout()->addWidget(_label);
        ((QHBoxLayout*)ui->widget_content->layout())->addStretch();
        _btnClose = new QPushButton(ui->widget_content);
        _btnClose->setObjectName(QStringLiteral("pushButton_close"));
        _btnClose->setFixedSize(QSize(18, 18));
        ui->widget_content->layout()->addWidget(_btnClose);
        connect(_btnClose, SIGNAL(clicked()), this, SLOT(slotBtnCloseClicked()));
        ui->label_marginright->setFixedWidth(26);
    }
    else if (Expand_LabelButton == type)
    {
        _label = new QLabel(ui->widget_content);
        //_label->show();
        _label->setObjectName(QStringLiteral("label"));
        ui->widget_content->layout()->addWidget(_label);
        ((QHBoxLayout*)ui->widget_content->layout())->addStretch();
        _btnExpand = new QPushButton(ui->widget_content);
        //_btnExpand->show();
        _btnExpand->setObjectName(QStringLiteral("pushButton_expand"));
        _btnExpand->setFixedSize(QSize(16, 16));
        //_btnExpand->setCursor(Qt::BusyCursor);
        ui->widget_content->layout()->addWidget(_btnExpand);
        ui->label_marginright->setFixedWidth(16);
    }

    if (_btnExpand)
        connect(_btnExpand, SIGNAL(clicked()), this, SLOT(slotBtnExpandClicked()));
}


void QMTExpandItemWidget::SetTextStr(QString text)
{
    _keyValue = text;

    if (_label)
        _label->setText(text);
}

void QMTExpandItemWidget::SetWidgetList(QList<QWidget*> widgetList)
{
    _widgetList = widgetList;
}

void QMTExpandItemWidget::AddWidgetToList(QWidget* widget)
{
    _widgetList.append(widget);
}

void QMTExpandItemWidget::AddWidgetToList(int index, QWidget* widget)
{
    _widgetList.insert(index, widget);
}

bool QMTExpandItemWidget::IsWidgetExist(QWidget* widget)
{
    bool ret = false;

    for (int i = 0; i < _widgetList.size(); ++i)
    {
        QWidget* tmpWidget = _widgetList.at(i);

        if (tmpWidget == widget)
        {
            ret = true;
            break;
        }
    }

    return ret;
}

void QMTExpandItemWidget::RemoveExpandItem(QWidget* widget)
{
    if (widget == nullptr)
        return;

    for (int i = 0; i < _widgetList.size(); ++i)
    {
        QWidget* tmpWidget = _widgetList.at(i);

        if (tmpWidget == widget)
        {
            _widgetList.removeAt(i);
            break;
        }
    }
}

void QMTExpandItemWidget::ClearWidgetData()
{
    _state = 0;
    _widgetList.clear();
}

void QMTExpandItemWidget::ChangeState(int state)
{
    //if (isVisible() == false)//不能这样判断，否则会有展示异常问题
    //    return;
    if (1 == state)//shrink
    {
        for (int i = 0; i < _widgetList.size(); ++i)
        {
            QWidget* widget = _widgetList.at(i);
            widget->hide();
        }

        QString buttonStyle = "QPushButton{"
            "background-image:url(:/AccuUIComponentImage/images/btn_shrink.png);"
            "border:none;}"
            "QPushButton:hover{"
            "background-image:url(:/AccuUIComponentImage/images/btn_shrink_hover.png);}";

        if (_btnExpand)
            _btnExpand->setStyleSheet(buttonStyle);
    }
    else if (2 == state || 0 == state)//expand
    {
        for (int i = 0; i < _widgetList.size(); ++i)
        {
            QWidget* widget = _widgetList.at(i);
            widget->show();
        }

        QString buttonStyle = "QPushButton{"
            "background-image:url(:/AccuUIComponentImage/images/btn_order.png);"
            "border:none;}"
            "QPushButton:hover{"
            "background-image:url(:/AccuUIComponentImage/images/btn_order_hover.png);}";

        if (_btnExpand)
            _btnExpand->setStyleSheet(buttonStyle);
    }
}

void QMTExpandItemWidget::ChangeStateByHand(int state)
{
    if (state < 0 || state > 2)
        return;

    _state = state;
    ChangeState(_state);
}

void QMTExpandItemWidget::HideSelf()
{
    for (int i = 0; i < _widgetList.size(); ++i)
    {
        QWidget* widget = _widgetList.at(i);
        widget->hide();
    }

    this->hide();
}

void QMTExpandItemWidget::ShowSelf()
{
    this->show();
    int state = _state;

    if (0 == _state)
    {
        state = 2;
    }

    ChangeState(state);
}

void QMTExpandItemWidget::slotBtnExpandClicked()
{
    if (2 <= _state)//expand
    {
        _state = 0;
    }

    _state++;
    ChangeState(_state);

    if (2 <= _state)//expand
    {
        _state = 0;
    }

    emit sigExpandClicked(_state);
}

void QMTExpandItemWidget::slotBtnCloseClicked()
{
    //QMessageBox::StandardButton ret = MMessageBox::ShowOKAndNo(true, tr("是否清除该记录"));
    //if (ret == QMessageBox::Yes)
    //{
    //    //ClearWidgetData();
    //    emit sigExpandBtnCloseClicked();
    //}
    QString msg = QString(tr("是否清除该记录？"));
#if 0
    QList<QString> BtnName;
    int ret = QMessageBox::NoButton;
    BtnName.push_back(QString(tr("是")));
    BtnName.push_back(QString(tr("否")));
    ret = MMessageBox::Show(QMessageBox::Yes | QMessageBox::No | QMessageBox::Cancel, BtnName, msg, true);
#endif
    MtMessageBox dlg;
    dlg.getButton(MtTemplateDialog::BtnRight1)->setText(tr("是"));
    dlg.getButton(MtTemplateDialog::BtnRight2)->setText(tr("否"));
    dlg.setText(msg);
    dlg.exec();
    int ret = dlg.buttonIndex2StandardButton(dlg.clickedButtonIndex());

    if (ret == QMessageBox::Yes)
    {
        emit sigExpandBtnCloseClicked();
    }
}