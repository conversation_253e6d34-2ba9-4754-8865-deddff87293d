﻿#include "RoiLibraryWidget.h"
#include <QFileDialog>
#include <QScreen>
#include "MtMessageBox.h"
#include "MTRoiLibraryDialog\EclipseSub\ImportEclipseTemplateDialog.h"
#include "MtScrollBar.h"
#include "MTRoiLibraryDialog\RoiLibraryHelp\RoiLibraryHelp.h"
#include "MTRoiLibraryDialog\CWidgetButton\CWidgetButton.h"
#include "MTRoiLibraryDialog\ModelNameList\ModelNameListItem.h"
#include "MTRoiLibraryDialog\ModelNameList\ModelInfoSetting.h"
#include "MTRoiLibraryDialog\ModelNameList\ModelImportSuccessDlg.h"

RoiLibraryWidget::RoiLibraryWidget(QWidget* parent)
    : QWidget(parent)
    , m_parentDialog(parent)
    , m_bNeedSave2File(false)
{
    ui.setupUi(this);
    ui.mtPushButton_search->setDisabled(true);
    ui.widget_table->setFrameDialog(m_parentDialog);
    ui.wdgAllROI->setType(CWidgetButton::btnType_All);
    ui.wdgDefaultROI->setType(CWidgetButton::btnType_Default);
    ui.wdgEmptyROI->setType(CWidgetButton::btnType_Empty);
    ui.wdgImportedModel->setType(CWidgetButton::btnType_Model);

    if (CMtLanguageUtil::type == english)
    {
        ui.mtFrameEx_chName->hide();
        ui.mtLineEdit_roiName->setFixedWidth(225);
        ui.mtLineEdit_label->setFixedWidth(225);
        ui.mtComboBox_group->setFixedWidth(225);
    }

    //信号槽
    connect(ui.wdgAllROI, &CWidgetButton::sigButtonClicked, this, &RoiLibraryWidget::slotVisibleROITypeChange);
    connect(ui.wdgDefaultROI, &CWidgetButton::sigButtonClicked, this, &RoiLibraryWidget::slotVisibleROITypeChange);
    connect(ui.wdgEmptyROI, &CWidgetButton::sigButtonClicked, this, &RoiLibraryWidget::slotVisibleROITypeChange);
    connect(ui.wdgImportedModel, &CWidgetButton::sigButtonClicked, this, &RoiLibraryWidget::slotVisibleROITypeChange);
    connect(ui.wdgImportedModel, &CWidgetButton::sigImportModelClicked, this, &RoiLibraryWidget::slotImportModel);
    connect(ui.widget_table, &RoiLibraryTable::sigOrganInfoChanged, this, &RoiLibraryWidget::slotOrganInfoChanged);     //表格信息发生了变化，同步缓存信息
    connect(ui.widget_table, &RoiLibraryTable::sigTableInfoChanged, this, &RoiLibraryWidget::slotTableInfoChanged);     //表格信息发生了变化
    connect(ui.widget_table, &RoiLibraryTable::sigUpdateRoiGroup, this, &RoiLibraryWidget::slotUpdateRoiGroup);         //更新了roi分组
    connect(ui.widget_table, &RoiLibraryTable::sigTableInitialized, this, [&]()      //隐藏加载状态
    {
        showLoadingState(false);
    });
    connect(ui.widget_table, &RoiLibraryTable::sigGetLabelLibraryInfo, this, &RoiLibraryWidget::sigGetLabelLibraryInfo);         //获取标签库信息
    connect(ui.widget_table, &RoiLibraryTable::sigGetOrganDefaultInfo, this, &RoiLibraryWidget::sigGetOrganDefaultInfo);         //获取器官默认设置
    //
    connect(this, &RoiLibraryWidget::sigModelImportProgress, this, &RoiLibraryWidget::slotModelImportProgress);
    connect(this, &RoiLibraryWidget::sigModelImportFinish, this, &RoiLibraryWidget::slotModelImportFinish);
    connect(this, &RoiLibraryWidget::sigModelImportResult, this, &RoiLibraryWidget::slotModelImportResult);
    connect(this, &RoiLibraryWidget::sigModelDeleteResult, this, &RoiLibraryWidget::slotModelDeleteResult);
    connect(this, &RoiLibraryWidget::sigDeleteSelf, this, &RoiLibraryWidget::slotDeleteSelf, Qt::QueuedConnection);
}

RoiLibraryWidget::~RoiLibraryWidget()
{
    widgetDestroying();
}

void RoiLibraryWidget::showEvent(QShowEvent* event)
{
    QWidget::showEvent(event);
    //
    QRect mRect = QGuiApplication::primaryScreen()->geometry();
    QPoint btnGlobalPt = ui.mtPushButton_resetSearch->mapToGlobal(QPoint(ui.mtPushButton_resetSearch->width(), 0));

    if (mRect.width() < btnGlobalPt.x() || width() < 1433)
    {
        ui.horizontalLayout_2->removeWidget(ui.mtFrameEx_opBtn);
        ui.horizontalLayout_10->insertWidget(0, ui.mtFrameEx_opBtn);
        ui.horizontalLayout_2->setStretch(5, 1);
    }
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void RoiLibraryWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    ui.widget_table->setGroupColumnBtnImage(imagePathHash["icon_setting"]);
    ui.label_rotate->setUrl(imagePathHash["icon_loading"]);
    ui.wdgAllROI->setImportModelBtnPixmap(imagePathHash["icon_add"]);
    ui.wdgDefaultROI->setImportModelBtnPixmap(imagePathHash["icon_add"]);
    ui.wdgEmptyROI->setImportModelBtnPixmap(imagePathHash["icon_add"]);
    ui.wdgImportedModel->setImportModelBtnPixmap(imagePathHash["icon_add"]);
    ui.btnRecoverROI->setPixmapFilename(imagePathHash["icon_recoverROI_green"]);
    ui.btnROI2Template->setPixmapFilename(imagePathHash["icon_ROI2Template_green"]);
    ui.btnBatchROISetting->setPixmapFilename(imagePathHash["icon_batch_green"]);
    ui.btnCreateEmptyROI->setPixmapFilename(imagePathHash["icon_add_green"]);
    ui.btnDeleteEmptyROI->setPixmapFilename(imagePathHash["icon_del_green"]);
    m_modelNameEditBtnPixmap = imagePathHash["icon_elide"];
    m_icon_success_green2Path = imagePathHash["icon_success_green2"];
}

void RoiLibraryWidget::widgetDestroying()
{
    ui.widget_table->tableDestroying();
}

void RoiLibraryWidget::deleteSafe()
{
    hide();
    std::thread t([&]()
    {
        ui.widget_table->tableDestroying();
        emit sigDeleteSelf();
    });
    t.detach();
    //emit sigDeleteSelf();
}

void RoiLibraryWidget::slotDeleteSelf()
{
    //ui.widget_table->tableDestroying();
    delete this;
}

void RoiLibraryWidget::showLoadingState(bool bShow)
{
    if (bShow)
    {
        ui.label_rotate->setFixedSize(14, 14);
        ui.label_rotate->setClockwise(true);
        ui.label_rotate->setInterval(5);
        ui.label_rotate->show();
        ui.label_data_loading->show();
    }
    else
    {
        ui.label_rotate->hide();
        ui.label_data_loading->hide();
    }
}

void RoiLibraryWidget::updateRoiTableInfo(int modelId)
{
    QList<n_mtautodelineationdialog::ST_Organ> curModelOrganList;
    //更新器官名过滤列表
    m_roiListNameMap.clear();
    m_roiListChNameMap.clear();

    for (const auto& organItem : m_allOrganList)
    {
        if (organItem.modelId == modelId
            || 0 == modelId && m_modelInfoMap.contains(organItem.modelId) && m_modelInfoMap[organItem.modelId].modelType == 1   //modelID为0，表示所有内置模型
            || modelId == -2                                                                                                    //modelID为-2，表示所有的ROI
            )
        {
            if (organItem.optTypeEnum != n_mtautodelineationdialog::OptType_Del)//删除的不显示
            {
                curModelOrganList.append(organItem);
            }

            m_roiListNameMap[organItem.defaultOrganName] = organItem.customOrganName;
            m_roiListChNameMap[organItem.defaultOrganName] = organItem.organChineseName;
        }
    }

    m_completerNameStrListModel->setStringList(m_roiListNameMap.values());
    m_completerChNameStrListModel->setStringList(m_roiListChNameMap.values());
    //显示加载状态
    showLoadingState(true);
    //刷新ROI列表
    ui.widget_table->updateTable(curModelOrganList);
}

QListWidgetItem* RoiLibraryWidget::addModelItem(int modelId, const QString& modelName)
{
    ModelNameListItem* modelNameItem = new ModelNameListItem();
    connect(modelNameItem, &ModelNameListItem::sigEditModel, this, &RoiLibraryWidget::slotEditModel);
    connect(modelNameItem, &ModelNameListItem::sigDeleteModel, this, &RoiLibraryWidget::slotDeleteModel);
    //
    modelNameItem->init(modelId, modelName, m_modelNameEditBtnPixmap);
    QListWidgetItem* listItem = new QListWidgetItem;
    listItem->setSizeHint(QSize(260, 36));
    listItem->setData(Qt::UserRole, modelId);
    ui.tableModel->addItem(listItem);
    ui.tableModel->setItemWidget(listItem, modelNameItem);
    return listItem;
}

void RoiLibraryWidget::removeModelItem(int modelId)
{
    //移除列表
    int itemCount = ui.tableModel->count();

    for (int i = 0; i < itemCount; i++)
    {
        QListWidgetItem* item = ui.tableModel->item(i);
        QVariant dataCur = item->data(Qt::UserRole);
        int curModelId = dataCur.toInt();

        if (curModelId == modelId)
        {
            //ui.tableModel->removeItemWidget(item);
            ui.tableModel->takeItem(i);

            //如果移除的是当前选中的项，则要切换到其第一项
            if (m_preSelectedModelNameItem == item && ui.tableModel->count() > 0)
            {
                QListWidgetItem* item = ui.tableModel->item(0);
                m_preSelectedModelNameItem = nullptr;
                slotModelNameListItemClicked(item);
            }

            delete item;
            break;
        }
    }

    updateModelItemWidth();
}

void RoiLibraryWidget::updateModelItem(int modelId, const QString& modelName)
{
    int itemCount = ui.tableModel->count();

    for (int i = 0; i < itemCount; i++)
    {
        QListWidgetItem* item = ui.tableModel->item(i);
        QVariant dataCur = item->data(Qt::UserRole);
        int curModelId = dataCur.toInt();

        if (curModelId == modelId)
        {
            QWidget* curWidget = ui.tableModel->itemWidget(item);
            ModelNameListItem* curListItem = qobject_cast<ModelNameListItem*>(curWidget);

            if (nullptr != curListItem)
            {
                curListItem->updateItemText(modelName);
            }

            break;
        }
    }
}

void RoiLibraryWidget::updateModelItemWidth()
{
    int itemCount = ui.tableModel->count();
    int itemWidth = itemCount > 20 ? 253 : 260;

    for (int i = 0; i < itemCount; i++)
    {
        QListWidgetItem* item = ui.tableModel->item(i);
        item->setSizeHint(QSize(itemWidth, 36));
    }
}

RoiLibraryTable* RoiLibraryWidget::getTableWidget()
{
    return ui.widget_table;
}

void RoiLibraryWidget::init(const QStringList& allRoiTypeList,
                            const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoList,
                            const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList,
                            const QList<n_mtautodelineationdialog::ST_Organ>& stOrganList,
                            const QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap,
                            const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& modelCollectionInfoList)
{
    //拷贝一份数据
    m_allRoiTypeList = allRoiTypeList;
    m_curAllGroupList = allGroupList;
    m_allOrganList = stOrganList;
    m_modelInfoMap = modelInfoMap;
    m_modelCollectionInfoList = modelCollectionInfoList;
    //分组过滤
    QStringList groupList;

    for (const auto& gItem : m_curAllGroupList)
    {
        groupList.append(gItem.name);
    }

    //标签过滤
    QStringList labelList = QStringList() << "NONE";

    for (const auto& item : stRoiLabelInfoList)
    {
        labelList.append(item.manteiaRoiLabel);
    }

    //初始化roi列表(要放在初始化模型名之前)
    ui.widget_table->init(m_allRoiTypeList, stRoiLabelInfoList, m_curAllGroupList, m_allOrganList, m_modelInfoMap, m_modelCollectionInfoList);
    //初始化ROI名搜索控件
    initNameFilterEdit(/*m_roiListNameMap.values()*/QStringList());
    //初始化器官名搜索控件
    initChNameFilterEdit(/*m_roiListChNameMap.values()*/QStringList());
    //初始化标签搜索控件
    initLabelFilterEdit(labelList);
    //初始化分组搜索列表
    initGroupFilterBox(groupList);
    //初始化模型列表
    initModelNameList(m_modelInfoMap);
    //设置当前显示为默认roi
    //slotVisibleROITypeChange(CWidgetButton::btnType_Default);
    emit ui.wdgDefaultROI->sigButtonClicked(CWidgetButton::btnType_Default);
}

/// <summary>
/// 获取所有Organ信息
/// 发生修改的optTypeEnum会设置为OptType_Mod
/// </summary>
/// <returns>所有Organ信息</returns>
QList<n_mtautodelineationdialog::ST_Organ> RoiLibraryWidget::getAllOrganInfo()
{
    return ui.widget_table->getAllOrganInfo();
}

const QList<n_mtautodelineationdialog::ST_SketchModelCollection>& RoiLibraryWidget::getAllModelCollectionInfoList()
{
    return ui.widget_table->getAllModelCollectionInfoList();
}

const QList<n_mtautodelineationdialog::ST_RoiLabelInfo> RoiLibraryWidget::getAllLabelInfoList()
{
    return ui.widget_table->getAllLabelInfoList();
}

QList<n_mtautodelineationdialog::ST_OrganGroupInfo> RoiLibraryWidget::getAllOrganGroupInfo()
{
    return ui.widget_table->getAllOrganGroupInfo();
}

void RoiLibraryWidget::setTableChangedStatus()
{
    m_bNeedSave2File = true;
    return;
    MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_parentDialog);

    if (nullptr != parentDialog)
    {
        parentDialog->getButton(MtTemplateDialog::BtnRight1)->setEnabled(true);
    }
}

void RoiLibraryWidget::setSearchFilterChanged()
{
    QString name = ui.mtLineEdit_roiName->text();
    QString label = ui.mtLineEdit_label->text();
    QString chName = ui.mtLineEdit_roiChName->text();
    QString group = ui.mtComboBox_group->currentText();

    if (name.isEmpty() && label.isEmpty() && chName.isEmpty() && group == tr("全部分组"))
    {
        ui.mtPushButton_search->setDisabled(true);

        //若列表处于搜索结果状态，情况搜索条件时，需要重置搜索结果
        if (m_bFilterTable)
        {
            ui.widget_table->filterTable("", "", "", "");
            m_bFilterTable = false;
        }
    }
    else
    {
        ui.mtPushButton_search->setEnabled(true);
    }
}

void RoiLibraryWidget::removeFocusFromTable()
{
    ui.widget_table->setFocus();
}

bool RoiLibraryWidget::isNeedSave2File()
{
    ui.widget_table->setFocus();
    return m_bNeedSave2File;
}

void RoiLibraryWidget::resetNeedSave2FileStatus()
{
    m_bNeedSave2File = false;
}

void RoiLibraryWidget::on_mtPushButton_search_clicked()
{
    if (!ui.widget_table->isTableInitialized())//要列表初始化完成才可进行搜索
    {
        MtMessageBox::NoIcon::information(this, tr("请等数据加载完成后再进行检索"));
        return;
    }

    QString name = ui.mtLineEdit_roiName->text();
    QString label = ui.mtLineEdit_label->text();
    QString chName = ui.mtLineEdit_roiChName->text();
    QString group = ui.mtComboBox_group->currentText();
    ui.widget_table->filterTable(name, label, chName, group);
    m_bFilterTable = true;
}

void RoiLibraryWidget::on_mtPushButton_resetSearch_clicked()
{
    ui.mtLineEdit_roiName->clear();
    ui.mtLineEdit_label->clear();
    ui.mtLineEdit_roiChName->clear();
    ui.mtComboBox_group->setCurrentIndex(0);
    ui.widget_table->filterTable("", "", "", "");
    m_bFilterTable = false;
}

void RoiLibraryWidget::on_btnRecoverROI_clicked()
{
    ui.widget_table->resetROIDefaultInfo();
}

void RoiLibraryWidget::on_btnROI2Template_clicked()
{
    ui.widget_table->addROI2Template();
}

void RoiLibraryWidget::on_btnBatchROISetting_clicked()
{
    ui.widget_table->batchROISetting();
}

void RoiLibraryWidget::on_btnCreateEmptyROI_clicked()
{
    if (!ui.widget_table->isTableInitialized())//要列表初始化完成才可进行搜索
    {
        MtMessageBox::NoIcon::information(this, tr("请等数据加载完成后再进行检索"));
        return;
    }

    n_mtautodelineationdialog::ST_Organ stOrgan;
    stOrgan.id = m_tempOrganId--;
    stOrgan.modelId = -1;
    stOrgan.enable = true;
    stOrgan.isVisiable = true;
    stOrgan.isAssociateTemplate = false;
    stOrgan.organChineseName = "";
    stOrgan.defaultOrganName = "";
    stOrgan.customOrganName = "";
    stOrgan.defaultColor = "ff0000";
    stOrgan.customColor = "ff0000";
    stOrgan.roiType = "NONE";
    stOrgan.roiLabel = "";
    stOrgan.roiParam = "";

    for (const n_mtautodelineationdialog::ST_OrganGroupInfo& item : m_curAllGroupList)
    {
        if (item.name == tr("空勾画"))
        {
            stOrgan.organGroupInfoMap[item.id] = item;
            break;
        }
    }

    stOrgan.optTypeEnum = n_mtautodelineationdialog::OptType_Add;
    m_allOrganList.append(stOrgan);
    ui.widget_table->addStaticRow(stOrgan);
    //列表滚动到最后
    ui.widget_table->verticalScrollBar()->setValue(ui.widget_table->verticalScrollBar()->maximum());
    //设置信息被修改状态
    setTableChangedStatus();
}

void RoiLibraryWidget::on_btnDeleteEmptyROI_clicked()
{
    if (ui.widget_table->delSelectedRow())
    {
        //设置信息被修改状态
        setTableChangedStatus();
    }
}

void RoiLibraryWidget::on_mtPushButton_help_clicked()
{
    RoiLibraryHelp helpDlg(this);
    helpDlg.exec();
}

void RoiLibraryWidget::slotVisibleROITypeChange(int type)
{
    ui.wdgAllROI->setChecked(false);
    ui.wdgDefaultROI->setChecked(false);
    ui.wdgEmptyROI->setChecked(false);
    ui.wdgImportedModel->setChecked(false);
    //
    ui.btnBatchROISetting->setVisible(false);
    ui.btnCreateEmptyROI->setVisible(false);
    ui.btnDeleteEmptyROI->setVisible(false);
    ui.btnRecoverROI->setVisible(false);
    ui.btnROI2Template->setVisible(false);
    //设置模型列表选中状态
    auto funcUpdateModelNameListSelected = [&](bool bChecked)
    {
        if (nullptr != m_preSelectedModelNameItem)
        {
            QWidget* preWidget = ui.tableModel->itemWidget(m_preSelectedModelNameItem);
            ModelNameListItem* preListItem = qobject_cast<ModelNameListItem*>(preWidget);

            if (nullptr != preListItem)
            {
                bool bPreState = preListItem->isChecked();
                preListItem->setChecked(bChecked);

                //如果将未选中设为选中，则需要刷新右侧的roi列表
                if (!bPreState && bChecked)
                {
                    QVariant dataCur = m_preSelectedModelNameItem->data(Qt::UserRole);
                    int curModelId = dataCur.toInt();
                    updateRoiTableInfo(curModelId);
                    //
                    QWidget* curWidget = ui.tableModel->itemWidget(m_preSelectedModelNameItem);
                    ModelNameListItem* curListItem = qobject_cast<ModelNameListItem*>(curWidget);
                    ui.mtLabel_ModelName->setText(curListItem->text());
                }
            }
        }
    };
    CWidgetButton* wdgBtn = qobject_cast<CWidgetButton*>(sender());

    switch (type)
    {
        case CWidgetButton::btnType_All:
            ui.wdgAllROI->setChecked(true);
            ui.btnROI2Template->setVisible(true);
            funcUpdateModelNameListSelected(false);
            updateRoiTableInfo(-2);

            if (nullptr != wdgBtn)
            {
                ui.mtLabel_ModelName->setText(wdgBtn->text());
            }

            break;

        case CWidgetButton::btnType_Default:
            ui.wdgDefaultROI->setChecked(true);
            ui.btnRecoverROI->setVisible(true);
            ui.btnROI2Template->setVisible(true);
            funcUpdateModelNameListSelected(false);
            updateRoiTableInfo(0);

            if (nullptr != wdgBtn)
            {
                ui.mtLabel_ModelName->setText(wdgBtn->text());
            }

            break;

        case CWidgetButton::btnType_Empty:
            ui.wdgEmptyROI->setChecked(true);
            ui.btnCreateEmptyROI->setVisible(true);
            ui.btnDeleteEmptyROI->setVisible(true);
            ui.btnROI2Template->setVisible(true);
            funcUpdateModelNameListSelected(false);
            updateRoiTableInfo(-1);

            if (nullptr != wdgBtn)
            {
                ui.mtLabel_ModelName->setText(wdgBtn->text());
            }

            break;

        case CWidgetButton::btnType_Model:
            if (nullptr == m_preSelectedModelNameItem)//之前未选中，则默认选中第一个
            {
                if (ui.tableModel->count() > 0)
                {
                    QListWidgetItem* item = ui.tableModel->item(0);
                    slotModelNameListItemClicked(item);
                    ui.btnBatchROISetting->setVisible(true);
                    ui.btnROI2Template->setVisible(true);
                }
            }
            else
            {
                funcUpdateModelNameListSelected(true);
                ui.btnBatchROISetting->setVisible(true);
                ui.btnROI2Template->setVisible(true);
            }

            break;

        default:
            ui.btnBatchROISetting->setVisible(true);
            ui.btnROI2Template->setVisible(true);
            break;
    }
}

void RoiLibraryWidget::slotImportModel()
{
    QString fileName = QFileDialog::getOpenFileName(this, tr("Open"), "", tr("Pack Files (*.pack)"));

    if (fileName.isNull())
    {
        return;
    }

    /*m_progressDialog = new MtProgressDialog(this);放到服务外部实现
    m_progressDialog->show();
    m_progressDialog->setLabelText(tr("正在导入模型，请稍候..."));
    m_progressDialog->setCancelButtonVisible(false);
    m_progressDialog->setRange(0, 100);*/
    //发送导入模型信号
    emit sigModelImport(fileName);
}

void RoiLibraryWidget::slotTableInfoChanged(const QString& defOrganName, int col, const QString& newText)
{
    setTableChangedStatus();

    if (col == RoiLibraryTable::ColType_Name)
    {
        m_roiListNameMap[defOrganName] = newText;
        m_completerNameStrListModel->setStringList(m_roiListNameMap.values());
    }
    else if (col == RoiLibraryTable::ColType_ChName)
    {
        m_roiListChNameMap[defOrganName] = newText;
        m_completerChNameStrListModel->setStringList(m_roiListChNameMap.values());
    }
}

void RoiLibraryWidget::slotOrganInfoChanged(const n_mtautodelineationdialog::ST_Organ& organInfo)
{
    //同步器官缓存信息
    for (int i = 0; i < m_allOrganList.size(); ++i)
    {
        if (m_allOrganList[i].id == organInfo.id)
        {
            m_allOrganList[i].bodypart = organInfo.bodypart;
            m_allOrganList[i].customColor = organInfo.customColor;
            m_allOrganList[i].customOrganName = organInfo.customOrganName;
            m_allOrganList[i].defaultColor = organInfo.defaultColor;
            m_allOrganList[i].defaultOrganName = organInfo.defaultOrganName;
            m_allOrganList[i].enable = organInfo.enable;
            m_allOrganList[i].isAssociateTemplate = organInfo.isAssociateTemplate;
            m_allOrganList[i].isVisiable = organInfo.isVisiable;
            m_allOrganList[i].modelId = organInfo.modelId;
            m_allOrganList[i].optTypeEnum = organInfo.optTypeEnum;
            m_allOrganList[i].organChineseName = organInfo.organChineseName;
            m_allOrganList[i].organEnglishName = organInfo.organEnglishName;
            m_allOrganList[i].organGroupInfoMap = organInfo.organGroupInfoMap;
            m_allOrganList[i].roiDesc = organInfo.roiDesc;
            m_allOrganList[i].roiLabel = organInfo.roiLabel;
            m_allOrganList[i].roiParam = organInfo.roiParam;
            m_allOrganList[i].roiType = organInfo.roiType;
            break;
        }
    }
}

void RoiLibraryWidget::slotUpdateRoiGroup(const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& curGroupList, const QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& delGroupList, QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& updtedGroupList)
{
    //抛出信号，由外部更新到数据库，然后返回处理后的最新信息
    emit sigUpdateRoiGroup(curGroupList, delGroupList, updtedGroupList);

    if (updtedGroupList.size() == 0)
    {
        updtedGroupList = curGroupList;
    }

    //更新最新分组到缓存
    m_curAllGroupList = updtedGroupList;
    //更新组过滤列表
    QStringList groupList;

    for (const auto& gItem : updtedGroupList)
    {
        groupList.append(gItem.name);
    }

    //初始化搜索控件
    initGroupFilterBox(groupList);
}

void RoiLibraryWidget::slotSearchNameTriggered(const QString& text)
{
    slotSearchNameChanged(text);
}

void RoiLibraryWidget::slotSearchNameChanged(const QString& text)
{
    //重置搜索框文本
    ui.mtLineEdit_roiName->blockSignals(true);
    ui.mtLineEdit_roiName->setText(text);
    ui.mtLineEdit_roiName->blockSignals(false);
    setSearchFilterChanged();
}

void RoiLibraryWidget::slotSearchLabelTriggered(const QString& text)
{
    slotSearchLabelChanged(text);
}

void RoiLibraryWidget::slotSearchLabelChanged(const QString& text)
{
    //重置搜索框文本
    ui.mtLineEdit_label->blockSignals(true);
    ui.mtLineEdit_label->setText(text);
    ui.mtLineEdit_label->blockSignals(false);
    setSearchFilterChanged();
}

void RoiLibraryWidget::slotSearchChNameTriggered(const QString& text)
{
    slotSearchChNameChanged(text);
}

void RoiLibraryWidget::slotSearchChNameChanged(const QString& text)
{
    //重置搜索框文本
    ui.mtLineEdit_roiChName->blockSignals(true);
    ui.mtLineEdit_roiChName->setText(text);
    ui.mtLineEdit_roiChName->blockSignals(false);
    setSearchFilterChanged();
}

void RoiLibraryWidget::slotModelNameListItemClicked(QListWidgetItem* current)
{
    QWidget* curWidget = ui.tableModel->itemWidget(current);
    ModelNameListItem* curListItem = qobject_cast<ModelNameListItem*>(curWidget);

    if (current == m_preSelectedModelNameItem)
    {
        if (curListItem->isChecked())//重复点击同一个模型
        {
            return;
        }
        else
        {
            m_preSelectedModelNameItem = nullptr;
        }
    }

    QVariant dataCur = current->data(Qt::UserRole);
    int curModelId = dataCur.toInt();

    if (nullptr != curListItem)
    {
        curListItem->setChecked(true);
        //取消其他模型的选中状态
        slotVisibleROITypeChange(-1);
        //刷新roi列表
        updateRoiTableInfo(curModelId);
        //设置右侧模型标题
        ui.mtLabel_ModelName->setText(curListItem->text());
    }

    if (nullptr != m_preSelectedModelNameItem)
    {
        QWidget* preWidget = ui.tableModel->itemWidget(m_preSelectedModelNameItem);
        ModelNameListItem* preListItem = qobject_cast<ModelNameListItem*>(preWidget);

        if (nullptr != preListItem)
        {
            preListItem->setChecked(false);
        }
    }

    m_preSelectedModelNameItem = current;
}

void RoiLibraryWidget::slotModelNameListCurrentItemChanged(QListWidgetItem* current, QListWidgetItem* previous)
{
    return;
}

void RoiLibraryWidget::slotEditModel(int modelId, const QString& modelName)
{
    ModelInfoSetting dlg(this);
    dlg.setInfo(m_modelInfoMap[modelId].modelName, m_modelInfoMap[modelId].importTime, m_modelInfoMap[modelId].modelDesc);

    if (QDialog::Accepted == dlg.exec())
    {
        QString modelName, modelDesc;
        dlg.info(modelName, modelDesc);
        int result = -1;
        emit sigSaveModelInfo(QString::number(modelId), modelName, modelDesc, result);

        //修改成功，更新列表和缓存信息
        if (0 == result)
        {
            //更新缓存
            m_modelInfoMap[modelId].modelName = modelName;
            m_modelInfoMap[modelId].modelDesc = modelDesc;
            ui.widget_table->updateModelInfo(modelId, modelName, modelDesc);
            //更新列表
            updateModelItem(modelId, modelName);
        }
        else
        {
            MtMessageBox::NoIcon::information_Title(this, m_modelInfoMap[modelId].modelName + tr("模型信息修改失败！"));
        }
    }
}

void RoiLibraryWidget::slotDeleteModel(int modelId, const QString& modelName)
{
    QStringList modelCollectionNameList;
    QString modelCollStr;
    QString msg = QString(tr("确定删除[%1]模型吗？").arg(modelName));

    if (QMessageBox::Yes != MtMessageBox::redWarning(this, msg, tr("删除后将无法恢复!"), tr("删除")))
    {
        return;
    }

    for (const auto& organItem : m_modelInfoMap[modelId].organList)
    {
        for (const auto& collItem : m_modelCollectionInfoList)
        {
            if (collItem.showGroupIdMap.keys().contains(organItem.id) && !modelCollectionNameList.contains(collItem.templateName))
            {
                modelCollectionNameList.append(collItem.templateName);
                modelCollStr += collItem.templateName + "; ";
            }
        }
    }

    if (modelCollectionNameList.size() > 0)
    {
        modelCollStr.chop(2);

        if (QMessageBox::StandardButton::Yes != MtMessageBox::NoIcon::question_Title(this, tr("下列模板引用了该模型的ROI，删除模型将移除模板中对应的ROI，是否确定删除？"), modelCollStr))
        {
            return;
        }
    }

    //发送删除模型信号
    emit sigDeleteModel(QString::number(modelId), modelName);
}

void RoiLibraryWidget::slotModelImportProgress(int value)
{
    m_progressDialog->setValue(value);
}

void RoiLibraryWidget::slotModelImportFinish(bool bSuccess, const QString& errMsg)
{
    m_progressDialog->setValue(100);
    m_progressDialog->close();
    delete m_progressDialog;
    m_progressDialog = nullptr;

    if (bSuccess)
    {
        ModelImportSuccessDlg dlg(this);
        dlg.setIconPath(m_icon_success_green2Path);
        dlg.exec();
    }
    else
    {
        MtMessageBox::information(this, errMsg);
    }
}

void RoiLibraryWidget::slotModelImportResult(const QList<n_mtautodelineationdialog::ST_Organ>& stOrganInfoVec, const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap)
{
    QMap<int, n_mtautodelineationdialog::ST_SketchModel>::const_iterator itor = modelInfoMap.constBegin();
    int modelId = 0;//导入的模型id
    QListWidgetItem* newItem = nullptr;

    for (; itor != modelInfoMap.constEnd(); ++itor)
    {
        modelId = itor.key();
        const n_mtautodelineationdialog::ST_SketchModel& modelItemInfo = itor.value();

        if (m_modelInfoMap.contains(modelId) || modelId == 0 || modelId != modelItemInfo.id)//id==0为默认模型
        {
            continue;
        }

        //更新信息
        m_modelInfoMap.insert(modelId, modelItemInfo);
        //添加列表项
        newItem = addModelItem(modelItemInfo.id, modelItemInfo.modelName);
        break;
    }

    //更新模型列表项宽度
    updateModelItemWidth();

    //说明有新导入的模型，1. 合并新增的模型器官信息；2. 切换到新导入模型
    if (nullptr != newItem)
    {
        n_mtautodelineationdialog::ST_OrganGroupInfo groupInfo;

        for (int i = 0; i < m_curAllGroupList.size(); ++i)
        {
            if (m_curAllGroupList[i].name == tr("临时分组") || m_curAllGroupList[i].id == 9)
            {
                groupInfo = m_curAllGroupList[i];
                break;
            }
        }

        //更新器官的组和描述信息
        QList<n_mtautodelineationdialog::ST_Organ> tempOrganInfoList;

        for (auto organItem : stOrganInfoVec)
        {
            //将模型名称填入ROI描述字段，且统一归到“临时分组”分组中
            if (organItem.modelId == modelId)
            {
                organItem.roiDesc = itor.value().modelName;
                organItem.organGroupInfoMap[groupInfo.id] = groupInfo;
            }

            tempOrganInfoList.append(organItem);
        }

        //更新缓存
        m_allOrganList.append(tempOrganInfoList);
        m_modelInfoMap[itor.key()] = itor.value();
        //
        ui.widget_table->mergeNewOrganInfo(tempOrganInfoList);
        ui.widget_table->mergeNewModelInfo(modelInfoMap);
        //切换到导入模型
        slotModelNameListItemClicked(newItem);
    }
}

void RoiLibraryWidget::slotModelDeleteResult(const QString& modelId, const QString& modelName, bool bSuccess, const QString& errMsg)
{
    int nModelId = modelId.toInt();//id是空的，不能用id

    for (QMap<int, n_mtautodelineationdialog::ST_SketchModel>::const_iterator itor = m_modelInfoMap.constBegin();
         itor != m_modelInfoMap.constEnd();
         ++itor)
    {
        if (modelName == itor.value().modelName)
        {
            nModelId = itor.key();
            break;
        }
    }

    if (bSuccess)
    {
        //删除缓存信息
        m_modelInfoMap.remove(nModelId);

        for (int i = m_allOrganList.size() - 1; i >= 0; --i)
        {
            if (m_allOrganList[i].modelId == nModelId)
            {
                m_allOrganList.removeAt(i);
            }
        }

        ui.widget_table->removeOrganAndModelCacheItem(nModelId);
        //移除列表项--模型
        removeModelItem(nModelId);
    }
    else
    {
        MtMessageBox::information(this, errMsg);
    }
}

void RoiLibraryWidget::initNameFilterEdit(const QStringList& filterNameList)
{
    //绑定搜索框信号
    connect(ui.mtLineEdit_roiName, SIGNAL(textChanged(const QString&)), this, SLOT(slotSearchNameChanged(const QString&)));
    //过滤列表
    MtComboBoxListView* listView = new MtComboBoxListView(this);
    MtScrollBar* bar = new MtScrollBar();
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    listView->setHorizontalScrollBar(bar);
    bar = new MtScrollBar();
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    listView->setVerticalScrollBar(bar);
    m_completerName = new QCompleter(this);
    m_completerName->setPopup(listView);
    m_completerNameStrListModel = new QStringListModel(this);
    m_completerNameStrListModel->setStringList(filterNameList);
    m_completerName->setModel(m_completerNameStrListModel);
    m_completerName->setCompletionMode(QCompleter::PopupCompletion);
    m_completerName->setFilterMode(Qt::MatchContains);
    m_completerName->setCaseSensitivity(Qt::CaseInsensitive);
    m_completerName->setMaxVisibleItems(10);
    ui.mtLineEdit_roiName->setCompleter(m_completerName);
    ui.mtLineEdit_roiName->setFocusOutCursorPosition(0);
    listView->setItemDelegate(new MtComboBoxListViewStyledItemDelegate(listView));
    QString styleSheetStr = "QAbstractItemView{background-color:rgba(@colorA0,0.5); border:1px solid rgba(@colorA2,1); font-size:12px;}"
        "QAbstractItemView::item{border:none; padding-left:11px; height:24px; color:rgba(@colorB4,1);}"
        "QAbstractItemView::item:hover{background-color:rgba(@colorA2,1)}"
        "QAbstractItemView::item:selected{background-color:rgba(@colorA2,1)}"
        "QScrollBar:vertical {border: none;background: rgba(@colorA0,0.5); width: 6px;margin: 0px 0 0px 0;}"
        "QScrollBar::handle:vertical {background:rgba(@colorA2,1);min-height: 30px;}"
        "QScrollBar::add-line:vertical {border: none;background: none;height: 0px;subcontrol-position: bottom;subcontrol-origin: margin;}"
        "QScrollBar::sub-line:vertical {border: none;background: none;height: 0px;subcontrol-position: top;subcontrol-origin: margin;}"
        "QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {border: none;background: none;color:rgba(@colorA0,0.5);height: 0px;width: 0px;}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {background: none;}";
    CMtCoreWidgetUtil::formatStyleSheet(styleSheetStr);
    listView->setStyleSheet(styleSheetStr);
    auto funcLineEditFinished = [&]()
    {
        slotSearchNameTriggered(ui.mtLineEdit_roiName->text());
    };
    connect(ui.mtLineEdit_roiName, &QLineEdit::returnPressed, funcLineEditFinished);
    connect(ui.mtLineEdit_roiName, SIGNAL(editingFinished()), ui.mtLineEdit_roiName, SIGNAL(returnPressed()));
    connect(m_completerName, SIGNAL(activated(const QString&)), this, SLOT(slotSearchNameTriggered(const QString&)));
}

void RoiLibraryWidget::initChNameFilterEdit(const QStringList& filterChNameList)
{
    //绑定搜索框信号
    connect(ui.mtLineEdit_roiChName, SIGNAL(textChanged(const QString&)), this, SLOT(slotSearchChNameChanged(const QString&)));
    //过滤列表
    MtComboBoxListView* listView = new MtComboBoxListView(this);
    MtScrollBar* bar = new MtScrollBar();
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    listView->setHorizontalScrollBar(bar);
    bar = new MtScrollBar();
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    listView->setVerticalScrollBar(bar);
    m_completerChName = new QCompleter(this);
    m_completerChName->setPopup(listView);
    m_completerChNameStrListModel = new QStringListModel(this);
    m_completerChNameStrListModel->setStringList(filterChNameList);
    m_completerChName->setModel(m_completerChNameStrListModel);
    m_completerChName->setCompletionMode(QCompleter::PopupCompletion);
    m_completerChName->setFilterMode(Qt::MatchContains);
    m_completerChName->setCaseSensitivity(Qt::CaseInsensitive);
    m_completerChName->setMaxVisibleItems(10);
    ui.mtLineEdit_roiChName->setCompleter(m_completerChName);
    ui.mtLineEdit_roiChName->setFocusOutCursorPosition(0);
    listView->setItemDelegate(new MtComboBoxListViewStyledItemDelegate(listView));
    QString styleSheetStr = "QAbstractItemView{background-color:rgba(@colorA0,0.5); border:1px solid rgba(@colorA2,1); font-size:12px;}"
        "QAbstractItemView::item{border:none; padding-left:11px; height:24px; color:rgba(@colorB4,1);}"
        "QAbstractItemView::item:hover{background-color:rgba(@colorA2,1)}"
        "QAbstractItemView::item:selected{background-color:rgba(@colorA2,1)}"
        "QScrollBar:vertical {border: none;background: rgba(@colorA0,0.5); width: 6px;margin: 0px 0 0px 0;}"
        "QScrollBar::handle:vertical {background:rgba(@colorA2,1);min-height: 30px;}"
        "QScrollBar::add-line:vertical {border: none;background: none;height: 0px;subcontrol-position: bottom;subcontrol-origin: margin;}"
        "QScrollBar::sub-line:vertical {border: none;background: none;height: 0px;subcontrol-position: top;subcontrol-origin: margin;}"
        "QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {border: none;background: none;color:rgba(@colorA0,0.5);height: 0px;width: 0px;}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {background: none;}";
    CMtCoreWidgetUtil::formatStyleSheet(styleSheetStr);
    listView->setStyleSheet(styleSheetStr);
    auto funcLineEditFinished = [&]()
    {
        slotSearchChNameTriggered(ui.mtLineEdit_roiChName->text());
    };
    connect(ui.mtLineEdit_roiChName, &QLineEdit::returnPressed, funcLineEditFinished);
    connect(ui.mtLineEdit_roiChName, SIGNAL(editingFinished()), ui.mtLineEdit_roiChName, SIGNAL(returnPressed()));
    connect(m_completerChName, SIGNAL(activated(const QString&)), this, SLOT(slotSearchChNameTriggered(const QString&)));
}

void RoiLibraryWidget::initLabelFilterEdit(const QStringList& filterLabelList)
{
    //绑定搜索框信号
    connect(ui.mtLineEdit_label, SIGNAL(textChanged(const QString&)), this, SLOT(slotSearchLabelChanged(const QString&)));
    //过滤列表
    MtComboBoxListView* listView = new MtComboBoxListView(this);
    MtScrollBar* bar = new MtScrollBar();
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    listView->setHorizontalScrollBar(bar);
    bar = new MtScrollBar();
    bar->setMtType(MtScrollBar::MtType::scrollbar1);
    listView->setVerticalScrollBar(bar);
    m_completerLabel = new QCompleter(this);
    m_completerLabel->setPopup(listView);
    m_completerLabelStrListModel = new QStringListModel(this);
    m_completerLabelStrListModel->setStringList(filterLabelList);
    m_completerLabel->setModel(m_completerLabelStrListModel);
    m_completerLabel->setCompletionMode(QCompleter::PopupCompletion);
    m_completerLabel->setFilterMode(Qt::MatchContains);
    m_completerLabel->setCaseSensitivity(Qt::CaseInsensitive);
    m_completerLabel->setMaxVisibleItems(10);
    ui.mtLineEdit_label->setCompleter(m_completerLabel);
    ui.mtLineEdit_label->setFocusOutCursorPosition(0);
    listView->setItemDelegate(new MtComboBoxListViewStyledItemDelegate(listView));
    QString styleSheetStr = "QAbstractItemView{background-color:rgba(@colorA0,0.5); border:1px solid rgba(@colorA2,1); font-size:12px;}"
        "QAbstractItemView::item{border:none; padding-left:11px; height:24px; color:rgba(@colorB4,1);}"
        "QAbstractItemView::item:hover{background-color:rgba(@colorA2,1)}"
        "QAbstractItemView::item:selected{background-color:rgba(@colorA2,1)}"
        "QScrollBar:vertical {border: none;background: rgba(@colorA0,0.5); width: 6px;margin: 0px 0 0px 0;}"
        "QScrollBar::handle:vertical {background:rgba(@colorA2,1);min-height: 30px;}"
        "QScrollBar::add-line:vertical {border: none;background: none;height: 0px;subcontrol-position: bottom;subcontrol-origin: margin;}"
        "QScrollBar::sub-line:vertical {border: none;background: none;height: 0px;subcontrol-position: top;subcontrol-origin: margin;}"
        "QScrollBar::up-arrow:vertical, QScrollBar::down-arrow:vertical {border: none;background: none;color:rgba(@colorA0,0.5);height: 0px;width: 0px;}"
        "QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {background: none;}";
    CMtCoreWidgetUtil::formatStyleSheet(styleSheetStr);
    listView->setStyleSheet(styleSheetStr);
    auto funcLineEditFinished = [&]()
    {
        slotSearchLabelTriggered(ui.mtLineEdit_label->text());
    };
    connect(ui.mtLineEdit_label, &QLineEdit::returnPressed, funcLineEditFinished);
    connect(ui.mtLineEdit_label, SIGNAL(editingFinished()), ui.mtLineEdit_label, SIGNAL(returnPressed()));
    connect(m_completerLabel, SIGNAL(activated(const QString&)), this, SLOT(slotSearchLabelTriggered(const QString&)));
}

void RoiLibraryWidget::initGroupFilterBox(const QStringList& filterGroupList)
{
    ui.mtComboBox_group->clear();
    ui.mtComboBox_group->addItem(tr("全部分组"));
    ui.mtComboBox_group->addItems(filterGroupList);
    connect(ui.mtComboBox_group, &MtComboBox::currentTextChanged, this, [&](const QString&)
    {
        setSearchFilterChanged();
    });
}

void RoiLibraryWidget::initModelNameList(const QMap<int, n_mtautodelineationdialog::ST_SketchModel>& modelInfoMap)
{
    QString styleSheetStr = "QListWidget::item:hover { background-color: rgba(0,0,0,0); color: rgba(@colorA3, 0.8); }"
        "QListWidget::item:selected { background-color: rgba(0,0,0,0); color: rgba(@colorA3, 0.8); }";
    CMtCoreWidgetUtil::formatStyleSheet(styleSheetStr);
    ui.tableModel->setStyleSheet(styleSheetStr);
    //
    connect(ui.tableModel, &QListWidget::currentItemChanged, this, &RoiLibraryWidget::slotModelNameListCurrentItemChanged);
    connect(ui.tableModel, &QListWidget::itemClicked, this, &RoiLibraryWidget::slotModelNameListItemClicked);
    //
    QMap<int/*modelID*/, n_mtautodelineationdialog::ST_SketchModel>::const_iterator itor = modelInfoMap.constBegin();
    int itemWidth = modelInfoMap.size() > 20 ? 253 : 260;

    for (; itor != modelInfoMap.constEnd(); ++itor)
    {
        if (itor.value().modelType == 1)
        {
            continue;
        }

        addModelItem(itor.key(), itor.value().modelName);
    }

    //     for (int i = 0; i < 10; ++i)
    //     {
    //         addModelItem(3 * i + 0, "modelName1");
    //         addModelItem(3 * i + 1, "modelName22");
    //         addModelItem(3 * i + 2, "modelName333");
    //     }
        //更新模型列表项宽度
    updateModelItemWidth();
}
