﻿// *********************************************************************************
// <remarks>
// FileName    : ImportEclipseTemplateDialog
// Author      : zlw
// CreateTime  : 2023-11-03
// Description : Eclipse模板设置弹窗
// </remarks>
// **********************************************************************************
#pragma once

#include "MtTemplateDialog.h"
#include "ui_ImportEclipseTemplateDialog.h"
#include "DataDefine/InnerStruct.h"
#include "GParseXmlThread.h"
#include "MtProgressDialog.h"
#include <Windows.h>


class ImportEclipseTemplateDialog : public MtTemplateDialog
{
    Q_OBJECT

public:
    ImportEclipseTemplateDialog(QWidget* parent = nullptr);
    ~ImportEclipseTemplateDialog();

    /// <summary>
    /// 设置检索目录完整路径
    /// 测试提出要记忆住路径,但是不显示在edit上，路径选择按钮点击时打开之前记忆的路径
    /// </summary>
    /// <param name="dirPath">[IN]待检索目录完整路径</param>
    /// <param name="isSub">[IN]是否检索子目录</param>
    void setSearchDirPath(const QString& dirPath, const bool isSub);

    /// <summary>
    /// 获取最新的检索目录完整路径
    /// </summary>
    /// <param name="dirPath">[OUT]待检索目录完整路径</param>
    /// <param name="isSub">[OUT]是否检索子目录</param>
    void getNewSearchDirPath(QString& dirPath, bool& isSub);

    /// <summary>
    /// 获取最新的Eclipse-ROI信息
    /// </summary>
    /// <returns>key-roiCode value-ST_EclipseRoi</returns>
    QMap<QString/*roiCode*/, ST_EclipseRoi> getNewEclipseRoiInfo();

    /// <summary>
    /// 获取导入的eclipse模板.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>eclipse模板.</returns>
    QMap<QString/*templateId*/, ST_EclipseTemplate> getNewEclipseTemplate();

    /// <summary>
    /// 是否需要同步信息.
    /// </summary>
    /// <remarks>
    /// <para> [Version]:*******</para>
    /// ChangeInfo:
    /// </remarks>
    /// <returns>bool.</returns>
    bool isNeedSyncInfo();

protected slots:
    /// <summary>
    /// 选择文件夹
    /// </summary>
    void onBtnFile();

    /// <summary>
    /// 全选
    /// </summary>
    void onMtCheckBox_all(int state);

    /// <summary>
    /// 读取XML进度
    /// </summary>
    void slotPersent(const int persent);

    /// <summary>
    /// 读取XML结束
    /// </summary>
    void slotDone(const QVector<ST_EclipseTemplate> eclipseTemplateVec);

    /// <summary>
    /// 某个单元格状态改变了(目前只有checkbox,可支持更多)
    /// </summary>
    /// <param name="cellItemIndex">[IN]一级列表行信息结构体</param>
    /// <param name="state">[IN]Qt::CheckState</param>
    void slotCellWidgetStateChange(const mtuiData::TableWidgetItemIndex& cellItemIndex, int state);

protected:
    virtual void onBtnCloseClicked() override;  //关闭按钮
    virtual void onBtnRight2Clicked() override; //取消按钮
    virtual void onBtnRight1Clicked() override; //确认按钮

protected:
    bool IsDotNetApp();
    static int CALLBACK BrowseCallbackProc(HWND hwnd, UINT uMsg, LPARAM lParam, LPARAM lpData);

private:
    Ui::ImportEclipseTemplateDialogClass ui;
    int m_allRecordSum = 0;
    int m_selectSum = 0;
    QString m_searchDir;
    GParseXmlThread m_parseXmlThread;
    MtProgressDialog m_mtProgressDialog = nullptr;
};
