﻿#include "ROIInsert2TemplateDlg.h"
#include <QAbstractButton>
#include "MtCheckBox.h"

ROIInsert2TemplateDlg::ROIInsert2TemplateDlg(QWidget* parent)
    : MtTemplateDialog(parent)
{
    ui.setupUi(this);
    //
    this->setMainLayout(ui.verticalLayout);
    this->setTitle(tr("提示"));
    this->setFixedSize(466, 130);
    //this->setMargin(24, 18, 24, 20);
    this->getButton(MtTemplateDialog::BtnRight1)->setText(tr("添加"));
    this->getButton(MtTemplateDialog::BtnRight2)->setText(tr("取消"));
    this->setAllowDrag(true);
    ui.listTemplate->setSelectionMode(QAbstractItemView::NoSelection);
    //绑定搜索框信号
    connect(ui.edit_TemplateSearch, SIGNAL(textChanged(const QString&)), this, SLOT(slotSearchTemplateNameChanged(const QString&)));
}

ROIInsert2TemplateDlg::~ROIInsert2TemplateDlg()
{
}

void ROIInsert2TemplateDlg::initList(QMap<int, QString> templateIdNameMap)
{
    QMap<int, QString>::const_iterator itor = templateIdNameMap.constBegin();

    for (; itor != templateIdNameMap.constEnd(); itor++)
    {
        MtCheckBox* templateItem = new MtCheckBox();
        templateItem->setMtType(MtCheckBox::checkbox1);
        //
        templateItem->setText(itor.value());
        QListWidgetItem* listItem = new QListWidgetItem;
        listItem->setSizeHint(QSize(260, 36));
        listItem->setData(Qt::UserRole, itor.key());
        ui.listTemplate->addItem(listItem);
        ui.listTemplate->setItemWidget(listItem, templateItem);
    }
}

QList<int> ROIInsert2TemplateDlg::getSelectTemplateId()
{
    return m_selectedTemplateIdList;
}

void ROIInsert2TemplateDlg::slotSearchTemplateNameChanged(const QString& text)
{
    int nCount = ui.listTemplate->count();

    for (int i = 0; i < nCount; ++i)
    {
        QListWidgetItem* item = ui.listTemplate->item(i);
        QVariant dataCur = item->data(Qt::UserRole);
        int curTemplateId = dataCur.toInt();
        QWidget* curWidget = ui.listTemplate->itemWidget(item);
        MtCheckBox* curListItem = qobject_cast<MtCheckBox*>(curWidget);
        item->setHidden(false);

        if (!curListItem->text().contains(text))
        {
            //curListItem->setChecked(false);
            item->setHidden(true);
        }
    }
}

void ROIInsert2TemplateDlg::onBtnRight1Clicked()
{
    m_selectedTemplateIdList.clear();
    //
    int nCount = ui.listTemplate->count();

    for (int i = 0; i < nCount; ++i)
    {
        QListWidgetItem* item = ui.listTemplate->item(i);
        QVariant dataCur = item->data(Qt::UserRole);
        int curTemplateId = dataCur.toInt();
        QWidget* curWidget = ui.listTemplate->itemWidget(item);
        MtCheckBox* curListItem = qobject_cast<MtCheckBox*>(curWidget);

        if (curListItem->isChecked() && curListItem->isVisible())
        {
            m_selectedTemplateIdList.append(curTemplateId);
        }
    }

    this->accept();
}

void ROIInsert2TemplateDlg::onBtnRight2Clicked()
{
    this->reject();
}

void ROIInsert2TemplateDlg::onBtnCloseClicked()
{
    this->reject();
}

