﻿#include "CustMultiSelectComboBox.h"
#include "ui_CustMultiSelectComboBox.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include <QMouseEvent>
#include <QPainter>
#include "CMtCoreDefine.h"

/*********************单元组件的Param*********************************/
CustMultiSelectComboBoxParam::CustMultiSelectComboBoxParam()
{
    _cellWidgetType = DELEAGATE_TYPE_User + 3;
}

CustMultiSelectComboBoxParam::~CustMultiSelectComboBoxParam()
{
}
QWidget* CustMultiSelectComboBoxParam::CreateUIModule(QWidget* parent)
{
    CustMultiSelectComboBox* comboBox = new CustMultiSelectComboBox(parent, _bEnabaleDrawSquare);
    comboBox->SetupCellWidget(*this);
    return comboBox;
}
/*****************************************************************/


/********************单元组件****************************/
CustMultiSelectComboBox::CustMultiSelectComboBox(QWidget* parent, bool bEnabaleDrawSquare/* = false*/)
    : QWidget(parent)
{
    ui = new Ui::CustMultiSelectComboBox;
    ui->setupUi(this);

    if (false == bEnabaleDrawSquare)
    {
        m_comboBox = new n_mtautodelineationdialog::MultiSelectComboBox(this);
    }
    else
    {
        m_comboBox = new MultiSelectComboBoxDrawSquareColor(this);
    }

    ui->horizontalLayout->addWidget(m_comboBox);
    m_comboBox->setMtType(MtComboBox::MtType::combobox1);
    m_comboBox->setElideMode(Qt::ElideRight);
    m_comboBox->setViewTextElideMode(Qt::ElideRight);
    m_comboBox->setEnabledWheelEvent(false);
    m_comboBox->installEventFilter(this);
}

CustMultiSelectComboBox::~CustMultiSelectComboBox()
{
    disconnect(m_comboBox, &n_mtautodelineationdialog::MultiSelectComboBox::currentTextChanged, this, &CustMultiSelectComboBox::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
    MT_DELETE(m_comboBox);
    MT_DELETE(ui);
}

void CustMultiSelectComboBox::SetupCellWidget(CustMultiSelectComboBoxParam& cellWidgetParam)
{
    disconnect(m_comboBox, &n_mtautodelineationdialog::MultiSelectComboBox::currentTextChanged, this, &CustMultiSelectComboBox::slotCurrentTextChanged);
    disconnect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
    //
    AddItems(cellWidgetParam._textList, cellWidgetParam._userDataList);

    if (cellWidgetParam._comboBoxIndexList.size() > 0)
    {
        m_comboBox->setCurrentIndex(cellWidgetParam._comboBoxIndexList);
    }
    else if (cellWidgetParam._text.size() >= 0/* && cellWidgetParam._textList.indexOf(cellWidgetParam._text) >= 0*/)
    {
        QStringList curTextList = cellWidgetParam._text.split(';');
        m_comboBox->setCurrentText(curTextList);
    }
    else
    {
        m_comboBox->setCurrentIndex(-1);
    }

    m_comboBox->setDisabledIndex(cellWidgetParam._comboBoxDisableIndexList);
    //
    connect(m_comboBox, &n_mtautodelineationdialog::MultiSelectComboBox::currentTextChanged, this, &CustMultiSelectComboBox::slotCurrentTextChanged);
    connect(m_comboBox, SIGNAL(currentIndexChanged(int)), this, SLOT(slotCurrentIndexChanged(int)));
}

bool CustMultiSelectComboBox::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString text = updateData.toString();
        QStringList updateText = text.split(';');
        m_comboBox->blockSignals(true);
        m_comboBox->setCurrentText(updateText);
        m_comboBox->blockSignals(false);
        return true;
    }
    else if (QMetaType::QStringList == userType)
    {
        QStringList textUpdate = updateData.toStringList();
        m_comboBox->blockSignals(true);
        m_comboBox->setCurrentText(textUpdate);
        m_comboBox->blockSignals(false);
        return true;
    }
    else if (QMetaType::Bool == userType)
    {
        bool bEditEnable = updateData.toBool();
        m_comboBox->setEnabled(bEditEnable);
        return true;
    }
    else if (QMetaType::Int == userType)
    {
        bool index = updateData.toInt();
        m_comboBox->setCurrentIndex(index);
        return true;
    }

    return false;
}

QStringList CustMultiSelectComboBox::currentText()
{
    return m_comboBox->currentText();
}

void CustMultiSelectComboBox::SetEnableEdit(bool bEdit)
{
    m_comboBox->setEnabled(bEdit);
}

void CustMultiSelectComboBox::setCurrentIndex(int index)
{
    m_comboBox->setCurrentIndex(index);
}

void CustMultiSelectComboBox::setCurrentIndex(const QList<int> indexList)
{
    m_comboBox->setCurrentIndex(indexList);
}

void CustMultiSelectComboBox::setCurrentText(const QString& text)
{
    m_comboBox->setCurrentText(text);
}

void CustMultiSelectComboBox::setCurrentText(const QStringList& text_list)
{
    m_comboBox->setCurrentText(text_list);
}

QStringList CustMultiSelectComboBox::GetAllItemStrList()
{
    QStringList itemStrList;

    for (int idx = 0; idx < m_comboBox->count(); idx++)
    {
        QString itemName = m_comboBox->itemText(idx);
        itemStrList << itemName;
    }

    return itemStrList;
}

void CustMultiSelectComboBox::AddItem(const QString& itemStr, const QVariant& auserData)
{
    m_comboBox->addItem(itemStr, auserData);
}

void CustMultiSelectComboBox::AddItems(const QStringList& itemStrList, const QList<QVariant>& userDataList/* = QList<QVariant>()*/)
{
    if (0 == userDataList.size() || itemStrList.size() != userDataList.size())
    {
        m_comboBox->addItems(itemStrList);
    }
    else if (itemStrList.size() == userDataList.size())
    {
        for (int i = 0; i < itemStrList.size(); ++i)
        {
            m_comboBox->addItem(itemStrList[i], userDataList[i]);
        }
    }
}

void CustMultiSelectComboBox::RemoveItem(const QString& itemStr)
{
    QStringList itemStrList = GetAllItemStrList();
    int index = itemStrList.indexOf(itemStr);
    m_comboBox->removeItem(index);
}

void CustMultiSelectComboBox::ClearItems()
{
    m_comboBox->clear();
}

void CustMultiSelectComboBox::SetDisableIndex(const QList<int>& indexList)
{
    m_comboBox->setDisabledIndex(indexList);
}

void CustMultiSelectComboBox::RegisterSquareColor(int index, const QColor& color)
{
    MultiSelectComboBoxDrawSquareColor* drawComboBox = qobject_cast<MultiSelectComboBoxDrawSquareColor*>(m_comboBox);

    if (nullptr == drawComboBox)
    {
        return;
    }

    drawComboBox->RegisterSquareColor(index, color);
}

void CustMultiSelectComboBox::UnRegisterSquareColor(int index)
{
    MultiSelectComboBoxDrawSquareColor* drawComboBox = qobject_cast<MultiSelectComboBoxDrawSquareColor*>(m_comboBox);

    if (nullptr == drawComboBox)
    {
        return;
    }

    drawComboBox->UnRegisterSquareColor(index);
}

n_mtautodelineationdialog::MultiSelectComboBox* CustMultiSelectComboBox::GetMtComboBox()
{
    return m_comboBox;
}

bool CustMultiSelectComboBox::eventFilter(QObject* obj, QEvent* evt)
{
    QEvent::Type type = evt->type();

    if (m_comboBox == obj)
    {
        if (QEvent::MouseButtonPress == type)
        {
            emit sigClicked(1);
        }
    }

    return QWidget::eventFilter(obj, evt);
}

void CustMultiSelectComboBox::resizeEvent(QResizeEvent* event)
{
    QWidget::resizeEvent(event);
}

void CustMultiSelectComboBox::mousePressEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton)
    {
        emit sigClicked(1);
    }

    QWidget::mousePressEvent(event);
}

void CustMultiSelectComboBox::slotCurrentTextChanged(const QString& text)
{
    emit currentTextChanged(text);
}

void CustMultiSelectComboBox::slotCurrentIndexChanged(int index)
{
    emit currentIndexChanged(index);
}

MultiSelectComboBoxDrawSquareColor::MultiSelectComboBoxDrawSquareColor(QWidget* parent /*= Q_NULLPTR*/)
    : MultiSelectComboBox(parent)
{
}

MultiSelectComboBoxDrawSquareColor::~MultiSelectComboBoxDrawSquareColor()
{
}

void MultiSelectComboBoxDrawSquareColor::RegisterSquareColor(int index, const QColor& color)
{
    m_indexSquareColorMap.insert(index, color);
}

void MultiSelectComboBoxDrawSquareColor::UnRegisterSquareColor(int index)
{
    m_indexSquareColorMap.remove(index);
}

void MultiSelectComboBoxDrawSquareColor::paintEvent(QPaintEvent* event)
{
    QList<int> indexList = this->currentIndex();
    bool bExist = false;

    for (int index : indexList)
    {
        if (m_indexSquareColorMap.contains(index))
        {
            bExist = true;
            break;
        }
    }

    if (indexList.size() == 0 || !bExist)
    {
        MultiSelectComboBox::paintEvent(event);
        return;
    }

    MultiSelectComboBox::paintEvent(event);
    DrawSquareAppendText(event);
}

void MultiSelectComboBoxDrawSquareColor::DrawSquareAppendText(QPaintEvent* event)
{
    QList<int> indexList = this->currentIndex();
    bool bExist = false;

    for (int index : indexList)
    {
        if (m_indexSquareColorMap.contains(index))
        {
            bExist = true;
            break;
        }
    }

    if (!bExist)
    {
        return;
    }

    QFontMetrics fm(qApp->font());
    int itemWidth = 10;

    //查找所有下拉框的长度
    do
    {
        for (int idx = 0; idx < this->count(); idx++)
        {
            QString itemName = this->itemText(idx);
            int tmpItemWidth = fm.width(itemName);

            if (tmpItemWidth > itemWidth)
            {
                itemWidth = tmpItemWidth;
            }
        }
    }
    while (0);

    for (int index : indexList)
    {
        QSize size = this->size();
        QPainter painter(this);
        painter.save();
        QColor color = m_indexSquareColorMap.value(index);
        painter.setPen(Qt::NoPen);

        if (false == this->isEnabled())
        {
            color.setAlpha(153);
        }
        else
        {
        }

        painter.setBrush(color);
        int width = 10;
        int height = 10;
        int x = itemWidth + 24;

        if (x > (size.width() - width))
        {
            x = size.width() / 2 - width / 2;
        }

        int y = size.height() / 2 - height / 2;
        QRectF rectangle(x, y, width, height);
        painter.drawRoundedRect(rectangle, 2, 2);
        painter.restore();
    }
}
