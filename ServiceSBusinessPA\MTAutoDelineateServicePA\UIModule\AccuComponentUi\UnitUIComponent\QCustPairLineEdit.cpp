﻿#include "AccuComponentUi\Header\UnitUIComponent\QCustPairLineEdit.h"
#include "ui_QCustPairLineEdit.h"
#include "AccuComponentUi\Header\QMTUIDefine.h"
#include <qDebug>
#include <QMouseEvent>

QCustPairLineEditParam::QCustPairLineEditParam()
{
    _cellWidgetType = DELEAGATE_QCustPairLineEdit;
}

QWidget* QCustPairLineEditParam::CreateUIModule(QWidget* parent)
{
    QCustPairLineEdit* lineEdit = new QCustPairLineEdit(parent);
    lineEdit->SetupCellWidget(*this);
    return lineEdit;
}

/*****************************************************************/

QCustPairLineEdit::QCustPairLineEdit(QWidget* parent)
    : QWidget(parent)
    , ui(new Ui::QCustPairLineEdit())
{
    ui->setupUi(this);
    ui->lineEdit->setContextMenuPolicy(Qt::NoContextMenu);
    ui->lineEdit->setMtType(MtLineEdit::MtType::lineedit2);
    ui->lineEdit_2->setContextMenuPolicy(Qt::NoContextMenu);
    ui->lineEdit_2->setMtType(MtLineEdit::MtType::lineedit2);
    connect(ui->lineEdit, SIGNAL(textChanged(const QString&)), this, SLOT(slotTextChanged(const QString&)));
    connect(ui->lineEdit_2, SIGNAL(textChanged(const QString&)), this, SLOT(slotTextChanged(const QString&)));
    connect(ui->lineEdit, SIGNAL(sigFocusOut()), this, SLOT(slotLineEditingFinished()));
    connect(ui->lineEdit_2, SIGNAL(sigFocusOut()), this, SLOT(slotLineEditingFinished()));
    connect(ui->lineEdit, SIGNAL(SigFocusIn()), this, SIGNAL(SigEditClicked()));
    connect(ui->lineEdit_2, SIGNAL(SigFocusIn()), this, SIGNAL(SigEditClicked()));
    ui->label_mid->hide();
    SetMargins(2, 1, 2, 1);
}

QCustPairLineEdit::~QCustPairLineEdit()
{
    if (_validator)
    {
        _validator->deleteLater();
        _validator = nullptr;
    }

    if (ui)
    {
        delete ui;
        ui = nullptr;
    }
}

void QCustPairLineEdit::SetupCellWidget(QCustPairLineEditParam& cellWidgetParam)
{
    this->SetRegExpStr(cellWidgetParam._regExpStr);
    ui->lineEdit->setText(cellWidgetParam._text1);
    ui->lineEdit_2->setText(cellWidgetParam._text2);

    if (cellWidgetParam._maxLength > 0)
    {
        ui->lineEdit->setMaxLength(cellWidgetParam._maxLength);
        ui->lineEdit_2->setMaxLength(cellWidgetParam._maxLength);
    }
}

bool QCustPairLineEdit::UpdateUi(const QVariant& updateData)
{
    int userType = updateData.userType();

    if (QMetaType::QString == userType)
    {
        QString str = updateData.toString();
        disconnect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        ui->lineEdit->setText(str);
        connect(ui->lineEdit, SIGNAL(editingFinished()), this, SLOT(slotLineEditingFinished()));
        return true;
    }
    else if (updateData.canConvert<QCustPairLineEditParam>())
    {
        QCustPairLineEditParam editParam = updateData.value<QCustPairLineEditParam>();
        MtWarningLineEdit::RangeParamInfo inputRange;
        inputRange.minValue = editParam.minValue;
        inputRange.maxValue = editParam.maxValue;
        inputRange.initValue = editParam.initValue;
        inputRange.decimals = editParam.decimals;
        inputRange.bContainMin = editParam.bContainMin;
        inputRange.bContainMax = editParam.bContainMax;
        inputRange.seperateLeftValue = editParam.seperateLeftValue;
        inputRange.seperateRightValue = editParam.seperateRightValue;
        inputRange.unit = editParam.unit;
        //ui->lineEdit->SetEditRange(inputRange);
        //ui->lineEdit_2->SetEditRange(inputRange);
        ui->lineEdit->setText(editParam._text1);
        ui->lineEdit_2->setText(editParam._text2);
    }

    return false;
}

void QCustPairLineEdit::SetEditRange(const QCustPairLineEditParam& inputInfo)
{
    MtWarningLineEdit::RangeParamInfo rangeInfo;
    rangeInfo.decimals = inputInfo.decimals;
    rangeInfo.minValue = inputInfo.minValue;
    rangeInfo.maxValue = inputInfo.maxValue;
    rangeInfo.unit = inputInfo.unit;
    rangeInfo.bContainMin = inputInfo.bContainMin;
    rangeInfo.bContainMax = inputInfo.bContainMax;
    ui->lineEdit->SetEditRange(rangeInfo);
    ui->lineEdit_2->SetEditRange(rangeInfo);
}

void QCustPairLineEdit::SetRegExpStr(QString& regExpStr)
{
    if (0 == regExpStr.size())
        return;

    if (nullptr != _validator)
    {
        delete _validator;
        _validator = nullptr;
    }

    QRegExp regExp(regExpStr);
    _validator = new QRegExpValidator(regExp, this);
    ui->lineEdit->setValidator(_validator);
    ui->lineEdit_2->setValidator(_validator);
}

void QCustPairLineEdit::SetItemValidator(QValidator* regExp)
{
    //这边不能对_validator赋值,外面delete后，析构会引起奔溃
    ui->lineEdit->setValidator(_validator);
    ui->lineEdit_2->setValidator(_validator);
}

void QCustPairLineEdit::SetMyStyleSheet(QString& sheetStr)
{
    ui->lineEdit->setStyleSheet(sheetStr);
    ui->lineEdit_2->setStyleSheet(sheetStr);
}

void QCustPairLineEdit::ClearWarning()
{
    ui->lineEdit->SetBoderShowStatus(true);
    ui->lineEdit->setTips("");
    ui->lineEdit_2->SetBoderShowStatus(true);
    ui->lineEdit_2->setTips("");
}

void QCustPairLineEdit::SetWarning(QList<int> indexLst, const QString& tips, bool bClear)
{
    if (bClear)
    {
        ClearWarning();
    }

    for (int i = 0; i < indexLst.size(); ++i)
    {
        if (0 == indexLst[i])
        {
            ui->lineEdit->SetBoderShowStatus(false);
            ui->lineEdit->setTips(tips);
        }

        if (1 == indexLst[i])
        {
            ui->lineEdit_2->SetBoderShowStatus(false);
            ui->lineEdit_2->setTips(tips);
        }
    }
}

void QCustPairLineEdit::SetText(QString& text1, QString& text2)
{
    ui->lineEdit->blockSignals(true);
    ui->lineEdit_2->blockSignals(true);
    ui->lineEdit->setText(text1);
    ui->lineEdit_2->setText(text2);
    ui->lineEdit->blockSignals(false);
    ui->lineEdit_2->blockSignals(false);
}

void QCustPairLineEdit::GetText(QString& text1, QString& text2)
{
    text1 = ui->lineEdit->text();
    text2 = ui->lineEdit_2->text();
}

QStringList QCustPairLineEdit::GetTips()
{
    QStringList tips;

    if (!ui->lineEdit->getTips().isEmpty())
    {
        tips.push_back(ui->lineEdit->getTips());
    }

    if (!ui->lineEdit_2->getTips().isEmpty())
    {
        tips.push_back(ui->lineEdit_2->getTips());
    }

    return tips;
}

MtWarningLineEdit* QCustPairLineEdit::GetLeftEdit()
{
    return ui->lineEdit;
}

MtWarningLineEdit* QCustPairLineEdit::GetRightEdit()
{
    return ui->lineEdit_2;
}

void QCustPairLineEdit::SetMidLabelVisible(bool isVisible)
{
    ui->label_mid->setVisible(isVisible);
}

void QCustPairLineEdit::SetMidLabelText(const QString& text)
{
    ui->label_mid->setText(text);
}

void QCustPairLineEdit::SetMargins(int left, int top, int right, int bottom)
{
    ui->horizontalLayout->setContentsMargins(left, top, right, bottom);
}

void QCustPairLineEdit::SetIsFocusInEventEnabled(bool bEnabled)
{
    ui->lineEdit->SetIsFocusInClearWarning(bEnabled);
    ui->lineEdit_2->SetIsFocusInClearWarning(bEnabled);
}

void QCustPairLineEdit::SetShowPlaceholderText(bool bIsShowPlaceholderText)
{
    ui->lineEdit->SetShowPlaceholderText(bIsShowPlaceholderText);
    ui->lineEdit_2->SetShowPlaceholderText(bIsShowPlaceholderText);
}

void QCustPairLineEdit::slotLineEditingFinished()
{
    QString text1 = ui->lineEdit->text();
    QString text2 = ui->lineEdit_2->text();
    emit sigPairEditFinish(text1, text2);
}

void QCustPairLineEdit::keyReleaseEvent(QKeyEvent* event)
{
    if (event->key() == Qt::Key_Tab)
    {
        if (ui->lineEdit->hasFocus())
        {
            ui->lineEdit_2->setFocus();
        }
        else if (ui->lineEdit_2->hasFocus())
        {
            emit SigLeftChangeCell();
        }

        return;
    }
    else if (event->modifiers() & (Qt::ShiftModifier | Qt::Key_Tab))
    {
        if (ui->lineEdit_2->hasFocus())
        {
            ui->lineEdit->setFocus();
        }
        else if (ui->lineEdit->hasFocus())
        {
            emit SigRightChangeCell();
        }

        return;
    }
}

void QCustPairLineEdit::mousePressEvent(QMouseEvent* event)
{
    emit SigEditClicked();
}

void QCustPairLineEdit::slotTextChanged(const QString& text)
{
    QObject* sender = this->sender();
    QString newText1 = ui->lineEdit->text();
    QString newText2 = ui->lineEdit_2->text();
    emit sigPairTextChange(newText1, newText2);
}