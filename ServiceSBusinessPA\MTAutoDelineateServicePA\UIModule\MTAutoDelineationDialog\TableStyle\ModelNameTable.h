﻿// *********************************************************************************
// <remarks>
// FileName    : ModelNameTable
// Author      : zlw
// CreateTime  : 2023-10-30
// Description : 模板名称table列表(内嵌于: AutoSketchTemplateWidget 自动勾画模板页签)
// </remarks>
// **********************************************************************************
#pragma once

#include <QString>
#include "AccuComponentUi\Header\QMTAbstractTableView.h"
#include "DataDefine/MTAutoDelineationDialogData.h"


/// <summary>
/// 表格信息
/// </summary>
class ModelNameTableInfo
{
public:
    int templateId;
    QString templateName;
    bool isUnattended = false;
};


class ModelNameTable : public QMTAbstractTableView
{
    Q_OBJECT

public:
    enum ColumnType
    {
        COL_Move,
        COL_ModelName,
        COL_Unattended
    };
    ModelNameTable(QWidget* parent = nullptr);
    ~ModelNameTable();

    /// <summary>
    /// 添加图片路径
    /// </summary>
    /// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
    void setImagePathHash(const QHash<QString, QString>& imagePathHash);

    /// <summary>
    /// 初始化
    /// </summary>
    void init();

    /// <summary>
    /// 添加一行
    /// </summary>
    /// <param name="modelNameTableInfo">[IN]模板名称信息</param>
    /// <param name="isSelect">[IN]是否选中</param>
    void addRow(const ModelNameTableInfo& modelNameTableInfo, bool isSelect = false, bool isEmit = false);

    /// <summary>
    /// 隐藏一行
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    /// <param name="isHide">[IN]true隐藏</param>
    void hideRow(const QString& rowValue, const bool isHide);

    /// <summary>
    /// 删除一行
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    void delRow(const QString& rowValue);

    /// <summary>
    /// 删除所有行
    /// </summary>
    void delAllRow();

    /// <summary>
    /// 置顶一行
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    void topRow(const QString& rowValue);

    /// <summary>
    /// 清空选中
    /// </summary>
    void clearSelect();

    /// <summary>
    /// 获取当前选中的行
    /// </summary>
    /// <returns>rowValue</returns>
    QString getCurSelectRow();

    /// <summary>
    /// 获取指定的行
    /// </summary>
    /// <param name="row">[IN]行号</param>
    /// <returns>rowValue</returns>
    QString getCurSelectRow(const int row);

    /// <summary>
    /// 获取当前模板id排序
    /// </summary>
    /// <returns>模板id排序</returns>
    QList<int> getCurTemplateIdSortList();

    /// <summary>
    /// 是否是首位item
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    /// <returns>true是</returns>
    bool isTopItem(const QString& rowValue);

    /// <summary>
    /// 更新模板名称
    /// </summary>
    /// <param name="rowValue">[IN]行唯一标识</param>
    /// <param name="templateName">[IN]模板名称</param>
    void updateTemplateName(const QString& rowValue, const QString& templateName);

    /// /// <summary>
    /// 设置拖拽使能
    /// </summary>
    /// <param name="enable">[IN]true使能</param>
    void setIsDropEnable(const bool enable);

    /// <summary>
    /// 设置是否是无人值守
    /// </summary>
    /// <param name="rowValue">[IN]模板id</param>
    /// <param name="isUnattended">[IN]true是</param>
    void setIsUnattended(const QString& rowValue, const bool isUnattended);

    /// <summary>
    /// 是否是无人值守item
    /// </summary>
    /// <param name="rowValue">[IN]模板id</param>
    /// <returns>是无人值守item</returns>
    bool isUnattendedItem(const QString& rowValue);

signals:
    void sigItemSelect(const QString rowValue, Qt::MouseButton button, QPoint point); //选中item，rowValue就是templateId
    void sigSortOccurs();   //发生排序

protected slots:

protected:
    /// <summary>
    /// 初始化表格
    /// </summary>
    void initTableView();

    /// <summary>
    /// 获取一个新的CellParamMap
    /// </summary>
    /// <param name="modelNameTableInfo">[IN]模板名称信息</param>
    /// <param name="outCellParamMap">[OUT]CellParamMap(key-列号0开始)</param>
    void getNewCellParamMap(const ModelNameTableInfo& modelNameTableInfo, QMap<int, ICellWidgetParam*>& outCellParamMap);

    /// <summary>
    /// 插入行
    /// </summary>
    /// <param name="row">[IN]行号</param>
    /// <param name="modelNameTableInfo">[IN]行信息</param>
    void insertRow(const int row, ModelNameTableInfo& modelNameTableInfo);

    /// <summary>
    /// 创建拖拽时的背景.
    /// </summary>
    /// <remarks>[Version]:1.0.0.1 Change: </remarks>
    void initItemDragShade();

    /// <summary>
    /// 移动行.
    /// </summary>
    /// <param name="templateId">要移动的模板id.</param>
    /// <param name="destRow">移动到目标行.</param>
    /// <remarks>[Version]:1.0.0.1 Change: </remarks>
    void MoveRow(const QString& templateId, int destRow);

protected:
    virtual void dropEvent(QDropEvent* event) override;
    virtual void mousePressEvent(QMouseEvent* event) override;
    virtual void mouseMoveEvent(QMouseEvent* event) override;
    virtual void mouseReleaseEvent(QMouseEvent* event) override;

private:
    bool m_dropEnable = true;   //是否允许拖拽
    bool m_dragging = false;    //是否正在拖拽
    QString m_preSelectRowValue;//上一次选中的行key
    QMap<int, ModelNameTableInfo> m_allModelNameTableInfoMap; //所有模板信息集合
    QHash<QString, QString> m_imagePathHash;    //图标map(key-name value-图片相对路径)

    QLabel* m_dragItemShade;
    int m_rowHeight = 36;
};