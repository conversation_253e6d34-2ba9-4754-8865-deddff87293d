﻿#include "LabelLibraryWidget.h"
#include "MtMessageBox.h"
#include "MTRoiLibraryDialog\EclipseSub\ImportEclipseTemplateDialog.h"


LabelLibraryWidget::LabelLibraryWidget(QWidget* parent)
    : QWidget(parent)
    , m_parentDialog(parent)
    , m_bNeedSave2File(false)
{
    ui.setupUi(this);
    //信号槽
    connect(ui.mtToolButton_add, &QToolButton::clicked, this, &LabelLibraryWidget::onMtToolButton_add);   //添加按钮
    connect(ui.mtToolButton_del, &QToolButton::clicked, this, &LabelLibraryWidget::onMtToolButton_del);   //删除按钮
    connect(ui.mtToolButton_flush, &QToolButton::clicked, this, &LabelLibraryWidget::onMtToolButton_flush);   //刷新
    connect(ui.mtPushButton_import, &QToolButton::clicked, this, &LabelLibraryWidget::onMtPushButton_import);   //导入
    connect(ui.mtLineEdit, &QLineEdit::textChanged, this, &LabelLibraryWidget::onMtLineEditTextChanged);      //搜索框文本变化
    connect(ui.widget_table, &LabelLibraryTable::sigTableInfoChanged, this, &LabelLibraryWidget::setTableChangedStatus);   //表格信息发生了变化
    connect(ui.widget_table, &LabelLibraryTable::sigTableInitialized, this, &LabelLibraryWidget::slotTableInitialized);   //列表初始化完成
}

LabelLibraryWidget::~LabelLibraryWidget()
{
}

/// <summary>
/// 添加图片路径
/// </summary>
/// <param name="imagePathHash">[IN]图片资源路径(key-name value-图片相对路径)</param>
void LabelLibraryWidget::setImagePathHash(const QHash<QString, QString>& imagePathHash)
{
    ui.mtToolButton_add->setPixmapFilename(imagePathHash["icon_add"]);
    ui.mtToolButton_del->setPixmapFilename(imagePathHash["icon_del"]);
    ui.mtToolButton_flush->setPixmapFilename(imagePathHash["icon_flush"]);
    ui.label_rotate->setUrl(imagePathHash["icon_loading"]);
    //搜索图标
    ui.mtLineEdit->setPlaceholderText(CMtLanguageUtil::type == english ? tr("搜索标签/匹配字段") : tr("搜索器官名称/标签/匹配字段"));
    m_searchIconAction = new QAction(QIcon::QIcon(imagePathHash["icon_zoom"]), "");
    ui.mtLineEdit->addAction(m_searchIconAction, QLineEdit::LeadingPosition);
}

void LabelLibraryWidget::showLoadingState(bool bShow)
{
    if (bShow)
    {
        ui.label_rotate->setFixedSize(14, 14);
        ui.label_rotate->setClockwise(true);
        ui.label_rotate->setInterval(5);
        ui.label_rotate->show();
        ui.label_data_loading->show();
    }
    else
    {
        ui.label_rotate->hide();
        ui.label_data_loading->hide();
    }
}

/// <summary>
/// 初始化
/// </summary>
/// <param name="dirPath">[IN]待检索目录完整路径</param>
/// <param name="isSub">[IN]是否检索子目录</param>
/// <param name="isUsedDef">[IN]是否默认使用Roi库颜色等</param>
/// <param name="roiTypeList">[IN]Roi类型集合</param>
/// <param name="stRoiLabelInfoVec">[IN]初始manteiaRoiLabel信息集合</param>
void LabelLibraryWidget::init(const QString& dirPath, const bool isSub, const bool isUsedDef, const QStringList& roiTypeList, const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& stRoiLabelInfoVec)
{
    //显示加载状态
    showLoadingState(true);
    //
    m_searchDir = dirPath;
    m_bSub = isSub;
    m_roiTypeList = roiTypeList;
    ui.mtCheckBox->setChecked(isUsedDef);
    ui.widget_table->init(roiTypeList, stRoiLabelInfoVec);
}

void LabelLibraryWidget::hideLabelListColumn(const QVector<int>& columnIndexVec, bool bHide /* = true */)
{
    ui.widget_table->hideLabelListColumn(columnIndexVec, bHide);
}

void LabelLibraryWidget::enableLabelListColumn(const QVector<int> columnIndexVec, bool bEnable /* = true */)
{
    ui.widget_table->enableLabelListColumn(columnIndexVec, bEnable);
}

void LabelLibraryWidget::hideImportButton()
{
    ui.mtPushButton_import->hide();
}

void LabelLibraryWidget::hideUseROIFirstButton()
{
    ui.mtCheckBox->hide();
}

/// <summary>
/// 获取所有manteiaRoiLabel信息
/// </summary>
/// <returns>所有manteiaRoiLabel信息</returns>
QList<n_mtautodelineationdialog::ST_RoiLabelInfo> LabelLibraryWidget::getAllRoiLabelInfo()
{
    return ui.widget_table->getAllRoiLabelInfo();
}

void LabelLibraryWidget::removeFocusFromTable()
{
    ui.widget_table->setFocus();
}

bool LabelLibraryWidget::isNeedSave2File()
{
    return m_bNeedSave2File;
}

void LabelLibraryWidget::resetSaveFileStatus()
{
    m_bNeedSave2File = false;
}

/// <summary>
/// 添加按钮
/// </summary>
void LabelLibraryWidget::onMtToolButton_add()
{
    ui.widget_table->addNewManteiaRoiLabel();
    setTableChangedStatus();
}

/// <summary>
/// 删除按钮
/// </summary>
void LabelLibraryWidget::onMtToolButton_del()
{
    ui.widget_table->delCurrentRow();
    setTableChangedStatus();
}

/// <summary>
/// 刷新按钮
/// </summary>
void LabelLibraryWidget::onMtToolButton_flush()
{
    ui.mtLineEdit->clear();
}

/// <summary>
/// 导入Eclipse模板按钮
/// </summary>
void LabelLibraryWidget::onMtPushButton_import()
{
    ImportEclipseTemplateDialog dlg(this);
    dlg.setSearchDirPath(m_searchDir, m_bSub);

    if (dlg.exec() == QDialog::Accepted)
    {
        //获取最新的检索文件夹配置
        dlg.getNewSearchDirPath(m_searchDir, m_bSub);
        int updateNum = 0, newNum = 0;
        //
        QSet<QString> roiTypeSet;

        for (int i = 0; i < m_roiTypeList.size(); i++)
        {
            roiTypeSet.insert(m_roiTypeList[i]);
        }

        //structureID充当manteiaLabel
        QMap<QString/*RoiCode*/, ST_EclipseRoi> roiCodeMap = dlg.getNewEclipseRoiInfo();

        for (QMap<QString/*RoiCode*/, ST_EclipseRoi>::iterator it = roiCodeMap.begin(); it != roiCodeMap.end(); it++)
        {
            if (it.value().structureID.isEmpty() == true)
                continue;

            n_mtautodelineationdialog::ST_RoiLabelInfo info;
            info.isbuiltIn = false;
            info.manteiaRoiLabel = it.value().structureID;
            info.roiName = it.value().structureID;
            info.roiAlias = info.manteiaRoiLabel + ";" + info.roiName;
            info.roiColor = "ff0000";
            info.roiType = roiTypeSet.contains(it.value().volumeType) ? it.value().volumeType : "NONE";
            info.roiCodeMap[n_mtautodelineationdialog::Manufacturer_Eclipse] = it.value().roiCodeMap;
            ui.widget_table->updateManteiaRoiLabel(info, updateNum, newNum);
        }

        MtMessageBox::NoIcon::information_Title(this, QString(tr("已更新ROI: ")) + QString::number(updateNum) + QString(tr("个")) + "\r\n" +
                                                QString(tr("已新增ROI: ")) + QString::number(newNum) + QString(tr("个")));
        setTableChangedStatus();
    }
}

/// <summary>
/// 搜索框文本变化
/// </summary>
void LabelLibraryWidget::onMtLineEditTextChanged(const QString& text)
{
    if (ui.widget_table->isTableInitialized())
    {
        ui.widget_table->hideShowRow(text);
    }
}

void LabelLibraryWidget::slotTableInitialized()
{
    //隐藏加载状态
    if (ui.widget_table->isTableInitialized())
    {
        showLoadingState(false);
        QString txt = ui.mtLineEdit->text();

        if (!txt.isEmpty())
        {
            onMtLineEditTextChanged(txt);
        }
    }
}

void LabelLibraryWidget::setTableChangedStatus()
{
    m_bNeedSave2File = true;
    return;
    MtTemplateDialog* parentDialog = qobject_cast<MtTemplateDialog*>(m_parentDialog);

    if (nullptr != parentDialog)
    {
        parentDialog->getButton(MtTemplateDialog::BtnRight1)->setEnabled(true);
    }
}