﻿// *********************************************************************************
// <remarks>
// FileName    : AutoDelineationDataOpt
// Author      : zlw
// CreateTime  : 2024-02-08
// Description : 自动勾画组件-数据交互
// </remarks>
// **********************************************************************************
#pragma once

#include <QObject>
#include <QList>
#include <QMap>
#include <QSet>
#include <QJsonObject>
#include <QJsonArray>
#include <iostream>

#include "Config/DBRoiLibraryConfig.h"

#include "DataDefine/MTAutoDelineationDialogData.h"
#include "MTAutoDelineateServiceDataDefine.h"

/// <summary>
/// 自动勾画数据操作类
/// </summary>
class AutoDelineationDataOpt : public QObject
{
    Q_OBJECT

public:
    /// <summary>
    /// 构造函数
    /// </summary>
    AutoDelineationDataOpt(QObject* parent = Q_NULLPTR);
    /// <summary>
    /// 析构函数
    /// </summary>
    ~AutoDelineationDataOpt();

    /**************************************************读取接口**************************************************/
    /// <summary>
    /// 获取不用的器官名集合
    /// </summary>
    /// <returns>不用的器官名集合</returns>
    static QSet<QString> getNotUseOrganOfOrganInfoJson();

    /// <summary>
    /// 获取所有分组信息
    /// </summary>
    /// <returns>key-ogangroupinfo表id value-ST_OrganGroupInfo</returns>
    static QMap<int, n_mtautodelineationdialog::ST_OrganGroupInfo> getAllGroupInfo();

    /// <summary>
    /// 获取所有分组信息并排序
    /// </summary>
    /// <returns>所有分组信息并排序</returns>
    static QList<n_mtautodelineationdialog::ST_OrganGroupInfo> getAllGroupInfoAndSort();
    static void getAllGroupInfoAndSort(QList<n_mtautodelineationdialog::ST_OrganGroupInfo>& allGroupList);

    /// <summary>
    /// 获取所有Organ信息,默认模型同名去重
    /// </summary>
    /// <param name="isEnbale">[IN]是否只收集使能的器官</param>
    /// <param name="outOrganList">[OUT]器官集合</param>
    /// <param name="outOrganMap">[OUT]器官集合 key-organinfoconfig表id</param>
    /// <param name="bDefaultRepeat">[IN]默认模型器官是否保留同名</param>
    static void getAllOrganUnique(const bool isEnbale, QList<n_mtautodelineationdialog::ST_Organ>& outOrganList, QMap<int, n_mtautodelineationdialog::ST_Organ>& outOrganMap, bool bDefaultRepeat = false);

    /// <summary>
    /// 获取指定modelId的Organ信息
    /// </summary>
    /// <param name="isEnbale">[IN]是否只收集使能的器官</param>
    /// <param name="modelId">[IN]模型id</param>
    /// <returns>指定modelId的Organ信息</returns>
    static QList<n_mtautodelineationdialog::ST_Organ> getAllOrganByModelId(const bool isEnbale, const int modelId);

    /// <summary>
    /// 获取所有AL-Organ信息
    /// </summary>
    /// <param name="isEnbale">[IN]是否只收集使能的器官</param>
    /// <returns>key-modelId</returns>
    static QMap<int, QList<n_mtautodelineationdialog::ST_Organ>> getALOrganByModelId(const bool isEnbale);

    /// <summary>
    /// 获取器官默认设置信息
    /// </summary>
    /// <param name="organDefaultConfigInfoPath">器官默认设置配置文件路径</param>
    /// <param name="stOrganDefaultList">器官默认设置信息</param>
    static void GetOrganDefaultConfigInfo(const QString& organDefaultConfigInfoPath, QList<n_mtautodelineationdialog::ST_Organ>& stOrganDefaultList);

    /// <summary>
    /// 获取默认模型
    /// </summary>
    /// <param name="isEnbale">[IN]是否只收集使能的器官</param>
    /// <returns>默认模型(就一个)</returns>
    static QList<n_mtautodelineationdialog::ST_SketchModel> getDefSketchModel(const bool isEnbale);

    /// <summary>
    /// 获取空勾画模型
    /// </summary>
    /// <param name="isEnbale">[IN]是否只收集使能的器官</param>
    /// <returns>空勾画模型(就一个)</returns>
    static QList<n_mtautodelineationdialog::ST_SketchModel> getEmptySketchModel(const bool isEnbale);

    /// <summary>
    /// 获取AL模型
    /// </summary>
    /// <returns>AL模型</returns>
    static QList<n_mtautodelineationdialog::ST_SketchModel> getALSketchModel(const bool isEnbale);
    static QMap<int, n_mtautodelineationdialog::ST_SketchModel> getALSketchModelMap(const bool isEnbale);

    /// <summary>
    /// 获取所有模板,剔除没归组的器官(没有排序)
    /// </summary>
    /// <returns>所有模板,剔除没归组的器官(没有排序)</returns>
    static QList<n_mtautodelineationdialog::ST_SketchModelCollection> getAllSketchCollection();

    /// <summary>
    /// 获取所有模板(没有排序，也不过滤数据)
    /// </summary>
    /// <returns>所有模板</returns>
    static QList<n_mtautodelineationdialog::ST_SketchModelCollection> getAllSketchCollectionOriginal();

    /// <summary>
    /// 获取排序后的所有模板,剔除没归组的器官
    /// </summary>
    /// <returns>排序后的所有模板,剔除没归组的器官</returns>
    static QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> getAllSketchCollectionSort();

    /// <summary>
    /// 获取排序后的所有无人值守模板,剔除没归组的器官
    /// </summary>
    /// <returns>排序后的所有无人值守模板,剔除没归组的器官</returns>
    static QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<n_mtautodelineationdialog::ST_SketchModelCollection>> getAllUnattendSketchCollectionSort();

    /// <summary>
    /// 获取所有无人值守模板id-名称集合
    /// </summary>
    /// <returns>无人值守模板id-名称集合</returns>
    static QMap<n_mtautodelineationdialog::EM_OptDcmType, QMap<int, QString>> getAllUnattendSketchCollectionName();

    /// <summary>
    /// 获取所有本地服务器名(SCP/共享文件夹/Ftp站点)
    /// </summary>
    /// <returns>key-1:共享文件夹 2:FTP服务器 4:SCP服务器</returns>
    static QMap<int, QStringList> getAllLocalServerName();

    /// <summary>
    /// 获取所有远程Scp服务器
    /// </summary>
    /// <returns>所有远程Scp服务器</returns>
    static QList<n_mtautodelineationdialog::ST_AddrSimple> getAllRemoteScpAddrList();

    /// <summary>
    /// 获取软件记忆的自动勾画导出地址信息(用于提前选中)
    /// </summary>
    /// <param name="outCheckExport">[OUT]是否打勾</param>
    /// <returns>软件记忆的自动勾画导出地址信息</returns>
    static n_mtautodelineationdialog::ST_AddrSimple getSoftMemoryAutoSketchExportAddr(bool& outCheckExport);

    /// <summary>
    /// 获取待合并的rt信息
    /// </summary>
    /// <param name="ctSeriesUID">[IN]图像的seriesUID</param>
    /// <param name="outMergeRtSopInsUIDList">[OUT]所有相关rtSopInsUID</param>
    /// <param name="outMergeRtValMap">[OUT]key-sopInsUID value-显示文本</param>
    static void getMergeRtInfo(const QString& ctSeriesUID, QStringList& outMergeRtSopInsUIDList, QMap<QString, QString>& outMergeRtValMap);

    /// <summary>
    /// 获取所有远程Scp服务器
    /// </summary>
    /// <returns>所有远程Scp服务器 key-serverName</returns>
    static QMap<QString/*serverName*/, n_mtautodelineationdialog::ST_AddrSimple> getAllRemoteScpAddrMap();

    /// <summary>
    /// 获取所有无人值守信息
    /// </summary>
    /// <param name="outUnattendedConfigMap">[OUT]无人值守信息</param>
    /// <param name="outUnattendedEnable">[OUT]无人值守开关</param>
    static void getUnattendedConfig(QMap<QString, n_mtautodelineationdialog::ST_UnattendedConfig>& outUnattendedConfigMap, bool& outUnattendedEnable);

    /// <summary>
    /// 获取已经被无人值守使用的勾画模板id
    /// </summary>
    /// <returns>已经被无人值守使用的勾画模板id</returns>
    static QSet<int> getUsedTemplateIdOfUnattended();

    /// <summary>
    /// 获取所有的标签信息.
    /// </summary>
    /// <param name="allRoiLabelInfoList">[OUT}返回所有的标签信息.</param>
    /// <param name="roiLibraryCfgVec">[IN}若该参数不为空，则直接从该参数中解析数据.</param>
    static void getAllRoiLabelInfoList(QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& allRoiLabelInfoList, std::vector<DBRoiLibraryConfig>& roiLibraryCfgVec = std::vector<DBRoiLibraryConfig>());

    /// <summary>
    /// 获取当模板被无人值守使用时，是否显示提示框
    /// </summary>
    static bool getIsShowTipOfModUnattendUsed();

    /// <summary>
    /// 获取无人值守默认导出地址
    /// </summary>
    static n_mtautodelineationdialog::ST_AddrSimple getDefaultExportAddrUnattended();

    /// <summary>
    /// 获取unattendedfeature表获取无人值守开关是否打开
    /// </summary>
    /// <returns>true打开</returns>
    static bool getUnattendedEnable();


    /**************************************************写入接口**************************************************/
    /// <summary>
    /// 更新自动导出到共享文件夹配置
    /// </summary>
    /// <param name="stAddrSimple">[IN]导出地址信息</param>
    /// <returns>成功true</returns>
    static bool updateExportDirConfigInfo(const n_mtautodelineationdialog::ST_AddrSimple& stAddrSimple);

    /// <summary>
    /// 更新勾画模板id排序
    /// </summary>
    /// <param name="templateIdMap">[IN]勾画模板id</param>
    /// <returns>成功true</returns>
    static bool updateSketchTemplateIdSort(const QMap<n_mtautodelineationdialog::EM_OptDcmType, QList<int>>& templateIdMap);

    /// <summary>
    /// 更新模板信息
    /// </summary>
    /// <param name="optTypeEnum">[IN]操作类型</param>
    /// <param name="stSketchCollection">[IN]模板信息</param>
    /// <param name="newTemplateId">[IN]如果是新增则返回数据库modelCollection表id</param>
    /// <returns>成功true</returns>
    static bool updateSketchCollection(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const n_mtautodelineationdialog::ST_SketchModelCollection stSketchCollection, int& newTemplateId);

    /// <summary>
    /// 更新数据库ModelCollection表是否是无人值守字段isUnattended
    /// </summary>
    /// <param name="templateId">[IN]数据库modelcollection表id</param>
    /// <param name="isUnattended">[IN]是否</param>
    /// <returns>成功true</returns>
    static bool updateSketchCollectionUnattended(const int templateId, const bool isUnattended);

    /// <summary>
    /// 更新无人值守信息
    /// </summary>
    /// <param name="optTypeEnum">[IN]操作类型</param>
    /// <param name="customId">[IN]unattendedfeature表customId</param>
    /// <param name="stUnattendedConfig">[IN]无人值守信息</param>
    /// <returns>成功true</returns>
    static bool updateUnattemdedInfoToDB(const n_mtautodelineationdialog::EM_OptType optTypeEnum, const QString customId, const n_mtautodelineationdialog::ST_UnattendedConfig stUnattendedConfig);

    /// <summary>
    /// 更新数据库FeaturesConfig表unattended_rule_config字段开关
    /// </summary>
    /// <param name="unattendedEnable">[IN]true使能</param>
    static void updateEnableOfUnattendedDB(const bool unattendedEnable);

    /// <summary>
    /// 更新数据库unattendedfeature表enable字段
    /// </summary>
    /// <param name="customId">[IN]unattendedfeature表customId</param>
    /// <param name="ruleEnable">[IN]true使能</param>
    static void updateEnableOfUnattendedfeatureDB(const QString customId, const bool ruleEnable);

    /// <summary>
    /// 更新数据库unattendedfeature表unattend_default_exportAddr字段
    /// </summary>
    static void updateUnattendDefaultExportAddrDB(const n_mtautodelineationdialog::ST_AddrSimple& stAddr);

    /// <summary>
    /// 更新器官列表信息到数据库
    /// </summary>
    /// <param name="organInfoList">器官信息.</param>
    static void updateOrganInfo2DB(const QList<n_mtautodelineationdialog::ST_Organ>& organInfoList);

    /// <summary>
    /// 更新标签列表到数据库
    /// </summary>
    /// <param name="roiLabelInfoList">标签列表信息.</param>
    static void updateRoiLabelInfo2DB(const QList<n_mtautodelineationdialog::ST_RoiLabelInfo>& roiLabelInfoList);

    /// <summary>
    /// 设置当模板被无人值守使用时，是否显示提示框
    /// </summary>
    static void setIsShowTipOfModUnattendUsed(const bool isShow);


    /**************************************************JSON转换**************************************************/
    /// <summary>
    /// 自动勾画信息转结构体
    /// </summary>
    /// <param name="seriesUID">[IN]图像seriesUID</param>
    /// <param name="sketchCollection">[IN]待勾画的模板信息</param>
    /// <param name="checkedMergeRt">[IN]是否选择了合并已有rtStruct选项</param>
    /// <param name="mergeRtSopInsUID">[IN]合并已有rtStruct-sopInsUID</param>
    /// <param name="checkedExport">[IN]是否选择了勾画完导出选项</param>
    /// <param name="stAddrSimple">[IN]导出地址简易信息</param>
    static ST_SketchBusinessInfo ToStructOfAutoSketch(const QString& seriesUID
                                                      , const n_mtautodelineationdialog::ST_SketchModelCollection& sketchCollection
                                                      , const bool checkedMergeRt
                                                      , const QString mergeRtSopInsUID
                                                      , const bool checkedExport
                                                      , const n_mtautodelineationdialog::ST_AddrSimple& stAddrSimple);
    /// <summary>
    /// 自动勾画器官选择
    /// </summary>
    /// <param name="sketchCollection">[IN]待勾画的模板信息</param>
    /// <param name="sketchOrganVec">返回勾画器官</param>
    /// <param name="trainOrganVec">返回训练器官</param>
    /// <param name="emptyOrganVec">返回空勾画器官</param>
    static bool GetSketchOrganInfo(const n_mtautodelineationdialog::ST_SketchModelCollection& sketchCollection,
                                   std::vector<ST_REQ_AutoSketchSuper_Organ>& sketchOrganVec,
                                   std::vector<ST_REQ_AutoSketchSuper_Organ>& trainOrganVec,
                                   std::vector<ST_REQ_AutoSketchSuper_Organ>& emptyOrganVec);

private:
    /// <summary>
    /// QString转QJsonObject
    /// </summary>
    static QJsonObject qStringToqJsonObject(const QString& jsonString);

    /// <summary>
    /// QString转QJsonArray
    /// </summary>
    static QJsonArray qStringToqJsonArray(const QString& jsonString);

    /// <summary>
    ///QJsonObject转QString
    /// </summary>
    static QString qJsonObjectToqString(const QJsonObject& jsonObject);

    /// <summary>
    ///QVariantList转QStringList
    /// </summary>
    static QStringList qVariantListToQStringList(const QVariantList& variantList);

    /// <summary>
    /// 格式化时间
    /// </summary>
    static QString getDateTimeStr(QString dateStr, QString timeStr);

    // <summary>
    /// 解析JSON文件到QJsonObject
    /// </summary>
    /// <param name="filePath">[IN]json文件完整路径</param>
    /// <param name="errMsg">[OUT]错误信息</param>
    /// <returns>QJsonObject</returns>
    static QJsonObject getQJsonObjectOfFile(const QString& filePath, QString& errMsg);
};

