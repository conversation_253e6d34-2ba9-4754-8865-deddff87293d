﻿#pragma once

#include <QObject>
#include <QWidget>
#include <QVariantHash>
#include <QJsonObject>
#include <QJsonValue>
#include <QJsonArray>
#include <QJsonDocument>
#include <QMutex>
#include "QMTEnumDef.h"


using namespace std;



Q_DECLARE_METATYPE(QVariantList)
//Q_DECLARE_METATYPE(QWidget)
//class QMTAbstractRowItemWidget;

#define TOString(key)   #key
//既可以是一个病人，也可以是一个病人对应多个序列
class  QMTRowRecord : public QVariantHash
{
public:
    /// <summary>
    /// 增加参数
    /// 1. 也可以嵌套QMTRowRecord进入，形成一个树形结构
    ///    示例： params.SetValue("2", childnode);
    inline  void SetValue(QString name, QVariant value)
    {
        this->insert(name, value);
    }
    /// <summary>
    /// 增加参数表，注意valus带 {}
    /// 示例：
    /// params.SetValueList("stringlist", { "abc" , "def" , "ghijklmnopqrst" });
    /// params.SetValueList("numlist", { 13 , 456 , 789 , 1234 , 5678 , 912 , 456.123 , 456 });
    inline  void SetValueList(QString name, QList<QVariant> values)
    {
        QList<QVariant> tmplist;

        for (auto ptr = values.begin(); ptr != values.end(); ptr++)  //类似于容器的操作
        {
            tmplist.append(*ptr);
        }

        this->insert(name, tmplist);
    }

    //判断两者是否相等，可根据主键判断，也可以根据所有的key
    inline bool Equal(QVariantHash& record, QString mainKey = QString())
    {
        if (this == &record)
        {
            return true;
        }

        if (mainKey.size() > 0)
        {
            QString value = this->find(mainKey).value().toString();
            QString tmp = record[mainKey].toString();

            //if (value != tmp)
            if (QString::compare(value, tmp, Qt::CaseInsensitive) != 0)
            {
                return false;
            }
        }
        else
        {
            QMTRowRecord::iterator it;

            for (it = record.begin(); it != record.end(); ++it)
            {
                QString key = it.key();
                QVariant value = it.value();

                if (value.canConvert(QMetaType::QString))
                {
                    if (value.toString() != this->find(key).value().toString())
                    {
                        return false;
                    }
                }
            }
        }

        return true;
    }
};

Q_DECLARE_METATYPE(QMTRowRecord)


class  QMTListViewModelIndex
{
public:

    QMTListViewModelIndex()
    {}
    QMTListViewModelIndex(int row, int parentRow = -1, bool isFirstLevel = true)
    {
        _isFirstLevel = isFirstLevel;
        _parentRow = parentRow;
        _row = row;
    }
    ~QMTListViewModelIndex()
    {}

    bool operator == (const QMTListViewModelIndex& data) const
    {
        if (this == &data)
        {
            return true;
        }

        if (_isFirstLevel != data.IsFirstLevel()/* ||
            this->_type != data._type*/)//暂时不通过类型判断
        {
            return false;
        }

        if (true == _isFirstLevel)
        {
            if (this->_firstValue != data._firstValue)
                return false;
        }
        else
        {
            if (this->_firstValue != data._firstValue ||
                this->_secondValue != data._secondValue ||
                this->_secondChildValue != data._secondChildValue)
                return false;
        }

        return true;
    }
    bool operator != (const QMTListViewModelIndex& data) const
    {
        if (this == &data)
        {
            return false;
        }

        if (_isFirstLevel != data.IsFirstLevel()/* ||
            this->_type != data._type*/)//暂时不通过类型判断
        {
            return true;
        }

        if (true == _isFirstLevel)
        {
            if (this->_firstValue != data._firstValue)
                return true;
        }
        else
        {
            if (this->_firstValue != data._firstValue ||
                this->_secondValue != data._secondValue ||
                this->_secondChildValue != data._secondChildValue)
                return true;
        }

        return false;
    }
    int GetParentRow() const
    {
        return _parentRow;
    }
    int Row() const
    {
        return _row;
    }
    bool IsFirstLevel() const
    {
        return _isFirstLevel;
    }
public:
    QString _firstValue;            //一级主键,必须存在
    QString _secondValue;           //二级主键,是二级必须存在
    QString _secondChildValue;      //二级子项主键,二级子项必须存在
    int _secondChildIndex = -1;     //二级子项下标
    int _type = 0;//1:patient, 2:series, 3:rt,见EPerRowType
    bool _isFirstLevel = false;     //是否一级列表, 必须存在
    int _parentRow = -1;            //一级下标
    int _row = -1;                  //当前下标
    //数据内容
    int _checkedState = 0;          //是否打钩
    QJsonObject _expandDate;        //拓展数据
};

/// <summary>
/// 患者一对多CT(MR等），CT一对多Rt，Rt一对多Dose
/// </summary>
#define RtMainKey  "referencedSeriesUID" //同序列多个tr文件这个值是相同的
#define RtUniqueKey "sopInsUID"          //每个rt文件的key
#define DoseMainKey  "referencedRtStructSopInsUID" //同Rt下多个Dose此值相同
#define DoseUniqueKey "sopInsUID"          //每个Dose文件的key

enum PushOrder
{
    Push_Back,   //push back
    Push_Front, //push front

};
/// <summary>
/// 列表model
/// </summary>
class  QMTListModel : public QObject
{
    Q_OBJECT

public:
    QMTListModel(QObject* parent = nullptr);
    ~QMTListModel();
    void DebugIsCTExist();
    /******************** add ***************************************/
    void SetMainKey(int column, QString mainKey);
    void AddItem(QMTRowRecord, bool isUpdate = true);
    void AddRtRecord(QMTRowRecord rowRecord);//only add rt file
    void AddRtFileWidget(QString, QString, QString, QWidget*);
    void AddFirstLevelWidget(QString, QWidget*);
    void AddSecondLevelWidget(QString, QString, QWidget*);
    /************************ set ******************************************/
    void SetSeriesDeleteFlag(QString);
    void SetRtDeleteFlag(QString);
    void SetPushOrder(PushOrder);
    /******************** remove ***************************************/
    void RemoveSelectItems();
    void ResetModel();
    void RemoveSeriesList(QStringList);
    void RemoveRtList(QStringList);
    /******************** get ***************************************/
    int GetPushOrder();
    QList<QMTRowRecord>& GetDataModel();
    int FirstRowRecordIndex(QMTRowRecord);
    int FirstRowRecordIndex(QString);
    QVariantHash GetFirstLevelHash(QString, int& index);
    QVariantHash GetSecondLevelItemHash(int row, QString value);
    QMTListViewModelIndex ModelIndex(QString, QVariant);
    QVariant value(QMTListViewModelIndex, QString);
    QVariantHash Value(QString seriesUID);
    QJsonObject valueObj(QMTListViewModelIndex);
    QList<QVariantHash>valueList(int level = 0);
    QList<QVariantHash>valueList(QList<QMTListViewModelIndex>&);
    QVariantList RtValueList(QString, QString);
    /******************** update ***************************************/
    void UpdateValue(QString seriesUID, QString, QVariant);
    void UpdateValue(QString seriesUID, QJsonObject);
    void UpdateValue(QMTListViewModelIndex, QString, QVariant);
    void UpdateValue(QMTListViewModelIndex, QJsonObject);
    void UpdateRtValue(QString sopInsUID, QJsonObject);//此接口用于更新除key外的值
    bool UpdateFirstValue(int, QString, QString);
    void UpdateFirstValue(QString key, QString, QString);
    /******************** sort ***************************************/
    QVariantList OrderHashByList(QString key, QVariantList hashList, QStringList strList);
    void SortPatientID(QStringList);
    void OrderBySeriesUID(QString, QStringList);//仅仅对ct排序
    void OrderBySeriesUID(QString, QList<std::pair<QString, QStringList>>);//rt也参与排序
    QVariantList GetRemainRtHash(QVariantList hashList, QList<std::pair<QString, QStringList>> seriesPairList);//获取单独RT结构
    /***********************************************************/
    /// 从QJsonObject转换
    virtual void FromJsonObject(QJsonObject obj);
    /// 转换为OJsonObject，字符串为空的就不再记录json，避免浪费资源
    virtual QJsonObject ToJsonObject();

signals:
    //add
    void sigAddItem(QString, QJsonObject obj);
    void sigAddRtFiles(QString, QJsonObject, int existState);//可能一次新增多个rt文件
    //update
    void sigUpdateValue(QMTListViewModelIndex, QString, QVariant);
    void sigUpdateValue(QMTListViewModelIndex, QJsonObject);
    void sigUpdateViewValue(QMTListViewModelIndex, QJsonObject);
    void sigUpdateFirstViewValue(int, QString, QString);
    //remove
    void sigRemoveSelectItems();
    void sigRemoveItems(QStringList);
    void sigRemoveRtItems(QStringList);
    void sigResetModel();
    void sigResetModelFinish();
    void sigRemoveItemsFinish();
    //sort
    void sigSortPatientID(QStringList);
    void sigSortBySeriesUID(QString);

private slots:
    //void slotRemoveListViewItems(QJsonObject);
    void slotResetListViewFinish();
    void slotRemoveItemsViewFinish();
private:
    //void RmoveItem(int row, int parentRow);
    //void SetRemoveItemDeleteStatus(int row, int parentRow);
    void UpdateModelValue(QMTListViewModelIndex, QJsonObject);
    int IsRtExist(QString sopInsUID, QVariantList);//传入的是rtlist
    void CheckFirstIsNeedDelete();
    void RemoveModelItems();
    void RemoveItems(QStringList);
    bool IsAllRtDelete(QVariantList);
    void RemoveItemsWithDelete();

public:
    QMap<int, QString> _mainKeyMap; //用于判断是否相等的主键
    QList<QMTRowRecord> _listDataModel;//tree数据模型
    int _pushOrder = Push_Back;//0:back, 1:front
    QMutex _mutex;
};
